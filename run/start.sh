#! /usr/bin/sh
# 从 config.ini 中读取 CMD_LINE 配置，并作为命令执行进行启动.
# @11:01 2019/07/02

# config.ini 配置说明：
# 
# | config                                        | 用于                          | 性能参数                          | setup_dpdk_env.sh 配合          |
# |-----------------------------------------------+-------------------------------+-----------------------------------+---------------------------------|
# | config.ini                                    | 线上生产运行                  | 10G 网卡 + 17 核心 cpu + 40G 巨页 | setup_dpdk_env.sh -b eth0 eth1  |
# | config_devel_1G-IF_12-cores_20G-hugepages.ini | 功能开发(replay 模式)         | 1 G 网卡 + 12 核心 cpu + 20G 巨页 | setup_dpdk_env.sh -m 20 -b eth0 |
# | config_devel_read_from_pcap_dir.ini           | 功能开发(6G内存 + 6核 centos) | 4 核心 cpu (巨页无需配置)         | setup_dpdk_env.sh -b            |
#
# 修改为 replay 模式的开发配置：
# ln -sf ../etc/config_devel_1G-IF_12-cores_20G-hugepages.ini config.ini
# ../env/setup_dpdk_env.sh -m 20 -b eth0

cd $(dirname $0)

CONFIG_PATH='./config.ini'

# check config file exist.
if [ ! -f $CONFIG_PATH ]
then
    echo "there is no $CONFIG_PATH"
    exit
fi

# drop blanks around '=' and eval the config file.
eval "$(sed 's/[[:space:]]*=[[:space:]]*/=/g' $CONFIG_PATH)"

# check CMD_LINE exist.
if [ -z "$CMD_LINE" ]
then
    echo "$CONFIG_PATH should define CMD_LINE."
    exit
fi

if [ "N"$1 == "Ngdb" ]
then
     CMD_LINE="gdb --args ${CMD_LINE}"
fi

# exec cmd.
echo "CMD_LINE is $CMD_LINE"
eval $CMD_LINE
