/****************************************************************************************
 * 文 件 名 : dpi_wx_location_sharing.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq         2021/12/07
 修改: chenzq         2021/12/07
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <arpa/inet.h>

#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "wxcs_def.h"
#include "jhash.h"


#define LS_PAYLOAD_MIN_LEN  32  // 每包最小负载


// wx 共享实时位置端口
#define LS_PORT_00      80
#define LS_PORT_01      8000 
#define LS_PORT_02      8080
#define LS_PORT_03      16285
// wx 共享实时位置端口数量，根据上面展示端口判断
#define LS_PORT_CNT     4

// 协议识别标识
#define LS_PAYLOAD_FIRST        0xd5    // 包头标识字段
#define LS_IDENTIFY_FLAG_0      0x0a
#define LS_IDENTIFY_FLAG_10     0x21
#define LS_IDENTIFY_FLAG_11     0x2c    // 0x21 和 0x2c 标识字段每个报文只会出现其中一个
#define LS_IDENTIFY_FLAG_2      0x10
#define LS_IDENTIFY_FLAG        0x1801


wxc_handle  g_wxls_handle = NULL;

static 
void wx_ls_timeout(struct flow_info *flow, void *ptr)
{
    int                     C2S           = 0;
    char                    Hash_Key[32];
    int                     salt          = 0;
    void                   *hash          = NULL;
    ST_WXLocSharing *value = NULL;
    struct sonwden          sonwden;

    if (ptr == NULL) return;

    value = (ST_WXLocSharing *)ptr;

    // value->isTimeout = 1;

    // 发送给聚合
    dpi_TrailerUpdateTS(&value->trailer);
    wxc_sendMsg(g_wxls_handle, (const unsigned char*)value, sizeof(ST_WXLocSharing), WXCS_WX_LOC_SHARING);

    return;
}

static 
void init_app_session(struct flow_info * flow)
{
    if(NULL == flow) {
        return;
    }

    if(NULL == flow->app_session) {
        ST_WXLocSharing * data;
        data = malloc(sizeof(ST_WXLocSharing));
        if(NULL == data)
        {
            DPI_LOG(DPI_LOG_ERROR, "error on malooc ST_MediaChat");
            exit(-1);
        }
        memset(data, 0, sizeof(ST_WXLocSharing));
        flow->app_session = data;
        flow->flow_EOF = wx_ls_timeout;
        data->begin_time = time(NULL);
    }

    if (NULL == g_wxls_handle) {
        wxc_init(&g_wxls_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    }
}


static int
dissect_wx_loc_sharing(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_WX_LOCSHARING] == 0)
    {
        return 0;
    }

    uint32_t    offset = 0;
    uint8_t     unuse_len = 0;
    int         c2s = 0;
    ST_WXLocSharing * value = NULL;
    time_t      now;

    // app sesion 初始化
    init_app_session(flow);
    value = (ST_WXLocSharing *)flow->app_session;

    // 包头识别  
    offset += 1;

    // unuse offset
    offset += 4;

    // flag 0
    offset += 1;

    // flag 1
    offset += 1;

    // flag 2
    offset += 1;

    // 无用段落
    unuse_len = get_uint8_t(payload, offset);
    offset += 1;
    // offset + nuse_len + flag2 + sessionid + flag3 > payloadlen
    if (offset + unuse_len + 1 +4 + 2 >= payload_len) {
        return -1;
    }
    offset += unuse_len;

    // flag 3
    offset += 1;

    // session id
    uint32_t session = get_uint32_t(payload, offset);
    memcpy(&value->sessionid+0, &session, sizeof(session));
    memcpy(((char*)value->sessionid)+4, "\x00\x00\x00\x00", 4);
    offset += 4;


    if (!value->tuple_flag) {
        struct sonwden sonwden;    
        memset(&sonwden, 0, sizeof(sonwden));
        get_ip_port(&flow->pkt, &sonwden, &c2s);

        value->ip_version            = sonwden.ip_ver;
        memcpy(value->client_ip.ipv6,  sonwden.client_ip.ipv6, 16);
        memcpy(value->server_ip.ipv6,  sonwden.server_ip.ipv6, 16);
        value->client_port           = sonwden.client_port;
        value->server_port           = sonwden.server_port;
    }

    if (!value->trailer_flag) {
        dpi_TrailerParser(&value->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
        dpi_TrailerGetMAC(&value->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
        dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);
        dpi_TrailerSetDev(&value->trailer, g_config.devname);        // 解析板号
        dpi_TrailerSetOpt(&value->trailer, g_config.operator_name);  // 运营商
        dpi_TrailerSetArea(&value->trailer,g_config.devArea); 

        // 判断 trailer 是否解析成功
        if (value->trailer.MSISDN != 0) {
            value->trailer_flag = 1;
        }
    }

    if (c2s) {
        value->c2s_pkts++;
    } else {
        value->s2c_pkts++;
    }

    now = time(NULL);
    if (value->begin_time == 0) value->begin_time = now;
    if (now - value->end_time >= g_config.wx_session_timeloop) {
        value->end_time = now;    // 更新时间戳

        // 发送给聚合
        dpi_TrailerUpdateTS(&value->trailer);
        wxc_sendMsg(g_wxls_handle, (const unsigned char*)value, sizeof(ST_WXLocSharing), WXCS_WX_LOC_SHARING);
    }
        // // 原子操作，更新计数
        // if (value->send_flag != 1) {
        //     ATOMIC_ADD_FETCH(&g_voice_cnt.total);

        //     if (value->trailer.MSISDN && value->trailer.IMEI && value->trailer.IMSI) {
        //         ATOMIC_ADD_FETCH(&g_voice_cnt.contact);
        //     }
        //     value->send_flag = 1;
        // }

        // // 打印布控
        // print_st(value);
        // char phone_num[20];
        // snprintf(phone_num, 20, "%ld", value->trailer.MSISDN);
        // if(1 == phone_watch_find(g_phone_hash, phone_num))
        // {
        //     printf("find_phone        :%s\n", phone_num);
        //     print_session(value);
        // }

        // value->end_time = now;    // 更新时间戳
    // }    

    return 0;
}

static void
identify_wx_loc_sharing(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_WX_LOCSHARING] == 0) {
        return;
    }

    if(payload_len < LS_PAYLOAD_MIN_LEN) {
        return;
    }

    uint16_t offset = 0;
    uint16_t ports[] = {LS_PORT_00, LS_PORT_01, LS_PORT_02, LS_PORT_03};
    uint8_t  is_port = 0; // 是否为协议端口
    uint8_t  flag1 = 0;
    uint8_t  unuse_len = 0;


    // /* 判断报文的目标端口  */
    // int port_src = ntohs(flow->tuple.inner.port_src);
    // int port_dst = ntohs(flow->tuple.inner.port_dst);
    // for (int i = 0; i < LS_PORT_CNT; ++i) {
    //     if (port_src == ports[i] || port_dst == ports[i]) {
    //         is_port = 1;
    //         break;
    //     }
    // }
    // if (!is_port) return;

    // 包头识别  
    if (payload[offset] != 0xd5) return;
    offset += 1;

    // unused 
    offset += 5;

    //length
    offset += 1;

    // unused 
    offset += 1;

    uint8_t len = get_uint8_t(payload, offset);

    offset += 1;
    offset += len;

    // unused
    offset += 1;

    // session id 
    offset += 4;

    uint16_t flag = get_uint16_ntohs(payload, offset);
    if (flag != LS_IDENTIFY_FLAG) return;

    
    flow->real_protocol_id = PROTOCOL_WX_LOCSHARING;
    return;
}

void init_wx_location_sharing_dissector(void)
{
    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_WX_LOCSHARING
    port_add_proto_head(IPPROTO_UDP, 80,    PROTOCOL_WX_LOCSHARING);
    port_add_proto_head(IPPROTO_UDP, 8000,  PROTOCOL_WX_LOCSHARING);
    port_add_proto_head(IPPROTO_UDP, 8080,  PROTOCOL_WX_LOCSHARING);
    port_add_proto_head(IPPROTO_UDP, 16285, PROTOCOL_WX_LOCSHARING);

    // 协议为 PROTOCOL_WX_LOCSHARING 的回调
    udp_detection_array[PROTOCOL_WX_LOCSHARING].proto         = PROTOCOL_WX_LOCSHARING;
    udp_detection_array[PROTOCOL_WX_LOCSHARING].identify_func = identify_wx_loc_sharing;
    udp_detection_array[PROTOCOL_WX_LOCSHARING].dissect_func  = dissect_wx_loc_sharing;


    // 自我标识.当前结构体的协议类型. 辅助流识别.
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_WX_LOCSHARING].excluded_protocol_bitmask, PROTOCOL_WX_LOCSHARING);
    return;
}