# Changelog
大部分重要修改将会写入 changelog
发版之前请将更新日志暂存到 Unreleased 模块

## [4.0.18] - 2025-06-24
### Added
  - 添加 主被叫校验

## [4.0.17] - 2025-06-24
### Fixed
  - 修复变量初始化导致的 wx 话单主被叫被覆盖的问题

## [4.0.16] - 2025-06-17
### Added
  - 话单识别添加严格模式
  - 一对一通话纯下行包不填充主被叫

## [4.0.15] - 2025-06-16
### Fixed
  - 修复 wx 个人话单识别为群话单的问题

## [4.0.14] - 2025-06-12
### Fixed
  - 修复 zxsk trailer 5G标签展示错误

## [4.0.13] - 2025-06-10
### Added
  - 添加 zxsk 标签解析

## [4.0.12] - 2025-05-29
### Added
  - qqav个人话单增加解析号码与公网ip的格式

## [4.0.11] - 2025-05-29
### Fixed
  - 修复qq号码与ip对应不上的问题

## [4.0.10] - 2025-05-26
### Added
  - qqav增加字段，用于输出超过人数的qq号列表
  - qq_group 增加新特征
### Fixed
  - 修复qqfile中时间错误
  - 修复个人话单字段错位
## [4.0.9] - 2025-05-19
### Added
  - qq相关增加新端口
  - qq个人通话增加新特征

## [4.0.8] - 2025-05-16
### Changed
  - 修改hwzz标签输出格式

## [4.0.7] - 2025-05-15
###Added
  - hwzz中添加运营商解析
  - 增加tbl输出引号配置
  - 新增http中关于cgi-bin的协议
### Fixed
  - 修复公共字段中的空值

## [4.0.6] - 2024-12-24
### Added
  - 话单新增识别模式配置，支持模糊识别，精确识别
  - 添加异常话单输出日志，丢弃话单会写到日志文件中

### Changed
  - 话单 SessionID 支持 8 字节输出

## [4.0.5] - 2024.11.14
### Added
  - 新增了话单新的报文格式
      只要存在 0x9815 即为个人，否则为群组
      0x9613中新增 0x21(视频类型)

## [4.0.4] - 2024.05.31
### Fixed
  - 修复 82599 网卡时间戳更新失败的 bug

## [4.0.3] - 2024.05.15
### Fixed
  - 修复在线模式少读一个网口的bug

## [4.0.1] - 2024.05.15
### Fixed
  - 修复了读取lua脚本时的退出行为

## [4.0.0] - 2024.05.15
### Changed
  - 区分在线解析和离线读包方式时间戳，在线获取当前时间，离线获取pcap包帧时间戳
### Added
  - 离线读包添加速率控制功能，具体用法见配置文件
### Fixed
## [3.9.13] - 2024.04.30     陈政权
### Changed
### Added
  - 添加中新赛克标签支持
  - 添加中新赛克 trailer 配置项   TRAILER_TYPE = 6
### Fixed

## [3.9.10] - 2024.03.11      武柄屹
### Changed
  - 更新脚本
### Added
### Fixed

## [3.9.9] - 2024.03.11      武柄屹
### Changed
  - 更新脚本
### Added
### Fixed
  - vtysh增加showfailinfo;
  - 日志修改为warining，防止IO吞吐到达瓶颈；
  - 修复配置文件中的解释错误

## [3.9.8] - 2024.02.05      武柄屹
### Changed
### Added
### Fixed
  - vtysh增加showfailinfo;
  - 日志修改为warining，防止IO吞吐到达瓶颈；
  - 更新脚本

## [3.9.7] - 2024.02.02      武柄屹
### Changed
### Added
### Fixed
  - 更新了lua脚本
    |- -- adapt_weixin_pyq_V0.5：by xumh at 2024.02.02
    |     1) 修改了hex_to_ip函数的table为空报错
    |- -- adapt_weixin_V1.2     : by xumh at 2024.01.16
    |     1) 增加对nestedTLV1[2]的判断

## [3.9.6] - 2024.01.04      陈政权
### Changed
  - 优化 wxdpi 打包内容
### Added
### Fixed

## [3.9.5] - 2023.11.16      陈政权
### Changed
  - 添加 jl 5G 新版标签支持
### Added
### Fixed

## [3.9.4] - 2023.07.27      武柄屹
### Changed
  - 修改配置文件
### Added
### Fixed

## [3.9.3] - 2023.07.27      武柄屹
### Changed
### Added
### Fixed
  - 修复ipv6入队列分配线程id错误
## [3.9.2] - 2023.06.30      武柄屹
### Changed
### Added
  - hz 标签 添加 eci 判定网络类型
### Fixed

## [3.9.1] - 2023.06.29      武柄屹
### Changed
### Added
  - 增加配置TCP_RSM_REPORT_ERR
### Fixed
  - 修复tcp重组递归错误
## [3.9.0] - 2023.06.21      武柄屹
### Changed
### Added
  - 增加http端口过滤规则，增加http重组开关
### Fixed
## [3.8.19] - 2023.06.21      武柄屹
### Changed
### Added
### Fixed
  - 修复了wx中resv_1输出错误
## [3.8.18] - 2023.06.20      武柄屹
### Changed
### Added
  - 增加内存监测
### Fixed
## [3.8.18] - 2023.06.15      武柄屹
### Changed
### Added
  - 增加使用命令控制程序收包退出
### Fixed
## [3.8.17] - 2023.06.15      武柄屹
### Changed
### Added
  - 增加内存监测
### Fixed
## [3.8.16] - 2023.06.15      武柄屹
### Changed
### Added
  - 增加http协议中wxpyq的输出（适配record）
### Fixed
  ## [3.8.15] - 2023.06.13      武柄屹
### Changed
### Added
  - 增加内存监测
### Fixed
## [3.8.14] - 2023.06.7      武柄屹
### Changed
### Added
### Fixed
  - 修复了某些异常数据导致的崩溃
  - 修复了可能导致http内存泄漏的部分
## [3.8.13] - 2023.06.6      武柄屹
### Changed
### Added
  - 更新了lua脚本
  - 增加了内存监测
### Fixed
## [3.8.12] - 2023.06.6      武柄屹
### Changed
### Added
### Fixed
  - 修复了打印日志引起的崩溃
## [3.8.11] - 2023.06.5      武柄屹
### Changed
### Added
  - 更新了lua脚本适配失败的日志打印
  - 更新了入队失败的打印
### Fixed
## [3.8.10] - 2023.06.1      武柄屹
### Changed
### Added
  - 更新了lua脚本adapt_weixin_V0.7
### Fixed
  - wxpay host末尾匹配模式修复
## [3.8.9] - 2023.05.30      武柄屹
### Changed
  - resv6 中填入wxnum的长度
### Added
### Fixed
  - 修复内存泄漏，升级适配脚本从0.3到adapt_weixin_V0.6
  - 恢复tbl文件 结尾的"|" 缺失问题
    tbl格式为       "字段"|"字段"|"字段"|...
    一定会以"|"结尾
## [3.8.8] - 2023.05.26      武柄屹
### Changed
  - 增加停止收包配置
### Added
### Fixed
## [3.8.7] - 2023.05.26      武柄屹
### Changed
### Added
  - 更新lua脚本
### Fixed
  - 修复内存溢出导致的重启
## [3.8.5] - 2023.05.24      武柄屹
### Changed
### Added
### Fixed
  - 将输出中存在换行符号的值的部分，替换成“ ”
  - 修复内存溢出导致的重启
## [3.8.4] - 2023.05.23      武柄屹
### Changed
  - wxpay修改为尾端匹配host模式
### Added
### Fixed
  - 将输出中存在“|”符号的值的部分，替换成“_”
## [3.8.3] - 2023.05.19      武柄屹
### Changed
### Added
### Fixed
  - 修复频繁内存泄漏
## [3.8.2] - 2023.05.16      武柄屹
### Changed
### Added
### Fixed
  - 修复写trailer字段
## [3.8.1] - 2023.05.12      武柄屹
### Changed
  - lua脚本后缀支持.yalua后缀
### Added
### Fixed
  - 修复了程序崩溃
## [3.8.0] - 2023.05.10      武柄屹
### Changed
### Added
  - 原始weixin与weixin_pyq输出到weixin2与weixin_pyq2目录做备份保留，新模式下输出到weixin与weixin_pyq目录
### Fixed

## [3.7.16] - 2023.05.10      武柄屹
### Changed
### Added
  - 增加lua脚本适配功能
  - 配置增加LUA_ADAPT_DIR  = ./lua_adapt #lua脚本目录
  - 若无lua脚本目录，则不读取，输出原始字段
### Fixed

## [3.7.14] - 2023.04.21      武柄屹
### Changed
  - weixin协议：fileid_1字段改为写入fileid3中倒数第二个tlv，为服务器ip
### Added
### Fixed
  - 优化weixin协议：resv5与resv6中堆内存改为栈内存
  - 修复了wx中resv_1只填充前4字节

## [3.7.13] - 2023.04.20      武柄屹
### Changed
### Added
### Fixed
  - 修复weixin中回填的resv3与resv4，修复了resv4填入videoflag标志为所有wxf协议都会填入，不为视频号为0 为视频号为1，修正了resv3中填入wxnum的base64加密为		EM_WX_URL和EM_WX_FILEURL中取出的wxnum
  - 优化了weixin_pyq的内存使用

## [3.7.12] - 2023.04.19      武柄屹：
### Changed
### Added
### Fixed
		  1. 修改内存检查工具

## [3.7.12] - 2023.04.17      武柄屹：
### Changed
### Added
  - weixin协议 : wxf : resv5填入时戳，resv6填 "fileid关键字段值;fileid最后一个TLV"，resv6中，若是未解析出两个值同时存在，分号依然存在
        resv5中，时间戳格式为以秒为单位的时间戳，格式为
              1681451003
        fileid关键字段值格式为
        4字节值
              0x01140803
        fileid最后一个TLV格式为
        2字节值
              0x0405
              0x0400
        resv6格式为
              0x01140803;0x0400
### Fixed
  - weixin_pyq协议 : 修复了resv4填入是否是视频号的videoflag标志,当resv4=2时为视频号

## [3.7.11] - 2023-04-14      武柄屹：
### Changed
  - weixin_pyq中resv3改为wxnum->base64加密的值
### Added
  - resv4填入是否是视频号的videoflag标志,1为视频号
### Fixed

## [Unreleased]
### Added
  - 对端ip添加ttl和包数判断识别
### Changed
### Fixed

## [3.4.6] - 2021-07-07
### Added
### Changed
  - 修改对端ip去重方法
### Fixed
  - 修复话单为ipv6时候对端有且只有一个ipv4
  - 修复程序异常崩溃

## [3.4.4] - 2021-07-14
### Added
  - 话单对端 ip 添加 ipv6 和 ipv4 支持
### Changed
### Fixed

## [3.4.3] - 2021-07-07
### Added
  - 添加话单音视频数据包自统计
### Changed
  - 修改音视频识别方法
### Fixed
  - 修复一对一通话一方数据被丢弃

## [3.4.2] - 2021-07-02
### Added
### Changed
### Fixed
  - 修复多流导致的话单异常，部分流丢弃问题
  - 修复单向流概率不出现对端 ip 的问题

0210618
  新增功能：
    1.新增字段：
      referer_version  referfer中提取的版本号
      u_province       新增库字段
      u_city           新增库字段
      u_isp            新增库字段
      taskid           新增库字段
      cli-quic-flag    新增库字段
      downpicformat    新增库字段
      tp               pyq url 提取字段
      token            pyq url 提取字段
      idx              pyq url 提取字段
      length           pyq url 提取字段
      width            pyq url 提取字段
      useragent        http pyq 填充useragent
      unknown_val      未知字段，暂时未填值
      data_origin      标识该tbl来源于http还是wxf
    2.http pyq改变识别规则，从以前host改为uri contains “/mmsns”

yaWxDpi + yaWxcs v3.4.1 2021.06.11
        新增功能：
                1.wx 对端 ip 功能添加
                2.wx 话单多流合并
                3.wx 一对一通话主被叫重新识别
                4.wx 97 相关报文程序框架层做 16:1 衰减
        缺陷修复：
		1. 解决崩溃等问题.

        性能优化：
                1、 97 衰减可提升程序解析能力

yaWxDpi + yaWxcs v3.3.0 2021.02.01
        新增功能：
                1. D6 微信 info 数据包解析
                2. 微信 发送语音 行为解析
                3. 微信 收红包   行为解析
                4. 微信 HTTP 文字消息解析
        缺陷修复：
		1. 解决时钟回拨 导致的 话单类 HASH节点 无法超时

        性能优化：
                无

        新增字段:
                1. wxa 新增两个字段 PersonWxId, PersonWxUin
yaWxDpi + yaWxcs v3.3.0 2021.03.01
        新增功能：
                1.微信文件朋友新增两个字段，mul_url,uin
                2.微信朋友增加多url解析，字段存放在mul_url
                3.微信朋友增加referer中含有uin解析提取，字段存放在uin；
                4.微信朋友增加http流量产生get记录解析。
yaWxDpi + yaWxcs v3.3.0 2021.02.01
        新增功能：
                1. D6 微信 info 数据包解析
				2. 微信 发送语音 行为解析
				3. 微信 收红包   行为解析
				4. 微信 HTTP 文字消息解析
        缺陷修复：
				1. 解决时钟回拨 导致的 话单类 HASH节点 无法超时

        性能优化：
                无

        新增字段:
                1. wxa 新增两个字段 PersonWxId, PersonWxUin

yaWxDpi + yaWxcs v3.2.16 2021.01.21
        新增功能：
                无
        缺陷修复：
                修复 memdup 导致的崩溃

        性能优化：
                无

        新增字段:
                无

yaWxDpi + yaWxcs v3.2.15 2021.01.20
        新增功能：
                无
        缺陷修复：
                HTTP_WX V51输出文字消息,  且支持输出WX群图像的图片

        性能优化：
                无

        新增字段:
                无

yaWxDpi + yaWxcs v3.2.14 2021.01.19
        新增功能：
                1. 支持 X722网卡
        缺陷修复：
                无

        性能优化：
                无

        新增字段:
                无

yaWxDpi + yaWxcs v3.2.13 2020.12.22
        新增功能：
                1. 支持 HTTP_WX_MSG 并输出V51
        缺陷修复：
                无

        性能优化：
                无

        新增字段:
                无

yaWxDpi + yaWxcs v3.2.9.5 2020.12.1
        新增功能：
                1. 添加富乐trailer
                2. tac改为2个字节而不是4个
        缺陷修复：
                无

        性能优化：
                无

        新增字段:
                无


yaWxDpi + yaWxcs v3.2.9.4 2020.10.29
yaWxDpi + yaWxcs v3.2.9.3 2020.10.19
yaWxDpi + yaWxcs v3.2.9.2 2020.10.14
yaWxDpi + yaWxcs v3.2.9.1 2020.10.14
	新增功能:
		1. 微信话单 每个Person保留字段, 填充调试信息

	缺陷修复:
		1. 修复 微信话单模块比底层框架提前超时,导致的TBL重复. DPI:30, WXA:35, WXCS:45
		2. 修复 同SessionID的多条UDP条资源竞争导致的崩溃.
		3. 调整新的抖动值, 以适应群视频两种序号不同步的抖动. jitter->2048

	性能优化：
		无.

	新增字段:
		无.


yaWxDpi + yaWxcs v3.2.9 2020.10.12
	新增功能:
		1. 新增微信话单 UDP 前缀为 7612 协议格式解析
		2. 新增ipv6协议适配
		3. 解析端调试输出 ip版本, 是否TCP, UDP前缀统计
		4. 聚合端TBL 输出 保留字段填充: ip版本, 是否TCP, UDP前缀统计
		5. 建联补偿. 汇聚分流设备 有的时候 C/S 某个方法 建联不上

	缺陷修复:
		无.

	性能优化：
		1. 解决微信话单Hash处理慢的问题 [未处理!]

	新增字段:
		1. 相对于 v3.0.3, 微信话单增加了 is_video, is_groupa 两个字段

yaWxDpi + yaWxcs v3.2.8 2020.8.25
	新增功能:
		1. 新增 QQ_VOIP_SESSION_SEQ_JITTER    QQ 话单序号检测独立(配置文件中默认为5)

	缺陷修复:
		1. qq 话单 会话多合一问题。

	性能优化：
		1. qq 话单 解析性能提升，不再影响其他协议的解析。

yaWxDpi + yaWxcs v3.2.7  2020.85
    新增功能:
       1. 聚合端 WXA_SESSION_TIMEOUT_IN_SECOND    调整为 30秒
       2. 新增  ZOOM_SESSION_TIMEOUT_IN_SECOND    ZOOM 会议的独立超时配置项

    缺陷修复:
       1. WXA  话单 重复问题.
       2. ZOOM 话单 重复问题.
       3. 聚合 TCP 消息解风封装 错乱
       4. 修复 WXA SESSIONID  显示多余 00000000

    性能优化:
        无.

yaWxDpi + yaWxcs v3.2.6  2020.7.27
    新增功能:

    缺陷修复:
       1. ACCEPT ERROR 问题

    性能优化:
        无.

yaWxDpi + yaWxcs v3.2.5  2020.7.26
    新增功能:

    缺陷修复:
       1. 修复微信文件 UPDATE_VALUE 崩溃问题

    性能优化:
        无.

yaWxDpi + yaWxcs v3.2.4  2020.7.25
    新增功能:
       1. 代码加入 地址安全检测 功能. 便于快速查错.

    缺陷修复:

    性能优化:
        无.

yaWxDpi + yaWxcs v3.2.3  2020.7.24
    新增功能:
        无.

    缺陷修复:
        1. 修复聚合程序无法启动, 报告 string::resize  错误
        2. 修复QQ群视频无主叫的bug

    性能优化:
        无.

yaWxDpi + yaWxcs v3.2.2  2020.7.24
    新增功能:
            无.

    缺陷修复:
            1. 修复QQ会话偶尔崩溃的问题，减少误识别.
            2. 改善聚合程序崩溃
            3. 改善HTTP_WX解析崩溃

    性能优化:
            无.

yaWxDpi + yaWxcs v3.2.1  2020.7.17
    新增功能:
            无.

    缺陷修复:
           调整 QQ/WX HTTP URI  长度限制.

    性能优化:
            无.

yaWxDpi + yaWxcs v3.2.0  2020.7.17
    新增功能:
        1. 支持列表配置  qqacc 输出
            1.1 HTTP_HOST_URI_QQ ="http_host_uri_qq_list.txt"    #QQ相关HTTP, 识别并输出
            1.1 输出格式: http_n

        2 支持列表配置 wxph 输出
            2.1 uin内容存放 K01 字段中
            2.2 HTTP_HOST_URI_WX ="http_host_uri_wx_list.txt"    #WX相关HTTP, 识别并输出
            2.1 输出格式: http_n

        QQ/WX 列表文件配置统一格式:
            host uri 拼接格式, 一行一条
            http://pinghot.qq.com/pingd
            http://mtrace.qq.com/mkvcollect
            http://report.gamecenter.qq.com/cgi-bin/gc_dc_report_async_fcgi
            http://report.gamecenter.qq.com/cgi-bin/gc_pg_act_fcgi
            http://iacc.qq.com/

    缺陷修复:
            无.

    性能优化:
            无.

yaWxDpi + yaWxcs v3.1.0  2020.6.19
    新增功能:
        支持解析 ZOOM会议

    缺陷修复:
            无.

    性能优化:
            无.

yaWxDpi + yaWxcs v3.0.3  2020.6.8
    新增功能:
            无.

    缺陷修复:
            无.

    性能优化:
            优化微信文件对单个报文非正常KV结构

yaWxDpi + yaWxcs v3.0.2  2020.6.2
    新增功能:
            微信话单 支持 TCP.

    缺陷修复:
            无.

    性能优化:
            无.
