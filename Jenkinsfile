pipeline {
    agent any
    
    post {
        failure {
            updateGitlabCommitStatus name : 'build', state : 'failed'
        }
        success {
            updateGitlabCommitStatus name: 'build', state: 'success'
        }
    }
    
    options {
        gitLabConnection('gitlab_*************')
    }
    
    triggers {
        gitlab(
            triggerOnPush: true,
            triggerOnMergeRequest : true,
            branchFilterType: 'All'
        )
    }
    
    stages {
        stage("build") {
        when {
                anyOf {
                    branch 'master'
                    branch 'develop'
                }
            }

            steps {
                script {
                    sh "echo checkout branch"
                    sh "git checkout ${BRANCH_NAME}"
                    sh "git pull"
                    sh "rm jenkins -rf"
                    sh "echo clone gitlab jenkins"
                    sh "git clone git@*************:dev_dpi/jenkins.git"
                    sh "cd jenkins && git checkout jenkinsWxDpi"
                    sh "echo do jenkins script"
                    sh "./jenkins/do_jenkins.sh"
                }
                
            }
        }
    }

}