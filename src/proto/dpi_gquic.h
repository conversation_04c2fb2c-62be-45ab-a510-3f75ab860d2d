/****************************************************************************************
 * 文 件 名 : dpi_gquic.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq         2022/06/16
编码: chenzq         2022/06/16
修改: chenzq         2022/06/16
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights R
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_GQUIC_H
#define _DPI_GQUIC_H 

#include <stdint.h>
// 协议识别端口
#define GQUIC_PORT_0        8000
#define GQUIC_PORT_1        443

// 字段哈希表桶 大小
#define QQUICK_HASH_SIZE    200


#define GQUIC_UPSTREAM_FLAG_0C      0x0c
#define GQUIC_UPSTREAM_FLAG_0D      0x0d
#define GQUIC_UPSTREAM_FLAG_1C      0x1c

#define GQUIC_DOWNSTREAM_FLAG_18    0x18
#define GQUIC_DOWNSTREAM_FLAG_08    0x08

#define GQUIC_WX_START              0xab00

// gquic 头部大长度
#define GQUIC_HEAD_MAX_LEN          300


// gquic 微信负载最小长度
#define GQUIC_WX_PAYLOAD_MIN_LEN    50

// 微信头部长度
#define GQUIC_WX_HEAD_LEN           25


typedef enum _em_gquic_wx_type
{
  EM_GQUIC_CID = 156,
  EM_GQUIC_MAX
}gquic_wx_type;

typedef struct gquic_info_
{
  uint8_t   cid_index;
  char      gquic_cid[32];
  uint8_t   gquic_cid_len;
}GquicInfo;

#endif // dpi_guquic.h