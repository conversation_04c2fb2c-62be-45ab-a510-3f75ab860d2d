/****************************************************************************************
 * 文 件 名 : dpi_wxmisc.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq  		    2022/03/01
编码: chenzq			2022/03/01
修改: chenzq            2022/03/01
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_WXMISC_H    
#define _DPI_WXMISC_H

extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];

typedef enum _em_weixin_misc_type{
    EM_WX_MISC_DATA,
    EM_WX_MISC_MAX
}EM_WX_MISC_TYPE;


typedef struct wx_misc_
{
    uint8_t flag ;
    char misc_data[2048];
}WXMisc;


int dissect_weixin_misc(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, 
                        const uint32_t payload_len, uint8_t flag);

#endif // dpi_wxmisc.h