#ifndef DPI_PRECORD_WRITER_IF_H
#define DPI_PRECORD_WRITER_IF_H
#include <stdint.h>

typedef struct ProtoRecord        precord_t;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

int dpi_output_create_writer_keeper_for_n_thread(uint32_t write_thread_count, const char *output_root_path,const char *output_format);

int dpi_output_set_writer_record_count_per_file(uint32_t write_thread_intdex, uint32_t record_count);

int dpi_output_set_writer_property(uint32_t write_thread_index, const char *name, const char *value);

int dpi_output_set_writer_property_number(uint32_t write_thread_index, const char *name, uint64_t number);

int dpi_output_write_one_record(uint32_t write_thread_index, precord_t *record);

int dpi_output_do_idle_check(uint32_t write_thread_index, uint64_t time_now);

int dpi_output_on_process_going_to_terminate(uint32_t write_thread_index);

#ifdef __cplusplus
}
#endif

#endif /* DPI_PRECORD_WRITER_IF_H */
