#include "tbl_statistics.h"
#include "tbl_logger.h"
#include "tbl_config.h"
#include "tbl_utils.h"

TblStatistics& LOG_ST = TblStatistics::getInstance();
std::map<std::string ,std::map<std::string, std::atomic<uint64_t>>> TblStatistics::statistics_result;

void TblStatistics::init()
{
    log_path = CFG.getCurrentConfigValue({"log_conf","log_statistics"}, std::string("."));
    
    ensureDirExist(log_path.c_str());
    
    log_path += "/statistics.txt";

    col.clear();
    raw.clear();

    for(const auto &proto : CFG.protoRecord_)
    {
        col.emplace_back(proto);
    }

    raw = {"has scanned", "has read", "has output"};

    for(const auto &dir : CFG.allOutputDir_)
    {
        raw.emplace_back(dir);
    }

    for(auto & i : raw)
    {
        for(auto & j : col)
        {
            statistics_result[j][i] = ATOMIC_VAR_INIT(uint64_t(0));
        }
    }
}

void TblStatistics::setLogPath(const std::string &path)
{
    log_path = path;
}

void TblStatistics::writeLog()
{
    log_file = fopen(log_path.c_str(), "w+");

    if(log_file == nullptr)
    {
        LOG_DEF->error("Statistics file create failed!");
        return;
    }
    string_statistics_result = "Statistics result:\n";


    if(!get_string_statistics())
    {
        fputs(string_statistics_result.c_str(), log_file);

        fflush(log_file);
    }

    fclose(log_file);
}

int TblStatistics::get_string_statistics()
{
    char tmp_str[PATH_MAX] = {0};

    /* 初始化表格第一行 */
    char table_first[PATH_MAX] = {0};
    
    int max_key_width = 15;

    for(auto & key:col)
    {
        max_key_width = MAX(max_key_width, int(key.size()));
    }

    for(auto &raw_key : raw)
    {
        max_key_width = MAX(max_key_width, int(raw_key.size()));
    }

    max_key_width += 2;

    snprintf(table_first, PATH_MAX, "%*s", max_key_width, " ");

    string_statistics_result.append(table_first);

    for(auto & key:col)
    {
        snprintf(tmp_str, PATH_MAX, "%*s", max_key_width, key.c_str());
        string_statistics_result.append(tmp_str);
    }
    string_statistics_result.append("\n");

    for(auto &raw_key : raw)
    {
        /* 输出每行的key */
        snprintf(tmp_str, 256, "%-*s", max_key_width, raw_key.c_str());
        string_statistics_result.append(tmp_str);
        /* 输出每行的value */
        for(auto &col_key : col)
        {
            snprintf(tmp_str, 256, "%*lu",max_key_width, statistics_result[col_key][raw_key].load());
            string_statistics_result.append(tmp_str);
        }
        /* 输出每行的换行 */
        string_statistics_result.append("\n");
    }

    return 0;
}

void TblStatistics::work()
{
    while(CFG.getWorkMode()>0)
    {
        writeLog();
        sleep(log_loop_time);
    }
}

void TblStatistics::startWork()
{
    log_thread = std::thread(std::bind(&TblStatistics::work, this));
}

void TblStatistics::finishWork()
{
    writeLog();
    if(log_thread.joinable())
    {
        log_thread.join();
    }
}

void TblStatistics::addScan(const std::string& proto)
{
    std::atomic_fetch_add(&statistics_result[proto]["has scanned"], uint64_t(1));
}

void TblStatistics::addRead(const std::string& proto)
{
    std::atomic_fetch_add(&statistics_result[proto]["has read"], uint64_t(1));
}

void TblStatistics::addWrite(const std::string&proto, const std::string&dir)
{
    std::atomic_fetch_add(&statistics_result[proto][dir], uint64_t(1));
    std::atomic_fetch_add(&statistics_result[proto]["has output"], uint64_t(1));
}



