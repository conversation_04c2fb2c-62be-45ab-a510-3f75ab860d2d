#include "dpi_detect.h"
#include "dpi_log.h"

extern struct global_config g_config;
typedef struct Tencent_meeting_session_t {
  int FlagPacketC2S;
  int FlagPacketS2C;
  int SessionType;
} Tencent_meeting_session;

static int dissect_tencent_meeting_28(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  return 0;
}
static int dissect_tencent_meeting_36(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  return 0;
}
static int dissect_tencent_meeting_51(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  return 0;
}
static int dissect_tencent_meeting_tcp(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  return 0;
}

static int dissect_tencent_meeting_chat(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  if (NULL == flow->app_session) {
    Tencent_meeting_session *tcmeeting_session;
    tcmeeting_session = malloc(sizeof(Tencent_meeting_session));
    if (NULL == tcmeeting_session) {
      DPI_LOG(DPI_LOG_ERROR, "error on malloc Tencent_meeting_session");
      return 0;
    }
    memset(tcmeeting_session, 0, sizeof(Tencent_meeting_session));
    flow->app_session = tcmeeting_session;
  }

  switch (payload[0]) {
    case 0x28:
      return dissect_tencent_meeting_28(flow, direction, seq, payload, payload_len);
      break;
    case 0x36:
      return dissect_tencent_meeting_36(flow, direction, seq, payload, payload_len);
      break;
    case 0x51:
      return dissect_tencent_meeting_51(flow, direction, seq, payload, payload_len);
      break;
    case 0x41:
      return 0;
      break;
    default:
      if (get_uint32_ntohl(payload, 4) != 0x00001770)
        return 0;
      break;
  }
  dissect_tencent_meeting_tcp(flow, direction, seq, payload, payload_len);
  return 0;
}

static void identify_tencent_meeting_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return;
  }

  if (payload_len < 20) {
    return;
  }
  /* 判断报文的目标端口  */
  int port_src = ntohs(flow->tuple.inner.port_src);
  int port_dst = ntohs(flow->tuple.inner.port_dst);
  if (flow->tuple.inner.proto == 6) {
    if ((80 == port_dst || 80 == port_src || 443 == port_dst || 443 == port_src || 8080 == port_dst || 8080 == port_src) &&
        (get_uint32_ntohl(payload, 4) == 0x00001770)) {
      //tcp个人常规
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    } else if ((443 == port_dst || 443 == port_src) && (payload[0] == 0x28)) {
      // tcp个人会议
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
  } else {
    if ((443 == port_dst || 443 == port_src) &&
        (payload[0] == 0x28 || payload[0] == 36 || payload[0] == 28 || payload[0] == 41 || payload[0] == 51)) {
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
    return;
  }
}

void init_tencent_meeting_chat_dissector(void) {
  port_add_proto_head(IPPROTO_UDP, 443, PROTOCOL_TENCENT_MEETING);

  udp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
  port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_TENCENT_MEETING);

  tcp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;

  DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
  DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);

  return;
}
