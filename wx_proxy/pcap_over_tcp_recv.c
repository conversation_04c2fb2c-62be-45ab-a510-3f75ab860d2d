#include <stdio.h>
#include <netdb.h>
#include <netinet/in.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h> // read(), write(), close()

size_t cnt = 0;
size_t length = 0;
size_t buffsize = 0;

int func(int connfd)
{
    char buff[8192];
    int len = 0;
    int i   =0;
    int num   =0;

    if(buffsize > sizeof(buff))
    {
        buffsize = sizeof(buff);
    }

    for (;;)
    {
        int rc =read(connfd, buff, buffsize);
        if(0 == rc)
        {
            printf("EOF read\n");
            return -1;
        }
        else
        if(rc < 0)
        {
            printf("read Error %d\n", rc);
            return -1;
        }
        length += rc;
        printf("read length:%u new:%d\n", length, rc);
        continue;


        len = 0;
        num = read(connfd, &len, 4);
        if(4 != num)
        {
            return -1;
        }
        cnt++;
        printf("cnt=%u pkt_len=%u\n", cnt, len);

        num = 0;
        while(num != len)
        {
            int size = 0;
            if(len - num > sizeof(buff))
            {
                size = sizeof(buff);
            }
            else
            {
                size = len - num;
            }

            int cnt = read(connfd, buff, size);
            if(cnt)
            {
                num+=cnt;
            }
            else
            {
                printf("Read Erorr code:%d\n", cnt);
                return -1;
            }
        }
        length += len;
    }
}

int show_info()
{
    printf("接收计数:%zu(pkt) 总计:%zu(Byte)\n", cnt, length);
}

void on_signal(int sig)
{
    show_info();
    abort();
}




#include <signal.h>
int main(int argc, const char *args[])
{
    int sockfd, connfd, len;
    struct sockaddr_in servaddr, cli;
    int ListenPort = 0;
    if(3 != argc)
    {
        printf("Usage:%s <ListenPort> <buffsize>\n", args[0]);
        return 0;
    }
    ListenPort = atoi(args[1]);
    buffsize  = atoi(args[2]);

    signal(SIGINT,   on_signal);
    signal(SIGTERM,  on_signal);
    signal(SIGQUIT,  on_signal);

    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd == -1)
    {
        printf("socket creation failed...\n");
        exit(0);
    }
    else
    {
        printf("Socket successfully created..\n");
    }
    bzero(&servaddr, sizeof(servaddr));

    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
    servaddr.sin_port = htons(ListenPort);


    int optval = 1;
    setsockopt(sockfd, SOL_SOCKET, SO_REUSEPORT, &optval, sizeof(optval));
    setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

    if ((bind(sockfd, (struct sockaddr*)&servaddr, sizeof(servaddr))) != 0)
    {
        printf("socket bind failed...\n");
        exit(0);
    }
    else
    {
        printf("Socket successfully binded..\n");
    }

    if ((listen(sockfd, 5)) != 0) {
        printf("Listen failed...\n");
        exit(0);
    }
    else
    {
        printf("Server listening..\n");
    }

    len = sizeof(cli);
NEXT:
    connfd = accept(sockfd, (struct sockaddr*)&cli, &len);
    if (connfd < 0)
    {
        printf("server accept failed...\n");
        exit(0);
    }
    else
    {
        printf("server accept the client...\n");
    }

    length = 0;
    func(connfd);

    show_info();
goto NEXT;

    close(sockfd);
}


