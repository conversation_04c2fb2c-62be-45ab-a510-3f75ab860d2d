#!/bin/bash
cd $(dirname $0)
SOURCE_DIR=$(pwd)

#默认路径，如果没有环境变量，默认为此路径
PROGRAM_DIR="/root/program" 

color=1
function LOG_DEBUG() {
    if [[ $color -eq 1 ]]; then
        echo -e "\033[1;30;40m"$*"\033[0m"
    else
        echo "$*"
    fi
}

function LOG_ERR() {
    if [[ $color -eq 1 ]]; then
        echo -e "\033[1;31;40m"$*"\033[0m"
    else
        echo "$*"
    fi
}

function LOG_INFO() {
    if [[ $color -eq 1 ]]; then
        echo -e "\033[1;32;40m"$*"\033[0m"
    else
        echo "$*"
    fi
}

function LOG_WAR() {
    if [[ $color -eq 1 ]]; then
        echo -e "\033[1;33;40m"$*"\033[0m"
    else
        echo "$*"
    fi
}

function LOG_SAVE() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')]:$*" >> ${PROGRAM_DIR}/update_history/log.txt
}

function usage() {
    LOG_WAR "usage:"
    LOG_WAR "$0 -n newDir"
    LOG_WAR "$0 -u updateDir"
    LOG_WAR "-n 表示全新安装，newDir应写入当前压缩包内需要安装的程序目录名，并且/program中不能含有该目录"
    LOG_WAR "例如：$0 -n yaWxDpi "
    LOG_WAR "\t脚本会在/program创建yaWxDpi目录，并且拷贝tar包中yaWxDpi中的所有文件到/program/yaWxDpi\n\t然后创建软链接和对应服务"
    LOG_WAR "-u 表示升级安装，需要指定解压包中的目录名，目录名需要一致"
    LOG_WAR "例如：$0 -u yaWxDpi"
    LOG_WAR "\t脚本会将tar包中yaWxDpi目录里面的二进制拷贝到/program/yaWxDpi中，并更新软链接"
    LOG_ERR "请规范化填写，避免不必要的错误!!!\n请勿随意指定目录，避免不必要的麻烦!!!"
    LOG_ERR "此脚本不做任何删除操作，所有相关文件都可在/program/update_history目录中追溯"
}

function check_dpi() {
    floder=$1
    if [[ "$floder" == "wxcs" ]];then
        dpi_name="wxcs"
    else
        item=`find $SOURCE_DIR -type f -iname "config.ini*"`
        echo $item
        dpi_name=`cat $item | grep -ie "^CMD_LINE"`
        dpi_name=`echo $dpi_name | sed 's/.*\///' | cut -d ' ' -f1`
        echo $dpi_name
    fi
}

function do_copy() {

    cd ${SOURCE_DIR}
}

function do_install() {
    do_copy
}

#全新安装
function new_install() {
    nDir=$1
    LOG_INFO "拷贝目标所有文件到${PROGRAM_DIR}"
	mkdir ${PROGRAM_DIR}/${nDir}
    cp -ra ${SOURCE_DIR}/${nDir}*/* ${PROGRAM_DIR}/${nDir}
    for item in `find ${PROGRAM_DIR}/${nDir}`;do
        name=${item##*/}
        prefix=${name%%_*}
        if [[ "$name" =~ .*conf.* ]];then
            cd ${PROGRAM_DIR}/${nDir}
            LOG_INFO "为配置文件创建软连接"
            ln -fsr ${PROGRAM_DIR}/${nDir}/${name} ${prefix}
            cd ${SOURCE_DIR}
        fi

        if [[ "$name" =~ ya.* && "${name}" != "${prefix}" ]];then
            echo $prefix
            cd ${PROGRAM_DIR}/${nDir}
            LOG_INFO "为程序创建软连接"
            ln -fsr ${PROGRAM_DIR}/${nDir}/${name} ${prefix}
            LOG_INFO "为程序创建服务"
            if [[ -f "${PROGRAM_DIR}/${nDir}/start.sh" ]];then
                /root/tools/gen_service.sh $prefix ${PROGRAM_DIR}/${nDir}/start.sh
            else
                /root/tools/gen_service.sh $prefix ${PROGRAM_DIR}/${nDir}/$prefix
            fi
            LOG_INFO "设置程序开机自启动"
            systemctl enable $prefix
            LOG_WAR "如如需要,请手动启动服务 $prefix"
            cd ${SOURCE_DIR}
        fi
    done
}
#升级安装,升级安装只更换二进制文件
function update_install() {
    srcDir=$1
    dstDir=$2
    name=
    tname=
    for item in `find ${SOURCE_DIR}/${srcDir} -type f`;do
        item=${item##*/}
        echo $item
        if [[ "$item" =~ ${srcDir}.* ]];then
            name=$item 
        fi
    done
    LOG_INFO "拷贝新的程序到目标目录"
    cp -ra ${SOURCE_DIR}/${srcDir}/$name ${PROGRAM_DIR}/${dstDir}
    for item in `find ${PROGRAM_DIR}/${dstDir}`;do
        if [[ -L "${item}" ]];then
            cd ${PROGRAM_DIR}/${dstDir}
            tname=`readlink -e ${item}`
            oname=${tname##*/}
            if [[ -z "$tname" ]];then
                LOG_ERR "错误的软连接 $item"
                exit
            fi
            isexe=`file ${tname} | grep -ie "LSB executable"`
            if [[ -n "${isexe}" ]];then
                ##############查找并创建目录和log文件############################
                if [[ ! -d ${PROGRAM_DIR}/update_history/${dstDir} ]];then
                    LOG_INFO "mkdir -p ${PROGRAM_DIR}/update_history/${dstDir}"
                    mkdir -p ${PROGRAM_DIR}/update_history/${dstDir}
                fi
                if [[ ! -f  ${PROGRAM_DIR}/update_history/log.txt ]];then
                    LOG_INFO "touch ${PROGRAM_DIR}/update_history/log.txt!!!"
                    touch ${PROGRAM_DIR}/update_history/log.txt
                fi
                ###############################################################
                LOG_INFO "更新软链接"
                ln -fsr ${PROGRAM_DIR}/${dstDir}/${name} $item
                LOG_SAVE "ln -fsr ${PROGRAM_DIR}/${dstDir}/${name} $item"
                if [[ "${oname}" != "${name}" ]];then
                    LOG_INFO "移动旧的二进制到 update_history目录"
                    mv -f ${oname} ${PROGRAM_DIR}/update_history/${dstDir}
                    LOG_SAVE "mv -f ${oname} ${PROGRAM_DIR}/update_history/${dstDir}"
                fi

                
                if [[ -f ${SOURCE_DIR}/${srcDir}/change_log.txt ]];then
                    if [[ -f ${PROGRAM_DIR}/${dstDir}/change_log.txt  ]];then
                        LOG_INFO "移动旧的change_log.txt到update_history"
                        mv -f change_log.txt ${PROGRAM_DIR}/update_history/${dstDir}/change_log.txt_$(date '+%Y-%m-%d')
                    fi
                    cp -r ${SOURCE_DIR}/${srcDir}/change_log.txt ${PROGRAM_DIR}/${dstDir}
                fi

                LOG_WAR "升级安装程序成功，建议重启服务"
            fi
            cd ${SOURCE_DIR}
        fi
    done
}

argc=$#
if [[ "$argc" -lt 2 ]];then
    usage && exit
else
    case $1 in
    -n)
        if [[ "$argc" -ne 2 ]];then
            usage && exit
        fi
        newDir=$2
        flag=`find ${PROGRAM_DIR} -maxdepth 1 -type d -iname $newDir`
        cflag=`find ${SOURCE_DIR} -maxdepth 1 -type d -iname $newDir`
        #echo $flag
        if [[ -z  $flag && -n $cflag ]];then
            new_install $newDir
        else
            LOG_ERR "目录 $PROGRAM_DIR 已经存在 $newDir 或者当前目录不存在 $newDir目录"
            exit
        fi
    ;;
    -u)
        if [[ "$argc" -ne 2 ]];then
            usage && exit
        fi
        src_dir=$2
        #dst_dir=$3
        echo $src_dir 
        #echo $dst_dir
        #检测源目录和目的目录是否存在
        #源目录标记
        sflag=`find ${SOURCE_DIR} -maxdepth 1 -type d -iname ${src_dir}`
        #根目录标记，目录名应当一样
        dflag=`find ${PROGRAM_DIR} -maxdepth 1 -type d -iname ${src_dir}`

        if [[ -n $sflag && -n $dflag ]];then
            update_install ${src_dir} ${src_dir}
            #echo ${dst_dir}
        else
            LOG_ERR "目录 $SOURCE_DIR 不存在 $src_dir 或者 $PROGRAM_DIR 不存在 ${dst_dir}"
            exit
        fi
    ;;
    *) usage && exit
    ;;
    esac
fi

do_install
