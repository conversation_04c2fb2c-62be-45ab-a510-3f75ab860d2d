/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <getopt.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <glib.h>

#include "wx_proxy_common.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_version.h"

/*****************************************************************
*Function    : proxy_parse_argv
*Description : parse cmd line
*Input       : cmd line
*Output      : ctx paremerter
*Return      : [0:OK, othres:ERROR]
*Others      : none
*****************************************************************/
int
proxy_parse_argv(int args, char**argv,  struct context *ctx)
{
    int opt;

    while ((opt = getopt(args, argv, "c:hv")) != -1)
    {
        switch (opt)
        {
            case 'c':
                ctx->conf_file_path = optarg;
                break;

            case 'v':
                show_version();
                pexit(EXIT_SUCCESS);

            case 'h':
            default: /* '?' */
                fprintf(stderr, "Usage: %s [-c conf_file]\n", argv[0]);
                pexit(EXIT_FAILURE);
        }
    }

    if(NULL == ctx->conf_file_path)
    {
        fprintf(stderr, "Usage: %s [-c conf_file]\n", argv[0]);
        pexit(EXIT_FAILURE);
    }

    return EXIT_SUCCESS;
}
