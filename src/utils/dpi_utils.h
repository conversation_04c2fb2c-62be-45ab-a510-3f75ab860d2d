#ifndef _DPI_UTILS_H_
#define _DPI_UTILS_H_

#include <stdint.h>
#include <stddef.h>

#define ARRAY_LEN(array) ((sizeof (array)) / (sizeof (array)[0]))

const char *dpi_utils_show_bytes(uint64_t bytes, char out[], int len);

const char *dpi_utils_show_pps(uint64_t pps, char out[], int len);

char *dpi_utils_lstrip(char *str, int max_len, const char *chars);

char *dpi_utils_rstrip(char *str, int max_len, const char *chars);

/**
 * 同时执行 dpi_utils_lstrip 和 dpi_utils_rstrip
*/
char *dpi_utils_strip(char *str, int max_len, const char *chars);

#ifdef __cplusplus
extern "C"
{
#endif

// char * dpi_strstr(const char * haystack, int len_haystack, 
//                    const char * needle, int len_needle);
// char * dpi_strstr_kmp(const char * haystack, const char * neddle);

int dpi_utils_strftime(char buff[], int buff_size, const char *format);

char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name);

#ifdef __cplusplus
}
#endif

#endif /* _DPI_UTILS_H_*/
