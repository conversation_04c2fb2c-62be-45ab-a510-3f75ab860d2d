/****************************************************************************************
 * 文 件 名 : dpi_http.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/07/06
编码: wangy            2018/07/06
修改: xuxn          2019/03/11
修改: chunli         2020/07/10  输出HTTP BODY解析错误的报文
修改: chunli         2020/07/13  采用新版TCP重组
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <string.h>
#include <sys/time.h>
#include <openssl/aes.h>

#include "dpi_common.h"
#include "dpi_protorecord.h"
#include "list.h"
#include "dpi_tcp_rsm.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
// #include "dpi_ocsp.h"
#include "post.h"

#include "dpi_http.h"
// #include "dpi_rules.h"

//#include <sys/types.h>// TCP_RSM_DEBUG
//#include <sys/stat.h> // TCP_RSM_DEBUG
//#include <fcntl.h>    // TCP_RSM_DEBUG
//#include <unistd.h>   // TCP_RSM_DEBUG
//static int fd_tcp;    // TCP_RSM_DEBUG

// extern rte_atomic64_t log_256k_fail;
// extern rte_atomic64_t rsm_fail_get;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;
extern struct rte_mempool *tbl_log_content_mempool_4k;
size_t ws_base64_decode_inplace(char *s);


dpi_field_table  http_field_array[] = {
    DPI_FIELD_D(EM_HTTP_METHOD,                      EM_F_TYPE_STRING,               "Method"),
    DPI_FIELD_D(EM_HTTP_URI,                         EM_F_TYPE_STRING,               "URI"),
    DPI_FIELD_D(EM_HTTP_VERSION,                     EM_F_TYPE_STRING,               "Version"),
    DPI_FIELD_D(EM_HTTP_STATUS,                      EM_F_TYPE_STRING,               "Status"),
    DPI_FIELD_D(EM_HTTP_RESPONSESTATUS,              EM_F_TYPE_STRING,               "ResponseStatus"),
    DPI_FIELD_D(EM_HTTP_CACHE_CONTROL,               EM_F_TYPE_NULL,                 "Cache-Control"),
    DPI_FIELD_D(EM_HTTP_CONNECTION,                  EM_F_TYPE_NULL,                 "Connection"),
    DPI_FIELD_D(EM_HTTP_COOKIE,                      EM_F_TYPE_NULL,                 "Cookie"),
    DPI_FIELD_D(EM_HTTP_COOKIE2,                     EM_F_TYPE_NULL,                 "Cookie2"),
    DPI_FIELD_D(EM_HTTP_DATE,                        EM_F_TYPE_NULL,                 "Date"),
    DPI_FIELD_D(EM_HTTP_PRAGMA,                      EM_F_TYPE_NULL,                 "Pragma"),
    DPI_FIELD_D(EM_HTTP_TRAILER,                     EM_F_TYPE_NULL,                 "Trailer"),
    DPI_FIELD_D(EM_HTTP_TRANSFER_ENCODING,           EM_F_TYPE_NULL,                 "Transfer-Encoding"),
    DPI_FIELD_D(EM_HTTP_UPGRADE,                     EM_F_TYPE_NULL,                 "Upgrade"),
    DPI_FIELD_D(EM_HTTP_VIA,                         EM_F_TYPE_NULL,                 "Via"),
    DPI_FIELD_D(EM_HTTP_WARNING,                     EM_F_TYPE_NULL,                 "Warning"),
    DPI_FIELD_D(EM_HTTP_ACCEPT,                      EM_F_TYPE_NULL,                 "Accept"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_CHARSET,              EM_F_TYPE_NULL,                 "Accept-Charset"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_ENCODING,             EM_F_TYPE_NULL,                 "Accept-Encoding"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_LANGUAGE,             EM_F_TYPE_NULL,                 "Accept-Language"),
    DPI_FIELD_D(EM_HTTP_AUTHORIZATION,               EM_F_TYPE_NULL,                 "Authorization"),
    DPI_FIELD_D(EM_HTTP_EXPECT,                      EM_F_TYPE_NULL,                 "Expect"),
    DPI_FIELD_D(EM_HTTP_FROM,                        EM_F_TYPE_NULL,                 "From"),
    DPI_FIELD_D(EM_HTTP_HOST,                        EM_F_TYPE_NULL,                 "Host"),
    DPI_FIELD_D(EM_HTTP_IF_MATCH,                    EM_F_TYPE_NULL,                 "If-Match"),
    DPI_FIELD_D(EM_HTTP_IF_MODIFIED_SINCE,           EM_F_TYPE_NULL,                 "If-Modified-Since"),
    DPI_FIELD_D(EM_HTTP_IF_NONE_MATCH,               EM_F_TYPE_NULL,                 "If-None-Match"),
    DPI_FIELD_D(EM_HTTP_IF_RANGE,                    EM_F_TYPE_NULL,                 "If-Range"),
    DPI_FIELD_D(EM_HTTP_IF_UNMODIFIED_SINCE,         EM_F_TYPE_NULL,                 "If-Unmodified-Since"),
    DPI_FIELD_D(EM_HTTP_MAX_FORWARDS,                EM_F_TYPE_NULL,                 "Max-Forwards"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION,         EM_F_TYPE_NULL,                 "Proxy-Authorization"),
#ifndef   DPI_DBZZ
    DPI_FIELD_D(EM_HTTP_PROXY_TYPE,                  EM_F_TYPE_NULL,                 "Proxy-Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_LOGIN,                 EM_F_TYPE_NULL,                 "Proxy-Login"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION_INFO,    EM_F_TYPE_NULL,                 "Proxy-Authorization-Info"),
#endif
    DPI_FIELD_D(EM_HTTP_RANGE,                       EM_F_TYPE_NULL,                 "Range"),
    DPI_FIELD_D(EM_HTTP_REFERER,                     EM_F_TYPE_NULL,                 "Referer"),
    DPI_FIELD_D(EM_HTTP_TE,                          EM_F_TYPE_NULL,                 "TE"),
    DPI_FIELD_D(EM_HTTP_USER_AGENT,                  EM_F_TYPE_NULL,                 "User-Agent"),
#ifndef   DPI_DBZZ
    DPI_FIELD_D(EM_HTTP_PROXY_PROXYAGENT,            EM_F_TYPE_NULL,                 "Proxy-Agent"),
    DPI_FIELD_D(EM_HTTP_USER_CPU,                    EM_F_TYPE_NULL,                 "UA-CPU"),
    DPI_FIELD_D(EM_HTTP_USER_OS,                     EM_F_TYPE_NULL,                 "UA-OS"),
#endif
    DPI_FIELD_D(EM_HTTP_ACCEPT_RANGES,               EM_F_TYPE_NULL,                 "Accept-Ranges"),
    DPI_FIELD_D(EM_HTTP_AGE,                         EM_F_TYPE_NULL,                 "Age"),
    DPI_FIELD_D(EM_HTTP_ETAG,                        EM_F_TYPE_NULL,                 "ETag"),
    DPI_FIELD_D(EM_HTTP_LOCATION,                    EM_F_TYPE_NULL,                 "Location"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHENTICATE,          EM_F_TYPE_NULL,                 "Proxy-Authenticate"),
#ifndef   DPI_DBZZ
    DPI_FIELD_D(EM_HTTP_INQUIRY_TYPE,                EM_F_TYPE_NULL,                 "Inquiry_Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_PORT,          EM_F_TYPE_NULL,                 "Proxy-Connect_Port"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_HOST,          EM_F_TYPE_NULL,                 "Proxy-Connect_Host"),
#endif
    DPI_FIELD_D(EM_HTTP_RETRY_AFTER,                 EM_F_TYPE_NULL,                 "Retry-After"),
    DPI_FIELD_D(EM_HTTP_SERVER,                      EM_F_TYPE_NULL,                 "Server"),
    DPI_FIELD_D(EM_HTTP_VARY,                        EM_F_TYPE_NULL,                 "Vary"),
    DPI_FIELD_D(EM_HTTP_WWW_AUTHENTICATE,            EM_F_TYPE_NULL,                 "WWW-Authenticate"),
    DPI_FIELD_D(EM_HTTP_ALLOW,                       EM_F_TYPE_NULL,                 "Allow"),
    DPI_FIELD_D(EM_HTTP_CONTENT_ENCODING,            EM_F_TYPE_NULL,                 "Content-Encoding"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LANGUAGE,            EM_F_TYPE_NULL,                 "Content-Language"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH,              EM_F_TYPE_NULL,                 "Content-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LOCATION,            EM_F_TYPE_NULL,                 "Content-Location"),
    DPI_FIELD_D(EM_HTTP_CONTENT_MD5,                 EM_F_TYPE_NULL,                 "Content-MD5"),
    DPI_FIELD_D(EM_HTTP_CONTENT_RANGE,               EM_F_TYPE_NULL,                 "Content-Range"),
    DPI_FIELD_D(EM_HTTP_CONTENT_TYPE,                EM_F_TYPE_NULL,                 "Content-Type"),
    DPI_FIELD_D(EM_HTTP_EXPIRES,                     EM_F_TYPE_NULL,                 "Expires"),
    DPI_FIELD_D(EM_HTTP_LAST_MODIFIED,               EM_F_TYPE_NULL,                 "Last-Modified"),
    DPI_FIELD_D(EM_HTTP_X_FORWARDED_FOR,             EM_F_TYPE_NULL,                 "X-Forwarded-For"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE,                  EM_F_TYPE_NULL,                 "Set-Cookie"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE2,                 EM_F_TYPE_NULL,                 "Set-Cookie2"),
    DPI_FIELD_D(EM_HTTP_DNT,                         EM_F_TYPE_NULL,                 "DNT"),
    DPI_FIELD_D(EM_HTTP_X_POWERED_BY,                EM_F_TYPE_NULL,                 "X-Powered-By"),
    DPI_FIELD_D(EM_HTTP_P3P,                         EM_F_TYPE_NULL,                 "P3P"),
#ifndef   DPI_DBZZ
    DPI_FIELD_D(EM_HTTP_GET_SVR_REQ_URL,             EM_F_TYPE_NULL,                 "Get_Svr_Req_Url"),
    DPI_FIELD_D(EM_HTTP_ENTITY_TITLE_HEAD,           EM_F_TYPE_NULL,                 "Entity_Title_Head"),
#endif
    DPI_FIELD_D(EM_HTTP_H_A_NUMBER,                  EM_F_TYPE_NULL,                 "H_A_NUMBER"),
    DPI_FIELD_D(EM_HTTP_H_X_NUMBER,                  EM_F_TYPE_NULL,                 "H_X_NUMBER"),

    DPI_FIELD_D(EM_HTTP_K00,                 EM_F_TYPE_NULL,                 "K00"),
    DPI_FIELD_D(EM_HTTP_V00,                 EM_F_TYPE_NULL,                 "V00"),
    DPI_FIELD_D(EM_HTTP_K01,                 EM_F_TYPE_NULL,                 "K01"),
    DPI_FIELD_D(EM_HTTP_V01,                 EM_F_TYPE_NULL,                 "V01"),
    DPI_FIELD_D(EM_HTTP_K02,                 EM_F_TYPE_NULL,                 "K02"),
    DPI_FIELD_D(EM_HTTP_V02,                 EM_F_TYPE_NULL,                 "V02"),
    DPI_FIELD_D(EM_HTTP_K03,                 EM_F_TYPE_NULL,                 "K03"),
    DPI_FIELD_D(EM_HTTP_V03,                 EM_F_TYPE_NULL,                 "V03"),
    DPI_FIELD_D(EM_HTTP_K04,                 EM_F_TYPE_NULL,                 "K04"),
    DPI_FIELD_D(EM_HTTP_V04,                 EM_F_TYPE_NULL,                 "V04"),
    DPI_FIELD_D(EM_HTTP_K05,                 EM_F_TYPE_NULL,                 "K05"),
    DPI_FIELD_D(EM_HTTP_V05,                 EM_F_TYPE_NULL,                 "V05"),
    DPI_FIELD_D(EM_HTTP_K06,                 EM_F_TYPE_NULL,                 "K06"),
    DPI_FIELD_D(EM_HTTP_V06,                 EM_F_TYPE_NULL,                 "V06"),
    DPI_FIELD_D(EM_HTTP_K07,                 EM_F_TYPE_NULL,                 "K07"),
    DPI_FIELD_D(EM_HTTP_V07,                 EM_F_TYPE_NULL,                 "V07"),
    DPI_FIELD_D(EM_HTTP_K08,                 EM_F_TYPE_NULL,                 "K08"),
    DPI_FIELD_D(EM_HTTP_V08,                 EM_F_TYPE_NULL,                 "V08"),
    DPI_FIELD_D(EM_HTTP_K09,                 EM_F_TYPE_NULL,                 "K09"),
    DPI_FIELD_D(EM_HTTP_V09,                 EM_F_TYPE_NULL,                 "V09"),
    DPI_FIELD_D(EM_HTTP_K10,                 EM_F_TYPE_NULL,                 "K10"),
    DPI_FIELD_D(EM_HTTP_V10,                 EM_F_TYPE_NULL,                 "V10"),
    DPI_FIELD_D(EM_HTTP_K11,                 EM_F_TYPE_NULL,                 "K11"),
    DPI_FIELD_D(EM_HTTP_V11,                 EM_F_TYPE_NULL,                 "V11"),
    DPI_FIELD_D(EM_HTTP_K12,                 EM_F_TYPE_NULL,                 "K12"),
    DPI_FIELD_D(EM_HTTP_V12,                 EM_F_TYPE_NULL,                 "V12"),
    DPI_FIELD_D(EM_HTTP_K13,                 EM_F_TYPE_NULL,                 "K13"),
    DPI_FIELD_D(EM_HTTP_V13,                 EM_F_TYPE_NULL,                 "V13"),
    DPI_FIELD_D(EM_HTTP_K14,                 EM_F_TYPE_NULL,                 "K14"),
    DPI_FIELD_D(EM_HTTP_V14,                 EM_F_TYPE_NULL,                 "V14"),
    DPI_FIELD_D(EM_HTTP_K15,                 EM_F_TYPE_NULL,                 "K15"),
    DPI_FIELD_D(EM_HTTP_V15,                 EM_F_TYPE_NULL,                 "V15"),
    DPI_FIELD_D(EM_HTTP_K16,                 EM_F_TYPE_NULL,                 "K16"),
    DPI_FIELD_D(EM_HTTP_V16,                 EM_F_TYPE_NULL,                 "V16"),
    DPI_FIELD_D(EM_HTTP_K17,                 EM_F_TYPE_NULL,                 "K17"),
    DPI_FIELD_D(EM_HTTP_V17,                 EM_F_TYPE_NULL,                 "V17"),
    DPI_FIELD_D(EM_HTTP_K18,                 EM_F_TYPE_NULL,                 "K18"),
    DPI_FIELD_D(EM_HTTP_V18,                 EM_F_TYPE_NULL,                 "V18"),
    DPI_FIELD_D(EM_HTTP_K19,                 EM_F_TYPE_NULL,                 "K19"),
    DPI_FIELD_D(EM_HTTP_V19,                 EM_F_TYPE_NULL,                 "V19"),
    DPI_FIELD_D(EM_HTTP_K20,                 EM_F_TYPE_NULL,                 "K20"),
    DPI_FIELD_D(EM_HTTP_V20,                 EM_F_TYPE_NULL,                 "V20"),
    DPI_FIELD_D(EM_HTTP_K21,                 EM_F_TYPE_NULL,                 "K21"),
    DPI_FIELD_D(EM_HTTP_V21,                 EM_F_TYPE_NULL,                 "V21"),
    DPI_FIELD_D(EM_HTTP_K22,                 EM_F_TYPE_NULL,                 "K22"),
    DPI_FIELD_D(EM_HTTP_V22,                 EM_F_TYPE_NULL,                 "V22"),
    DPI_FIELD_D(EM_HTTP_K23,                 EM_F_TYPE_NULL,                 "K23"),
    DPI_FIELD_D(EM_HTTP_V23,                 EM_F_TYPE_NULL,                 "V23"),
    DPI_FIELD_D(EM_HTTP_K24,                 EM_F_TYPE_NULL,                 "K24"),
    DPI_FIELD_D(EM_HTTP_V24,                 EM_F_TYPE_NULL,                 "V24"),
    DPI_FIELD_D(EM_HTTP_K25,                 EM_F_TYPE_NULL,                 "K25"),
    DPI_FIELD_D(EM_HTTP_V25,                 EM_F_TYPE_NULL,                 "V25"),
    DPI_FIELD_D(EM_HTTP_K26,                 EM_F_TYPE_NULL,                 "K26"),
    DPI_FIELD_D(EM_HTTP_V26,                 EM_F_TYPE_NULL,                 "V26"),
    DPI_FIELD_D(EM_HTTP_K27,                 EM_F_TYPE_NULL,                 "K27"),
    DPI_FIELD_D(EM_HTTP_V27,                 EM_F_TYPE_NULL,                 "V27"),
    DPI_FIELD_D(EM_HTTP_K28,                 EM_F_TYPE_NULL,                 "K28"),
    DPI_FIELD_D(EM_HTTP_V28,                 EM_F_TYPE_NULL,                 "V28"),
    DPI_FIELD_D(EM_HTTP_K29,                 EM_F_TYPE_NULL,                 "K29"),
    DPI_FIELD_D(EM_HTTP_V29,                 EM_F_TYPE_NULL,                 "V29"),
    DPI_FIELD_D(EM_HTTP_K30,                 EM_F_TYPE_NULL,                 "K30"),
    DPI_FIELD_D(EM_HTTP_V30,                 EM_F_TYPE_NULL,                 "V30"),
    DPI_FIELD_D(EM_HTTP_K31,                 EM_F_TYPE_NULL,                 "K31"),
    DPI_FIELD_D(EM_HTTP_V31,                 EM_F_TYPE_NULL,                 "V31"),
    DPI_FIELD_D(EM_HTTP_K32,                 EM_F_TYPE_NULL,                 "K32"),
    DPI_FIELD_D(EM_HTTP_V32,                 EM_F_TYPE_NULL,                 "V32"),
    DPI_FIELD_D(EM_HTTP_K33,                 EM_F_TYPE_NULL,                 "K33"),
    DPI_FIELD_D(EM_HTTP_V33,                 EM_F_TYPE_NULL,                 "V33"),
    DPI_FIELD_D(EM_HTTP_K34,                 EM_F_TYPE_NULL,                 "K34"),
    DPI_FIELD_D(EM_HTTP_V34,                 EM_F_TYPE_NULL,                 "V34"),
    DPI_FIELD_D(EM_HTTP_K35,                 EM_F_TYPE_NULL,                 "K35"),
    DPI_FIELD_D(EM_HTTP_V35,                 EM_F_TYPE_NULL,                 "V35"),
    DPI_FIELD_D(EM_HTTP_K36,                 EM_F_TYPE_NULL,                 "K36"),
    DPI_FIELD_D(EM_HTTP_V36,                 EM_F_TYPE_NULL,                 "V36"),
    DPI_FIELD_D(EM_HTTP_K37,                 EM_F_TYPE_NULL,                 "K37"),
    DPI_FIELD_D(EM_HTTP_V37,                 EM_F_TYPE_NULL,                 "V37"),
    DPI_FIELD_D(EM_HTTP_K38,                 EM_F_TYPE_NULL,                 "K38"),
    DPI_FIELD_D(EM_HTTP_V38,                 EM_F_TYPE_NULL,                 "V38"),
    DPI_FIELD_D(EM_HTTP_K39,                 EM_F_TYPE_NULL,                 "K39"),
    DPI_FIELD_D(EM_HTTP_V39,                 EM_F_TYPE_NULL,                 "V39"),
    DPI_FIELD_D(EM_HTTP_K40,                 EM_F_TYPE_NULL,                 "K40"),
    DPI_FIELD_D(EM_HTTP_V40,                 EM_F_TYPE_NULL,                 "V40"),
    DPI_FIELD_D(EM_HTTP_K41,                 EM_F_TYPE_NULL,                 "K41"),
    DPI_FIELD_D(EM_HTTP_V41,                 EM_F_TYPE_NULL,                 "V41"),
    DPI_FIELD_D(EM_HTTP_K42,                 EM_F_TYPE_NULL,                 "K42"),
    DPI_FIELD_D(EM_HTTP_V42,                 EM_F_TYPE_NULL,                 "V42"),
    DPI_FIELD_D(EM_HTTP_K43,                 EM_F_TYPE_NULL,                 "K43"),
    DPI_FIELD_D(EM_HTTP_V43,                 EM_F_TYPE_NULL,                 "V43"),
    DPI_FIELD_D(EM_HTTP_K44,                 EM_F_TYPE_NULL,                 "K44"),
    DPI_FIELD_D(EM_HTTP_V44,                 EM_F_TYPE_NULL,                 "V44"),
    DPI_FIELD_D(EM_HTTP_K45,                 EM_F_TYPE_NULL,                 "K45"),
    DPI_FIELD_D(EM_HTTP_V45,                 EM_F_TYPE_NULL,                 "V45"),
    DPI_FIELD_D(EM_HTTP_K46,                 EM_F_TYPE_NULL,                 "K46"),
    DPI_FIELD_D(EM_HTTP_V46,                 EM_F_TYPE_NULL,                 "V46"),
    DPI_FIELD_D(EM_HTTP_K47,                 EM_F_TYPE_NULL,                 "K47"),
    DPI_FIELD_D(EM_HTTP_V47,                 EM_F_TYPE_NULL,                 "V47"),
    DPI_FIELD_D(EM_HTTP_K48,                 EM_F_TYPE_NULL,                 "K48"),
    DPI_FIELD_D(EM_HTTP_V48,                 EM_F_TYPE_NULL,                 "V48"),
    DPI_FIELD_D(EM_HTTP_K49,                 EM_F_TYPE_NULL,                 "K49"),
    DPI_FIELD_D(EM_HTTP_V49,                 EM_F_TYPE_NULL,                 "V49"),
    DPI_FIELD_D(EM_HTTP_K50,                 EM_F_TYPE_NULL,                 "K50"),
    DPI_FIELD_D(EM_HTTP_V50,                 EM_F_TYPE_NULL,                 "V50"),
    DPI_FIELD_D(EM_HTTP_K51,                 EM_F_TYPE_NULL,                 "K51"),
    DPI_FIELD_D(EM_HTTP_V51,                 EM_F_TYPE_NULL,                 "V51"),
    DPI_FIELD_D(EM_HTTP_K52,                 EM_F_TYPE_NULL,                 "K52"),
    DPI_FIELD_D(EM_HTTP_V52,                 EM_F_TYPE_NULL,                 "V52"),
    DPI_FIELD_D(EM_HTTP_K53,                 EM_F_TYPE_NULL,                 "K53"),
    DPI_FIELD_D(EM_HTTP_V53,                 EM_F_TYPE_NULL,                 "V53"),
    DPI_FIELD_D(EM_HTTP_K54,                 EM_F_TYPE_NULL,                 "K54"),
    DPI_FIELD_D(EM_HTTP_V54,                 EM_F_TYPE_NULL,                 "V54"),
    DPI_FIELD_D(EM_HTTP_K55,                 EM_F_TYPE_NULL,                 "K55"),
    DPI_FIELD_D(EM_HTTP_V55,                 EM_F_TYPE_NULL,                 "V55"),
    DPI_FIELD_D(EM_HTTP_K56,                 EM_F_TYPE_NULL,                 "K56"),
    DPI_FIELD_D(EM_HTTP_V56,                 EM_F_TYPE_NULL,                 "V56"),
    DPI_FIELD_D(EM_HTTP_K57,                 EM_F_TYPE_NULL,                 "K57"),
    DPI_FIELD_D(EM_HTTP_V57,                 EM_F_TYPE_NULL,                 "V57"),
    DPI_FIELD_D(EM_HTTP_K58,                 EM_F_TYPE_NULL,                 "K58"),
    DPI_FIELD_D(EM_HTTP_V58,                 EM_F_TYPE_NULL,                 "V58"),
    DPI_FIELD_D(EM_HTTP_K59,                 EM_F_TYPE_NULL,                 "K59"),
    DPI_FIELD_D(EM_HTTP_V59,                 EM_F_TYPE_NULL,                 "V59"),
    DPI_FIELD_D(EM_HTTP_K60,                 EM_F_TYPE_NULL,                 "K60"),
    DPI_FIELD_D(EM_HTTP_V60,                 EM_F_TYPE_NULL,                 "V60"),
    DPI_FIELD_D(EM_HTTP_K61,                 EM_F_TYPE_NULL,                 "K61"),
    DPI_FIELD_D(EM_HTTP_V61,                 EM_F_TYPE_NULL,                 "V61"),
    DPI_FIELD_D(EM_HTTP_K62,                 EM_F_TYPE_NULL,                 "K62"),
    DPI_FIELD_D(EM_HTTP_V62,                 EM_F_TYPE_NULL,                 "V62"),
    DPI_FIELD_D(EM_HTTP_K63,                 EM_F_TYPE_NULL,                 "K63"),
    DPI_FIELD_D(EM_HTTP_V63,                 EM_F_TYPE_NULL,                 "V63"),
    DPI_FIELD_D(EM_HTTP_K64,                 EM_F_TYPE_NULL,                 "K64"),
    DPI_FIELD_D(EM_HTTP_V64,                 EM_F_TYPE_NULL,                 "V64"),
    DPI_FIELD_D(EM_HTTP_K65,                 EM_F_TYPE_NULL,                 "K65"),
    DPI_FIELD_D(EM_HTTP_V65,                 EM_F_TYPE_NULL,                 "V65"),
    DPI_FIELD_D(EM_HTTP_K66,                 EM_F_TYPE_NULL,                 "K66"),
    DPI_FIELD_D(EM_HTTP_V66,                 EM_F_TYPE_NULL,                 "V66"),
    DPI_FIELD_D(EM_HTTP_K67,                 EM_F_TYPE_NULL,                 "K67"),
    DPI_FIELD_D(EM_HTTP_V67,                 EM_F_TYPE_NULL,                 "V67"),
    DPI_FIELD_D(EM_HTTP_K68,                 EM_F_TYPE_NULL,                 "K68"),
    DPI_FIELD_D(EM_HTTP_V68,                 EM_F_TYPE_NULL,                 "V68"),
    DPI_FIELD_D(EM_HTTP_K69,                 EM_F_TYPE_NULL,                 "K69"),
    DPI_FIELD_D(EM_HTTP_V69,                 EM_F_TYPE_NULL,                 "V69"),
    DPI_FIELD_D(EM_HTTP_K70,                 EM_F_TYPE_NULL,                 "K70"),
    DPI_FIELD_D(EM_HTTP_V70,                 EM_F_TYPE_NULL,                 "V70"),
    DPI_FIELD_D(EM_HTTP_K71,                 EM_F_TYPE_NULL,                 "K71"),
    DPI_FIELD_D(EM_HTTP_V71,                 EM_F_TYPE_NULL,                 "V71"),
    DPI_FIELD_D(EM_HTTP_K72,                 EM_F_TYPE_NULL,                 "K72"),
    DPI_FIELD_D(EM_HTTP_V72,                 EM_F_TYPE_NULL,                 "V72"),
    DPI_FIELD_D(EM_HTTP_K73,                 EM_F_TYPE_NULL,                 "K73"),
    DPI_FIELD_D(EM_HTTP_V73,                 EM_F_TYPE_NULL,                 "V73"),
    DPI_FIELD_D(EM_HTTP_K74,                 EM_F_TYPE_NULL,                 "K74"),
    DPI_FIELD_D(EM_HTTP_V74,                 EM_F_TYPE_NULL,                 "V74"),
    DPI_FIELD_D(EM_HTTP_K75,                 EM_F_TYPE_NULL,                 "K75"),
    DPI_FIELD_D(EM_HTTP_V75,                 EM_F_TYPE_NULL,                 "V75"),
    DPI_FIELD_D(EM_HTTP_K76,                 EM_F_TYPE_NULL,                 "K76"),
    DPI_FIELD_D(EM_HTTP_V76,                 EM_F_TYPE_NULL,                 "V76"),
    DPI_FIELD_D(EM_HTTP_K77,                 EM_F_TYPE_NULL,                 "K77"),
    DPI_FIELD_D(EM_HTTP_V77,                 EM_F_TYPE_NULL,                 "V77"),
    DPI_FIELD_D(EM_HTTP_K78,                 EM_F_TYPE_NULL,                 "K78"),
    DPI_FIELD_D(EM_HTTP_V78,                 EM_F_TYPE_NULL,                 "V78"),
    DPI_FIELD_D(EM_HTTP_K79,                 EM_F_TYPE_NULL,                 "K79"),
    DPI_FIELD_D(EM_HTTP_V79,                 EM_F_TYPE_NULL,                 "V79"),
    DPI_FIELD_D(EM_HTTP_K80,                 EM_F_TYPE_NULL,                 "K80"),
    DPI_FIELD_D(EM_HTTP_V80,                 EM_F_TYPE_NULL,                 "V80"),
    DPI_FIELD_D(EM_HTTP_K81,                 EM_F_TYPE_NULL,                 "K81"),
    DPI_FIELD_D(EM_HTTP_V81,                 EM_F_TYPE_NULL,                 "V81"),
    DPI_FIELD_D(EM_HTTP_K82,                 EM_F_TYPE_NULL,                 "K82"),
    DPI_FIELD_D(EM_HTTP_V82,                 EM_F_TYPE_NULL,                 "V82"),
    DPI_FIELD_D(EM_HTTP_K83,                 EM_F_TYPE_NULL,                 "K83"),
    DPI_FIELD_D(EM_HTTP_V83,                 EM_F_TYPE_NULL,                 "V83"),
    DPI_FIELD_D(EM_HTTP_K84,                 EM_F_TYPE_NULL,                 "K84"),
    DPI_FIELD_D(EM_HTTP_V84,                 EM_F_TYPE_NULL,                 "V84"),
    DPI_FIELD_D(EM_HTTP_K85,                 EM_F_TYPE_NULL,                 "K85"),
    DPI_FIELD_D(EM_HTTP_V85,                 EM_F_TYPE_NULL,                 "V85"),
    DPI_FIELD_D(EM_HTTP_K86,                 EM_F_TYPE_NULL,                 "K86"),
    DPI_FIELD_D(EM_HTTP_V86,                 EM_F_TYPE_NULL,                 "V86"),
    DPI_FIELD_D(EM_HTTP_K87,                 EM_F_TYPE_NULL,                 "K87"),
    DPI_FIELD_D(EM_HTTP_V87,                 EM_F_TYPE_NULL,                 "V87"),
    DPI_FIELD_D(EM_HTTP_K88,                 EM_F_TYPE_NULL,                 "K88"),
    DPI_FIELD_D(EM_HTTP_V88,                 EM_F_TYPE_NULL,                 "V88"),
    DPI_FIELD_D(EM_HTTP_K89,                 EM_F_TYPE_NULL,                 "K89"),
    DPI_FIELD_D(EM_HTTP_V89,                 EM_F_TYPE_NULL,                 "V89"),
    DPI_FIELD_D(EM_HTTP_K90,                 EM_F_TYPE_NULL,                 "K90"),
    DPI_FIELD_D(EM_HTTP_V90,                 EM_F_TYPE_NULL,                 "V90"),
    DPI_FIELD_D(EM_HTTP_K91,                 EM_F_TYPE_NULL,                 "K91"),
    DPI_FIELD_D(EM_HTTP_V91,                 EM_F_TYPE_NULL,                 "V91"),
    DPI_FIELD_D(EM_HTTP_K92,                 EM_F_TYPE_NULL,                 "K92"),
    DPI_FIELD_D(EM_HTTP_V92,                 EM_F_TYPE_NULL,                 "V92"),
    DPI_FIELD_D(EM_HTTP_K93,                 EM_F_TYPE_NULL,                 "K93"),
    DPI_FIELD_D(EM_HTTP_V93,                 EM_F_TYPE_NULL,                 "V93"),
    DPI_FIELD_D(EM_HTTP_K94,                 EM_F_TYPE_NULL,                 "K94"),
    DPI_FIELD_D(EM_HTTP_V94,                 EM_F_TYPE_NULL,                 "V94"),
    DPI_FIELD_D(EM_HTTP_K95,                 EM_F_TYPE_NULL,                 "K95"),
    DPI_FIELD_D(EM_HTTP_V95,                 EM_F_TYPE_NULL,                 "V95"),
    DPI_FIELD_D(EM_HTTP_K96,                 EM_F_TYPE_NULL,                 "K96"),
    DPI_FIELD_D(EM_HTTP_V96,                 EM_F_TYPE_NULL,                 "V96"),
    DPI_FIELD_D(EM_HTTP_K97,                 EM_F_TYPE_NULL,                 "K97"),
    DPI_FIELD_D(EM_HTTP_V97,                 EM_F_TYPE_NULL,                 "V97"),
    DPI_FIELD_D(EM_HTTP_K98,                 EM_F_TYPE_NULL,                 "K98"),
    DPI_FIELD_D(EM_HTTP_V98,                 EM_F_TYPE_NULL,                 "V98"),
    DPI_FIELD_D(EM_HTTP_K99,                 EM_F_TYPE_NULL,                 "K99"),
    DPI_FIELD_D(EM_HTTP_V99,                 EM_F_TYPE_NULL,                 "V99"),
};


static struct {const char *method; int len;} HTTP_METHOD[] =
{
    {"GET ",     4},
    {"POST ",    5},
    {"OPTIONS ", 8},
    {"HEAD ",    5},
    {"PUT ",     4},
    {"DELETE ",  7},
    {"CONNECT ", 8},
    {"PROPFIND ",9},
    {"REPORT ",  7},
    {NULL,       0}
};

static inline void _free_header(gpointer data)
{
    free(data);
}

static inline void _free_key_value(gpointer data)
{
    struct header_tmp_value *_value = (struct header_tmp_value *)data;

    if (_value->need_free)
        dpi_free((void *)_value->ptr);
    dpi_free(data);
}

static void _find_hash_write_log_delete(struct http_request_info *line_info, const char *header_name, struct tbl_log *log_ptr, int *idx)
{
    gpointer _value;
    struct header_value *value;

    if(line_info->table==NULL){
         write_n_empty_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, 1);
         return ;
    }

    _value = g_hash_table_lookup(line_info->table, header_name);
    value = (struct header_value *)_value;
    if (!value)
        write_n_empty_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, 1);
    else
        write_one_str_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, (const char *)value->ptr, value->len);
    g_hash_table_remove(line_info->table, header_name);
}

/* 0-没命中规则，1-命中规则，是想要的数据写tbl */
static int _http_content_type_filter(char *buf)
{
    if(g_config.http.num<=0){
        return 1;
    }
    regmatch_t pmatch[1];
    const size_t nmatch = 1;
    int status=0;
    int i=0;
    for(i=0;i<g_config.http.num;i++){
        status=regexec(&g_config.http.reg[i],buf,nmatch,pmatch,0);
        if(0==status){
            return 1;
        }
    }

    return 0;
}

static int _recreate_http_tbl_dir(int thread_id)
{
    char http_writing[COMMON_FILE_PATH]={0};
    struct timeval tv;
    gettimeofday(&tv, NULL);

    bzero(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH);
    bzero(flow_thread_info[thread_id].file_tbl_name,COMMON_FILE_PATH);
    flow_thread_info[thread_id].file_count=0;
    flow_thread_info[thread_id].timeout_sec=g_config.g_now_time;

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d_%s.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id,
                                  g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id);
    }

    mkdirs(flow_thread_info[thread_id].file_tbl_dir);

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d_%s.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id,
                                           g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id);
    }

    flow_thread_info[thread_id].tbl_fp=fopen(flow_thread_info[thread_id].file_tbl_name, "w");
    if(NULL==flow_thread_info[thread_id].tbl_fp){
        printf("http tbl file:[%s] create error!\n",flow_thread_info[thread_id].file_tbl_name);
        return 0;
    }

    return 1;
}


static int _get_http_file_name(char *file_name, int thread_id, int diretion)
{
    if(NULL==file_name){
        return 0;
    }
    int ret=0;

    if(flow_thread_info[thread_id].tbl_fp==NULL){
        ret=_recreate_http_tbl_dir(thread_id);
        if(0==ret){
            return 0;
        }
    }else{
        if(flow_thread_info[thread_id].file_count>= g_config.log_max_num ||
           (flow_thread_info[thread_id].timeout_sec + g_config.write_tbl_maxtime < g_config.g_now_time)){
            if(flow_thread_info[thread_id].tbl_fp!=NULL){
                fclose(flow_thread_info[thread_id].tbl_fp);
                flow_thread_info[thread_id].tbl_fp=NULL;
            }
            char http_tbl_file[COMMON_FILE_PATH]={0};
            memcpy(http_tbl_file,flow_thread_info[thread_id].file_tbl_dir,strlen(flow_thread_info[thread_id].file_tbl_dir)-8);
            rename(flow_thread_info[thread_id].file_tbl_dir,http_tbl_file);
            ret=_recreate_http_tbl_dir(thread_id);
            if(0==ret){
                return 0;
            }
        }
    }

    if(g_config.show_task_id){
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d_%s.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==0?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id,
                               g_config.task_id);
    }
    else{
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==0?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id);
    }
    return 1;
}


/*
* 解析http头部的每一行头域，寻找空行位置
*/
static void parse_http_line_info(const uint8_t *payload, const uint32_t payload_len, struct http_request_info *line_info)
{
    uint32_t a;
    int             line_len    = 0;
    int             header_len  = 0;
    uint8_t        parsed_lines = 0;
    uint32_t                end = payload_len - 1;
    const uint8_t     *line_ptr = payload;
    char               *_header = NULL;
    struct header_value *_value = NULL;


    if ((payload_len == 0) || (payload == NULL) || (end == 0))
        return;

    line_info->table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_header, _free_key_value);
    if (line_info->table == NULL) {
        DPI_LOG(DPI_LOG_DEBUG, "ghash table create error");
        return;
    }

    for (a = 0; a < end; a++) {
        if (get_uint16_t(payload, a) == ntohs(0x0d0a)) {
            line_len = (uint32_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

            if (line_len == 0) {
                line_info->empty_line_position = a;
                break;
            }

            _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
            if (!_value) {
                DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                goto next_line;
            }
            _value->need_free = 0;

            if (parsed_lines == 0) {
                _header = strdup("head_line");
                if (!_header) {
                    DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                    dpi_free(_value);
                    goto next_line;
                }
                _value->len = line_len;
                _value->ptr = line_ptr;

                g_hash_table_insert(line_info->table, _header, _value);
            } else {
                header_len = find_special_char(line_ptr, line_len, ':');
                if (header_len <= 0) {
                    dpi_free(_value);
                    DPI_LOG(DPI_LOG_DEBUG, "no ':' in header line or index 0 is ':'");
                    goto next_line;
                }
                if (line_ptr[header_len - 1] == ' ')
                    _header = strndup((const char *)line_ptr, header_len - 1);
                else
                    _header = strndup((const char *)line_ptr, header_len);

                strdown_inplace(_header);

                if (header_len + 1 >= line_len) {
                    DPI_LOG(DPI_LOG_DEBUG, "no value in header line");
                    free(_header);
                    dpi_free(_value);
                    goto next_line;
                }

                if (line_ptr[header_len + 1] == ' ') {
                    _value->len = line_len - header_len - 2;
                    _value->ptr = line_ptr + header_len + 2;
                } else {
                    _value->len = line_len - header_len - 1;
                    _value->ptr = line_ptr + header_len + 1;
                }
                g_hash_table_insert(line_info->table, _header, _value);
            }

next_line:
            parsed_lines++;
            line_ptr = &payload[a + 2];
            line_len = 0;

            if((a + 2) >= payload_len)
                break;

            a++; /* next char in the payload */
        }
    }
}

struct write_position
{
    char *ptr;
    int *index;
    int unknow_line_num;
};

struct merge_unknown_line
{
    char unknown_line[1500];
    int index;
    int unknow_line_num;
};

static void _write_unknown_line(gpointer key, gpointer value, gpointer user_data)
{
    struct write_position *position = (struct write_position *)user_data;
    char *_key = (char *)key;
    struct header_value *_value = (struct header_value *)value;

    if (position->unknow_line_num >= HTTP_UNKNOWN_LINE_NUM_MAX)
        return;

    if (strlen(_key) > 0 || _value->len > 0) {
        write_one_str_reconds(position->ptr, position->index, TBL_LOG_MAX_LEN, _key, strlen(_key));
        write_one_str_reconds(position->ptr, position->index, TBL_LOG_MAX_LEN, (const char *)_value->ptr, _value->len);
    } else {
        write_n_empty_reconds(position->ptr, position->index, TBL_LOG_MAX_LEN, 2);
    }
    position->unknow_line_num++;
}

static int write_http_unknown_reconds(char *log, int *idx, int max_len, const char *key, unsigned int key_len,
        const char *val, unsigned int val_len)
{
    unsigned int i;
    int index = *idx;

    for (i = 0; i < key_len; i++) {
        if (index >= max_len)
            break;

        if (key[i] == '|')
            log[index++] = '_';
        else if (key[i] == '\n' || key[i] == '\r' || key[i] == '&' || key[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = key[i];
    }

    if (index < max_len)
        log[index++] = '=';

    for (i = 0; i < val_len; i++) {
        if (index >= max_len)
            break;

        if (val[i] == '|')
            log[index++] = '_';
        else if (val[i] == '\n' || val[i] == '\r' || val[i] == '&' || val[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = val[i];
    }

    if (index + 1 < max_len) {
        log[index++] = '&';
    }

    *idx = index;
    return 0;
}

static void _merge_unknown_line(gpointer key, gpointer value, gpointer user_data)
{
    struct merge_unknown_line *position = (struct merge_unknown_line *)user_data;
    char *_key = (char *)key;
    struct header_value *_value = (struct header_value *)value;

    if (strlen(_key) > 0 || _value->len > 0) {
        write_http_unknown_reconds(position->unknown_line, &position->index, sizeof(position->unknown_line),
                    _key, strlen(_key), (const char *)_value->ptr, _value->len);
    }
    position->unknow_line_num++;
}

static void _get_http_client_server_port(struct flow_info *flow, const uint8_t *payload, uint32_t payload_len)
{
    if(payload==NULL || payload_len<7){
        return;
    }
    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
        //s2c
        flow->drt_port_src[FLOW_DIR_DST2SRC]=flow->port_src;
        flow->drt_port_dst[FLOW_DIR_DST2SRC]=flow->port_dst;
    }
    else if (payload_len >= 10) {
        for(int i = 0; HTTP_METHOD[i].method; i++){
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)){
                //c2s
                flow->drt_port_src[FLOW_DIR_SRC2DST]=flow->port_src;
                flow->drt_port_dst[FLOW_DIR_SRC2DST]=flow->port_dst;
                break;
            }
        }
    }
}

static int http_change_and_account_gbk(uint8_t *pData, int len)
{
    int loop = len;
    uint8_t *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            *p++ = ' ';
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }


        return p - pData;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}


void write_http_log_v51(struct flow_info *flow, int direction, struct http_request_info *line_info, struct tbl_log *log_ptr)
{
    //解析post请求包和http下行包中带body的数据,不再写入 .POST文件,将解析后的内容写入字段V51
    /*http内容*/
    //char* OutBuffer = NULL;
    const char *str= NULL;
    struct http_session *session = (struct http_session *)flow->app_session;
    struct rte_mempool *pool = NULL;
    if (line_info->PROTOCOL_VAL_LEN(content) > 0)
    {
        pool = tbl_log_content_mempool_256k;

        if (rte_mempool_get(pool, (void **)&log_ptr->content_ptr) >= 0)
        {
            log_ptr->content_len = 262144; //256*1024
            log_ptr->content_ptr[0] = '\0';

            // ATOMIC_ADD_FETCH(&post_total);

            //printf("HTTP POST len: %d\n", line_info->PROTOCOL_VAL_LEN(content));

            if(g_config.http.http_tax_switch
               && session->uri && strlen(session->uri) > 20
               && strncmp(session->uri, "/InterfaceForClient/", 20) == 0
               && strcmp(session->host, "www.i-tax.cn") == 0)
            {
                AES_KEY tax_aes;
                unsigned char key[256]="aisino1234onisia";
                unsigned char iv[256] ="onisia1234aisino";

                if(!AES_set_decrypt_key(key, 128, &tax_aes)){
                    if(direction == 0){
                        AES_cbc_encrypt((const unsigned char *)line_info->PROTOCOL_VAL_PTR(content), (unsigned char *)log_ptr->content_ptr, line_info->PROTOCOL_VAL_LEN(content), &tax_aes, iv, AES_DECRYPT);
                        int len = http_change_and_account_gbk(log_ptr->content_ptr, line_info->PROTOCOL_VAL_LEN(content));
                        log_ptr->content_ptr[len] = 0;
                    }
                    else{
                        char *a = malloc(line_info->PROTOCOL_VAL_LEN(content));
                        char *b = malloc(line_info->PROTOCOL_VAL_LEN(content));
                        memcpy(a, line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content));
                        ws_base64_decode_inplace(a);
                        AES_cbc_encrypt((unsigned char*)a, (unsigned char*)b, line_info->PROTOCOL_VAL_LEN(content) / 4 * 3, &tax_aes, iv, AES_DECRYPT);
                        YV_HttpPostParse_ParseRAW(NULL, b, line_info->PROTOCOL_VAL_LEN(content) / 4 * 3,
                                (char *)log_ptr->content_ptr, 262144);
                        free(a);
                        free(b);
                    }
                }
            }
            else{
                str = filetype((const char *)line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content));
                if(str)
                {
                    snprintf((char *)log_ptr->content_ptr, 262144, "HTTP_BODY_FORMAT=%s&LEN=%d", str, line_info->PROTOCOL_VAL_LEN(content));
                    flow->err_pcap_dump = 0;  // 传输类型识别成功. 不抓包
                }
                else
                {
                   YV_HttpPostParse_ParseRAW(NULL, (const char *)line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content),
                            (char *)log_ptr->content_ptr, 262144);
                }
            }

            log_ptr->content_ptr[262143] = '\0';
            uint32_t buf_len = strlen((const char *)log_ptr->content_ptr);
            if(buf_len > 0)
            {
                // ATOMIC_ADD_FETCH(&post_success);

                if(0 == strncmp((const char *)log_ptr->content_ptr, "ERR_", 4))
                {
                    flow->err_pcap_dump = 1;  // 解析失败. 抓包
                }
                else
                {
                    flow->err_pcap_dump = 0;  // 解析成功. 不抓包
                }

                //将OutBuffer中所有的"|" 替换为 "_"
                uint32_t i = 0;
                for(i=0; i < buf_len; i++)
                {
                    if('|' == log_ptr->content_ptr[i])
                        log_ptr->content_ptr[i] = '_';
                }
            }
        }
        else{
            // rte_atomic64_inc(&log_256k_fail);
        }
    }
    else
    {
        flow->err_pcap_dump = 0;  // 没有Content-length. 不抓包
    }

}

//add by xuxn 统计http解析post的总次数、成功率、失败率
// extern volatile uint64_t post_total;    //http 解析.POST文件 总记录条数
// extern volatile uint64_t post_success;    //http 解析.POST文件 解析成功的记录条数

static int write_http_log(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    struct http_session *session = (struct http_session *)flow->app_session;
    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_HTTP_H_X_NUMBER+1;i++)
    {
        switch(http_field_array[i].index){
        case EM_HTTP_METHOD:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
            break;
        case EM_HTTP_URI:
            //write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            if(session->uri!=NULL){
                write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->uri, strlen(session->uri));
            }else{
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_HTTP_VERSION:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
            break;
        case EM_HTTP_STATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
            break;
        case EM_HTTP_RESPONSESTATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
            break;
        case EM_HTTP_CACHE_CONTROL:
            _find_hash_write_log_delete(line_info, "cache-control", log_ptr, &idx);
            break;
        case EM_HTTP_CONNECTION:
            _find_hash_write_log_delete(line_info, "connection", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE:
            _find_hash_write_log_delete(line_info, "cookie", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE2:
            _find_hash_write_log_delete(line_info, "cookie2", log_ptr, &idx);
            break;
        case EM_HTTP_DATE:
            _find_hash_write_log_delete(line_info, "date", log_ptr, &idx);
            break;
        case EM_HTTP_PRAGMA:
            _find_hash_write_log_delete(line_info, "pragma", log_ptr, &idx);
            break;
        case EM_HTTP_TRAILER:
            _find_hash_write_log_delete(line_info, "trailer", log_ptr, &idx);
            break;
        case EM_HTTP_TRANSFER_ENCODING:
            _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_UPGRADE:
            _find_hash_write_log_delete(line_info, "upgrade", log_ptr, &idx);
            break;
        case EM_HTTP_VIA:
            _find_hash_write_log_delete(line_info, "via", log_ptr, &idx);
            break;
        case EM_HTTP_WARNING:
            _find_hash_write_log_delete(line_info, "warning", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT:
            _find_hash_write_log_delete(line_info, "accept", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_CHARSET:
            _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_ENCODING:
            _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "accept-language", log_ptr, &idx);
            break;
        case EM_HTTP_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "authorization", log_ptr, &idx);
            break;
        case EM_HTTP_EXPECT:
            _find_hash_write_log_delete(line_info, "expect", log_ptr, &idx);
            break;
        case EM_HTTP_FROM:
            _find_hash_write_log_delete(line_info, "from", log_ptr, &idx);
            break;
        case EM_HTTP_HOST:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            break;
        case EM_HTTP_IF_MATCH:
            _find_hash_write_log_delete(line_info, "if-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_MODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, &idx);
            break;
        case EM_HTTP_IF_NONE_MATCH:
            _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_RANGE:
            _find_hash_write_log_delete(line_info, "if-range", log_ptr, &idx);
            break;
        case EM_HTTP_IF_UNMODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, &idx);
            break;
        case EM_HTTP_MAX_FORWARDS:
            _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, &idx);
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_PROXY_TYPE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->proxy_type, strlen(line_info->proxy_type));
            break;
        case EM_HTTP_PROXY_LOGIN:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->proxy_login, strlen(line_info->proxy_login));
            break;
        case EM_HTTP_PROXY_AUTHORIZATION_INFO:
            _find_hash_write_log_delete(line_info, "proxy-authentication-info", log_ptr, &idx); //这个字段名称写错了,应该是 proxy-authentication-info 而不是 proxy-authorization-info
            break;
#endif
        case EM_HTTP_RANGE:
            _find_hash_write_log_delete(line_info, "range", log_ptr, &idx);
            break;
        case EM_HTTP_REFERER:
            _find_hash_write_log_delete(line_info, "referer", log_ptr, &idx);
            break;
        case EM_HTTP_TE:
            _find_hash_write_log_delete(line_info, "te", log_ptr, &idx);
            break;
        case EM_HTTP_USER_AGENT:
            {
                if(line_info->table==NULL){
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }
                gpointer _value;
                struct header_value *value;
                _value = g_hash_table_lookup(line_info->table, "user-agent");
                value = (struct header_value *)_value;
                if (!value)
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                else
                {
                    int i = 0, j = 0;
                    char tmp[value->len*2 + 1];
                    memset(tmp, 0, value->len*2 + 1);
                    for(i=0; i < value->len && j < value->len*2 + 1; i++)
                    {
                        if(!isprint(value->ptr[i]))
                        {
                            sprintf(tmp+j, "%2x", value->ptr[i]);
                            j++;
                        }
                        else
                            tmp[j] = value->ptr[i];
                        j++;
                    }
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)tmp, strlen(tmp));
                }
                g_hash_table_remove(line_info->table, "user-agent");
            }
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_PROXY_PROXYAGENT:
            _find_hash_write_log_delete(line_info, "proxy-agent", log_ptr, &idx);
            break;
        case EM_HTTP_USER_CPU:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->uaCPU, strlen(line_info->uaCPU));
            break;
        case EM_HTTP_USER_OS:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->uaOS, strlen(line_info->uaOS));
            break;
#endif
        case EM_HTTP_ACCEPT_RANGES:
            _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, &idx);
            break;
        case EM_HTTP_AGE:
            _find_hash_write_log_delete(line_info, "age", log_ptr, &idx);
            break;
        case EM_HTTP_ETAG:
            _find_hash_write_log_delete(line_info, "etag", log_ptr, &idx);
            break;
        case EM_HTTP_LOCATION:
            _find_hash_write_log_delete(line_info, "location", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, &idx);
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_INQUIRY_TYPE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->inquiry_type, strlen(line_info->inquiry_type));
            break;
        case EM_HTTP_PROXY_CONNECT_HOST:
            _find_hash_write_log_delete(line_info, "proxy_connect_host", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_CONNECT_PORT:
            _find_hash_write_log_delete(line_info, "proxy_connect_port", log_ptr, &idx);
            break;
#endif
        case EM_HTTP_RETRY_AFTER:
            _find_hash_write_log_delete(line_info, "retry-after", log_ptr, &idx);
            break;
        case EM_HTTP_SERVER:
            _find_hash_write_log_delete(line_info, "server", log_ptr, &idx);
            break;
        case EM_HTTP_VARY:
            _find_hash_write_log_delete(line_info, "vary", log_ptr, &idx);
            break;
        case EM_HTTP_WWW_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_ALLOW:
            _find_hash_write_log_delete(line_info, "allow", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_ENCODING:
            _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "content-language", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LENGTH:
            _find_hash_write_log_delete(line_info, "content-length", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LOCATION:
            _find_hash_write_log_delete(line_info, "content-location", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_MD5:
            _find_hash_write_log_delete(line_info, "content-md5", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_RANGE:
            _find_hash_write_log_delete(line_info, "content-range", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_TYPE:
            _find_hash_write_log_delete(line_info, "content-type", log_ptr, &idx);
            break;
        case EM_HTTP_EXPIRES:
            _find_hash_write_log_delete(line_info, "expires", log_ptr, &idx);
            break;
        case EM_HTTP_LAST_MODIFIED:
            _find_hash_write_log_delete(line_info, "last-modified", log_ptr, &idx);
            break;
        case EM_HTTP_X_FORWARDED_FOR:
            _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE:
            _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE2:
            _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, &idx);
            break;
        default:
            write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    struct merge_unknown_line unknown_line;
    memset(&unknown_line, 0, sizeof(unknown_line));

    //K00  merged unknown line
    g_hash_table_foreach(line_info->table, _merge_unknown_line, &unknown_line);
    if(unknown_line.unknow_line_num > 0)
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, unknown_line.unknown_line, unknown_line.index - 1);
    else
        write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //V00 null
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //K01 filename
    if(line_info->filename[0])
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->filename, strlen(line_info->filename));
    else
        write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
    //V01 NULL
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //K02,V02 --- K50,V50 NULL
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (HTTP_UNKNOWN_LINE_NUM_MAX-1) * 2);

    log_ptr->thread_id = flow->thread_id;
    // log_ptr->log_type = TBL_LOG_HTTP;
    log_ptr->type = TBL_LOG_HTTP;
    // log_ptr->log_len = idx;
    log_ptr->len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    //解析post请求包和http下行包中带body的数据,不再写入 .POST文件,将解析后的内容写入字段V51
    /*http内容*/
    //char* OutBuffer = NULL;
    write_http_log_v51(flow, direction, line_info, log_ptr);

    // if (write_tbl_log(log_ptr) != 1) {
    //     if (log_ptr->content_len > 0 && pool)
    //         rte_mempool_put(pool, (void *)log_ptr->content_ptr);
    //     rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    // }

    if (tbl_log_enqueue(log_ptr) != 1) {
        if (log_ptr->content_len > 0 && tbl_log_content_mempool_256k) {
            rte_mempool_put(tbl_log_content_mempool_256k, (void *)log_ptr->content_ptr);
        }

        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


static int write_http_file_log(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    struct http_session *session = (struct http_session *)flow->app_session;
    const char *str= NULL;

    if(NULL==flow_thread_info[flow->thread_id].tbl_fp){
        DPI_LOG(DPI_LOG_WARNING, "tbl_fp==NULL");
        return PKT_OK;
    }

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_HTTP_H_X_NUMBER+1;i++)
    {
        switch(http_field_array[i].index){
        case EM_HTTP_METHOD:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
            break;
        case EM_HTTP_URI:
            //write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            if(session->uri!=NULL){
                write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->uri, strlen(session->uri));
            }else{
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_HTTP_VERSION:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
            break;
        case EM_HTTP_STATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
            break;
        case EM_HTTP_RESPONSESTATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
            break;
        case EM_HTTP_CACHE_CONTROL:
            _find_hash_write_log_delete(line_info, "cache-control", log_ptr, &idx);
            break;
        case EM_HTTP_CONNECTION:
            _find_hash_write_log_delete(line_info, "connection", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE:
            _find_hash_write_log_delete(line_info, "cookie", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE2:
            _find_hash_write_log_delete(line_info, "cookie2", log_ptr, &idx);
            break;
        case EM_HTTP_DATE:
            _find_hash_write_log_delete(line_info, "date", log_ptr, &idx);
            break;
        case EM_HTTP_PRAGMA:
            _find_hash_write_log_delete(line_info, "pragma", log_ptr, &idx);
            break;
        case EM_HTTP_TRAILER:
            _find_hash_write_log_delete(line_info, "trailer", log_ptr, &idx);
            break;
        case EM_HTTP_TRANSFER_ENCODING:
            _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_UPGRADE:
            _find_hash_write_log_delete(line_info, "upgrade", log_ptr, &idx);
            break;
        case EM_HTTP_VIA:
            _find_hash_write_log_delete(line_info, "via", log_ptr, &idx);
            break;
        case EM_HTTP_WARNING:
            _find_hash_write_log_delete(line_info, "warning", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT:
            _find_hash_write_log_delete(line_info, "accept", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_CHARSET:
            _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_ENCODING:
            _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "accept-language", log_ptr, &idx);
            break;
        case EM_HTTP_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "authorization", log_ptr, &idx);
            break;
        case EM_HTTP_EXPECT:
            _find_hash_write_log_delete(line_info, "expect", log_ptr, &idx);
            break;
        case EM_HTTP_FROM:
            _find_hash_write_log_delete(line_info, "from", log_ptr, &idx);
            break;
        case EM_HTTP_HOST:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            break;
        case EM_HTTP_IF_MATCH:
            _find_hash_write_log_delete(line_info, "if-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_MODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, &idx);
            break;
        case EM_HTTP_IF_NONE_MATCH:
            _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_RANGE:
            _find_hash_write_log_delete(line_info, "if-range", log_ptr, &idx);
            break;
        case EM_HTTP_IF_UNMODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, &idx);
            break;
        case EM_HTTP_MAX_FORWARDS:
            _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, &idx);
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_PROXY_TYPE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->proxy_type, strlen(line_info->proxy_type));
            break;
        case EM_HTTP_PROXY_LOGIN:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->proxy_login, strlen(line_info->proxy_login));
            break;
        case EM_HTTP_PROXY_AUTHORIZATION_INFO:
            _find_hash_write_log_delete(line_info, "proxy-authentication-info", log_ptr, &idx); //这个字段名称写错了,应该是 proxy-authentication-info 而不是 proxy-authorization-info
            break;
#endif
        case EM_HTTP_RANGE:
            _find_hash_write_log_delete(line_info, "range", log_ptr, &idx);
            break;
        case EM_HTTP_REFERER:
            _find_hash_write_log_delete(line_info, "referer", log_ptr, &idx);
            break;
        case EM_HTTP_TE:
            _find_hash_write_log_delete(line_info, "te", log_ptr, &idx);
            break;
        case EM_HTTP_USER_AGENT:
            {
                if(line_info->table==NULL){
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }
                gpointer _value;
                struct header_value *value;
                _value = g_hash_table_lookup(line_info->table, "user-agent");
                value = (struct header_value *)_value;
                if (!value)
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                else
                {
                    int i = 0, j = 0;
                    char tmp[value->len*2 + 1];
                    memset(tmp, 0, value->len*2 + 1);
                    for(i=0; i < value->len && j < value->len*2 + 1; i++)
                    {
                        if(!isprint(value->ptr[i]))
                        {
                            sprintf(tmp+j, "%2x", value->ptr[i]);
                            j++;
                        }
                        else
                            tmp[j] = value->ptr[i];
                        j++;
                    }
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)tmp, strlen(tmp));
                }
                g_hash_table_remove(line_info->table, "user-agent");
            }
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_PROXY_PROXYAGENT:
            _find_hash_write_log_delete(line_info, "proxy-agent", log_ptr, &idx);
            break;
        case EM_HTTP_USER_CPU:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->uaCPU, strlen(line_info->uaCPU));
            break;
        case EM_HTTP_USER_OS:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->uaOS, strlen(line_info->uaOS));
            break;
#endif
        case EM_HTTP_ACCEPT_RANGES:
            _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, &idx);
            break;
        case EM_HTTP_AGE:
            _find_hash_write_log_delete(line_info, "age", log_ptr, &idx);
            break;
        case EM_HTTP_ETAG:
            _find_hash_write_log_delete(line_info, "etag", log_ptr, &idx);
            break;
        case EM_HTTP_LOCATION:
            _find_hash_write_log_delete(line_info, "location", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, &idx);
            break;
#ifndef   DPI_DBZZ
        case EM_HTTP_INQUIRY_TYPE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->inquiry_type, strlen(line_info->inquiry_type));
            break;
        case EM_HTTP_PROXY_CONNECT_HOST:
            _find_hash_write_log_delete(line_info, "proxy_connect_host", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_CONNECT_PORT:
            _find_hash_write_log_delete(line_info, "proxy_connect_port", log_ptr, &idx);
            break;
#endif
        case EM_HTTP_RETRY_AFTER:
            _find_hash_write_log_delete(line_info, "retry-after", log_ptr, &idx);
            break;
        case EM_HTTP_SERVER:
            _find_hash_write_log_delete(line_info, "server", log_ptr, &idx);
            break;
        case EM_HTTP_VARY:
            _find_hash_write_log_delete(line_info, "vary", log_ptr, &idx);
            break;
        case EM_HTTP_WWW_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_ALLOW:
            _find_hash_write_log_delete(line_info, "allow", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_ENCODING:
            _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "content-language", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LENGTH:
            _find_hash_write_log_delete(line_info, "content-length", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LOCATION:
            _find_hash_write_log_delete(line_info, "content-location", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_MD5:
            _find_hash_write_log_delete(line_info, "content-md5", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_RANGE:
            _find_hash_write_log_delete(line_info, "content-range", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_TYPE:
            _find_hash_write_log_delete(line_info, "content-type", log_ptr, &idx);
            break;
        case EM_HTTP_EXPIRES:
            _find_hash_write_log_delete(line_info, "expires", log_ptr, &idx);
            break;
        case EM_HTTP_LAST_MODIFIED:
            _find_hash_write_log_delete(line_info, "last-modified", log_ptr, &idx);
            break;
        case EM_HTTP_X_FORWARDED_FOR:
            _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE:
            _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE2:
            _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, &idx);
            break;
        default:
            write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    struct merge_unknown_line unknown_line;
    memset(&unknown_line, 0, sizeof(unknown_line));

    //K00  merged unknown line
    g_hash_table_foreach(line_info->table, _merge_unknown_line, &unknown_line);
    if(unknown_line.unknow_line_num > 0)
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, unknown_line.unknown_line, unknown_line.index - 1);
    else
        write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //V00 null
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //K01 filename
    if(strlen(line_info->filename)>0){
        char filename[128]={0};
        if(get_filename(line_info->filename, filename)){
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, filename, strlen(filename));
        }else{
            write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
        }
    }else{
        write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
    }
    //V01 NULL
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    //K02,V02 --- K50,V50 NULL
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (HTTP_UNKNOWN_LINE_NUM_MAX-1) * 2);

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_HTTP;
    log_ptr->len = idx;
    // log_ptr->log_len = idx;
    log_ptr->content_len = idx;
    log_ptr->content_ptr = NULL;

    //解析post请求包和http下行包中带body的数据,不再写入 .POST文件,将解析后的内容写入字段V51
    /*http内容*/
    //char* OutBuffer = NULL;
    struct rte_mempool *pool = NULL;
    if (line_info->PROTOCOL_VAL_LEN(content) > 0)
    {
        pool = tbl_log_content_mempool_256k;

        if (rte_mempool_get(pool, (void **)&log_ptr->content_ptr) >= 0)
        {
            log_ptr->content_len = 256*1024;
            log_ptr->content_ptr[0] = '\0';

            // ATOMIC_ADD_FETCH(&post_total);

            if(g_config.http.http_tax_switch
               && session->uri && strlen(session->uri) > 20
               && strncmp(session->uri, "/InterfaceForClient/", 20) == 0
               && strcmp(session->host, "www.i-tax.cn") == 0)
            {
                AES_KEY tax_aes;
                unsigned char key[256]="aisino1234onisia";
                unsigned char iv[256] ="onisia1234aisino";

                if(!AES_set_decrypt_key(key, 128, &tax_aes)){
                    if(direction == 0){
                        AES_cbc_encrypt((const unsigned char *)line_info->PROTOCOL_VAL_PTR(content), (unsigned char *)log_ptr->content_ptr, line_info->PROTOCOL_VAL_LEN(content), &tax_aes, iv, AES_DECRYPT);
                        int len = http_change_and_account_gbk(log_ptr->content_ptr, line_info->PROTOCOL_VAL_LEN(content));
                        log_ptr->content_ptr[len] = 0;
                    }
                    else{
                        char *a = malloc(line_info->PROTOCOL_VAL_LEN(content));
                        char *b = malloc(line_info->PROTOCOL_VAL_LEN(content));
                        if(a && b){
                            memcpy(a, line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content));
                            ws_base64_decode_inplace(a);
                            AES_cbc_encrypt((unsigned char*)a, (unsigned char*)b, line_info->PROTOCOL_VAL_LEN(content) / 4 * 3, &tax_aes, iv, AES_DECRYPT);
                            YV_HttpPostParse_ParseRAW(NULL, b, line_info->PROTOCOL_VAL_LEN(content) / 4 * 3,
                                    (char *)log_ptr->content_ptr, 262144);
                        }
                        free(a);
                        free(b);
                    }
                }
            }
            else{
                str = filetype((const char *)line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content));
                if(str)
                {
                    snprintf((char *)log_ptr->content_ptr, 256*1024, "HTTP_BODY_FORMAT=%s&LEN=%d", str, line_info->PROTOCOL_VAL_LEN(content));
                    flow->err_pcap_dump = 0;  // 传输类型识别成功. 不抓包
                }
                else
                {
                    YV_HttpPostParse_ParseRAW(NULL, (const char *)line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content),
                        (char *)log_ptr->content_ptr, 256*1024);
                }
            }
            uint32_t buf_len = strlen((const char *)log_ptr->content_ptr);
            buf_len = (buf_len < 256*1024 ? buf_len : 256*1024-1);
            if(buf_len > 0)
            {
                // ATOMIC_ADD_FETCH(&post_success);

                if(0 == strncmp((const char *)log_ptr->content_ptr, "ERR_", 4))
                {
                    flow->err_pcap_dump = 1;  // 解析失败. 抓包
                }
                else
                {
                    flow->err_pcap_dump = 0;  // 解析成功. 不抓包
                }

                //将OutBuffer中所有的"|" 替换为 "_"
                uint32_t i = 0;
                for(i=0; i < buf_len; i++)
                {
                    if('|' == log_ptr->content_ptr[i])
                        log_ptr->content_ptr[i] = '_';
                }
            }
        }
        else{
            // rte_atomic64_inc(&log_256k_fail);
        }
    }
    else
    {
        flow->err_pcap_dump = 0;  // 没有Content-length. 不抓包
    }

    fwrite(log_ptr->log_content, log_ptr->len, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    uint32_t len = (NULL == log_ptr->content_ptr ? 0 : strlen((const char *)log_ptr->content_ptr));

    if (log_ptr->content_ptr && len > 0 && (0 != memcmp(log_ptr->content_ptr, "ERR_", 4) && 0 != memcmp(log_ptr->content_ptr, "HTTP_BODY_FORMAT", 15)))
    {
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite(log_ptr->content_ptr, len, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    }
    else
    if (log_ptr->content_ptr && len > 0 && (0 == memcmp(log_ptr->content_ptr, "ERR_", 4) || 0 == memcmp(log_ptr->content_ptr, "HTTP_BODY_FORMAT", 15)))
    {
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite(log_ptr->content_ptr, len, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    }
    else
    {
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    }

    for (i = 0; i < KV53_KV99_LEN; i++)
    {
        fwrite("|", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    }

    fwrite("\n", 1, 1, flow_thread_info[log_ptr->thread_id].tbl_fp);
    //fflush(flow_thread_info[log_ptr->thread_id].tbl_fp);
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    if (log_ptr->content_len > 0 && pool)
        rte_mempool_put(pool, (void *)log_ptr->content_ptr);


    return 0;
}

static void write_http_data_to_file(struct flow_info *flow,
                                          const uint8_t *payload,
                                          int        payload_len,
                                          int        direction,
                                          struct http_request_info *line_info)
{
    int ret=0;

    FILE    *fp=NULL;
    char    file_name[COMMON_FILE_PATH]={0};

    if(0==g_config.http.http_switch_response_file && 1==direction){
        write_http_file_log(flow, direction, line_info);
        flow_thread_info[flow->thread_id].file_count++;
        flow_thread_info[flow->thread_id].timeout_sec=g_config.g_now_time;
        return;
    }

    ret = _get_http_file_name(file_name, flow->thread_id, direction);
    fp=fopen(file_name, "w");
    if(fp!=NULL && 1==ret){
        int write_size=g_config.http.http_file_size;
        if(payload_len<write_size){
            write_size=payload_len;
        }
        snprintf(line_info->filename, COMMON_FILE_PATH, "%s", file_name);
        write_http_file_log(flow, direction, line_info);
        fwrite(payload, write_size, 1, fp);
        fclose(fp);

        flow_thread_info[flow->thread_id].file_count++;
        flow_thread_info[flow->thread_id].timeout_sec=g_config.g_now_time;
    }

}


/*************************************************************************************************/


#define CHUNK_SIZE_MAX 32

static void http_parser_chunked_content(const uint8_t *chunked_content, uint32_t chunked_content_len,
        uint8_t *content, uint32_t *content_len)
{
    uint32_t offset = 0;
    int linelen;
    uint32_t max_len = *content_len;
    char chunk_string[CHUNK_SIZE_MAX];
    uint32_t chunk_size;

    *content_len = 0;

    while (offset < chunked_content_len) {
        linelen = find_packet_line_end(chunked_content + offset, chunked_content_len - offset);
        if (linelen <= 0 || linelen >= CHUNK_SIZE_MAX) {
            /* Can't get the chunk size line */
            break;
        }

        strncpy(chunk_string, (const char *)chunked_content + offset, linelen);
        chunk_string[linelen] = 0;
        chunk_size = strtol(chunk_string, NULL, 16);

        offset += linelen + 2;
        if (chunk_size > chunked_content_len - offset) {
            chunk_size = chunked_content_len - offset;
        }

        if (*content_len + chunk_size < max_len) {
            memcpy(content + *content_len, chunked_content + offset, chunk_size);
            *content_len += chunk_size;
        }

        offset += chunk_size + 2;
        if (offset >= chunked_content_len)
            break;

        if (chunk_size == 0)
            break;
    }

    return;
}

/*
* 解析alipay 第一个chunked 头部的每一行头域，寻找空行位置
*/
static void alipay_parse_http_first_chunked_info(const uint8_t *payload, const uint32_t payload_len, struct http_request_info *line_info)
{
    uint32_t a;
    int             line_len    = 0;
    int             header_len  = 0;
    uint8_t        parsed_lines = 0;
    uint32_t                end = payload_len - 1;
    const uint8_t     *line_ptr = payload;
    char               *_header = NULL;
    struct header_value *_value = NULL;


    if ((payload_len == 0) || (payload == NULL) || (end == 0))
        return;

    if (line_info->table == NULL) {
        DPI_LOG(DPI_LOG_DEBUG, "ghash table not create");
        return;
    }

    for (a = 0; a < end; a++) {
        if (get_uint16_t(payload, a) == ntohs(0x0d0a)) {
            line_len = (uint32_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

            if (line_len == 0) {
                break;
            }

            _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
            if (!_value) {
                DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                goto next_line;
            }
            _value->need_free = 0;

            header_len = find_special_char(line_ptr, line_len, ':');
            if (header_len <= 0) {
                dpi_free(_value);
                DPI_LOG(DPI_LOG_DEBUG, "no ':' in header line or index 0 is ':'");
                goto next_line;
            }
            if (line_ptr[header_len - 1] == ' ')
                _header = strndup((const char *)line_ptr, header_len - 1);
            else
                _header = strndup((const char *)line_ptr, header_len);

            strdown_inplace(_header);

            if (header_len + 1 >= line_len) {
                DPI_LOG(DPI_LOG_DEBUG, "no value in header line");
                free(_header);
                dpi_free(_value);
                goto next_line;
            }

            if (line_ptr[header_len + 1] == ' ') {
                _value->len = line_len - header_len - 2;
                _value->ptr = line_ptr + header_len + 2;
            } else {
                _value->len = line_len - header_len - 1;
                _value->ptr = line_ptr + header_len + 1;
            }
            g_hash_table_insert(line_info->table, _header, _value);

next_line:
            parsed_lines++;
            line_ptr = &payload[a + 2];
            line_len = 0;

            if((a + 2) >= payload_len)
                break;

            a++; /* next char in the payload */
        }
    }
}

static void alipay_http_parser_chunked_content(const uint8_t *chunked_content, uint32_t chunked_content_len,
        uint8_t *content, uint32_t *content_len, struct http_request_info *linfo)
{
    uint32_t offset = 0;
    int linelen;
    uint32_t max_len = *content_len;
    char chunk_string[CHUNK_SIZE_MAX];
    uint32_t chunk_size;
    *content_len = 0;

    for(int i=0; offset < chunked_content_len; i++){
        linelen = find_packet_line_end(chunked_content + offset, chunked_content_len - offset);
        if (linelen <= 0 || linelen >= CHUNK_SIZE_MAX) {
            /* Can't get the chunk size line */
            return;
        }

        strncpy(chunk_string, (const char *)chunked_content + offset, linelen);
        chunk_string[linelen] = 0;
        chunk_size = strtol(chunk_string, NULL, 16);

        offset += linelen + 2;
        if (chunk_size > chunked_content_len - offset) {
            chunk_size = chunked_content_len - offset;
        }

        if(i == 0){
            alipay_parse_http_first_chunked_info(chunked_content + offset, chunk_size, linfo);
        } else {
            if (*content_len + chunk_size < max_len) {
                memcpy(content + *content_len, chunked_content + offset, chunk_size);
                *content_len += chunk_size;
            } else {
                memcpy(content + *content_len, chunked_content + offset, max_len - *content_len);
                *content_len = max_len;
                break;
            }
        }

        offset += chunk_size + 2;
        if (offset >= chunked_content_len)
            break;

        if (chunk_size == 0)
            break;
    }

    return;
}

#define GET_DEV_INFO(devArr, arrlen, devFlag)    do{                                    \
    char* tmp0 = strcasestr((char *)value->ptr, (devFlag));                            \
    if(NULL != tmp0)                                                                \
    {                                                                                \
        char* tmp1 = strstr(tmp0, ";");                                                \
        char* tmp2 = strstr(tmp0, ")");                                                \
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))                            \
            memcpy((devArr), tmp0, (tmp2-tmp0<(arrlen) ? tmp2-tmp0:(arrlen)-1));    \
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)                        \
            memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));    \
        else if(NULL == tmp1 && NULL == tmp2)                                        \
        {                                                                            \
            tmp1 = strstr(tmp0,"\r\n");                                                \
            if(NULL != tmp1)                                                        \
                memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));\
        }                                                                            \
    }                                                                                \
}while(0)

size_t ws_base64_decode_inplace(char *s)
{
    static const char b64[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\r\n";
    int bit_offset, byte_offset, idx, i;
    unsigned char *d = (unsigned char *)s;
    char *p;
    int  cr_idx;
    // we will allow CR and LF - but ignore them
    cr_idx = (int)(strchr(b64, '\r') - b64);
    i = 0;
    while (*s && (p = strchr(b64, *s))) {
        idx = (int)(p - b64);
        if (idx < cr_idx) {
            byte_offset = (i * 6) / 8;
            bit_offset = (i * 6) % 8;
            d[byte_offset] &= ~((1 << (8 - bit_offset)) - 1);
            if (bit_offset < 3) {
                d[byte_offset] |= (idx << (2 - bit_offset));
            } else {
                d[byte_offset] |= (idx >> (bit_offset - 2));
                d[byte_offset + 1] = 0;
                d[byte_offset + 1] |= (idx << (8 - (bit_offset - 2))) & 0xFF;
            }
            i++;
        }
        s++;
    }
    d[i*3/4] = 0;
    return i*3/4;
}

static void dissect_http_authorization(struct http_request_info *line_info)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "proxy-authorization");
    if(!value)
        value = (struct header_value *)g_hash_table_lookup(line_info->table, "authorization");
    if(value)
    {
        const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
        if(tmp)
        {
            int len = tmp - value->ptr;
            memcpy(line_info->proxy_type,  value->ptr, DPI_MIN(len, TYPE_LEN-1));
            if(strncasecmp(line_info->proxy_type, "Basic", 5)  == 0){
                memcpy(line_info->proxy_login, tmp + 1,    DPI_MIN(value->len - len - 1, LOGIN_LEN-1));
                ws_base64_decode_inplace(line_info->proxy_login);
            }
        }
    }
}

static void dissect_http_authenticate(struct http_request_info *line_info)
{
   //质询认证方案："basic" 的截取
   struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "www-authenticate");
   if(!value)
           value = g_hash_table_lookup(line_info->table, "proxy-authenticate");
   if(value)
   {
       const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
       if(tmp)
       {
           int len = tmp - value->ptr;
           memcpy(line_info->inquiry_type, value->ptr, len >= TYPE_LEN ? TYPE_LEN - 1 : len);
       }
   }
}

static void dissect_http_user_agent(struct http_request_info *line_info)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "user-agent");
    if(!value)
        return;

    //GET_DEV_INFO(line_info->uaCPU, CPU_LEN, "cpu");
    char* tmp0 = strcasestr((const char *)value->ptr, "cpu");
    if(NULL != tmp0)
    {
        char* tmp1 = strstr(tmp0, ";");
        char* tmp2 = strstr(tmp0, ")");
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))
            memcpy(line_info->uaCPU, tmp0, (tmp2-tmp0<CPU_LEN ? tmp2-tmp0:CPU_LEN-1));
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)
            memcpy(line_info->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        else if(NULL == tmp1 && NULL == tmp2)
        {
            tmp1 = strstr(tmp0,"\r\n");
            if(NULL != tmp1)
                memcpy(line_info->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        }
    }

    //该数组元素为设备系统标识,最后一个元素必须是NULL, 如果新增系统标识,则直接在后面的NULL位置上依次添加
    //注意：此处如果有os标识，则os后面必须加一个空格
    const char *devFlags[] = {"linux", "windows ", "windows/", "iphone ", "android", "mac", NULL, NULL, NULL, NULL};
    int i = 0;
    for(i=0; devFlags[i]; i++)
    {
        //GET_DEV_INFO(line_info->uaOS, OS_LEN, devFlags[i]);
        char* tmp0 = strcasestr((const char *)value->ptr, devFlags[i]);
        if(tmp0)
        {
            char* tmp1 = strstr(tmp0, ";");
            char* tmp2 = strstr(tmp0, ")");
            if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2)){
                memcpy(line_info->uaOS, tmp0, (tmp2-tmp0<OS_LEN ? tmp2-tmp0:OS_LEN-1));
            }
            else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2){
                memcpy(line_info->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            else if(NULL == tmp1 && NULL == tmp2){
                tmp1 = strstr(tmp0,"\r\n");
                if(NULL != tmp1)
                    memcpy(line_info->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            if(line_info->uaOS[0])
                break;
        }
    }
}

static void get_http_host(struct http_request_info *line_info, struct http_session *session)
{
    //缓存host字段到session中
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "host");
    if (value)
    {
        size_t copy_len = value->len >= sizeof(session->host) ? sizeof(session->host) - 1 : value->len;
        memcpy(session->host, value->ptr, copy_len);
        session->host[copy_len] = 0;
        g_hash_table_remove(line_info->table, "host");

        //雅虎认证
        if(copy_len > 9 && strcmp(session->host + copy_len - 9, "yahoo.com") == 0){
            value = g_hash_table_lookup(line_info->table, "head_line");
            if(!memchr(value->ptr, '?', value->len))
                return;
            const uint8_t *login  = memstr(value->ptr, "login=", value->len);
            const uint8_t *passwd = memstr(value->ptr, "passwd=", value->len);
            if(login && passwd)
            {
                uint8_t *login_end  = memchr(login, '&', value->len);
                uint8_t *passwd_end = memchr(passwd, '&', value->len);
                if(login_end && passwd_end){ //FORMAT   login:passwd
                    strcpy(line_info->proxy_type, "Yahoo");
                    uint8_t off = DPI_MIN(login_end - login - 6, LOGIN_LEN - 1);
                    memcpy(line_info->proxy_login, login+6, off);
                    if(off < LOGIN_LEN - 1)
                        line_info->proxy_login[off++] = ':';
                    if(off + passwd_end - passwd - 7 < LOGIN_LEN - 1)
                        memcpy(line_info->proxy_login + off, passwd + 7, passwd_end - passwd - 7);
                }
            }
        }
    }
}


static void get_http_uri(struct http_request_info *line_info, struct http_session *session)
{
    if(session==NULL){
        return;
    }
    if(line_info->PROTOCOL_VAL_LEN(uri)==0){
        return;
    }

    size_t copy_len=line_info->PROTOCOL_VAL_LEN(uri);

    if(session->uri!=NULL){
        free(session->uri);
        session->uri=NULL;
    }

    session->uri=malloc(copy_len+1);
    if(session->uri==NULL){
        DPI_LOG(DPI_LOG_ERROR,"malloc failed!");
        return;
    }
    memcpy(session->uri, line_info->PROTOCOL_VAL_PTR(uri), copy_len);
    session->uri[copy_len] = 0;
}


/*!
 * 将字符串中非数字和字母的字符替换为指定的字符
 */
static char* str_instead(uint32_t len, char * str, char c)
{
    if (str != NULL) {
        for (uint32_t i = 0; i < len && str != NULL; i++) {
            if (!isalnum(*(str + i))) {
                *(str + i) = c;
            }
        }
    }
    return str;
}


static int
dissect_http(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
  char tmp_str[32] = { 0 };
  snprintf(tmp_str, 10, "%s", payload);
  // printf("length = %d ", payload_len);
  // printf("payload = %s\n", tmp_str);
    if(payload == NULL || payload_len < 3)
        return 0;

    int     ret=0;
    uint8_t chunked = 0;
    uint8_t is_http_request = 0;
    uint32_t uri_start;
    uint32_t http_content_len;
    uint32_t http_header_content_len;
    gpointer _value;
    struct http_session *session;
    struct header_value *value;
    struct http_request_info line_info;
    memset(&line_info, 0, sizeof(line_info));
    uint8_t is_alipay_flag = 0;
    uint8_t is_alipay_large_file_flag = 0;
    uint8_t *alipay_large_content = NULL;

    const char* Prefix_g = "/mmcrhead/"; // 标记群头像的前缀

    session = (struct http_session *)flow->app_session;

    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
        is_http_request = 0;
        session->direction[FLOW_DIR_DST2SRC] += 1;
    }
    else if (payload_len >= 10) {
        for(int i = 0; HTTP_METHOD[i].method; i++){
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)){
                uri_start = HTTP_METHOD[i].len;
                is_http_request = 1;
                break;
            }
        }
        if(!is_http_request)
              goto exit;
            // return PKT_DROP;
        session->direction[FLOW_DIR_SRC2DST] += 1;
    }
    else{
        goto exit;
        // return PKT_DROP;
    }

    //解析头部行，并找空行
    parse_http_line_info(payload, payload_len, &line_info);
    if (!line_info.table) {
        DPI_LOG(DPI_LOG_DEBUG, "hash table is null");
        goto exit;
        // return PKT_OK;
    }

    //查看头部行的transfer-encoding是否是chunked
    _value = g_hash_table_lookup(line_info.table, "transfer-encoding");
    if (_value) {
        char transfer[64] = {0};
        value = (struct header_value *)_value;
        memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
        if (strcasestr(transfer, "chunked")) {
            chunked = 1;
        }
    }

    if(is_http_request)//只属于http请求报文的字段
    {
        get_http_host(&line_info, session);
        dissect_http_user_agent(&line_info);
        dissect_http_authorization(&line_info);
    }
    else               //只属于http响应报文的字段
    {
        dissect_http_authenticate(&line_info);
    }

    /* =============== add by liugh =============== */
    _value = g_hash_table_lookup(line_info.table, "content-type");
    if (_value) {
        char transfer[64] = {0};
        value = (struct header_value *)_value;
        memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
        if (strcasestr(transfer, "application/ocsp-request")) {
            // dissect_ocsp_request(flow, payload, payload_len);
        }else if (strcasestr(transfer, "application/ocsp-response")){
            // dissect_ocsp_response(flow, payload, payload_len);
        }

        if( 0==is_http_request && 0==_http_content_type_filter(transfer)){  /* 只对S2C生效 */
            // g_hash_table_destroy(line_info.table);
            goto exit;
            // return PKT_DROP;
        }
    }else if(0==is_http_request && 1==g_config.http.drop_no_content_type){  /* no content-type丢弃与否也只对S2C生效 */
        goto exit;
        // g_hash_table_destroy(line_info.table);
        // return PKT_DROP;
    }

#if 0
    /*查看头部行中的content-length，如果有，则按照该值进行重组；
        *    如果没有 ：1，transfer-encoding是chunked ，设置content_len为HTTP_CONTENT_LEN_MAX
        *             2，transfer-encoding不是chunked ，设置content_len为0
    */
    _value = g_hash_table_lookup(line_info.table, "content-length");
    if (!_value) {
        http_header_content_len = 0;
        if (chunked)
            http_header_content_len = HTTP_CONTENT_LEN_MAX;
    } else {
        value = (struct header_value *)_value;
        char header_content_len[32] = {0};
        memcpy(header_content_len, value->ptr, value->len > 31 ? 31 : value->len);
        http_header_content_len = atoi(header_content_len);
    }
#endif

    //获取实际的内容长度
    if (line_info.empty_line_position > 0 && line_info.empty_line_position < payload_len - 2) {
        http_content_len = payload_len - line_info.empty_line_position - 2;
    } else
        http_content_len = 0;

    _value = g_hash_table_lookup(line_info.table, "head_line");
    if (!_value) {
        // g_hash_table_destroy(line_info.table);
        DPI_LOG(DPI_LOG_DEBUG, "no head_line in hash table");
        goto exit;
        // return PKT_OK;
    }

    value = (struct header_value *)_value;
    if (is_http_request && value->len > (9 + uri_start)) {
        line_info.PROTOCOL_VAL_PTR(method) = value->ptr;
        line_info.PROTOCOL_VAL_LEN(method) = uri_start - 1;

        line_info.PROTOCOL_VAL_PTR(uri) = &value->ptr[uri_start];
        line_info.PROTOCOL_VAL_LEN(uri) = value->len - (uri_start + 9);

        line_info.PROTOCOL_VAL_PTR(version) = line_info.PROTOCOL_VAL_PTR(uri) + line_info.PROTOCOL_VAL_LEN(uri) + 1;
        line_info.PROTOCOL_VAL_LEN(version) = 8;

        if(memcmp(line_info.PROTOCOL_VAL_PTR(version), "HTTP/1.", 7) && memcmp(line_info.PROTOCOL_VAL_PTR(version), "http/1.", 7)){
             session->direction[FLOW_DIR_SRC2DST] -= 1;
             DPI_LOG(DPI_LOG_DEBUG, "not http expect header");
             goto exit;
        }

        get_http_uri(&line_info, session); /* 获取http请求uri，回填到响应tbl中 */

        // wx group head
        if (strlen(Prefix_g) < line_info.PROTOCOL_VAL_LEN(uri) && line_info.PROTOCOL_VAL_LEN(uri) < 255) {
            if (0 == memcmp(line_info.PROTOCOL_VAL_PTR(uri), Prefix_g, strlen(Prefix_g))) {
                memcpy(session->wx_group_head_last_status.pic_uri, line_info.PROTOCOL_VAL_PTR(uri), line_info.PROTOCOL_VAL_LEN(uri));
                char * p_last_slash = strrchr(session->wx_group_head_last_status.pic_uri, '/');
                if(p_last_slash != session->wx_group_head_last_status.pic_uri && p_last_slash != NULL) {
                    *p_last_slash = '\0';
                }
                str_instead(strlen(session->wx_group_head_last_status.pic_uri), session->wx_group_head_last_status.pic_uri, '_');
                session->is_pic = 1;
            }
            else {
                session->is_pic = 0;
            }
        }


    } else if (0 == is_http_request && value->len > 12) {
        line_info.PROTOCOL_VAL_PTR(version) = value->ptr;
        line_info.PROTOCOL_VAL_LEN(version) = strlen("HTTP/1.1");

        line_info.PROTOCOL_VAL_PTR(code) = &value->ptr[strlen("HTTP/1.1 ")];
        line_info.PROTOCOL_VAL_LEN(code) = 3;

        line_info.PROTOCOL_VAL_PTR(response_code) = &value->ptr[strlen("HTTP/1.1 200 ")];
        line_info.PROTOCOL_VAL_LEN(response_code) = value->len - strlen("HTTP/1.1 200 ");

        //HTTP response status
        // if(g_config.http_status_whitelist[0] != '*'){
        //     char status[4];
        //     memcpy(status, line_info.PROTOCOL_VAL_PTR(code), 3);
        //     status[3] = 0;
        //     if(!strstr(g_config.http_status_whitelist, status))
        //         goto exit;
        // }
    }
    g_hash_table_remove(line_info.table, "head_line");

    ret = write_http_weixin_pyq(flow, session->direction[0], &line_info);
    if (ret == PKT_OK) goto exit;


    /* 规则检查，当配置规则时，即规则数大于等于0(表示配置了规则)，
     * 规则有个action，0-丢弃， 1-保留；
     * 命中规则，检查action是丢弃还是保留;
     * 所有规则都没有命中保留数据;
     *     1)当配置了规则想要达到命中的保留，需要一个能够匹配所有的默认规则action=0；
     *     2)当配置了规则想要达到命中的丢弃，只需配置上需要丢弃的规则即可；
     */
    // ret=http_fields_rules_check(&line_info,session);
    // if(ret>=0){
    //     if(line_info.action==0){
    //         goto exit;
    //     }
    // }
    if(is_http_request && !strncasecmp((const char*)line_info.PROTOCOL_VAL_PTR(method), "put ", 4) && !strncasecmp((const char*)line_info.PROTOCOL_VAL_PTR(version), "http/1.", 7)) {
        _value = g_hash_table_lookup(line_info.table, "x-arup-appkey");
        if (_value) {
            value = (struct header_value *)_value;
            if(value->ptr && !memcmp(value->ptr, "aliwallet", value->len))
                is_alipay_flag = 1;
        }
    }

    if (line_info.PROTOCOL_VAL_LEN(method) > 0 || line_info.PROTOCOL_VAL_LEN(code) > 0) {
        uint8_t chunked_content[REASSEMBLE_LEN_MAX];
        uint32_t chunked_content_len = REASSEMBLE_LEN_MAX;
        HttpSubAttr sub_attr;

        if (line_info.empty_line_position > 0 && line_info.empty_line_position < payload_len - 2) {
            int http_content_len = payload_len - line_info.empty_line_position - 2;
            if ((unsigned)http_content_len > g_config.tbl_log_content_256k - 8 && !is_alipay_flag)
                http_content_len = g_config.tbl_log_content_256k - 8;
            else if(is_alipay_flag && http_content_len > REASSEMBLE_LEN_MAX){
                 alipay_large_content = (uint8_t*)dpi_malloc(http_content_len);
                 if(alipay_large_content) {
                     chunked_content_len = http_content_len;
                     is_alipay_large_file_flag = 1;
                 }
            }

            line_info.PROTOCOL_VAL_LEN(content) = http_content_len;
            line_info.PROTOCOL_VAL_PTR(content) = &payload[line_info.empty_line_position + 2];
            //内容是chunked的报文，做chunked解析
            if (chunked) {
                if(is_alipay_flag) {
                    alipay_http_parser_chunked_content(line_info.PROTOCOL_VAL_PTR(content), line_info.PROTOCOL_VAL_LEN(content),
                            is_alipay_large_file_flag?alipay_large_content:chunked_content, &chunked_content_len, &line_info);
                } else {
                    http_parser_chunked_content(line_info.PROTOCOL_VAL_PTR(content), line_info.PROTOCOL_VAL_LEN(content),
                            chunked_content, &chunked_content_len);
                }
                line_info.PROTOCOL_VAL_PTR(content) = is_alipay_large_file_flag?alipay_large_content:chunked_content;
                line_info.PROTOCOL_VAL_LEN(content) = chunked_content_len;
            }
        }

        //创造子flow_id关联上下行
        if(session->direction[FLOW_DIR_DST2SRC] >= session->direction[FLOW_DIR_SRC2DST])
            session->direction[FLOW_DIR_SRC2DST] = session->direction[FLOW_DIR_DST2SRC];
        else
            session->direction[FLOW_DIR_DST2SRC] = session->direction[FLOW_DIR_SRC2DST] - 1;
        // flow->sub_flow_id = session->direction[FLOW_DIR_SRC2DST];

        // wx_group_head
        if (session->is_pic == 1 && is_http_request == 0) {
            if (dpi_strstr(line_info.content_val_ptr, line_info.content_val_len, "JFIF", 4) > 0
                && get_uint16_ntohs(line_info.content_val_ptr, 0) == 0xffd8) { // 对jpg的格式检测, 只输出jpg格式图片
                rte_atomic32_inc(&g_config.reassemble_info.content_statics[EM_CONTENT_WXGH].total_number);
              sub_attr.send_content = 1;
                // if (http_header_content_len == line_info.content_val_len) {

                // }
            }
        }

        sub_attr.is_request = is_http_request;
        write_wx_http_post_tbl(flow, flow->direction, line_info.PROTOCOL_VAL_PTR(content), line_info.PROTOCOL_VAL_LEN(content),
            &line_info);  //cgibing格式下的
        http_sub(flow, flow->direction, &line_info, &sub_attr);
        //  const uint8_t *payload, const uint32_t payload_len
        http_douyin(flow, flow->direction, payload, payload_len, &line_info);

        if(is_alipay_flag){
            http_alipay(flow, flow->direction, payload, payload_len, &line_info);
            if(is_alipay_large_file_flag){
                dpi_free(alipay_large_content);
                alipay_large_content = NULL;
            }
        }

        // direction: 0 -- > C2S
        // direction: 1 -- > S2C
        // !is_http_request 的 与 系统内部的 C2S 枚举值 一致
        if (session->has_sub != 1 && g_config.protocol_tbl_http) {
            write_http_log(flow, !is_http_request, &line_info);
        }
        // if(1==g_config.http.http_switch_store_file){
        //     write_http_data_to_file(flow,payload,payload_len,!is_http_request, &line_info);
        // }else{
        //     write_http_log(flow, !is_http_request, &line_info);
        // }
    }
exit:
    if(!is_http_request){
        if(session->uri!=NULL){
            free(session->uri);
            session->uri=NULL;
        }
    }
     if (line_info.table){
        g_hash_table_destroy(line_info.table);
     }
    return PKT_OK;
}



#define HTTP_NEED_MORE  0
#define HTTP_ERROR     -1
static int has_chunked(const char*p, int l)
{
    const char *find_str[] = {"\x0D\x0A\x0D\x0A", "chunked", "Transfer-Encoding", "HTTP/1.", NULL};
    const char *find       = p + l;
    int         i;

    for(i = 0; NULL != find_str[i]; i++)
    {
        find = memmem(p, find -p, find_str[i],  strlen(find_str[i]));
        if(NULL == find)
        {
            return 0;
        }
    }
    return 1;
}

static int64_t get_chunkedlen(const char *http, int http_len)
{
    int64_t      offset      = 0;
    int64_t      chunk_size  = 0;
    int64_t      chunk_len   = 0;
    const char   *str_split   = "\x0D\x0A\x0D\x0A";
    const char   *p           = NULL;

    if(0 == has_chunked(http, http_len))
    {
        return 0;  // no chunk
    }

    // skip http head
    p = memmem(http, http_len, str_split,  strlen(str_split));
    if(NULL == p)
    {
        return 0;
    }

    offset    = p - http + strlen(str_split);
    http     += offset;
    http_len -= offset;
    offset    = 0;

    while (offset < http_len)
    {
        chunk_len = find_packet_line_end((const uint8_t*)http + offset, (uint32_t)http_len - offset);
        if (chunk_len <= 0)
        {
            break;
        }

        chunk_size = strtol(http + offset, NULL, 16);
        if(chunk_size > (1024*1024*10) || chunk_size < 0) // 限定每个chunk_size
        {
            return -1;  // no chunk
        }

        offset    += (chunk_len + 2 + chunk_size + 2);  // point next chunked
        if (offset  > http_len)
        {
            break;
        }
        else
        if (0 == chunk_size && offset == http_len)
        {
            return http_len;
        }
    }
    return HTTP_NEED_MORE;
}

// 返回 Content-Length 数值类型
static int64_t get_contentlen(const char *p, int l)
{
#define CONTENT_STR_LEN 16
    const char *str_start   = "\r\nContent-Length";
    const char *str_end     = "\r\n";

    if(l < CONTENT_STR_LEN)
        return 0;

    const char *find_start = memmem(p, l, str_start,  CONTENT_STR_LEN);
    if(find_start)
        find_start += CONTENT_STR_LEN;
    else
        return 0;

    const char *find_end = memmem(find_start, l-(find_start-p), str_end,  strlen(str_end));
    if(find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
        return 0;

    int  i = 0;
    char buff[16];
    while(find_start < find_end)
    {
        if(isdigit(*find_start))
            buff[i++] =  *find_start;
        else if(*find_start != ':' && *find_start != ' ')
            return 0;
        find_start++;
    }
    buff[i] = 0;

    return atol(buff);
}

/*
*  0: not http
*  1: is  http
*/
static int is_http(const char *p, int l)
{
    int ST     ;
    int offset ;
    int i      ;
    int find   ;

    enum
    {
        HTTP_ST_DEFAULT,
        HTTP_ST_REQUEST,
        HTTP_ST_REQUEST_METHOD,
        HTTP_ST_REQUEST_URI,
        HTTP_ST_REQUEST_VERSION,
        HTTP_ST_RESPONSE,
        HTTP_ST_RESPONSE_VERSION,
        HTTP_ST_RESPONSE_NUM,
        HTTP_ST_END,
    };

    if(l <= 0)
    {
        return 0;
    }

    ST     = HTTP_ST_DEFAULT;
    offset = 0;
    while(offset < l)
    {
        switch(ST)
        {
            case HTTP_ST_DEFAULT:
                if(0 == memcmp(p, "HTTP/1.", 7))
                {
                    ST = HTTP_ST_RESPONSE;
                }
                else
                {
                    ST = HTTP_ST_REQUEST;
                }
                break;

            case HTTP_ST_REQUEST:
                ST = HTTP_ST_REQUEST_METHOD;
                break;

            case HTTP_ST_REQUEST_METHOD:
                find = 0;
                if(l - offset > 10){
                    for(i = 0; HTTP_METHOD[i].method; i++)
                    {
                        if(0 == strncasecmp(p + offset, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
                        {
                            offset += HTTP_METHOD[i].len;
                            ST = HTTP_ST_END;
                            find = 1;
                            break;
                        }
                    }
                }
                if(0 == find)
                {
                    return 0;
                }
                break;

            case HTTP_ST_REQUEST_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    ST = HTTP_ST_END;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE:
                ST = HTTP_ST_RESPONSE_VERSION;
                break;

            case HTTP_ST_RESPONSE_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    offset ++;
                    offset ++;
                    ST = HTTP_ST_RESPONSE_NUM;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE_NUM:
                for(i = 0; i < 3; i++)
                {
                    if(0 == isdigit(p[offset + i]))
                    {
                        return 0;
                    }
                }
                ST = HTTP_ST_END;
                break;

            case HTTP_ST_END:
                return 1;
                break;
        }

    }
    return 0;
}


// 返回 HTTP head 长度范围
// 负数: 没有找到
static int64_t get_header_len(const char *p, int l)
{
    if(0 == is_http(p,l))
    {
        return HTTP_ERROR;
    }

    const char *find = memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
    if(NULL == find)
    {
        return HTTP_NEED_MORE;
    }

    return  find - p + 4;
}


// 返回负数, 代表不存在一个完整HTTP 长度
// 返回正数, 代表  存在一个完整HTTP 长度
static int64_t get_http_len(const char *p, int len)
{
    const uint8_t *pstart   = NULL;
    const uint8_t *pend     = NULL;
    const uint8_t *data     = NULL;
    int64_t        remain   = 0;
    int64_t        success  = 0;
    int64_t        hl       = 0;  // header  len
    int64_t        content_len = 0;  // content len
    int64_t        chunck_len  = 0;  // chunck len

    if(len < 0)
    {
        return -1;
    }

    hl = get_header_len(p, len);
    if(HTTP_NEED_MORE == hl)
    {
        return HTTP_NEED_MORE;
    }
    else if(HTTP_ERROR == hl)
    {
        return HTTP_ERROR;
    }

    content_len = get_contentlen(p, hl);
    if(content_len > 0)
    {
        return hl + content_len;
    }

    chunck_len = get_chunkedlen(p, len);
    if(chunck_len > 0)
    {
        return hl + chunck_len;
    }
    else if(chunck_len == HTTP_NEED_MORE)
    {
        return HTTP_NEED_MORE;
    }

    //【非标】如果既没有 content-leng， 也没有transfer-chunk， 但是存在后续数据。 那就继续 NEED_MORE
    if(0 == content_len && 0 == chunck_len && len > hl)
    {
	    return HTTP_NEED_MORE;
    }

    return hl;
}


// 已缓存的数据直接输出
static int http_tcp_miss(void *user, int C2S, uint32_t miss)
{
    struct   flow_info    *f     = NULL;
    struct   http_session *s     = NULL;
    struct   http_cache   *c     = NULL;
    int      paddinglen  = 0;
    int      safelen     = 0;
    int      miss_len    = (int)miss;

    //if(miss)                                                      //TCP_RSM_DEBUG
    //{                                                             //TCP_RSM_DEBUG
    //    char buff[1024];                                          //TCP_RSM_DEBUG
    //    snprintf(buff, sizeof(buff), "%d missing_data", miss);    //TCP_RSM_DEBUG
    //    write(fd_tcp, buff, strlen(buff)+1);                      //TCP_RSM_DEBUG
    //    fsync(fd_tcp);                                            //TCP_RSM_DEBUG
    //}                                                             //TCP_RSM_DEBUG

    f = (struct flow_info*)user;
    if(f->app_session)
    {
        s = (struct http_session *)f->app_session;
        c = s->cache + C2S;

        // 如果允许 TCP PADDING
        if(c->cache && miss_len && g_config.http.tcp_padding_len > 0 && miss_len <= g_config.http.tcp_padding_len && c->cache_size - c->cache_hold > miss_len)
        {
            paddinglen = strlen(g_config.http.tcp_padding_str);
            while(miss_len > 0)
            {
                safelen = (paddinglen < miss_len) ? paddinglen : miss_len;
                memcpy(c->cache + c->cache_hold, g_config.http.tcp_padding_str, safelen);
                c->cache_hold += safelen;
                miss_len      -= safelen;
            }
        }
        else
        if(c->cache)
        {
            dissect_http(f, (const uint8_t*)c->cache, (uint32_t)c->cache_hold);
            free(c->cache);
            c->cache      = NULL;
            c->cache_hold = 0;
        }
    }
    return 0;
}

// HTTP 专业切割机(解决 pipeline)
// 切出一个完整的 HTTP给解析接口(HTTP_HEAD + content)
static int dissect_http_strip(void *user, int C2S, const uint8_t *p, uint16_t  pl)
{
    char                 t     = 0;
    //const char          *pkt   = p;	//记录当前报文
    //int                  pkt_len=pl;    //记录当前报文
    int64_t              hl    = 0;
    int64_t              offset= 0;
    int64_t              l     = pl;
    struct flow_info    *f     = (struct flow_info*)user;
    struct http_session *s     = NULL;
    struct http_cache   *c     = NULL;

    //write(fd_tcp, p, l); //TCP_RSM_DEBUG
    //fsync(fd_tcp);       //TCP_RSM_DEBUG

    if (NULL == f->app_session)
    {
        f->app_session = dpi_malloc(sizeof(struct http_session));
        if (NULL == f->app_session)
        {
            goto HTTP_NEED_MORE_PKT;
        }
        memset(f->app_session, 0, sizeof(struct http_session));
    }
    s = (struct http_session *)f->app_session;
    c = s->cache + C2S;

    // 是否开启缓存
    if(c->cache)
    {

        if(1 == is_http((const char*)p,l))
        {
            // 说明这是 HEAD 请求的响应
            dissect_http(f, (const uint8_t*)c->cache, (uint32_t)c->cache_hold);
            c->cache_hold = 0; //reset
        }

        if(l >= (c->cache_size - c->cache_hold))//如果cache最后一字节被填充即l=c->cache_size - c->cache_hold,后续末尾填充0发生越界
        {
            // 缓存撑爆前,  解析数据, 释放
            dissect_http(f, (const uint8_t*)c->cache, (uint32_t)c->cache_hold);
            goto HTTP_DROP;
        }

        //正常 拼装
        memcpy(c->cache + c->cache_hold, p, l);
        c->cache_hold  += l;
        c->cache[c->cache_hold] = '\0'; //末尾填充0
        p = (const uint8_t*)c->cache;
        l = c->cache_hold;
    }

    // 专业切割机
    // 	必须存在cache 才能解析
    // 	问题场景： HTTP 200 OK 相应报文中没有【content-length】也没有【transfer-type=chunck】
    // 	但真实数据 确实存在 HTTP-content
    // 	问题来了： HTTP 切割机 就会 把这个 HTTP 响应头 直接解析， 剩下的数据都是无头的垃圾数据。
    //
    // 	手段： 采用状态来判定
    // 	判定思想：当只有1个HTTP报文到达时， 在没有 【content-length】也没有【transfer-type=chunck】时， 不动作。 需参考后续报文的特征
    // 	判定思想：1. 当后续报文还是一个HTTP时， 先把CACHE里的上次的报文直接解析
    // 	判定思想：2. 当后续报文不是一个HTTP时，直接拼接。 暂不解析。 直到 会话超时。全部解析。
    while(offset < l && c->cache) //第一次解析，不会进入
    {

        hl = get_http_len((const char*)p + offset, l - offset);
        if(hl > 0 && l - offset >= hl)
        {
            dissect_http(f, (const uint8_t*)p + offset, (uint32_t)hl);
            offset += hl;
        }
        else if(hl == HTTP_ERROR)
        {
            goto HTTP_DROP;
        }
        else if(hl == HTTP_NEED_MORE)
        {
            break;
        }
        else if(hl > l - offset)
        {
            break;
        }
    }

    // 有没有剩料?
    if(offset < l)
    {
        if(NULL != c->cache && offset >0)  //已开启缓存, 直接将剩料挪到前面
        {
            memmove(c->cache, c->cache + offset, c->cache_hold - offset);
            c->cache_hold  -= offset;
        }
	//第一次都会被缓存在此
        else if(NULL == c->cache)          //未开启缓存, 创建缓存, 把剩料放在前面
        {
            c->cache_size = g_config.http.http_strip_cache_size;
            c->cache_hold = l - offset;
            c->cache      = dpi_malloc(c->cache_size);
            memcpy(c->cache, p + offset, l - offset);
        }
        goto HTTP_NEED_MORE_PKT;
    }
    else
    {
        if(NULL != c->cache)
        {
            free(c->cache);
            c->cache      = NULL;
            c->cache_hold = 0;
        }
        goto HTTP_NEED_MORE_PKT;
    }


// HTTP  太长, 只解析被缓存的部分.
HTTP_DROP:
    if(NULL != c->cache)
    {
        free(c->cache);
        c->cache = NULL;
        c->cache_hold = 0;
    }

// HTTP 解析,需要更多报文
HTTP_NEED_MORE_PKT:
    return 0;
}

static int flow_http_finish(struct flow_info *flow)
{
    struct tcp_rsm *rsm = flow->rsm;
    if(rsm){
        struct tcp_status status;
        tcp_rsm_status(rsm, 0, &status);
        uint32_t num = status.fail;
        tcp_rsm_status(rsm, 1, &status);
        num += status.fail;
        // if(num)
            // rte_atomic64_add(&rsm_fail_get, num);

        //tcp_rsm_dump(rsm);     //TCP_RSM_DEBUG
        tcp_rsm_free(rsm);
        flow->rsm = NULL;
    }

    if(flow->app_session)
    {
        struct http_session *session = (struct http_session *)flow->app_session;

        if(session->uri)
            free(session->uri);
        if(session->cache[0].cache)
            free(session->cache[0].cache);
        if(session->cache[1].cache)
            free(session->cache[1].cache);

        free(flow->app_session);
        flow->app_session = NULL;
    }
    return 0;
}

// HTTP 专业TCP重组(去重复, 去重传, 防抖动, 防踩头, 防踩尾)
static int dissect_http_rsm(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    //printf("%d, %u %u\n", direction, ntohs(flow->tuple.inner.port_src), ntohs(flow->tuple.inner.port_dst));

    if(DISSECT_PKT_ORIGINAL != flag)
    {
        return 0;
    }

    if(TCP_RSM_DISABLE == flow->rsm)
    {
        return 0;
    }

    if(flow->pkt_info == NULL || flow->pkt_info->tcph == NULL){
        DPI_LOG(DPI_LOG_ERROR, "HTTP flow pkt tcph = NULL");
        return 0;
    }

#if 0
        uint32_t l = DPI_MIN(payload_len, 299);
        char t[300];
        memcpy(t, payload, l);
        t[l] = 0;
        printf("\n----------%s-----------------------------\n", t);
#endif
    uint8_t flg = 0;
    flg |= flow->pkt_info->tcph->fin << 0;
    flg |= flow->pkt_info->tcph->syn << 1;
    flg |= flow->pkt_info->tcph->rst << 2;
    flg |= flow->pkt_info->tcph->psh << 3;

    //printf("flag: %u, seq: %u, ack: %u\n", flg, seq, ntohl(flow->pkt_info_info->tcph->ack_seq));
    _get_http_client_server_port(flow, payload, payload_len);

    if (!g_config.http_rsm) {
        DPI_LOG(DPI_LOG_DEBUG, "http dissect single packet");
        if (NULL == flow->app_session) {
            flow->app_session = dpi_malloc(sizeof(struct http_session));
            if (NULL == flow->app_session) {
                DPI_LOG(DPI_LOG_ERROR, "http creat session not enough memory");
                return 0;
            }
            memset(flow->app_session, 0, sizeof(struct http_session));
        }
        dissect_http(flow, payload, payload_len);
        return 0;
    }
    if (payload_len && flow->rsm && tcp_rsm_port_reuse(flow->rsm, direction, ntohl(flow->pkt_info->tcph->ack_seq))) {
        tcp_rsm_free(flow->rsm);
        flow->rsm = NULL;
    }

    if (!flow->rsm) {
        //fd_tcp = creat("/tmp/tbls/tcp_rsm.data", 0644); //TCP_RSM_DEBUG
        flow->rsm = tcp_rsm_init(dissect_http_strip, http_tcp_miss, flow, g_config.http.tcp_out_of_order);
    }

    if (flow->rsm) {
        int rc = tcp_rsm_add(flow->rsm, direction, seq, ntohl(flow->pkt_info->tcph->ack_seq), flg, payload, payload_len);
        if (rc < 0) {
            tcp_rsm_free(flow->rsm);
            flow->rsm = TCP_RSM_DISABLE;
        }
    }

    return 0;
}

static void identify_http_tcp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP] == 0)
        return;

    if (payload_len < 9)
        return;
    uint16_t sport = ntohs(flow->tuple.inner.port_src);
    uint16_t dport = ntohs(flow->tuple.inner.port_dst);
    if (sport == 80 || sport == 8080 || sport == 443 || sport == 16285|| dport == 80 || dport == 8080 || dport == 443||  dport == 16285) {
        // is response ?
        if (strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
            // flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
            flow->real_protocol_id = PROTOCOL_HTTP;
            return;
        }

        // is request ?
        for (int i = 0; HTTP_METHOD[i].method; i++) {
            if (0 == strncasecmp((const char *)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)) {
                // flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
                flow->real_protocol_id = PROTOCOL_HTTP;
                break;
            }
        }
    }
    return;
}


void init_http_dissector(void)
{
    write_proto_field_tab(http_field_array,EM_HTTP_MAX,"http_n");
    write_proto_field_tab(http_field_array,EM_HTTP_MAX,"http_qqacc");
    write_proto_field_tab(http_field_array,EM_HTTP_MAX,"http_wxph");
    write_proto_field_tab(http_field_array,EM_HTTP_MAX,"http_wx_msg");


    port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_HTTP);
    port_add_proto_head(IPPROTO_TCP, 8000, PROTOCOL_HTTP);
    port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_HTTP);
    port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_HTTP);
    port_add_proto_head(IPPROTO_TCP, 14000, PROTOCOL_HTTP);

    tcp_detection_array[PROTOCOL_HTTP].proto = PROTOCOL_HTTP;
    tcp_detection_array[PROTOCOL_HTTP].identify_func = identify_http_tcp;
    tcp_detection_array[PROTOCOL_HTTP].dissect_func  = dissect_http_rsm;
    tcp_detection_array[PROTOCOL_HTTP].flow_timeout  = flow_http_finish;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_HTTP].excluded_protocol_bitmask, PROTOCOL_HTTP);
    return;
}

void  register_weixin_http_post_field(){
  ya_record_register_proto(NULL,"wx_http_post","wx_http_post");
  pschema_t * schema = dpi_pschema_get_proto("wx_http_post");
  pschema_register_field(schema, "URI",YA_FT_STRING,"desc");
  pschema_register_field(schema, "Host",YA_FT_STRING,"desc");
  pschema_register_field(schema, "User-Agent",YA_FT_STRING,"desc");
  pschema_register_field(schema, "wxnum",YA_FT_UINT32,"desc");
}
static void init_http_protorecord(void){
  register_weixin_http_post_field();
    write_proto_record_field_tab((struct dpi_field_table_t *)http_field_array,EM_HTTP_MAX,"http");

}

static __attribute((constructor)) void     before_init_http(void){
    dpi_proto_register("http", init_http_protorecord);
}
// static __attribute((constructor)) void before_init_http(void){
//     register_tbl_array(TBL_LOG_HTTP, 1, "http_n", init_http_dissector);
// }

/*ADD_S by yangna 2020-10-12 */
// void fini_http_dissector(void)
// {
//     if (groupRegularComFlg == 1)
//     {
//         regfree(&regQQGroupFile);
//     }
//     if (singleRegularComFlg == 1)
//     {
//         regfree(&regQQSingleFile);
//     }


// }
/*ADD_E by yangna 2020-10-12 */
