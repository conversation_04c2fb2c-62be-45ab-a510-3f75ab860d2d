// WXA UDP 9711 报文过滤算法:
//      1. 允许前64个报文 通过.
//      2. 序号大于64, 每16个允许通过1个.
//      3. 支持规则优先级 自动浮动, 加快过滤.

// 支持过滤 报文类型:
//      1. mac/ip4/udp/9711
//      2. mac/ip6/udp/9711
//      3. mac/ip4/GTP/ip4/udp/9711
//      4. mac/ip4/GTP/ip6/udp/9711
//      5. mac/ip6/GTP/ip4/udp/9711
//      6. mac/ip6/GTP/ip6/udp/9711

// 测试效果:
// X722 板卡 4核心收包, 4核心解析, 极限测试(模拟实际环境)
// 3端口: 纯净 UDP WXA 9711 报文 11.4 Gbit/s, 9.26 Mpps
// 1端口: 杂质数据   非9711 报文  2.4 Gbit/s, 1.95 Mpps

// DPI-VTYSH# show  traffic  speed
// 9 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 8 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 7 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 6 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 5 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 4 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 3 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 2 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps
// 1 seconds ago, speed is 13.53 Gb/s, 11.27 Mpps, (miss, error) pkts (0, 0) percent is (0.00%, 0.00%), detect speed is 3.06 Gb/s, 2.55 Mpps

// CPU 资源占有情况
// top - 17:44:58 up 4 days,  1:05,  1 user,  load average: 7.59, 7.61, 7.06
// Tasks: 424 total,  21 running, 403 sleeping,   0 stopped,   0 zombie
// %Cpu0  : 41.4 us, 58.6 sy,  0.0 ni,  0.0 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
// %Cpu1  :100.0 us,  0.0 sy,  0.0 ni,  0.0 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
// %Cpu2  :100.0 us,  0.0 sy,  0.0 ni,  0.0 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
// %Cpu3  :100.0 us,  0.0 sy,  0.0 ni,  0.0 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
// %Cpu4  :100.0 us,  0.0 sy,  0.0 ni,  0.0 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
// %Cpu5  : 60.5 us, 26.6 sy,  0.0 ni,  7.0 id,  0.0 wa,  6.0 hi,  0.0 si,  0.0 st
// %Cpu6  : 56.1 us, 28.6 sy,  0.0 ni,  8.8 id,  0.0 wa,  6.5 hi,  0.0 si,  0.0 st
// %Cpu7  : 55.4 us, 31.0 sy,  0.0 ni,  7.9 id,  0.0 wa,  5.6 hi,  0.0 si,  0.0 st
// %Cpu8  : 59.7 us, 27.3 sy,  0.0 ni,  7.3 id,  0.0 wa,  5.7 hi,  0.0 si,  0.0 st
// %Cpu9  :  0.0 us,  0.3 sy,  0.0 ni, 99.7 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st

// 跨 NUMA 访问 测试
// numa1 访问 numa0  82599 双端口
// 测试效果
// 两个端口均为 WXA UDP 97 报文格式:  最大极限收包+解析能力, 9.21Mpps
// 两个端口均为 其他       报文格式:  最大极限收包+解析能力, 3.49Mpps
// 两个端口 97报文与  非97 报文格式:  最大极限收包+解析能力, 5.12Mpps


//////////// ip4 /////////////////////
uint8_t MAC_IP4_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, // MAC TYPE (ip4)
    0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,             // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // PAYLOAD NO SEQ
};
uint8_t MAC_IP4_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, // MAC TYPE
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,             // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // PAYLOAD NO SEQ
};


/////////// ip6 ///////////////////
uint8_t MAC_IP6_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0xdd,             // MAC
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00,                                                 // ip6 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ip6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                 // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                         // PAYLOAD NO SEQ
};
uint8_t MAC_IP6_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,              // MAC TYPE (ip6)
    0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,                                                  // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // IP6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                  // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                          // PAYLOAD NO SEQ
};


//////////// ipv6/GTP/ipv6 ////////////////////////
uint8_t MAC_IP6_UDP_GTP_IP6_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0xdd,            // MAC TYPE (ip6 限定)
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};
uint8_t MAC_IP6_UDP_GTP_IP6_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,            // MAC TYPE (ip6 限定)
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};

//////////// ipv6/GTP/ipv4 ////////////////////////
uint8_t MAC_IP6_UDP_GTP_IP4_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0xdd,            // MAC TYPE (ip6 限定)
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,                        // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};
uint8_t MAC_IP6_UDP_GTP_IP4_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,            // MAC TYPE (ip6 限定)
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,                        // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};

//////////// ipv4/GTP/ipv4 ////////////////////////
uint8_t MAC_IP4_UDP_GTP_IP4_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, // MAC TYPE (ip4)
    0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,             // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // GPRS Tunneling Protocol
    0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,             // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // PAYLOAD NO SEQ
};
uint8_t MAC_IP4_UDP_GTP_IP4_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, // MAC TYPE (ip4)
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,             // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // GPRS Tunneling Protocol
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,             // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // IP4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                     // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,             // PAYLOAD NO SEQ
};


//////////// ipv4/GTP/ipv6 ////////////////////////
uint8_t MAC_IP4_UDP_GTP_IP6_UDP_97_spec[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00,            // MAC TYPE (ip4)
    0x45, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,                        // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x97, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};
uint8_t MAC_IP4_UDP_GTP_IP6_UDP_97_mask[]={
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,            // MAC TYPE (ip4)
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,                        // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // ip4 hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // GPRS Tunneling Protocol
    0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,                                                // IP6 hdr (11 udp)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// Ip6 hdr SRC
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// IP6 hdr DST
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                                                // UDP hdr
    0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,                        // PAYLOAD NO SEQ
};

__thread
struct
{
    const uint8_t *spec;
    const uint8_t *mask;
    int            len;
    uint64_t       hit;
} match_list[] = {
    {
        .spec = MAC_IP6_UDP_GTP_IP6_UDP_97_spec,
        .mask = MAC_IP6_UDP_GTP_IP6_UDP_97_mask,
        .len  = sizeof(MAC_IP6_UDP_GTP_IP6_UDP_97_spec),
    },
    {
        .spec = MAC_IP6_UDP_GTP_IP4_UDP_97_spec,
        .mask = MAC_IP6_UDP_GTP_IP4_UDP_97_mask,
        .len  = sizeof(MAC_IP6_UDP_GTP_IP4_UDP_97_spec),
    },
    {
        .spec = MAC_IP4_UDP_GTP_IP6_UDP_97_spec,
        .mask = MAC_IP4_UDP_GTP_IP6_UDP_97_mask,
        .len  = sizeof(MAC_IP4_UDP_GTP_IP6_UDP_97_spec),
    },
    {
        .spec = MAC_IP4_UDP_GTP_IP4_UDP_97_spec,
        .mask = MAC_IP4_UDP_GTP_IP4_UDP_97_mask,
        .len  = sizeof(MAC_IP4_UDP_GTP_IP4_UDP_97_spec),
    },
    {
        .spec = MAC_IP4_UDP_97_spec,
        .mask = MAC_IP4_UDP_97_mask,
        .len  = sizeof(MAC_IP4_UDP_97_mask),
    },
    {
        .spec = MAC_IP6_UDP_97_spec,
        .mask = MAC_IP6_UDP_97_mask,
        .len  = sizeof(MAC_IP6_UDP_97_mask),
    },
    {
        .spec = NULL,
        .mask = NULL,
    },
};

// 返回 1: drop
// 返回 0: 放行
inline static int drop_udp_97(const uint8_t *packet, uint16_t packet_len)
{
    int  drop           = 0;
    int  seq            = 0;
    int  offset         = 0;
    int  is_9711        = 0;
    int  i              = 0;

    for(i = 0; match_list[i].spec; i++)
    {
        is_9711 = 1;
        for(offset = 0; offset < match_list[i].len; offset++)
        {
            if(match_list[i].mask[offset] && (match_list[i].spec[offset] != packet[offset]))
            {
                is_9711 = 0;
                break; // next one!
            }
        }

        if(is_9711) // hit !
        {
            seq = ntohs(*(const uint16_t *)(packet+offset));
            if((seq > 64) && packet[offset + 1] & 0x0f) // SEQ > 64 && drop 1~15
            {
                drop = 1;
            }
            //printf("debug %s offset %u seq=%u\n", 1 == drop ? "DROP" : "GOOD", offset, seq);
            match_list[i].hit++;
            if(unlikely(i && match_list[i].hit > match_list[0].hit))
            {
                const uint8_t *spec  = match_list[i].spec;
                const uint8_t *mask  = match_list[i].mask;
                uint32_t       len   = match_list[i].len;
                uint64_t       hit   = match_list[i].hit;

                match_list[i].spec = match_list[0].spec ;
                match_list[i].mask = match_list[0].mask ;
                match_list[i].len  = match_list[0].len  ;
                match_list[i].hit  = match_list[0].hit  ;

                match_list[0].spec   = spec;
                match_list[0].mask   = mask;
                match_list[0].len    = len;
                match_list[0].hit    = hit;
            }
            return drop;
        }
    }
    return 0; // 没有命中任何规则
}
