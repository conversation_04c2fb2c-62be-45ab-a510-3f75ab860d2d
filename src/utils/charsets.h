#ifndef _CHARSETS_H_
#define _CHARSETS_H_

#include <glib.h>
#include "dpi_pint.h"
#include "dpi_common.h"

#define ENC_NA			0x00000000


#define ENC_CHARENCODING_MASK		0x7FFFFFFE	/* mask out byte-order bits */
#define ENC_ASCII			0x00000000
#define ENC_UTF_8			0x00000002
#define ENC_UTF_16			0x00000004
#define ENC_UCS_2			0x00000006
#define ENC_UCS_4			0x00000008
#define ENC_ISO_8859_1			0x0000000A
#define ENC_ISO_8859_2			0x0000000C
#define ENC_ISO_8859_3			0x0000000E
#define ENC_ISO_8859_4			0x00000010
#define ENC_ISO_8859_5			0x00000012
#define ENC_ISO_8859_6			0x00000014
#define ENC_ISO_8859_7			0x00000016
#define ENC_ISO_8859_8			0x00000018
#define ENC_ISO_8859_9			0x0000001A
#define ENC_ISO_8859_10			0x0000001C
#define ENC_ISO_8859_11			0x0000001E
/* #define ENC_ISO_8859_12			0x00000020 ISO 8859-12 was abandoned */
#define ENC_ISO_8859_13			0x00000022
#define ENC_ISO_8859_14			0x00000024
#define ENC_ISO_8859_15			0x00000026
#define ENC_ISO_8859_16			0x00000028
#define ENC_WINDOWS_1250		0x0000002A
#define ENC_3GPP_TS_23_038_7BITS	0x0000002C
#define ENC_EBCDIC			0x0000002E
#define ENC_MAC_ROMAN			0x00000030
#define ENC_CP437			0x00000032
#define ENC_ASCII_7BITS			0x00000034
#define ENC_T61				0x00000036
#define ENC_EBCDIC_CP037		0x00000038


#define ENC_BIG_ENDIAN		0x00000000
#define ENC_LITTLE_ENDIAN	0x80000000

#define IS_LEAD_SURROGATE(uchar2) \
	((uchar2) >= 0xd800 && (uchar2) < 0xdc00)
#define IS_TRAIL_SURROGATE(uchar2) \
	((uchar2) >= 0xdc00 && (uchar2) < 0xe000)
#define SURROGATE_VALUE(lead, trail) \
	(((((lead) - 0xd800) << 10) | ((trail) - 0xdc00)) + 0x10000)

int get_utf_16_string(char *result, int max_len, const guint8 *ptr, gint length, const guint encoding);

int dpi_get_ascii_string(char *result, int max_len, const uint8_t *ptr, int length);

int dpi_get_utf_8_string(char *result, int max_len, const uint8_t *ptr, int length);
	
int dpi_get_ucs_2_string(char *result, int max_len, const guint8 *ptr, gint length, const guint encoding);

int dpi_get_ucs_4_string(char *result, int max_len, const guint8 *ptr, gint length, const guint encoding);

int dpi_get_8859_1_string(char *result, int max_len, const guint8 *ptr, gint length);

int dpi_get_unichar2_string(char *result, int max_len, const guint8 *ptr, gint length, const gunichar2 table[0x80]);

int dpi_get_ts_23_038_7bits_string(char *result, int max_len, const guint8 *ptr, const gint bit_offset, gint no_of_chars);

int dpi_get_ascii_7bits_string(char *result, int max_len, const guint8 *ptr, const gint bit_offset, gint no_of_chars);

int dpi_get_nonascii_unichar2_string(char *result, int max_len, const guint8 *ptr, gint length, const gunichar2 table[256]);
int dpi_get_t61_string(char *result, int max_len, const guint8 *ptr, gint length);
int dpi_get_string_enc(const uint8_t *ptr, const int length, const uint32_t encoding, char *result, int max_len);

int get_unicode_or_ascii_string(struct dpi_pkt_st *pkt, uint32_t *offsetp,	uint8_t useunicode, int *len, uint8_t nopad, uint8_t exactlen, uint16_t *bcp, char *result, int max_len);


#endif
