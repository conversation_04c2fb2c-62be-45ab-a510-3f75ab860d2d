/****************************************************************************************
 * 文 件 名 : dpi_zoom_conference.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: 李春利         2018/12/27
 修改: 李春利         2020/01/08
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "wxcs_def.h"
#include "dpi_detect.h"

#define                     MAX_TIME_OUT_NODE 1024*10
#define                     DATA_WITH_SID_LEN 20

extern struct             global_config g_config;
static void               *hash     = NULL;
static pthread_rwlock_t    rwlock   = PTHREAD_RWLOCK_INITIALIZER;

static void*
hash_init(void)
{
    pthread_rwlock_wrlock(&rwlock);
    if(NULL == hash)
    {
        hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    }
    pthread_rwlock_unlock(&rwlock);
    return hash;
}

static int
hash_insert(void *hash, char *key, void *value)
{
    if(NULL == hash || NULL==key || NULL==value)
    {
        return -1;
    }
    pthread_rwlock_wrlock(&rwlock);
    int ret = g_hash_table_insert ((GHashTable *)hash, key, value);
    pthread_rwlock_unlock(&rwlock);
    return  ret;
}

static void*
hash_find(void *hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return NULL;
    }

    pthread_rwlock_rdlock(&rwlock);
    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&rwlock);
    return ret;
}

static int
hash_delete(void *hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return -1;
    }

    pthread_rwlock_wrlock(&rwlock);
    g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&rwlock);
    return 0;
}

static void
hash_destory(void *hash)
{
    if(NULL == hash)
    {
        return;
    }

    pthread_rwlock_wrlock(&rwlock);
    g_hash_table_destroy((GHashTable *)hash);
    pthread_rwlock_unlock(&rwlock);
}

static int
BinToHex(const unsigned char *inPut, unsigned int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    char * pOrigin = OutBuffer;
    unsigned int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
        OutBuffer+=2;
    }
    return OutBuffer - pOrigin;
}

///////////////////////////////////////////
//Session thread

static void print_session(ST_ZOOM_person *p)
{
    int  ret = 0;
    char buff[64];

    unsigned char* ip_client = (unsigned char *)(&p->client_ip.ipv4);
    unsigned char* ip_server = (unsigned char *)(&p->server_ip.ipv4);

    dpi_TrailerDump(&p->trailer);

    ret = BinToHex(p->SID, sizeof(p->SID), buff, sizeof(buff));
    buff[ret] = 0;
    printf("ZOOM SID          :%s\n",  buff);

    printf("client_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->client_ip)+0),
            *((uint8_t*)(&p->client_ip)+1),
            *((uint8_t*)(&p->client_ip)+2),
            *((uint8_t*)(&p->client_ip)+3));

    printf("server_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->server_ip)+0),
            *((uint8_t*)(&p->server_ip)+1),
            *((uint8_t*)(&p->server_ip)+2),
            *((uint8_t*)(&p->server_ip)+3));

    printf("client_port       :%u\n", p->client_port);
    printf("server_port       :%u\n", p->server_port);

    printf("Packet_A         C:%6u  S:%6u\n", p->C2S_A_Packet, p->S2C_A_Packet);
    printf("Packet_V         C:%6u  S:%6u\n", p->C2S_V_Packet, p->S2C_V_Packet);
    printf("Packet_C         C:%6u  S:%6u\n", p->C2S_C_Packet, p->S2C_C_Packet);
    printf("Packet_K         C:%6u  S:%6u\n", p->C2S_K_Packet, p->S2C_K_Packet);

    printf("First             :%u\n", p->first);
    printf("Answered          :%u\n", p->answered);
    printf("Last              :%u\n", p->last);

    printf("视频通话          :%s\n", (p->C2S_V_Packet + p->S2C_V_Packet)>10?"yes":"no");
    printf("应答状态          :%s\n", (p->answered)?"yes":"no");
    printf("接入等待          :%d\n", (p->answered)?(p->answered - p->first):(p->last - p->first));
    printf("在线时长          :%d\n", p->last - p->first);
    printf("\n");
}

static void print_person(ST_ZOOM_person *p)
{
    if(0 == g_config.debug_zoom_conference)
    {
        return;
    }

    if(NULL == p)
    {
        return;
    }

    print_session(p);
}

static void* phone_watch_init(void)
{
    char  phone_number[16];
    int   safelen;

    if(0 == memcmp(g_config.wx_phone_watch, "null", 4))
    {
        printf("ZOOM 无添加监视手机号\n");
        return NULL;
    }

    void* hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if(NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    int  i   = 0;
    char*p   = (char*)g_config.wx_phone_watch;
    int size = sizeof(g_config.wx_phone_watch);

    char *pLeft  = p;
    char *pRight = pLeft;
    while('\0' != *p)
    {
        pRight = strchr(pLeft, ',');
        if(NULL == pRight)
        {
            // 探测是不是只有一个手机号，末尾没有逗号
            int len = strlen(pLeft);
            if(len >= 13 && len <15) //一个手机号就是13个字符
            {
                char *pStr = pLeft;
                printf("ZOOM监视手机[%s]\n", pStr);
                g_hash_table_insert((GHashTable *)hash, g_strdup(pStr), g_strdup(pStr));
            }
            break;
        }
        safelen = sizeof(phone_number) < (unsigned)(pRight - pLeft) ? sizeof(phone_number) : (unsigned)(pRight - pLeft);
        memset(phone_number, 0, sizeof(phone_number));
        memcpy(phone_number, pLeft, safelen);
        printf("ZOOM监视手机[%s]\n", phone_number);
        g_hash_table_insert((GHashTable *)hash, g_strdup(phone_number), g_strdup(phone_number));
        pLeft = ++pRight;
    }
    return hash;
}


static int phone_find(void *hash, const char *pStr)
{
    if(NULL == hash)
    {
        return -1;
    }

    gpointer p = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if(NULL == p)
    {
        return -1;
    }
    return 1;
}




static void*
zoom_session_thread(void *arg)
{
    wxc_handle handle      = 0;
    void* hash_phone   = NULL;

    hash = hash_init();
    if(NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    wxc_init(&handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    if(NULL == handle)
    {
        return NULL;
    }

    hash_phone = phone_watch_init();

    while(1)
    {
        int   KeyTop                  = 0;
        int   send                    = 0;
        int   relation                = 0;
        int   hash_count              = 0;
        char* key                     = NULL;
        ST_ZOOM_person *p             = NULL;
        void* Stack[MAX_TIME_OUT_NODE];

        sleep(g_config.wx_session_timeloop);
        pthread_rwlock_rdlock(&rwlock); // lock
        GHashTableIter iter;
        g_hash_table_iter_init(&iter, (GHashTable *) hash);
        while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&p))
        {
            if(NULL == p)
            {
                continue;
            }

            hash_count++;

            uint64_t now = time(NULL);
            if(now - p->last > g_config.wx_session_timeout)
            {
                if(KeyTop < MAX_TIME_OUT_NODE && NULL != key)
                {
                    p->isTimeout = 1;  // 设置超时标记
                    Stack[KeyTop++] = key;
                }
            }

            if(p->trailer.MSISDN && p->trailer.IMSI && p->trailer.IMEI)
            {
                relation++;
            }

            if(*(size_t*)p->SID && (p->C2S_K_Packet + p->S2C_K_Packet) >100)
            {
                send++;
                dpi_TrailerUpdateTS(&p->trailer);
                wxc_sendMsg(handle, (const unsigned char*)p, sizeof(ST_ZOOM_person), WXCS_ZOOM_CHAT);
            }

            if(hash_phone)
            {
                char phone_num[20];
                snprintf(phone_num, 20, "%ld", p->trailer.MSISDN);
                if(1 == phone_find(hash_phone, phone_num))
                {
                    printf("ZOOM %s got it !\n", phone_num);
                    print_session(p);
                }
            }

            print_person(p);
        } // end while
        pthread_rwlock_unlock(&rwlock); // unlock

        {
            char buffer[26];
            time_t timer;
            time(&timer);
            struct tm* tm_info = localtime(&timer);
            strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
            printf("ZOOM  online:[%s] 已发送/已超时/已建联/总人数=[%d/%d/%d/%d]\n", buffer, send, KeyTop, relation, hash_count);
        }

        while(KeyTop--)
        {
            hash_delete(hash, Stack[KeyTop]);
        }
    }
    return NULL;
}

int init_dissector_zoom_sesion_thread(void);
int init_dissector_zoom_sesion_thread(void)
{
    if (g_config.protocol_switch[PROTOCOL_ZOOM_CONFERENCE] == 0)
    {
        return 0;
    }

    pthread_t t_zoom;

    int status = pthread_create(&t_zoom, NULL, zoom_session_thread, NULL);
    if(status != 0)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create weixin_voice_session thread");
        exit(-1);
    }
    return 0;
}

/////////////////////////////////////////
static int
zoom_flow(unsigned int ip1, unsigned int ip2, char *buff, int size)
{
    snprintf(buff, size, "%u.%u", ip1, ip2);
    return 0;
}

static size_t
zoom_C(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    const char      * p     = NULL;
    const char      *find   = NULL;
    int              offset = 0;
    int              walk   = 0;
    unsigned short   prefix = 0;
    size_t           sid    = 0;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"\x0D\x01", 2},
        {"\x0C\x01", 2},
        {"\x0D\x01", 2},
        {"\x0C\x01", 2},
        {NULL,       0},
    };

    if(payload_len <20)
    {
        return 0;
    }

    prefix = ntohs(*(const unsigned short*)payload);
    if(!(0x050c == prefix || 0x050D == prefix))
    {
        return 0;
    }

    p      = (const char*)payload;
    walk   = (payload_len < DATA_WITH_SID_LEN)?payload_len:DATA_WITH_SID_LEN;
    offset = 0;
    for(int i = 0; 0 != magic[i].len; i++)
    {
        find = memmem(p, walk, magic[i].prefix,  magic[i].len);
        if(find)
        {
            offset = (find - p + magic[i].len);
            walk  -= offset;
            p     += offset;
            sid = *(const size_t*)(find+2);
        }
        else
        {
            break;
        }
    }
    return sid;
}


static size_t
zoom_V(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    const char      * p     = NULL;
    const char      *find   = NULL;
    int              offset = 0;
    int              walk   = 0;
    unsigned short   prefix = 0;
    size_t           sid    = 0;
    int              got    = 0;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"\x05\x10", 2},
        {"\x01\xBE\xDE\x00", 4},
        {"\x00\x00\x00\x00\x00\x00\x00\x00", 8},
        {NULL,       0},
    };

    if(payload_len <20)
    {
        return 0;
    }

    p      = (const char*)payload;
    walk   = payload_len;
    offset = 0;
    const char*str = (const char*)payload;
    for(int i = 0; 0 != magic[i].len && walk > magic[i].len; i++)
    {
        find = memmem(p, walk, magic[i].prefix,  magic[i].len);
        if(find)
        {
            offset = (find - p + magic[i].len);
            walk  -= offset;
            p     += offset;
            ++got;
        }
        else
        {
            return 0;
        }
    }
    if(3 == got)
    {
        return 1; //yes video
    }
    return 0;
}



static size_t
zoom_A(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    const char      * p     = NULL;
    const char      *find   = NULL;
    int              offset = 0;
    int              walk   = 0;
    unsigned short   prefix = 0;
    size_t           sid    = 0;
    int              got    = 0;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"\x05\x0F", 2},
        {"\x02\xBE\xDE\x00", 4},
        {"\x00\x00\x00\x00\x00\x00\x00\x00", 8},
        {NULL,       0},
    };

    if(payload_len <20)
    {
        return 0;
    }

    p      = (const char*)payload;
    walk   = payload_len;
    offset = 0;
    const char*str = (const char*)payload;
    for(int i = 0; 0 != magic[i].len && walk > magic[i].len; i++)
    {
        find = memmem(p, walk, magic[i].prefix,  magic[i].len);
        if(find)
        {
            offset = (find - p + magic[i].len);
            walk  -= offset;
            p     += offset;
            ++got;
        }
        else
        {
            return 0;
        }
    }
    if(3 == got)
    {
        return 1; //yes audio
    }
    return 0;
}

static size_t
zoom_K(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    const char      * p     = NULL;
    const char      *find   = NULL;
    int              offset = 0;
    int              walk   = 0;
    unsigned short   prefix = 0;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"\x03\x00", 2},
        {"\x04\x00", 2},
        {"\x05\x0A", 2},
        {NULL,       0},
    };

    if(payload_len < 8)
    {
        return 0;
    }

    p      = (const char*)payload;
    walk   = payload_len;
    offset = 0;
    const char*str = (const char*)payload;
    for(int i = 0; 0 != magic[i].len && walk > magic[i].len; i++)
    {
        find = memmem(p, walk, magic[i].prefix,  magic[i].len);
        if(find)
        {
            return 1;
        }
    }
    return 0;
}

struct zoom_s
{
    void *hash_value;
};

static int
zoom_flow_init(struct flow_info *flow)
{
    while(NULL == flow->app_session)
    {
        flow->app_session = malloc(sizeof(struct zoom_s));
    }
    memset(flow->app_session, 0, sizeof(struct zoom_s));
    return 0;
}

static int
dissect(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    int   client_ip            = 0;
    int   server_ip            = 0;
    short client_port          = 0;
    short server_port          = 0;
    int   C2S                  = 0;
    int   is_v                 = 0;
    int   is_a                 = 0;
    int   is_k                 = 0;
    size_t sid                 = 0;
    ST_ZOOM_person          *p = NULL;
    struct zoom_s           *s = NULL;

    if(DISSECT_PKT_ORIGINAL != flag)
    {
        return 0;
    }

    zoom_flow_init(flow);
    s = (struct zoom_s*)flow->app_session;

    sid  = zoom_C(flow, payload, payload_len);
    is_a = zoom_A(flow, payload, payload_len);
    is_v = zoom_V(flow, payload, payload_len);
    is_k = zoom_K(flow, payload, payload_len);
    if(0 == sid  && 0 == is_a && 0 == is_v && 0 == is_k)
    {
        return 0; // 提纯
    }

    get_ip_port_v4(flow, &client_ip, &server_ip, &client_port, &server_port, &C2S);
    if(NULL == s->hash_value)
    {
        char                  CS_IP[32];
        zoom_flow(client_ip, server_ip, CS_IP, sizeof(CS_IP));
        while(NULL == hash);
        p = hash_find(hash, CS_IP);
        if(NULL == p)
        {
            p = malloc(sizeof(ST_ZOOM_person));
            if(p)
            {
                memset(p, 0, sizeof(ST_ZOOM_person));
            }
            dpi_TrailerParser (&p->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
            dpi_TrailerGetMAC (&p->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
            dpi_TrailerGetHWZZMAC(&p->trailer, (const char *)flow->ethhdr);

            dpi_TrailerSetDev (&p->trailer, g_config.devname);         // 解析板号
            dpi_TrailerSetOpt (&p->trailer, g_config.operator_name);   // 运营商
            dpi_TrailerSetArea(&p->trailer,g_config.devArea);          // 地域名
            p->ip_version            = 4;
            p->client_ip.ipv4        = client_ip;
            p->server_ip.ipv4        = server_ip;
            p->client_port           = client_port;
            p->server_port           = server_port;
            p->first                 = time(NULL);
            *(size_t*)p->SID         = sid;
            hash_insert(hash, g_strdup(CS_IP), (void*)p);
        }
        s->hash_value = p;
    }

    p = (ST_ZOOM_person*)s->hash_value;
    p->answered = ((is_a||is_v) && (0 == p->answered))?time(NULL):p->answered;
    p->C2S_A_Packet += (1 == C2S && is_a)?1:0;
    p->S2C_A_Packet += (0 == C2S && is_a)?1:0;
    p->C2S_V_Packet += (1 == C2S && is_v)?1:0;
    p->S2C_V_Packet += (0 == C2S && is_v)?1:0;
    p->C2S_C_Packet += (1 == C2S && sid) ?1:0;
    p->S2C_C_Packet += (0 == C2S && sid) ?1:0;
    p->C2S_K_Packet += (1 == C2S && is_k)?1:0;
    p->S2C_K_Packet += (0 == C2S && is_k)?1:0;
    *(size_t*)p->SID = (sid && 0 == *(size_t*)p->SID)?sid:*(size_t*)p->SID;
    p->last  = time(NULL);
    return 0;
}

static void
identify(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_ZOOM_CONFERENCE] == 0)
    {
        return;
    }

    /* 判断报文的目标端口  */
    int port_src = ntohs(flow->tuple.inner.port_src);
    int port_dst = ntohs(flow->tuple.inner.port_dst);
    if(8801 == port_src || 8801 == port_dst)
    {
        flow->real_protocol_id = PROTOCOL_ZOOM_CONFERENCE;
    }

    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_ZOOM_CONFERENCE);
    return;
}

void init_ZOOM_Conference_dissector(void)
{
    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_ZOOM_CONFERENCE
    port_add_proto_head(IPPROTO_UDP, 8801, PROTOCOL_ZOOM_CONFERENCE);

    // 协议为 PROTOCOL_ZOOM_CONFERENCE 的回调
    udp_detection_array[PROTOCOL_ZOOM_CONFERENCE].proto         = PROTOCOL_ZOOM_CONFERENCE;
    udp_detection_array[PROTOCOL_ZOOM_CONFERENCE].identify_func = identify;
    udp_detection_array[PROTOCOL_ZOOM_CONFERENCE].dissect_func  = dissect;

    // 自我标识.当前结构体的协议类型. 辅助流识别.
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_ZOOM_CONFERENCE].excluded_protocol_bitmask, PROTOCOL_ZOOM_CONFERENCE);

    return;
}


