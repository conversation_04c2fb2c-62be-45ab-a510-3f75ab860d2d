/****************************************************************************************
 * 文 件 名 : wxcs_dpi_node.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设    计 :       '2019-08-14
 * 编    码 :       '2019-08-14
 * 修    改 :
 ****************************************************************************************
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2019 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 ***************************************************************************************/
#include <map>
#include <thread>
#include <mutex>

#ifndef _DPI_NODE_H_
#define _DPI_NODE_H_

// 用于维护每台 解析节点的 实时信息

int  set_node_timestamp(int node_id, int type, int time);
int  set_node_name     (int node_id, const char *Area, const char *Blade);
void inc_node_sessions  (int node_id);
void sub_node_sessions  (int node_id);

int  get_node_timestamp(int node_id);
int  get_wxcs_timestamp(int node_id);
int  get_node_sessions (int node_id);
std::string get_node_name(int node_id);

#endif
