/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_SERVER_H
#define WX_PROXY_SERVER_H

#ifdef __cplusplus
extern "C" {
#endif

struct evconnlistener *
create_server(int listen_port, struct context *ctx);

struct connection_t*
slave_connection_init(struct sockaddr *sa, struct mirror_t *mirror);

struct connection_t*
maser_connection_init(struct sockaddr *sa, struct mirror_t *mirror);

#ifdef __cplusplus
}
#endif

#endif

