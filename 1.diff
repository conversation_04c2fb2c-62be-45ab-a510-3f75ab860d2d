diff --git a/include/wxcs_def.h b/include/wxcs_def.h
index c4eefea..66e6947 100644
--- a/include/wxcs_def.h
+++ b/include/wxcs_def.h
@@ -148,6 +148,7 @@ enum
     WXCS_WX_PEERS,          /* wx 一对一通话 公网ip */
     WXCS_QQ_EVENT,         /* ZOOM 会议行为 */
     WXCS_WX_LOC_SHARING,    /* wx 共享实时位置 */
+    WXCS_TENCENT_MEETING,   /* 腾讯会议*/
     WXCS_ENUM_MAX,
 };
 
@@ -613,6 +614,34 @@ typedef struct wx_location_shared_
 /*=============================== wx 共享实时位置 ============================================================*/
 
 
+/*=============================== 腾讯会议  ============================================================*/
+typedef struct {
+    // 建联信息, 必须置于消息体的头部
+    ST_trailer trailer;
+
+    uint32_t srcIp;
+    uint32_t dstIp;
+    uint16_t srcPort;
+    uint16_t dstPort;
+
+    uint32_t c2sPackCount;
+    uint32_t c2sByteCount;
+    uint32_t s2cPackCount;
+    uint32_t s2cByteCount;
+
+    uint64_t sessionId;
+    char selfMeetingNum[18];
+    char selfLoginNum[18];
+
+    uint32_t firstActiveTime;
+    uint32_t lastActiveTime;
+    uint8_t  isTimeout;                         // 这个人是否已经超时
+} ST_TecentMeeting;
+
+/*=============================== 腾讯会议 ============================================================*/
+
+
+
 
 #define  WX_PYQ_JPG  0
 #define  WX_PYQ_MP4  1
diff --git a/src/PROTOCOL_LIST.txt b/src/PROTOCOL_LIST.txt
index 597e905..ff20f92 100644
--- a/src/PROTOCOL_LIST.txt
+++ b/src/PROTOCOL_LIST.txt
@@ -27,3 +27,4 @@ DOUYIN
 WXID
 WXPAY
 ALIPAY
+TENCENT_MEETING
\ No newline at end of file
diff --git a/src/framework/dpi_proto_ids.c b/src/framework/dpi_proto_ids.c
index bfd0f0e..70a436d 100644
--- a/src/framework/dpi_proto_ids.c
+++ b/src/framework/dpi_proto_ids.c
@@ -35,6 +35,7 @@ const char *protocol_name_array[PROTOCOL_MAX] =
     "WXID",
     "WXPAY",
     "ALIPAY",
+    "TENCENT_MEETING",
 };
 
 
diff --git a/src/framework/dpi_proto_ids.h b/src/framework/dpi_proto_ids.h
index 68c586c..23d0dbd 100644
--- a/src/framework/dpi_proto_ids.h
+++ b/src/framework/dpi_proto_ids.h
@@ -36,6 +36,7 @@ enum tbl_log_type {
     TBL_LOG_WXID,
     TBL_LOG_WXPAY,
     TBL_LOG_ALIPAY,
+    TBL_LOG_TENCENT_MEETING,
     TBL_LOG_MAX
 };
 
@@ -72,6 +73,7 @@ enum PROTOCOL_TYPE {
     PROTOCOL_WXID,
     PROTOCOL_WXPAY,
     PROTOCOL_ALIPAY,
+    PROTOCOL_TENCENT_MEETING,
     PROTOCOL_MAX
 };
 
diff --git a/src/proto/dpi_tencent_meeting.c b/src/proto/dpi_tencent_meeting.c
new file mode 100644
index 0000000..ea37c72
--- /dev/null
+++ b/src/proto/dpi_tencent_meeting.c
@@ -0,0 +1,512 @@
+#include "dpi_detect.h"
+#include "dpi_log.h"
+#include "dpi_cjson.h"
+#include "dpi_common.h"
+#include "wxcs_def.h"
+#include <glib.h>
+#include <pthread.h>
+
+extern struct global_config g_config;
+
+// 全局变量
+static wxc_handle g_tencent_meeting_handle = NULL;
+static GHashTable* g_tencent_meeting_user_hash = NULL;
+static pthread_rwlock_t g_tencent_meeting_rwlock = PTHREAD_RWLOCK_INITIALIZER;
+
+// 用户信息结构体，用于关联个人常规账号和个人会议账号
+typedef struct {
+    uint64_t msisdn;
+    uint32_t ip;
+    char selfLoginNum[18];
+    uint32_t lastActiveTime;
+} TencentMeetingUser;
+
+// protobuf解析函数声明
+uint8_t decode_protobuf(char *input, int input_size, char **output);
+
+// 腾讯会议信息解析接口
+typedef struct {
+    uint64_t meetingNum;
+    uint64_t sessionId;
+    int parseResult;  // 0: 失败, 1: 成功
+} TencentMeetingInfo;
+
+// 抽象的腾讯会议信息解析接口
+static int parse_tencent_meeting_info(const uint8_t *payload, uint32_t payload_len,
+                                      uint32_t info_offset, uint32_t info_len,
+                                      TencentMeetingInfo *meeting_info) {
+    if (!payload || !meeting_info || info_len == 0) {
+        return 0;
+    }
+
+    memset(meeting_info, 0, sizeof(TencentMeetingInfo));
+
+    // 解析protobuf格式的info部分
+    char *json_output = NULL;
+    uint8_t decode_result = decode_protobuf((char*)(payload + info_offset), info_len, &json_output);
+
+    if (decode_result && json_output) {
+        cJSON *json = dpi_cjson_parse_json(json_output);
+        if (json) {
+            // 获取个人会议账号 {"2":{"3": xxxxxxxxxxxx}}
+            cJSON *node2 = dpi_cjson_get_object_field(json, "2");
+            if (node2) {
+                cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
+                if (node3 && cJSON_IsNumber(node3)) {
+                    meeting_info->meetingNum = (uint64_t)node3->valuedouble;
+                    meeting_info->parseResult = 1;
+                }
+            }
+            dpi_cjson_free_json(json);
+        }
+        free(json_output);
+    }
+
+    return meeting_info->parseResult;
+}
+typedef struct Tencent_meeting_session_t {
+  int FlagPacketC2S;
+  int FlagPacketS2C;
+  int SessionType;
+  uint64_t sessionId;
+  char selfMeetingNum[18];
+  char selfLoginNum[18];
+  uint32_t firstActiveTime;
+  uint32_t lastActiveTime;
+  uint32_t c2sPackCount;
+  uint32_t c2sByteCount;
+  uint32_t s2cPackCount;
+  uint32_t s2cByteCount;
+} Tencent_meeting_session;
+
+// 初始化全局hash表
+static void init_tencent_meeting_hash(void) {
+    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
+    if (!g_tencent_meeting_user_hash) {
+        g_tencent_meeting_user_hash = g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, g_free);
+        if (!g_tencent_meeting_user_hash) {
+            DPI_LOG(DPI_LOG_ERROR, "error on create tencent meeting user hash");
+            exit(-1);
+        }
+    }
+    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
+}
+
+// 查找用户信息
+static TencentMeetingUser* find_user_by_msisdn_or_ip(uint64_t msisdn, uint32_t ip) {
+    TencentMeetingUser* user = NULL;
+
+    pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
+    if (g_tencent_meeting_user_hash) {
+        // 优先通过MSISDN查找
+        if (msisdn != 0) {
+            user = (TencentMeetingUser*)g_hash_table_lookup(g_tencent_meeting_user_hash, GUINT_TO_POINTER(msisdn));
+        }
+        // 如果MSISDN找不到，通过IP查找
+        if (!user && ip != 0) {
+            user = (TencentMeetingUser*)g_hash_table_lookup(g_tencent_meeting_user_hash, GUINT_TO_POINTER(ip));
+        }
+    }
+    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
+
+    return user;
+}
+
+// 添加或更新用户信息
+static void add_or_update_user(uint64_t msisdn, uint32_t ip, const char* selfLoginNum) {
+    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
+    if (g_tencent_meeting_user_hash) {
+        TencentMeetingUser* user = (TencentMeetingUser*)malloc(sizeof(TencentMeetingUser));
+        if (user) {
+            user->msisdn = msisdn;
+            user->ip = ip;
+            if (selfLoginNum) {
+                strncpy(user->selfLoginNum, selfLoginNum, sizeof(user->selfLoginNum) - 1);
+                user->selfLoginNum[sizeof(user->selfLoginNum) - 1] = '\0';
+            } else {
+                user->selfLoginNum[0] = '\0';
+            }
+            user->lastActiveTime = time(NULL);
+
+            // 使用MSISDN作为key，如果没有MSISDN则使用IP
+            gpointer key = GUINT_TO_POINTER(msisdn != 0 ? msisdn : ip);
+            g_hash_table_replace(g_tencent_meeting_user_hash, key, user);
+        }
+    }
+    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
+}
+
+// 解析个人会议数据 (0x28开头)
+static int dissect_tencent_meeting_28(
+    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
+
+  if (payload_len < 20) {
+    return 0;
+  }
+
+  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
+  if (!session) {
+    return 0;
+  }
+
+  // 解析Header长度和Info长度
+  uint32_t headerLen = get_uint32_ntohl(payload, 1);
+  uint32_t infoLen = get_uint32_ntohl(payload, 5);
+
+  if (payload_len < 9 + headerLen + infoLen + 1) {
+    return 0;
+  }
+
+  // 检查结尾是否为0x29
+  if (payload[9 + headerLen + infoLen] != 0x29) {
+    return 0;
+  }
+
+  // 使用抽象接口解析会议信息
+  TencentMeetingInfo meeting_info;
+  if (parse_tencent_meeting_info(payload, payload_len, 9 + headerLen, infoLen, &meeting_info)) {
+    snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);
+
+    // 更新会话信息
+    session->lastActiveTime = time(NULL);
+    if (session->firstActiveTime == 0) {
+      session->firstActiveTime = session->lastActiveTime;
+    }
+  }
+
+  return 0;
+}
+// 解析个人会议数据 (0x36开头)
+static int dissect_tencent_meeting_36(
+    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
+
+  if (payload_len < 20) {
+    return 0;
+  }
+
+  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
+  if (!session) {
+    return 0;
+  }
+
+  // 解析包长度
+  uint16_t packetLen = get_uint16_ntohs(payload, 1);
+  if (payload_len < packetLen) {
+    return 0;
+  }
+
+  // 检查固定字段
+  if (get_uint32_ntohl(payload, 3) != 0x00000000) {
+    return 0;
+  }
+
+  // 检查0x03a102
+  if (get_uint16_ntohs(payload, 8) != 0x03a1 || payload[10] != 0x02) {
+    return 0;
+  }
+
+  // 解析sessionID (4字节)
+  uint32_t sessionID = get_uint32_ntohl(payload, 18);
+  session->sessionId = sessionID;
+
+  // 使用抽象接口解析会议信息
+  uint32_t infoOffset = 29; // 跳过前面的固定字段
+  if (payload_len > infoOffset) {
+    uint32_t infoLen = payload_len - infoOffset;
+    TencentMeetingInfo meeting_info;
+    if (parse_tencent_meeting_info(payload, payload_len, infoOffset, infoLen, &meeting_info)) {
+      snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);
+    }
+  }
+
+  // 更新会话信息
+  session->lastActiveTime = time(NULL);
+  if (session->firstActiveTime == 0) {
+    session->firstActiveTime = session->lastActiveTime;
+  }
+
+  return 0;
+}
+// 解析个人会议数据 (0x51开头)
+static int dissect_tencent_meeting_51(
+    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
+
+  if (payload_len < 16) {
+    return 0;
+  }
+
+  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
+  if (!session) {
+    return 0;
+  }
+
+  // 检查固定字段 0x0011
+  if (get_uint16_ntohs(payload, 1) != 0x0011) {
+    return 0;
+  }
+
+  // 检查0x02
+  if (payload[5] != 0x02) {
+    return 0;
+  }
+
+  // 解析sessionID (4字节)
+  uint32_t sessionID = get_uint32_ntohl(payload, 13);
+  session->sessionId = sessionID;
+
+  // 使用抽象接口解析会议信息
+  uint32_t infoOffset = 17; // 跳过前面的固定字段
+  if (payload_len > infoOffset) {
+    uint32_t infoLen = payload_len - infoOffset;
+    TencentMeetingInfo meeting_info;
+    if (parse_tencent_meeting_info(payload, payload_len, infoOffset, infoLen, &meeting_info)) {
+      snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);
+    }
+  }
+
+  // 更新会话信息
+  session->lastActiveTime = time(NULL);
+  if (session->firstActiveTime == 0) {
+    session->firstActiveTime = session->lastActiveTime;
+  }
+
+  return 0;
+}
+// 解析个人常规账号 (TCP)
+static int dissect_tencent_meeting_tcp(
+    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
+
+  if (payload_len < 32) {
+    return 0;
+  }
+
+  // 检查标识符 0x00001770
+  if (get_uint32_ntohl(payload, 4) != 0x00001770) {
+    return 0;
+  }
+
+  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
+  if (!session) {
+    return 0;
+  }
+
+  // 解析个人常规账号
+  char selfLoginNum[19] = {0};
+  uint32_t accountOffset = 0;
+
+  // 根据方向确定账号位置
+  if (direction == 0) { // 上行
+    // 查找0x1400000016标识
+    for (uint32_t i = 8; i < payload_len - 18; i++) {
+      if (get_uint32_ntohl(payload, i) == 0x14000000 && get_uint16_ntohs(payload, i + 4) == 0x0016) {
+        accountOffset = i + 6;
+        break;
+      }
+    }
+  } else { // 下行
+    // 查找0x1400000016标识
+    for (uint32_t i = 8; i < payload_len - 18; i++) {
+      if (get_uint32_ntohl(payload, i) == 0x14000000 && get_uint16_ntohs(payload, i + 4) == 0x0016) {
+        accountOffset = i + 6;
+        break;
+      }
+    }
+  }
+
+  if (accountOffset > 0 && accountOffset + 18 <= payload_len) {
+    memcpy(selfLoginNum, payload + accountOffset, 18);
+    selfLoginNum[18] = '\0';
+    strncpy(session->selfLoginNum, selfLoginNum, sizeof(session->selfLoginNum) - 1);
+    session->selfLoginNum[sizeof(session->selfLoginNum) - 1] = '\0';
+
+    // 解析trailer获取MSISDN
+    ST_trailer tmp_trailer;
+    memset(&tmp_trailer, 0, sizeof(ST_trailer));
+
+    dpi_TrailerParser(&tmp_trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
+
+    uint64_t msisdn = tmp_trailer.MSISDN;
+    uint32_t ip = 0;
+
+    // 获取IP地址
+    if (flow->pkt.ip_ver == 4) {
+      ip = flow->pkt.src_ip.ipv4;
+    }
+
+    // 添加到全局hash表
+    add_or_update_user(msisdn, ip, selfLoginNum);
+
+    // 更新会话信息
+    session->lastActiveTime = time(NULL);
+    if (session->firstActiveTime == 0) {
+      session->firstActiveTime = session->lastActiveTime;
+    }
+  }
+
+  return 0;
+}
+
+// 发送腾讯会议数据到wxcs
+static void send_tencent_meeting_data(struct flow_info *flow, Tencent_meeting_session *session) {
+  if (!session || !g_tencent_meeting_handle) {
+    return;
+  }
+
+  ST_TecentMeeting person_info;
+  memset(&person_info, 0, sizeof(ST_TecentMeeting));
+
+  // 解析trailer
+  dpi_TrailerParser(&person_info.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
+  dpi_TrailerGetMAC(&person_info.trailer, (const char*)flow->ethhdr, g_config.RT_model);
+  dpi_TrailerGetHWZZMAC(&person_info.trailer, (const char*)flow->ethhdr);
+  dpi_TrailerSetDev(&person_info.trailer, g_config.devname);
+  dpi_TrailerSetOpt(&person_info.trailer, g_config.operator_name);
+  dpi_TrailerSetArea(&person_info.trailer, g_config.devArea);
+
+  // 设置IP和端口信息
+  if (flow->pkt.ip_ver == 4) {
+    person_info.srcIp = flow->pkt.src_ip.ipv4;
+    person_info.dstIp = flow->pkt.dst_ip.ipv4;
+  }
+  person_info.srcPort = ntohs(flow->tuple.inner.port_src);
+  person_info.dstPort = ntohs(flow->tuple.inner.port_dst);
+
+  // 设置会话信息
+  person_info.sessionId = session->sessionId;
+  strncpy(person_info.selfMeetingNum, session->selfMeetingNum, sizeof(person_info.selfMeetingNum) - 1);
+  strncpy(person_info.selfLoginNum, session->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);
+
+  person_info.firstActiveTime = session->firstActiveTime;
+  person_info.lastActiveTime = session->lastActiveTime;
+  person_info.c2sPackCount = session->c2sPackCount;
+  person_info.c2sByteCount = session->c2sByteCount;
+  person_info.s2cPackCount = session->s2cPackCount;
+  person_info.s2cByteCount = session->s2cByteCount;
+  person_info.isTimeout = 0;
+
+  // 尝试关联个人常规账号
+  uint64_t msisdn = person_info.trailer.MSISDN;
+  uint32_t ip = person_info.srcIp;
+
+  TencentMeetingUser* user = find_user_by_msisdn_or_ip(msisdn, ip);
+  if (user && user->selfLoginNum[0] != '\0') {
+    strncpy(person_info.selfLoginNum, user->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);
+  }
+
+  // 发送数据
+  wxc_sendMsg(g_tencent_meeting_handle, (const unsigned char*)&person_info, sizeof(ST_TecentMeeting), WXCS_TENCENT_MEETING);
+}
+
+static int dissect_tencent_meeting_chat(
+    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag) {
+  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
+    return 0;
+  }
+
+  if (NULL == flow->app_session) {
+    Tencent_meeting_session *tcmeeting_session;
+    tcmeeting_session = malloc(sizeof(Tencent_meeting_session));
+    if (NULL == tcmeeting_session) {
+      DPI_LOG(DPI_LOG_ERROR, "error on malloc Tencent_meeting_session");
+      return 0;
+    }
+    memset(tcmeeting_session, 0, sizeof(Tencent_meeting_session));
+    flow->app_session = tcmeeting_session;
+  }
+
+  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
+
+  // 更新包计数
+  if (direction == 0) { // C2S
+    session->c2sPackCount++;
+    session->c2sByteCount += payload_len;
+  } else { // S2C
+    session->s2cPackCount++;
+    session->s2cByteCount += payload_len;
+  }
+
+  int result = 0;
+  switch (payload[0]) {
+    case 0x28:
+      result = dissect_tencent_meeting_28(flow, direction, seq, payload, payload_len);
+      break;
+    case 0x36:
+      result = dissect_tencent_meeting_36(flow, direction, seq, payload, payload_len);
+      break;
+    case 0x51:
+      result = dissect_tencent_meeting_51(flow, direction, seq, payload, payload_len);
+      break;
+    case 0x41:
+      return 0;
+      break;
+    default:
+      if (get_uint32_ntohl(payload, 4) != 0x00001770)
+        return 0;
+      result = dissect_tencent_meeting_tcp(flow, direction, seq, payload, payload_len);
+      break;
+  }
+
+  // 如果解析到了sessionID和个人会议账号，发送数据
+  if (session->sessionId != 0 && session->selfMeetingNum[0] != '\0') {
+    send_tencent_meeting_data(flow, session);
+  }
+
+  return result;
+}
+
+static void identify_tencent_meeting_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
+  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
+    return;
+  }
+
+  if (payload_len < 20) {
+    return;
+  }
+  /* 判断报文的目标端口  */
+  int port_src = ntohs(flow->tuple.inner.port_src);
+  int port_dst = ntohs(flow->tuple.inner.port_dst);
+  if (flow->tuple.inner.proto == 6) {
+    if ((80 == port_dst || 80 == port_src || 443 == port_dst || 443 == port_src || 8080 == port_dst || 8080 == port_src) &&
+        (get_uint32_ntohl(payload, 4) == 0x00001770)) {
+      //tcp个人常规
+      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
+    } else if ((443 == port_dst || 443 == port_src) && (payload[0] == 0x28)) {
+      // tcp个人会议
+      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
+    }
+  } else {
+    if ((443 == port_dst || 443 == port_src) &&
+        (payload[0] == 0x28 || payload[0] == 36 || payload[0] == 28 || payload[0] == 41 || payload[0] == 51)) {
+      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
+    }
+    return;
+  }
+}
+
+void init_tencent_meeting_chat_dissector(void) {
+  // 初始化wxc连接
+  if (NULL == g_tencent_meeting_handle) {
+    wxc_init(&g_tencent_meeting_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
+  }
+
+  // 初始化hash表
+  init_tencent_meeting_hash();
+
+  port_add_proto_head(IPPROTO_UDP, 443, PROTOCOL_TENCENT_MEETING);
+
+  udp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
+  udp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
+  udp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
+  port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_TENCENT_MEETING);
+  port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_TENCENT_MEETING);
+  port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_TENCENT_MEETING);
+
+  tcp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
+  tcp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
+  tcp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
+
+  DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
+  DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
+
+  return;
+}
diff --git a/wxcs/wxcs_person.cpp b/wxcs/wxcs_person.cpp
index e447316..56c2a6c 100644
--- a/wxcs/wxcs_person.cpp
+++ b/wxcs/wxcs_person.cpp
@@ -1182,3 +1182,67 @@ int WxcsQQEventPerson::getAllMsisdnQQ(std::map<size_t, std::map<size_t, QQEventI
 /*********************************QQevent****************************************************/
 
 /*ADD_E by yangna 2020-08-20 */
+
+//////////////////// WxcsTencentMeetingPerson //////////////////////////////////////////
+WxcsTencentMeetingPerson::WxcsTencentMeetingPerson()
+{
+}
+
+WxcsTencentMeetingPerson::WxcsTencentMeetingPerson(ST_TecentMeeting *pMeetingMsg)
+    : WxcsPerson((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId), 0,
+                 pMeetingMsg->srcPort, pMeetingMsg->dstPort,
+                 pMeetingMsg->srcIp, pMeetingMsg->dstIp,
+                 pMeetingMsg->lastActiveTime,
+                 &pMeetingMsg->trailer)
+    , sessionId(pMeetingMsg->sessionId)
+    , selfMeetingNum(pMeetingMsg->selfMeetingNum)
+    , selfLoginNum(pMeetingMsg->selfLoginNum)
+    , firstActiveTime(pMeetingMsg->firstActiveTime)
+    , lastActiveTime(pMeetingMsg->lastActiveTime)
+    , c2sPackCount(pMeetingMsg->c2sPackCount)
+    , c2sByteCount(pMeetingMsg->c2sByteCount)
+    , s2cPackCount(pMeetingMsg->s2cPackCount)
+    , s2cByteCount(pMeetingMsg->s2cByteCount)
+    , isTimeout(pMeetingMsg->isTimeout)
+{
+}
+
+std::string WxcsTencentMeetingPerson::toStrRecord(char sep) const
+{
+    std::string strRecord;
+    strRecord += getStrTrailer(sep);  // 建联信息
+
+    // session info
+    CONS_RECORD_FIELD_NUM(strRecord,   sessionId, sep);
+    CONS_RECORD_FIELD_TEXT(strRecord,  selfMeetingNum, sep);
+    CONS_RECORD_FIELD_TEXT(strRecord,  selfLoginNum, sep);
+
+    // statistics
+    CONS_RECORD_FIELD_NUM(strRecord,   c2sPackCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   c2sByteCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   s2cPackCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   s2cByteCount, sep);
+
+    // date
+    CONS_RECORD_FIELD_TIME(strRecord,  firstActiveTime, sep);
+    CONS_RECORD_FIELD_TIME(strRecord,  lastActiveTime, sep);
+
+    // timeout flag
+    CONS_RECORD_FIELD_NUM(strRecord,   (int)isTimeout, sep);
+
+    //保留字段
+    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);
+
+    return strRecord;
+}
+
+std::string WxcsTencentMeetingPerson::toStrBlankRecord(char sep)
+{
+    std::string strRecord;
+    for (int i = 0; i < 31; i++)
+    {
+        strRecord += std::string("\"\"") + sep;
+    }
+
+    return strRecord;
+}
diff --git a/wxcs/wxcs_person.h b/wxcs/wxcs_person.h
index 03936dc..7a34fe3 100644
--- a/wxcs/wxcs_person.h
+++ b/wxcs/wxcs_person.h
@@ -344,6 +344,29 @@ public:
     uint32_t  isTimeout;               // 是否已经超时
 };
 
+/* WxcsTencentMeetingPerson */
+class WxcsTencentMeetingPerson : public WxcsPerson
+{
+public:
+    WxcsTencentMeetingPerson();
+    WxcsTencentMeetingPerson(ST_TecentMeeting *pMeetingMsg);
+
+    virtual std::string toStrRecord(char sep) const;
+    static  std::string toStrBlankRecord(char sep);
+
+public:
+    uint64_t  sessionId;               // 会议SessionID
+    std::string selfMeetingNum;        // 个人会议账号
+    std::string selfLoginNum;          // 个人常规账号
+    uint32_t  firstActiveTime;         // 首次活跃时间
+    uint32_t  lastActiveTime;          // 最后活跃时间
+    uint32_t  c2sPackCount;            // C2S包数量
+    uint32_t  c2sByteCount;            // C2S字节数
+    uint32_t  s2cPackCount;            // S2C包数量
+    uint32_t  s2cByteCount;            // S2C字节数
+    uint8_t   isTimeout;               // 是否已经超时
+};
+
 /**/
 class WXRelation
 {
diff --git a/wxcs/wxcs_server.cpp b/wxcs/wxcs_server.cpp
index 15119ce..4032ada 100644
--- a/wxcs/wxcs_server.cpp
+++ b/wxcs/wxcs_server.cpp
@@ -77,6 +77,10 @@ WxcsServer::WxcsServer(muduo::net::EventLoop *pLoop, uint16_t port, const std::s
                           CFG->GetValueOf<int>("SKYPE_HASH_TABLE_SIZE", SKYPE_HASH_TABLE_SIZE))
     , skypeTblWriter_(strTblDir, "skype", CFG->GetValueOf<int>("SKYPE_TBL_FILE_LINE", SKYPE_PER_TBL_LINE))
     , locSharingTblWrite_(strTblDir, "wxls", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))
+
+    , tencentMeetingSessionKeeper_(CFG->GetValueOf<int>("TENCENT_MEETING_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
+                                   CFG->GetValueOf<int>("TENCENT_MEETING_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
+    , tencentMeetingTblWriter_(strTblDir, "tencent_meeting", CFG->GetValueOf<int>("TENCENT_MEETING_TBL_FILE_LINE", WXA_PER_TBL_LINE))
     , addr_(port)
     , server_(pLoop, addr_, "wxcserver")
     , pProtoCodec_(NULL)
@@ -936,6 +940,63 @@ void WxcsServer::ProcessQQEvent(const unsigned char*pdata, int len)
     return;
 }
 
+void WxcsServer::ProcessTencentMeeting(const unsigned char*pdata, int len)
+{
+    if (len != sizeof(ST_TecentMeeting))
+    {
+        LOG_INTST->error("ProcessTencentMeeting len error, len={}, sizeof(ST_TecentMeeting)={}", len, sizeof(ST_TecentMeeting));
+        return;
+    }
+
+    ST_TecentMeeting *pMeetingMsg = (ST_TecentMeeting *)pdata;
+
+    // 创建腾讯会议Person对象
+    PersonPtr<WxcsTencentMeetingPerson> pPerson = std::make_shared<WxcsTencentMeetingPerson>(pMeetingMsg);
+
+    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
+    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && pPerson->wasUncrediableId())
+    {
+        LOG_DEF->debug("tencent meeting: drop uncredible person record {}", pPerson->getPrintablePersonMobileID());
+        return ;
+    }
+
+    // 是不是监视的 手机号 ?
+    bool bInterestingPerson = interestingMsisdnSet_.find(pPerson->getMsisdn()) != interestingMsisdnSet_.end();
+    if (bInterestingPerson)
+    {
+        LOG_INTST->warn("tencent meeting: found interesting person msg : {} of session {}",
+                        pPerson->getMsisdn(),
+                        pPerson->getPrintableSessionID());
+    }
+
+    // 查找或创建session（使用sessionId作为session标识）
+    auto pTencentMeetingSession = tencentMeetingSessionKeeper_.getSessionOf((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId));
+    if (!pTencentMeetingSession)
+    {   // 添加新 session,向该 session 中添加用户
+        SessionPtr<WxcsTencentMeetingPerson> newSession(new WxcsSession<WxcsTencentMeetingPerson>((char *)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId)
+                                                , WXCS_SESSION_NONE, pMeetingMsg->firstActiveTime));
+
+        newSession->addNewPerson(pPerson, bInterestingPerson);
+        tencentMeetingSessionKeeper_.addNewSession(newSession);
+        pTencentMeetingSession = newSession;
+    }
+    else
+    {   // session 存在，检查是否存在该用户
+        auto pPersonIn = pTencentMeetingSession->findPerson(pPerson);
+        if (!pPersonIn)
+        {   // 该用户不存在，添加新用户
+            pTencentMeetingSession->addNewPerson(pPerson, bInterestingPerson);
+        }
+        else
+        {   // 该用户存在，更新用户信息
+            pTencentMeetingSession->updatePersonInfo(pPerson, bInterestingPerson);
+        }
+    }
+
+    LOG_INTST->debug("ProcessTencentMeeting: sessionId={}, selfMeetingNum={}, selfLoginNum={}, msisdn={}",
+                     pMeetingMsg->sessionId, pMeetingMsg->selfMeetingNum, pMeetingMsg->selfLoginNum,
+                     pMeetingMsg->trailer.MSISDN);
+}
 
 /* 消息类型 分发器  */
 void WxcsServer::onMessage(const TcpConnectionPtr& conn _U_,
@@ -1006,6 +1067,9 @@ void WxcsServer::onMessage(const TcpConnectionPtr& conn _U_,
             WXLSKS->ProcessWXLS(pdata, len);
             break;
 
+        case WXCS_TENCENT_MEETING:
+            ProcessTencentMeeting(pdata, len);
+            break;
 
         default:
             return;
@@ -1025,6 +1089,7 @@ void WxcsServer::checkTblWriteTimeout()
     timerLoop_->runAfter(0, std::bind(&WxcsServer::QQVoipTblWriteTimeout, this));
     timerLoop_->runAfter(0, std::bind(&WxcsServer::SkypeTblWriteTimeout, this));
     timerLoop_->runAfter(0, std::bind(&WxcsServer::LocSharingTblWriteTimeout, this));
+    timerLoop_->runAfter(0, std::bind(&WxcsServer::TencentMeetingTblWriteTimeout, this));
 }
 
 
@@ -1067,6 +1132,11 @@ void WxcsServer::LocSharingTblWriteTimeout()
 {
     tblWriteTimeout(locSharingTblWrite_);
 }
+/*腾讯会议写TBL文件超时检测 */
+void WxcsServer::TencentMeetingTblWriteTimeout()
+{
+    tblWriteTimeout(tencentMeetingTblWriter_);
+}
 /*写TBL超时检测通用方法 */
 void WxcsServer::tblWriteTimeout(wxcsTblWriter& tblWriter)
 {
@@ -1112,6 +1182,7 @@ void WxcsServer::checkSessionTimeout()
     timerLoop_->runAfter(5 * checkInterval, std::bind(&WxcsServer::checkQQFileSessionTimeout, this));
     timerLoop_->runAfter(6 * checkInterval, std::bind(&WxcsServer::checkSkypeChatSessionTimeout, this));
     timerLoop_->runAfter(0 * checkInterval, std::bind(&WxcsServer::CheckLocSharingSessionTimeout, this));
+    timerLoop_->runAfter(7 * checkInterval, std::bind(&WxcsServer::checkTencentMeetingSessionTimeout, this));
 }
 
 void WxcsServer:: checkWXASessionTimeout()
@@ -1153,6 +1224,11 @@ void WxcsServer::CheckLocSharingSessionTimeout()
     WXLSKS->removeDeadSessions(std::bind(&WxcsServer::onRemveLocSharingSession, this, _1, _2));
 }
 
+void WxcsServer::checkTencentMeetingSessionTimeout()
+{
+    tencentMeetingSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemveTencentMeetingSession, this, _1, _2));
+}
+
 int WxcsServer::onRemveGroupHeadSession(uint32_t time _U_, const SessionPtr<WxcsGroupHeadPerson> & session)
 {
     wxghTblWriter_.writeToFile(session);
@@ -1422,6 +1498,38 @@ int WxcsServer::onRemveLocSharingSession(uint32_t time _U_, SessionPtr<WxcsLocSh
     return 0;
 }
 
+// 腾讯会议超时检测
+int WxcsServer::onRemveTencentMeetingSession(uint32_t time _U_, SessionPtr<WxcsTencentMeetingPerson> & session)
+{
+    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N个报文)
+    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
+    {
+        //判断报文个数
+        static uint32_t minCnt = CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20);
+        if(it->second->c2sPackCount < minCnt ||
+           it->second->s2cPackCount < minCnt )
+        {
+            LOG_DEF->debug("tencent meeting session: {}, invalid packet count, c2s: {}, s2c: {}", session->getSessionId(),
+                    it->second->c2sPackCount, it->second->s2cPackCount);
+            it = const_cast<SessionPtr<WxcsTencentMeetingPerson> &>(session)->personMap_.erase(it);
+        }
+        else // 这是一个正常的报文
+        {
+            it++;
+        }
+    }
+
+    // 有人吗 ?
+    if(session->personMap_.empty())
+    {
+        return 0;
+    }
+
+    // 剩下的都是 正常的Person 写入tbl
+    tencentMeetingTblWriter_.writeToFile(session);
+    return 0;
+}
+
 void WxcsServer::handleSignal(int signal)
 {
 	// printf("[%s][%d]receive signal: %d \n", __FILE__, __LINE__, signal);
diff --git a/wxcs/wxcs_server.h b/wxcs/wxcs_server.h
index 350ebb1..8c5a531 100644
--- a/wxcs/wxcs_server.h
+++ b/wxcs/wxcs_server.h
@@ -65,6 +65,7 @@ public:
     void checkQQFileSessionTimeout();
     void checkSkypeChatSessionTimeout();
     void CheckLocSharingSessionTimeout();
+    void checkTencentMeetingSessionTimeout();
 private:
    /*ADD_S by yangna 2020-09-16 */
     void checkWxcsTimeout();
@@ -77,6 +78,7 @@ private:
     void QQVoipTblWriteTimeout();
     void SkypeTblWriteTimeout();
     void LocSharingTblWriteTimeout();
+    void TencentMeetingTblWriteTimeout();
     void tblWriteTimeout(wxcsTblWriter& tblWriter);
 /*ADD_E by yangna 2020-09-16 */
 
@@ -104,6 +106,7 @@ private:
     void ProcessWXRelation(const unsigned char *pdata, int len);
     // void ProcessWXPeer(const unsigned char *pdata, int len);
     void ProcessQQEvent(const unsigned char*pdata, int len);
+    void ProcessTencentMeeting(const unsigned char*pdata, int len);
 
     int onRemveAudioSession(uint32_t time, SessionPtr<WxcsAudioPerson> & session);
     int onRemveZoomSession(uint32_t time, SessionPtr<WxcsZoomPerson> & session);
@@ -114,6 +117,7 @@ private:
     int onRemveQQFileSession(uint32_t time, const SessionPtr<WxcsQQFilePerson> & session);
     int onRemveSkypeChatSession(uint32_t time, SessionPtr<WxcsSkypePerson> & session);
     int onRemveLocSharingSession(uint32_t time, SessionPtr<WxcsLocSharingPerson> & session);
+    int onRemveTencentMeetingSession(uint32_t time, SessionPtr<WxcsTencentMeetingPerson> & session);
 
 private:
     WxcsContentWriter contentWriter_;
@@ -151,6 +155,10 @@ private: // sessions and tbl writer for skype
 private:
     wxcsTblWriter                           locSharingTblWrite_;
 
+private: // sessions and tbl writer for tencent meeting
+    WxcsSessionKeeper<WxcsTencentMeetingPerson> tencentMeetingSessionKeeper_;
+    wxcsTblWriter                           tencentMeetingTblWriter_;
+
 public:/*QQ活动事件处理 */
     WxcsQQEventPerson                       qqEventMap_;
 
diff --git a/wxcs/wxcs_session.cpp b/wxcs/wxcs_session.cpp
index 6f06465..2c308bc 100644
--- a/wxcs/wxcs_session.cpp
+++ b/wxcs/wxcs_session.cpp
@@ -1037,3 +1037,64 @@ std::string WxcsSessionBase<WxcsSkypePerson>::toStrRecordLine(char sep) const
 
     return strLine;
 }
+
+/* template specialization for WxcsTencentMeetingPerson */
+template<>
+std::string WxcsSessionBase<WxcsTencentMeetingPerson>::toStrRecordLine(char sep) const
+{
+    std::string strLine;
+
+    // 写入头部信息
+    CONS_RECORD_FIELD_TEXT(strLine, "tencent_meeting",               sep); // DevNO
+    CONS_RECORD_FIELD_TEXT(strLine, "001",                          sep); // LineNO
+    CONS_RECORD_FIELD_TIME(strLine, time(NULL),                     sep); // CapDate
+    CONS_RECORD_FIELD_TEXT(strLine, getPrintableSessionId(),        sep); // SessionID
+
+    // 统计信息
+    uint32_t totalC2SPackets = 0;
+    uint32_t totalC2SBytes = 0;
+    uint32_t totalS2CPackets = 0;
+    uint32_t totalS2CBytes = 0;
+    uint32_t firstActiveTime = 0;
+    uint32_t lastActiveTime = 0;
+
+    for (auto& kv : personMap_)
+    {
+        totalC2SPackets += kv.second->c2sPackCount;
+        totalC2SBytes += kv.second->c2sByteCount;
+        totalS2CPackets += kv.second->s2cPackCount;
+        totalS2CBytes += kv.second->s2cByteCount;
+
+        if (firstActiveTime == 0 || kv.second->firstActiveTime < firstActiveTime)
+            firstActiveTime = kv.second->firstActiveTime;
+        if (kv.second->lastActiveTime > lastActiveTime)
+            lastActiveTime = kv.second->lastActiveTime;
+    }
+
+    CONS_RECORD_FIELD_NUM(strLine, totalC2SPackets,                 sep); // C2S包数
+    CONS_RECORD_FIELD_NUM(strLine, totalC2SBytes,                   sep); // C2S字节数
+    CONS_RECORD_FIELD_NUM(strLine, totalS2CPackets,                 sep); // S2C包数
+    CONS_RECORD_FIELD_NUM(strLine, totalS2CBytes,                   sep); // S2C字节数
+    CONS_RECORD_FIELD_TIME(strLine, firstActiveTime,                sep); // 首次活跃时间
+    CONS_RECORD_FIELD_TIME(strLine, lastActiveTime,                 sep); // 最后活跃时间
+    CONS_RECORD_FIELD_NUM(strLine, personMap_.size(),               sep); // 参会人数
+
+    // 写入每个人的信息
+    int iChecked = 0;
+    for (auto& kv : personMap_)
+    {
+        if (iChecked >= SESSION_PERSON_COUNT_MAX)
+            break;
+
+        strLine += kv.second->toStrRecord(sep);
+        iChecked++;
+    }
+
+    // 人数不够, TBL 补齐
+    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
+    {
+        strLine += WxcsTencentMeetingPerson::toStrBlankRecord(sep);
+    }
+
+    return strLine;
+}
