/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_SESSION_H
#define WX_PROXY_SESSION_H

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

struct connection_t
{
    struct bufferevent       *bev;
    struct mirror_t          *mirror;
    void (*readcb) (struct bufferevent *bev, void *connection);
    void (*writecb)(struct bufferevent *bev, void *connection);
    void (*eventcb)(struct bufferevent *bev, short what, void *connection);
};

struct mirror_t
{
    struct connection_t        *master;
    struct connection_t        *slave_1;
    struct connection_t        *slave_2;
    struct event_base          *base;
    struct thread_info         *thread;
    struct context             *ctx;
};

#ifdef __cplusplus
extern "C" {
#endif

struct mirror_t*
mirror_create(struct context *ctx);


struct connection_t* connection_malloc();
void connection_free(struct connection_t *connection);

void
mirror_free(struct mirror_t *mirror);

#ifdef __cplusplus
}
#endif

#endif

