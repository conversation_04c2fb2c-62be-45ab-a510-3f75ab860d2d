#include <iostream>
#include <map>
#include <unordered_map>
#include <thread>
#include <chrono>
#include <mutex>
#include <set>

#include <cstring>

#include "dpi_wxpay.h"

typedef struct {
  std::unordered_map<std::string, void *> wxpay_map;
  FreeFunc free_func;
  RmFunc   remove_func;
  std::mutex mutex;
}WxpayMgr;

WxpayMgr g_wxpay_mgr;


void *wxpay_manager_thread(void)
{
  char str[256] = { 0 };
  std::set<std::string> key_list;
  while (1) {
    std::this_thread::sleep_for(std::chrono::seconds(5));

    std::lock_guard<std::mutex> guard(g_wxpay_mgr.mutex);

    auto it = g_wxpay_mgr.wxpay_map.begin();
    while (it != g_wxpay_mgr.wxpay_map.end()) {
      // std::cout << "ip = " << it->first << std::endl;
      strcpy(str, it->first.c_str());
      int flag = g_wxpay_mgr.remove_func(str, it->second, NULL);
      if (flag != 0) {
        g_wxpay_mgr.free_func(it->second);
        it = g_wxpay_mgr.wxpay_map.erase(it);
      } else {
        ++it;
      }
    }

    // for (auto & m : g_wxpay_mgr.wxpay_map) {
    //   strcpy(str, m.first.c_str());
    //   int flag = g_wxpay_mgr.remove_func(str, m.second, NULL);
    //   if (flag != 0) {
    //     key_list.insert(m.first);
    //   }
    // }

    // for (auto &s : key_list) {

    // }

    // std::cout << "manager thread!!!" << std::endl;
  }

  return nullptr;
}

void *wxpay_manager_find(void *key) {
  if (key == nullptr) {
    return nullptr;
  }
  std::string key_str = (char *)key;
  auto find = g_wxpay_mgr.wxpay_map.find(key_str);
  if (find != g_wxpay_mgr.wxpay_map.end()) {
    return find->second;
  }
  return nullptr;
}

void wxpay_manager_add(void *key, void *data)
{
  std::string k = (char *)key;
  std::lock_guard<std::mutex> guard(g_wxpay_mgr.mutex);
  g_wxpay_mgr.wxpay_map.insert({k, data});
}

void wxpay_manager_init(RmFunc r_func, FreeFunc f_func)
{
  g_wxpay_mgr.remove_func = r_func;
  g_wxpay_mgr.free_func = f_func;

  std::thread th(wxpay_manager_thread);
  th.detach();
}