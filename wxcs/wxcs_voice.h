/****************************************************************************************
 * 文 件 名 : wxcs_voice.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-05-10
* 编    码 : chenzq         '2021-05-10
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_VOICE_H
#define _WXCS_VOICE_H

#include <memory>

#include "wxcs_voice_peers.h"
#include "wxcs_session_keeper.h"

class WxcsAudioPerson;
WxcsAudioPerson operator+(const WxcsAudioPerson &lhs, const WxcsAudioPerson &rhs);

/* WxcsAudioPerson */
class WxcsAudioPerson : public WxcsPerson
{
public:
    friend WxcsAudioPerson operator+(const WxcsAudioPerson &lhs, const WxcsAudioPerson &rhs);
public:
    WxcsAudioPerson();
    WxcsAudioPerson(ST_wxAudioSessionAlive *pAliveMsg);

    uint64_t getPersonID() const override;

public:
    void Update(const std::shared_ptr<WxcsAudioPerson> &);

public:
    uint32_t            getC2STransPackets() const;
    uint32_t            getS2CTransPackets() const;
    virtual std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

    inline void         SetWxId(std::string wxid) { wxId = wxid; }
    inline void         SetWxUin(std::string uin) { wxUin = uin; }
    inline std::string  GetPeersList() { return peersinfo_.peerlist; }
    inline std::string  GetPeers4GIP() { return peersinfo_.ipfor4G; }
    inline uint8_t      GetSessionType() { return SessionType; }
    inline uint32_t     GetC2SD5Pkts() const { return c2sd5pkts_; }
    inline uint32_t     GetS2CD5Pkts() const { return s2cd5pkts_; }
    inline void         SetPeersInfo(PersonPeersInfo &_info) { peersinfo_ = _info; }
    void                SetResvInfo(std::vector<std::shared_ptr<WxcsAudioPerson>> &);

    inline uint32_t     GetStartTime() { return this->StartTime; }

    void  UpdateResvInfo(std::shared_ptr<WxcsAudioPerson> person);
    void  DebugPrint();

public:
    WxcsAudioPerson & operator+=(const WxcsAudioPerson &rhs);

public:
    uint32_t  PersonS2CUnknown;        // 流中包含的未识别的包数
    uint32_t  PersonC2SUnknown;        // 流中包含的未识别的包数
    uint32_t  PersonC2S_pkt_tcp;       // TCP 报文 块数
    uint32_t  PersonS2C_pkt_tcp;       // TCP 报文 块数
    uint32_t  Person_pkt_75;           // 前缀为75 报文数
    uint32_t  Person_pkt_76;           // 前缀为76 报文数
    uint32_t  Person_pkt_77;           // 前缀为77 报文数
    uint32_t  Person_pkt_95;           // 前缀为95 报文数
    uint32_t  Person_pkt_96;           // 前缀为96 报文数
    uint32_t  Person_pkt_97;           // 前缀为97 报文数
    uint32_t  Person_pkt_98;           // 前缀为98 报文数
    uint32_t  Person_pkt_drop;         // 异常     报文数

    uint32_t  StartTime;               // 用户首次出现的时间
    uint32_t  Calling = CALLTYPE_UNSET;// 是否为主叫
    uint32_t  C2STransPackets;         // 用户传输到服务器报文总数
    uint32_t  S2CTransPackets;         // 服务器传输到用户报文总数
    uint32_t  C2SVideoPackets;         // 用户传输到服务器视频报文数量s
    uint32_t  S2CVideoPackets;         // 服务器传输到用户视频报文数量
    uint32_t  C2STransBytes;           // 用户传输到服务器字节总数
    uint32_t  S2CTransBytes;           // 服务器传输到用户字节总数
    uint32_t  IsAnswered;              // 是否应答
    uint32_t  RingTime;                // 响铃时长
    uint32_t  isTimeout;               // 是否已经超时

    uint32_t  callflag_96_0_;
    uint32_t  callflag_96_1_;
    uint32_t  callflag_98_0_;
    uint32_t  callflag_98_1_;

    uint32_t    c2spkts_;
    uint32_t    s2cpkts_;
    uint32_t    c2sd5pkts_;
    uint32_t    s2cd5pkts_;
    uint32_t    c2svoicepkts_;
    uint32_t    s2cvoicepkts_;
    uint32_t    c2svideopkts_;
    uint32_t    s2cvideopkts_;

    std::string callflag_;
    std::string flowflag_;

    std::string wxId;
    std::string wxUin;

    PersonPeersInfo peersinfo_;         // wx 对端 ip

    std::string reserv_;
    std::string reserv1_;
    std::string reserv2_;

private:
    std::string resv_clientip_;
    std::string resv_serverip_;
    std::string resv_serverport_;
    std::string resv_clientport_;
    std::string resv_callflag_;
    std::string resv_flowflag_;
    uint8_t     resv_flownum_ = 0;
    std::string resv_c2spkt_;
    std::string resv_s2cpkt_;
    std::string resv_c2sbyte_;
    std::string resv_s2cbyte_;
};

using MulitPerson = std::map<std::uint64_t, std::vector<std::shared_ptr<WxcsAudioPerson>>>;
// WxcsSession for WxcsAudioPerson
template<>
class WxcsSession<WxcsAudioPerson> : public WxcsSessionBase<WxcsAudioPerson>
{
public:
  using WxcsSessionBase<WxcsAudioPerson>::WxcsSessionBase;

public:
    struct MonitorFlags {
        bool msisdn = false;
        bool imsi = false;
        bool imei = false;

        MonitorFlags() {}
        MonitorFlags(bool ms, bool im, bool ie) : msisdn(ms), imsi(im), imei(ie) {}

        bool HashAnyFlag() const { return msisdn || imsi || imei; }
        bool HashAllFlag() const { return msisdn && imsi && imei; }
    };

    void LogMonitorInfo(std::shared_ptr<WxcsAudioPerson> &,
                        const MonitorFlags &,
                        const std::string &) const;

private:
    MonitorFlags CheckMonitorFlags(const PersonPtr<WxcsAudioPerson> & person ) const
    {
        return MonitorFlags(
            // interestingmsisdn_.count(person->getMsisdn()) > 0,
            interestingmsisdn_.find(person->getMsisdn()) != interestingmsisdn_.end(),
            interestingimsi_.find(person->getImsi())     != interestingimsi_.end(),
            interestingimei_.find(person->getImei())     != interestingimei_.end()
        );
    }


public:
    int updatePersonInfo(const PersonPtr<WxcsAudioPerson> &, bool);
    void OnRemove();
    void PreProcess();
    void Combine();
    void DealPersonInfo();
    void WashData();
    void DealSubInfo();

    bool CheckOutput();

private:
    void VerifyCall();
    void ProcessCallRoles();

public:
    // 特化基类方法的满足不了需求，这里选择在子类重写
    std::string toStrRecordLine(char sep) const;
public:
    void PersonsCombine(MulitPerson & _mulitperson) const
    {
        for (auto & person : personMap_) {
            auto clientip = person.second->getSrcIP();
            _mulitperson[clientip].push_back(person.second);
        }
    }
private:
    std::map<uint64_t, std::shared_ptr<WxcsAudioPerson> > real_person_;

private:
    mutable uint8_t     isvideo_ = 0;
    mutable uint8_t     isgroup_ = 0;
    mutable std::string calling_;
    mutable std::string called_;
    mutable uint32_t    starttime_ = 0;
    mutable uint32_t    stoptime_ = 0;
    mutable uint8_t     isanswed_ = 0;
    mutable uint8_t     ringtime_ = 0;
    mutable uint32_t    durationtime_ = 0;
    mutable uint8_t     personcount_ = 0;
    mutable std::string personlist_;
    mutable std::string  pairsucc_;

public:
    std::set<uint64_t>      interestingmsisdn_;
    std::set<uint64_t>      interestingimsi_;
    std::set<uint64_t>      interestingimei_;

};

class WxcsAudioSessionKeeper : public WxcsSessionKeeper<WxcsAudioPerson>
{
public:
    ~WxcsAudioSessionKeeper() {}
    WxcsAudioSessionKeeper(const WxcsAudioSessionKeeper &) = delete;
    WxcsAudioSessionKeeper & operator=(const WxcsAudioSessionKeeper &) = delete;

    static std::shared_ptr<WxcsAudioSessionKeeper> & GetInstance() {
        static std::shared_ptr<WxcsAudioSessionKeeper> keeper(new WxcsAudioSessionKeeper);
        return keeper;
    }

private:
    WxcsAudioSessionKeeper()
      : WxcsSessionKeeper<WxcsAudioPerson>(CFG->GetValueOf<int>("WXA_SESSION_TIMEOUT_IN_SECOND", 90),
          CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", 5 * 1024 * 1024)) {

        // 把配置文件中的 MSISDN到 监视的 MSISDN
        for (auto msisdn : CFG->getInterestingMsisdn())
        {
            interestmsisdn_.emplace(msisdn);
        }
    }

public:
    void ProcessWXPeer(const unsigned char *pdata, int len);

public:
    inline WXPeers & GetWxPeers() { return wx_peers_; }
    bool GetPeersInfo(std::string & _clientip, uint64_t msisdn, TimeRange & _range, PersonPeersInfo & _peersinfo);
private:
    WXPeers                 wx_peers_;
    std::set<uint64_t>      interestmsisdn_;
};

#define WXAKS         WxcsAudioSessionKeeper::GetInstance()

#endif