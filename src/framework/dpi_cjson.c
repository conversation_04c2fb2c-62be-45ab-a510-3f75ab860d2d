#include "dpi_cjson.h"

char* dpi_cjson_get_string_field(cJSON* json_obj, const char* field_name) {
  cJSON* field = cJSON_GetObjectItemCaseSensitive(json_obj, field_name);
  if (cJSON_IsString(field)) {
    return strdup(field->valuestring);
  }
  return NULL;
}

int dpi_cjson_get_int_field(cJSON* json_obj, const char* field_name, int defult_value) {
  cJSON* field = cJSON_GetObjectItemCaseSensitive(json_obj, field_name);
  if (cJSON_IsNumber(field)) {
    return field->valueint;
  }
  return defult_value;
}

cJSON* dpi_cjson_get_object_field(cJSON* json_obj, const char* field_name) {
  cJSON* field = cJSON_GetObjectItemCaseSensitive(json_obj, field_name);
  if (cJSON_IsObject(field)) {
    return field;
  }
  return NULL;
}

cJSON* dpi_cjson_parse_json(const char* json_str) { return cJSON_Parse(json_str); }

void dpi_cjson_free_json(cJSON* json_obj) { cJSON_Delete(json_obj); }