#include <iostream>
#include <fstream>

#include "tbl_reader.h"
#include "tbl_scanner.h"
#include "tbl_logger.h"
#include "tbl_statistics.h"

std::queue<std::tuple<std::string, std::string, FILE*>> TblReader::tbl_file_queue;
//std::map<std::string, TblWorker *> TblReader::worker_manager;

std::mutex TblReader::mtx_;

TblReader::TblReader()
{

}

TblReader::~TblReader()
{

}

int TblReader::readTbl(std::vector<std::string> & rst, std::string& proto) // 一次读一个文件，按行读取
{

    FILE * tbl_stream_;

    char* tbl = nullptr;
    size_t tbl_len = 0;

    std::lock_guard<std::mutex> lock(mtx_);

    if(tbl_file_queue.empty())
    {
        return 0;
    }

    tbl_stream_ = std::get<2>(tbl_file_queue.front());

    if(tbl_stream_ == nullptr)
    {
        return 0;
    }

    while(getline(&tbl, &tbl_len, tbl_stream_) != -1)
    {   
        std::string tmp_str(tbl, tbl_len);

        if(tmp_str.find_first_of('\n') != std::string::npos)
        {
            tmp_str.erase(tmp_str.find_first_of('\n'));
        }
        rst.push_back(tmp_str);
    }

    free(tbl);

    proto = std::get<0>(tbl_file_queue.front());

    LOG_ST.addRead(proto);

    TblScanner::getInstance().shouldDeleteTbl(std::get<1>(tbl_file_queue.front()).c_str());

    tbl_file_queue.pop(); 

    return 1;
}

int TblReader::addTblName(std::string && proto_name, std::string && tbl_name, FILE *fp) // 添加tbl路径，一次一条
{
    tbl_file_queue.push(std::make_tuple(proto_name, tbl_name, fp));
    return 0;
}

int TblReader::work(TblWorkerManager * worker_manager)
{
    if(nullptr == worker_manager)
    {
        return -1;
    }
    std::vector<std::string>  rst_;
    std::string proto;
    if(readTbl(rst_, proto) > 0)
    {
        // worker_manager[proto]->setTblData(rst_);
        // worker_manager[proto]->work();
        worker_manager->setProtoAndData(proto, rst_);
    }
    return 0;
}
