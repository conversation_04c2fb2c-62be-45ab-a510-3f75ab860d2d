#include "dpi_wx_voice_peers.h"


#include <glib.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"

#include "ip2region.h"

#include "rte_memcpy.h"



extern struct global_config g_config;
void *g_phone_watch = NULL;

uint8_t             g_ip2reg_isload = 0;
ip2region_entry     g_ip2reg_entry;
wxc_handle          g_wx_peers_handle = NULL;

typedef struct {
    uint8_t     ip_version;
    uint8_t     first_flag;     // 是否第一次发送 a3
    uint16_t    a3_count;
    union { uint32_t ipv4; char ipv6[16]; } client_ip;
    union { uint32_t ipv4; char ipv6[16]; } peer_ip;

    uint8_t     c2scnt;     // 包计数
    uint8_t     s2ccnt;
    uint8_t     c2sttl;     // 双向ttl
    uint8_t     s2cttl;
    uint8_t     cnt;

}WxPeerSession;


static void * init_peers_flow_session(struct flow_info *flow);
static void peer_flow_timeout(dpi_flow_info_t * flow, void * app_session);

static void* phone_watch_init(void)
{
    char  phone_number[16];
    int   safelen;
    void* hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if(NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    if(0 != memcmp(g_config.wx_phone_watch, "null", 4))
    {
        int  i   = 0;
        char*p   = (char*)g_config.wx_phone_watch;
        int size = sizeof(g_config.wx_phone_watch);
        printf("wx_peers 监视手机列表[%s]\n", g_config.wx_phone_watch);

        char *pLeft  = p;
        char *pRight = pLeft;
        while('\0' != *p)
        {
            pRight = strchr(pLeft, ',');
            if(NULL == pRight)
            {
                // 探测是不是只有一个手机号，末尾没有逗号
                int len = strlen(pLeft);
                if(len >= 13 && len <15) //一个手机号就是13个字符
                {
                    char *pStr = pLeft;
                    printf("wx_peers 监视手机[%s]\n", pStr);
                    g_hash_table_insert((GHashTable *)hash, g_strdup(pStr), g_strdup(pStr));
                }

                break;
            }
            safelen = sizeof(phone_number) < (unsigned)(pRight - pLeft) ? sizeof(phone_number) : (unsigned)(pRight - pLeft);
            memset(phone_number, 0, sizeof(phone_number));
            memcpy(phone_number, pLeft, safelen);
            printf("wx_peers 监视手机[%s]\n", phone_number);
            g_hash_table_insert((GHashTable *)hash, g_strdup(phone_number), g_strdup(phone_number));
            pLeft = ++pRight;
        }
    }
    else
    {
        return NULL;
    }
    return hash;
}

static int phone_watch_find(void *hash, const char *pStr)
{
    if(NULL == hash)
    {
        return -1;
    }

    gpointer p = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if(NULL == p)
    {
        return -1;
    }
    return 1;
}

static void phone_watch_fini(void *hash)
{
    if(NULL == hash)
    {
        return;
    }
    g_hash_table_destroy((GHashTable *)hash);
}


static void
debug_interest(ST_WXPEERS * value, uint8_t is_timeout)
{
    char phone_num[20];
    snprintf(phone_num, 20, "%ld", value->trailer.MSISDN);
    if(1 != phone_watch_find(g_phone_watch, phone_num)) {
        return;
    }

    char client_ip[64];
    char peer_ip[64];

    uint8_t af = value->ip_version == 4 ? AF_INET : AF_INET6;
    inet_ntop(af, value->client_ip.ipv6, client_ip, sizeof(client_ip));
    inet_ntop(af, value->peer_ip.ipv6, peer_ip, sizeof(peer_ip));

    printf("--------------------- interest peer ip -----------------------------\n");
    printf("interest_phone  :%s\n", phone_num);
    printf("clientip        :%s\n", client_ip);
    printf("peer_ip         :%s\n", peer_ip);
    printf("location        :%s\n", value->location);
    printf("a3_count        :%d\n", value->a3_count);
    printf("解析对端        :%s\n", is_timeout ? "yes" : "no");
    printf("--------------------- interest peer ip -----------------------------\n");
}


static int
is_private_ip(uint32_t ipv4)
{
    uint32_t ip4 = ntohl(ipv4);
    if( ((ip4 & 0xFFF00000) == 0xac100000)    //********** ~ **************
       || ((ip4 & 0xFFFF0000) == 0xc0a80000))   //*********** ~ ***************
    {
        return 1;
    }
    return 0;
}

static int
is_4G_ip(uint32_t ipv4)
{
    uint32_t ip4 = ntohl(ipv4);
    if (((ip4 & 0xFF000000) == 0x0a000000)    //10.0.0.0 ~ **************
      || ((ip4 & 0xFF000000) == 0x64000000)     //100.0.0.0 ~ ***************
    ) {
        return 1;
    }
    return 0;
}

static void
peer_str_replace(dpi_str_t * str, const char c, const char d) {
    size_t i = 0;
    for (; i < str->len; ++i) {
        if (str->data[i] == '\0') {
            break;
        }
        if (str->data[i] == c) {
            str->data[i] = d;
        }
    }

    return;
}

static int
ip_to_loaction(dpi_str_t * loc, uint32_t ipv4) {
    datablock_entry datablock;
    memset(&datablock, 0, sizeof(datablock_entry));
    // ntohl(ip4)
    ip2region_memory_search(&g_ip2reg_entry, ntohl(ipv4), &datablock);
    // printf("++++++++ %s\n", datablock.region);
    strcpy((char *)loc->data, datablock.region);
    peer_str_replace(loc, '|', ',');

    return 0;
}

static int
location_split(char *loc, ST_WXPEERS * value) {
    char locs[6][64] = {{ 0 }};
    char strtokbuff[256] = { 0 };
    char * buff = strtokbuff;
    int i = 0;
    char delim[] = ",";

    char *ptr = strtok_r(loc, delim, &buff);
    while (ptr != NULL) {
        strcpy(locs[i], ptr);
        ptr = strtok_r(NULL, delim, &buff);
        i++;
    }

    if (i < 3) {
        return 0;
    }
    strcpy(value->country, locs[0]);
    strcpy(value->province, locs[2]);


    return 0;
}

static void
get_peer_loc(uint32_t _ipv4, uint8_t _ipver, ST_WXPEERS * _value)
{
    if (_value == NULL) {
        return;
    }

    if (_ipver != 4) {
        return;
    }


    dpi_str_t location;
    location.data = (uint8_t *)_value->location;
    location.len = sizeof(_value->location);



    if (is_private_ip(_ipv4)) {
        _value->ip_type = 1;      // 0 公网  1 wifi  2 4G
    } else if (is_4G_ip(_ipv4)) {
        _value->ip_type = 2;      // 0 公网  1 wifi  2 4G
    } else {
        _value->ip_type = 0;      // 0 公网  1 wifi  2 4G
    }

    if (g_ip2reg_isload &&  _value->ip_type == 0) {
        char tmp_loc[256] = { 0 };
        ip_to_loaction(&location, _ipv4);
        // rte_memcpy(tmp_loc, location.data, strlen(location.data));
        strcpy(tmp_loc, (char *)location.data);
        location_split(tmp_loc, _value);
    }

}

void peer_flow_timeout(dpi_flow_info_t * flow, void * app_session)
{
    ST_WXPEERS value;
    memset(&value, 0, sizeof(ST_WXPEERS));
    WxPeerSession * peer_session = (WxPeerSession *)app_session;

    // 双向 a3 无法判定对端
    // 通过 ttl 跳跃次数最少的认为是本端，因此先统计前 10 个包是否为双向
    // 再判定那个方向的 ttl与64的差值最小
    if (peer_session->c2scnt > 0 && peer_session->s2ccnt > 0) {
        int8_t near = abs(peer_session->c2sttl - 64) - abs(peer_session->s2cttl - 64);

        // 与 ttl 64 相聚最近的为本端 ip
        // 若 ttl 相等，判定包数，a3 包数多的那一向为本端
        if (near > 0) {
            memcpy(value.client_ip.ipv6, flow->pkt.dst_ip.ipv6, 16);
            memcpy(value.peer_ip.ipv6, flow->pkt.src_ip.ipv6, 16);
            value.peerport = ntohs(flow->pkt.src_port);
        } else if (near < 0) {
            memcpy(value.client_ip.ipv6, flow->pkt.src_ip.ipv6, 16);
            memcpy(value.peer_ip.ipv6, flow->pkt.dst_ip.ipv6, 16);
            value.peerport = ntohs(flow->pkt.dst_port);
        } else {
            if (peer_session->c2scnt > peer_session->s2ccnt) {
                memcpy(value.client_ip.ipv6, flow->pkt.src_ip.ipv6, 16);
                memcpy(value.peer_ip.ipv6, flow->pkt.dst_ip.ipv6, 16);
                value.peerport = ntohs(flow->pkt.dst_port);
            } else if (peer_session->c2scnt < peer_session->s2ccnt) {
                memcpy(value.client_ip.ipv6, flow->pkt.dst_ip.ipv6, 16);
                memcpy(value.peer_ip.ipv6, flow->pkt.src_ip.ipv6, 16);
                value.peerport = ntohs(flow->pkt.src_port);
            } else {
                // ttl 相同，包数相同，退出
                return;
            }
        }

        // 解析地理位置
        get_peer_loc(value.peer_ip.ipv4, flow->pkt.ip_ver, &value);


        char tmpsrc[64] = {0};
        char tmpdct[64] = {0};
        if (flow->pkt.ip_ver == 4) {
            get_ip4string(tmpsrc, sizeof(tmpsrc), value.client_ip.ipv4);
            get_ip4string(tmpdct, sizeof(tmpdct), value.peer_ip.ipv4);
        } else {
            get_ip6string(tmpsrc, sizeof(tmpsrc), (const uint8_t *)value.client_ip.ipv6);
            get_ip6string(tmpdct, sizeof(tmpdct), (const uint8_t *)value.peer_ip.ipv6);
        }
        snprintf(value.ttlstr, sizeof(value.ttlstr), "%s:%d,%s:%d", tmpsrc, peer_session->c2sttl, tmpdct, peer_session->s2cttl);
    } else {
        if (flow->pkt.ip_ver == 4) {
            get_ip4string(value.peeripstr, sizeof(value.peeripstr), flow->pkt.dst_ip.ipv4);
            get_ip4string(value.clientipstr, sizeof(value.clientipstr), flow->pkt.src_ip.ipv4);
        } else {
            get_ip6string(value.peeripstr, sizeof(value.peeripstr), (const uint8_t *)flow->pkt.dst_ip.ipv6);
            get_ip6string(value.clientipstr, sizeof(value.clientipstr), (const uint8_t *)flow->pkt.src_ip.ipv6);
        }

        value.peerport = ntohs(flow->pkt.dst_port);
    }


    value.ip_version = flow->pkt.ip_ver;
    memcpy(value.client_ip.ipv6, flow->pkt.src_ip.ipv6, 16);
    memcpy(value.peer_ip.ipv6, flow->pkt.dst_ip.ipv6, 16);

    // 设置建联 相关 参数
    dpi_TrailerParser(&value.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
    dpi_TrailerGetMAC(&value.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&value.trailer, (const char *)flow->ethhdr);

    dpi_TrailerSetDev(&value.trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&value.trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&value.trailer, g_config.devArea);       // 设定 地域名
    dpi_TrailerUpdateTS(&value.trailer);
    value.is_timeout = 1;
    value.a3_count = peer_session->a3_count;

    wxc_sendMsg(g_wx_peers_handle, (const unsigned char*)&value, sizeof(ST_WXPEERS), WXCS_WX_PEERS);

    debug_interest(&value, 0);

    // char clientip_[64] = {0};
    // inet_ntop(AF_INET, flow->pkt.dst_ip.ipv6, clientip_, 64);
    // printf("ip=>>%s, num==>%d\n", clientip_, value.a3_count);
}


void * init_peers_flow_session(struct flow_info *flow)
{
    if(NULL == flow) {
        return NULL;
    }

    if(NULL == flow->app_session) {
        WxPeerSession* peer_session = malloc(sizeof(WxPeerSession));
        if(NULL == peer_session) {
            DPI_LOG(DPI_LOG_ERROR, "error on malooc WxPeerSession");
            exit(-1);
        }
        memset(peer_session, 0, sizeof(WxPeerSession));
        flow->app_session = peer_session;
        flow->flow_EOF = peer_flow_timeout;
    }

    return flow->app_session;
}

static int
dissect_weixin_peers(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
    const uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_WX_PEERS] == 0) {
        return 0;
    }


    uint8_t prefix = 0;
    uint16_t offset = 0;
    uint32_t identify = 0;
    uint16_t suffix = 0;

    // --------------------------- 识别  ---------------------------------------
    // 识别函数的逻辑要在解析函数里面重新走一遍
    // 对端ip识别成功之后，只操作ip层的内容，框架层协议识别是基于流识别的
    // 如果不在解析层识别就会导致流中其他帧被误统计到对端数据中
    if (payload_len < DPI_WX_PEERS_DATA_MINLEN || payload_len > DPI_WX_PEERS_DATA_MAXLEN) {
        return 0;
    }

    if (get_uint8_t(payload, offset) != DPI_WX_PEERS_PERFIX) {
        return 0;
    }
    offset += 1;

    if (get_uint8_t(payload, offset + 11) == DPI_WX_PEERS_FLAG) {
        offset += 11;
    } else if(get_uint8_t(payload, offset + 12) == DPI_WX_PEERS_FLAG) {
        offset += 12;
    } else {
        return 0;
    }
    // unknown bytes
    //

    identify = get_uint32_ntohl(payload, offset);
    if (identify != DPI_WX_PEERS_FLAG0 && identify != DPI_WX_PEERS_FLAG1) {
        return 0;
    }

    offset = payload_len - 2;
    if (get_uint16_ntohs(payload, offset) != DPI_WX_PEERS_SUFFIX) {
        return 0;
    }

    init_peers_flow_session(flow);
    WxPeerSession *peer_session = (WxPeerSession *)flow->app_session;

    if (peer_session->first_flag == 1) {
        peer_session->a3_count++;
        return 0;
    }
    // --------------------------- 识别  ---------------------------------------

    // 前十个包用于判定
    if (peer_session->cnt <=10) {
        if (direction) {
            peer_session->s2ccnt++;
            peer_session->s2cttl = peer_session->s2cttl > flow->ttl ? peer_session->s2cttl : flow->ttl;
        } else {
            peer_session->c2scnt++;
            peer_session->c2sttl = peer_session->c2sttl > flow->ttl ? peer_session->c2sttl : flow->ttl;
        }

        peer_session->cnt++;
        return 0;
    }

    // 如果是双向流的话，在超时入口处理数据(有的双向流里面 a3 包可能不够10个，走不到后面的逻辑)
    if (peer_session->c2scnt > 0 && peer_session->s2ccnt > 0) {
        return 0;
    }


    ST_WXPEERS value;
    memset(&value, 0, sizeof(ST_WXPEERS));
    dpi_str_t location;
    location.data = (uint8_t *)value.location;
    location.len = sizeof(value.location);
    union { uint32_t  ipv4; char ipv6[16]; } clientip;
    union { uint32_t  ipv4; char ipv6[16]; } peerip;


    memcpy(clientip.ipv6, flow->pkt.src_ip.ipv6, 16);
    memcpy(peerip.ipv6, flow->pkt.dst_ip.ipv6, 16);
    value.peerport = ntohs(flow->pkt.dst_port);


    if (flow->pkt.ip_ver == 4) {
        get_ip4string(value.peeripstr, sizeof(value.peeripstr), peerip.ipv4);
        get_ip4string(value.clientipstr, sizeof(value.clientipstr), clientip.ipv4);
    } else {
        get_ip6string(value.peeripstr, sizeof(value.peeripstr), (const uint8_t *)peerip.ipv6);
        get_ip6string(value.clientipstr, sizeof(value.clientipstr), (const uint8_t *)clientip.ipv6);
    }

    // 对端 ip 解析地理位置
    get_peer_loc(peerip.ipv4, flow->pkt.ip_ver, &value);

    value.ip_version = flow->pkt.ip_ver;
    memcpy(value.client_ip.ipv6, clientip.ipv6, 16);
    memcpy(value.peer_ip.ipv6, peerip.ipv6, 16);
    value.a3_count = peer_session->cnt;

    // 添加第一次发送标记，当前流后续报文只需更新 a3 数量即可
    peer_session->a3_count = peer_session->cnt;


    // 设置建联 相关 参数
    dpi_TrailerParser(&value.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
    dpi_TrailerGetMAC(&value.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&value.trailer, (const char *)flow->ethhdr);

    dpi_TrailerSetDev(&value.trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&value.trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&value.trailer, g_config.devArea);       // 设定 地域名
    dpi_TrailerUpdateTS(&value.trailer);


    // char clientIP_[64] = {0};
    // int af = flow->pkt.ip_ver == 4 ? AF_INET : AF_INET6;
    // inet_ntop(af, flow->pkt.dst_ip.ipv6, clientIP_, 64);
    // printf("location=>%s,country=>%s,province=>%s\n", value.location, value.country, value.province);
    // printf("ip_type=>%d,ip=>%s\n", value.ip_type, clientIP_);

    wxc_sendMsg(g_wx_peers_handle, (const unsigned char*)&value, sizeof(ST_WXPEERS), WXCS_WX_PEERS);

    if (peer_session->first_flag != 1) {
        peer_session->first_flag = 1;
        debug_interest(&value, 1);
    }

    return 0;
}


static
void identify_weixin_peers(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_WX_PEERS] == 0) {
        return;
    }

    uint8_t prefix = 0;
    uint16_t offset = 0;
    uint32_t flag = 0;
    uint16_t suffix = 0;

    if (payload_len < DPI_WX_PEERS_DATA_MINLEN || payload_len > DPI_WX_PEERS_DATA_MAXLEN) {
        return;
    }

    if (get_uint8_t(payload, offset) != DPI_WX_PEERS_PERFIX) {
        return;
    }
    offset += 1;

    if (get_uint8_t(payload, offset + 11) == DPI_WX_PEERS_FLAG) {
        offset += 11;
    } else if(get_uint8_t(payload, offset + 12) == DPI_WX_PEERS_FLAG) {
        offset += 12;
    } else {
        return;
    }
    // unknown bytes
    //

    flag = get_uint32_ntohl(payload, offset);
    if (flag != DPI_WX_PEERS_FLAG0 && flag != DPI_WX_PEERS_FLAG1) {
        return;
    }

    offset = payload_len - 2;
    if (get_uint16_ntohs(payload, offset) != DPI_WX_PEERS_SUFFIX) {
        return;
    }

    flow->real_protocol_id = PROTOCOL_WX_PEERS;

    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_WX_PEERS);
    return;
}

void init_weixin_peers_dissector(void)
{

    udp_detection_array[PROTOCOL_WX_PEERS].proto         = PROTOCOL_WX_PEERS;
    udp_detection_array[PROTOCOL_WX_PEERS].identify_func = identify_weixin_peers;
    udp_detection_array[PROTOCOL_WX_PEERS].dissect_func  = dissect_weixin_peers;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WX_PEERS].excluded_protocol_bitmask, PROTOCOL_WX_PEERS);

    return;
}

void init_wx_peers(void)
{
    if (NULL == g_wx_peers_handle) {
        wxc_init(&g_wx_peers_handle, g_config.wx_rela_ip,  g_config.wx_rela_port);
    }

    g_ip2reg_isload = ip2region_create(&g_ip2reg_entry, "./ip2region.db");
    datablock_entry entry;
    // 初始化的时候需要查询一次，接口中有空值判断，再多线中可能会有竞争问题
    ip2region_memory_search(&g_ip2reg_entry, 0, &entry);

    g_phone_watch = phone_watch_init();

    return;
}