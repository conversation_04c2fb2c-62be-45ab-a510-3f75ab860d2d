/****************************************************************************************
 * 文 件 名 : dpi_log.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdarg.h>

#include "dpi_log.h"

void dpi_debug_printf(struct file_line_info *info, dpi_log_level_t log_level, const char *format, ...)
{
	va_list args;
	const char *level;
	char str[512];
	char time_now[64] = {0};
	char log[1024];

	if (log_level == DPI_LOG_DEBUG)
		level = "DEBUG";
	else if (log_level == DPI_LOG_WARNING)
		level = "WARNING";
	else if (log_level == DPI_LOG_ERROR)
		level = "ERROR";
	else
		return;
	
	get_now_datetime(time_now, sizeof(time_now));
	
	va_start(args, format);
	vsprintf(str, format, args);
	va_end(args);

	snprintf(log, sizeof(log), "[%s][%s]%s:%s:%u - %s\n", level, time_now, basename(info->debug_print_file),
			info->debug_print_function, info->debug_print_line, str);

	fwrite(log, strlen(log), 1, g_config.fp_log);
	fflush(g_config.fp_log);
	return;
}

