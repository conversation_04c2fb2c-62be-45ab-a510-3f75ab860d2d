/****************************************************************************************
 * 文 件 名 : wxcs_tbl_writer.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-05
* 编    码 : root      '2019-01-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_TBL_WRITER_H_
#define _WXCS_TBL_WRITER_H_

#include <stdlib.h>
#include <stdio.h>
#include <linux/limits.h>
#include <string>
#include <vector>
#include <sys/socket.h>


#include "wxcs_session.h"
#include "wxcs_logger.h"

/*ADD_S by yangna 2020-09-15 */
/*TBL文件信息结构体，指一个TBL文件 */
typedef struct
{
    std::string  tblFileFullPathWriteCompleted;          /*包含全路径的写tbl文件完成状态名称 */
    std::string  tblFileFullPathWriting;                 /*包含全路径的正在写状态tbl文件名称 */
    unsigned int tblWritenCnts;                          /*当前文件中已写条数 */
    FILE*        tblFilePtr;                             /*文件结构指针 */
    unsigned int tblFileLastWriteTime;                   /*tbl文件最后写时间 */
} TblFileInfo;

/*ADD_E by yangna 2020-09-15 */


class wxcsTblWriter
{
public:
    constexpr static const char *TBL_WRITING_SUFFIX = ".writing";

public:
    wxcsTblWriter(const std::string &strTblFileDir,
             const std::string &strProtoName,
             int lRecordCntPerFile = 5000);

    ~wxcsTblWriter()
    {
        for (size_t i = 0; i < tblFileInfoVec_.size(); ++i)
        {
            writeCurrentFileDone(i);
        }
    }

public:
    int writeColumnsFile(const std::string &strFieldsDir, const std::string &strFieldsList);

    template <typename PersonType>
    void writeToFile(const SessionPtr<PersonType>& session)
    {
        uint16_t code = session->getSessionCode();
        if (code >= maxFileCode_) {
            LOG_DEF->warn("Session code {} is too big, supported max code is {}, failed to write tbl to file", code, maxFileCode_-1);
            return;
        }

        if (!tblFileInfoVec_[code].tblFilePtr)
        {
            createTblFile(code);
        }

        // 创建成功则写tbl
        if (tblFileInfoVec_[code].tblFilePtr)
        {
            std::string strRecordLine = session->toStrRecordLine('|');
#ifdef DPI_WXA_WEB

            if(CFG->GetValueOf<int>("SEND_WXA_UDP", 1)){
                  sendToUDPSocket(strRecordLine);
            }
#endif

            writeToFileReal(code, strRecordLine);
        }
        else
        {
            LOG_DEF->warn("Failed to create tbl file");
        }

    }
#ifdef DPI_WXA_WEB
    void sendToUDPSocket(std::string message){
      if(sockfd_ ){
        LOG_DEF->warn("UDP SEND : {}", message);

        write(sockfd_, message.c_str(), message.size());
      }
    }
#endif
    std::string getFileFormatCode(int code) {
        char str[4] = {0};
        snprintf(str, 4, "%03d", code);
        return str;
    }

    void  writeCurrentFileDone(int index);
private:
    int   writeToFileReal(int index, std::string &strRecordLine);

private:
    void generateFileName(int index);
    int  createTblFile(int index);

public:
    std::string strTblFileDir_;                 /* tbl文件夹全路径 例如:/tmp/tbls/qqav*/
    std::string strProtoName_;                  /*协议名称，例如:wxa,zoom等，也是保存tbl文件父目录名称 */
    uint32_t lRecordCntPerFile_;                     /*每个tbl文件最大写条数 */
    int maxFileCode_;                           /*tbl及内容实体文件的文件名编码参数最大值 */
    /*ADD_S by yangna 2020-09-15 */
    unsigned int tblFileMaxWritingTime;         /*一个tbl最大写时间，即.writing文件存在最大时间 */
    /*为什么要用vector保存多个写文件指针，我也不知道，这原因问郭杨吧*/
    /*只是将原来代码优化，将一个文件多个属性放在一个对象中，用一个vector来管理*/
    std::vector<TblFileInfo> tblFileInfoVec_;   /*tbl文件信息 */
    /*ADD_E by yangna 2020-09-15 */
#ifdef DPI_WXA_WEB
    int sockfd_;
    union
    {
      struct sockaddr_in addr_;
      struct sockaddr_in6 addr6_;
    };
#endif
};

#endif /* _WXCS_TBL_WRITER_H_ */
