/****************************************************************************************
 * 文 件 名 : dpi_qq_voip.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 设计: zhangsx         2019/05/14
 编码: zhangsx         2019/05/14
 修改: zhangsx         2019/05/14
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2019 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
环境变量:
export YV_WX_MEDIA_CHAT_DEBUG="OK" 打开日志

 *****************************************************************************************/
#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <rte_rwlock.h>
#include <assert.h>

#include <rte_log.h>
#include <rte_common.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_cjson.h"
#include "wxcs_def.h"
uint8_t decode_protobuf(char *input, int input_size, char **output);
enum QQ_info_state
{
    QQ_START = 0x0ff0,
    QQ_END = 0x0ff7,
    QQ_UNUSED = 0x0fff,
};

extern struct global_config g_config;

typedef struct
{
    uint32_t qq_session[2];//02004800情况下的两个qq号

    //通过28拿到自己的qq
    uint32_t self_qq;
    //任意一包拿到自己的私网ip
    uint32_t self_private_ip;

    // 通过48中的qq_session，并且在有28拿到自己的qq的情况下，填充对方的公网ip与qq号
    uint32_t other_qq;
    uint32_t other_pub_ip;

    //状态
    uint16_t judge;

    //28情况下的端口
    uint16_t client_port;

    uint64_t msisdn;
    uint64_t last_active_time;
    uint16_t is_time_out;
}QQ_person;


typedef struct{
    uint8_t    get_02;
    uint8_t    get_28;
    uint8_t    get_5b;
    uint8_t    get_48;
    uint8_t    is_time_out;
    ST_qqGroup person_info;
}QQ_session;

pthread_rwlock_t  qq_rwlock = PTHREAD_RWLOCK_INITIALIZER; //定义和初始化读写锁
pthread_rwlock_t  qq_person_rwlock = PTHREAD_RWLOCK_INITIALIZER; //定义和初始化读写锁
//static rte_rwlock_t qq_rwlock;

static int qq_voip_log_type = 0;
static volatile uint64_t all_qq_session = 0;
static volatile uint64_t qq_group_session = 0;
static volatile uint64_t real_qq_group_session = 0;

static uint8_t qq_group_caller[] = {
  0x01, 0x18, 0x00, 0x28, 0x00, 0x30, 0xff, 0xff,
  0xff, 0xff, 0x0f, 0x52, 0x02, 0x0a, 0x00, 0x29
};

static uint8_t qq_group_called[] = {
  0x02, 0x08, 0x01, 0x32, 0x02, 0x0a, 0x00, 0x29
};

static void *session_hash = NULL;
static void *person_hash = NULL;
static wxc_handle qq_handle; //  仅供本协议使用

static inline void init_qq_person_info(QQ_person * qq_person_info, uint64_t msisdn, uint32_t client_ip, uint16_t client_port)
{
    if(NULL != qq_person_info)
    {
        qq_person_info->msisdn = msisdn;
        qq_person_info->last_active_time = time(NULL);
        qq_person_info->judge = QQ_START;
        qq_person_info->client_port = client_port;
        qq_person_info->self_qq = 0;
        qq_person_info->other_qq = 0;
        qq_person_info->is_time_out = 0;
        qq_person_info->qq_session[0] = 0;
        qq_person_info->qq_session[1] = 0;
    }
}

/* hash 表初始化 */
static void* wx_session_hash_init(void)
{
    void *hash = NULL;
    pthread_rwlock_wrlock(&qq_rwlock);     //写锁
    if(NULL == hash)
    {
        hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    }
    pthread_rwlock_unlock(&qq_rwlock);     //解锁

    return hash;
}

/* hash 添加节点 */
static int wx_session_hash_insert(void* hash, char *key, void* value)
{
    if(NULL == hash || NULL==key || NULL==value)
    {
        return -1;
    }
    pthread_rwlock_wrlock(&qq_rwlock);     //写锁
    int ret = g_hash_table_insert ((GHashTable *)hash, key, value);
    pthread_rwlock_unlock(&qq_rwlock);     //解锁
    return  ret;
}

/* hash 查找节点 */
static void* wx_session_hash_find(void* hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return NULL;
    }

    pthread_rwlock_rdlock(&qq_rwlock);     //读锁
    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&qq_rwlock);     //解锁
    return ret;
}
/* hash 节点个数 */
static size_t wx_session_hash_get_size(void* hash)
{
    if(NULL == hash)
    {
        return 0;
    }
    pthread_rwlock_rdlock(&qq_rwlock);     //读锁
    size_t ret = g_hash_table_size((GHashTable *)hash);
    pthread_rwlock_unlock(&qq_rwlock);     //解锁
    return ret;
}

/* hash 删除节点 */
static int wx_session_hash_delete(void* hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return -1;
    }
    pthread_rwlock_wrlock(&qq_rwlock);     //写锁
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&qq_rwlock);     //解锁
    return 0;
}

/* hash 删除节点 (线程不安全)*/
static int wx_session_hash_delete_unthread_safe(void* hash, char *pStr)
{
    if (NULL == hash || NULL == pStr)
    {
        return -1;
    }
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    return 0;
}

/* hash 销毁 */
static void wx_session_hash_destory(void* hash)
{
    if(NULL == hash)
    {
        return;
    }
    pthread_rwlock_wrlock(&qq_rwlock);     //写锁
    g_hash_table_destroy((GHashTable *)hash);
    pthread_rwlock_unlock(&qq_rwlock);     //解锁
}

static void * hash_init(void * hash, pthread_rwlock_t * lock)
{
    if (NULL == lock)
    {
        return NULL;
    }
    pthread_rwlock_wrlock(lock);     //写锁
    if(NULL == hash)
    {
        hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    }
    pthread_rwlock_unlock(lock); 
    return hash;
}

static int hash_insert(void* hash, char *pStr, void* value, pthread_rwlock_t * lock)
{
    if (NULL == hash || NULL == pStr || NULL == lock || NULL == value)
    {
        return -1;
    }
    pthread_rwlock_wrlock(lock);     //写锁
    int ret = g_hash_table_insert((GHashTable *)hash, pStr, value);
    pthread_rwlock_unlock(lock);     //解锁
    return ret;
}

static void * hash_insert_check(void* hash, char *pStr, void* value, pthread_rwlock_t * lock)
{
    if (NULL == hash || NULL == pStr || NULL == lock || NULL == value)
    {
        return NULL;
    }
    int ret = 1;
    pthread_rwlock_wrlock(lock);     //写锁
    void* val = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if (NULL == val)
    {
        ret = g_hash_table_insert((GHashTable *)hash, pStr, value);
        val = value;
    }
    pthread_rwlock_unlock(lock);     //解锁

    assert(ret != 0);

    return val;
}

static int hash_insert_unsafe(void* hash, char *pStr, void* value)
{
    if(NULL == hash || NULL == pStr || NULL == value)
    {
        return -1;
    }

    int ret = g_hash_table_insert((GHashTable *)hash, pStr, value);

    return ret;
}

static void* hash_find(void* hash, char *pStr, pthread_rwlock_t * lock)
{
    if (NULL == hash || NULL == pStr || NULL == lock)
    {
        return NULL;
    }
    pthread_rwlock_rdlock(lock);     //读锁
    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(lock);     //解锁
    return ret;
}

static void* hash_find_unsafe(void* hash, char *pStr)
{
    if (NULL == hash || NULL == pStr)
    {
        return NULL;
    }

    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);

    return ret;
}

static size_t hash_get_size(void* hash, pthread_rwlock_t * lock)
{
    if(NULL == hash ||NULL == lock )
    {
        return 0;
    }
    pthread_rwlock_rdlock(lock);     //读锁
    size_t ret = g_hash_table_size((GHashTable *)hash);
    pthread_rwlock_unlock(lock);     //解锁
    return ret;
}

static int hash_delete(void* hash, char *pStr, pthread_rwlock_t * lock)
{
    if (NULL == hash || NULL == pStr || NULL == lock)
    {
        return -1;
    }
    pthread_rwlock_wrlock(lock);     //写锁
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(lock);     //解锁
    return 0;
}

static void hash_destory(void * hash, pthread_rwlock_t * lock)
{
    if(NULL == hash ||NULL == lock)
    {
        return;
    }
    pthread_rwlock_wrlock(lock);
    g_hash_table_destroy((GHashTable *)hash);
    pthread_rwlock_unlock(lock);
}

static void print_session(ST_qqGroup* p, const char*hash_key)
{
    if (NULL == p)
    {
        return;
    }
    printf("hash_key          :%s\n", hash_key);
    printf("sessionID         :%d\n", p->groupId);

    printf("src_port          :%d\n", p->srcPort);
    printf("dst_port          :%d\n", p->dstPort);
    printf("src_ip            :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->srcIp)+0),
            *((uint8_t*)(&p->srcIp)+1),
            *((uint8_t*)(&p->srcIp)+2),
            *((uint8_t*)(&p->srcIp)+3));

    printf("dst_ip            :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->dstIp)+0),
            *((uint8_t*)(&p->dstIp)+1),
            *((uint8_t*)(&p->dstIp)+2),
            *((uint8_t*)(&p->dstIp)+3));
    printf("selfQQNum         :%lu\n", p->selfQQNum);
    for (int i = 0; p->otherQQNums[i] !=0 && p->otherQQNums[i] != QQ_END; ++i)
        printf("otherQQNum        :%lu\n", p->otherQQNums[i]);
    printf("C2STransPackets   :%d\n", p->c2sPackCount);
    printf("S2CTransPackets   :%d\n", p->s2cPackCount);
    printf("C2STransBytes     :%d\n", p->c2sByteCount);
    printf("S2CTransBytes     :%d\n", p->s2cByteCount);
    printf("FirstActiveTime   :%d\n", p->firstActiveTime);
    printf("LastActiveTime    :%d\n", p->lastActiveTime);
    printf("C2S Bytes/Pkts    :%d\n", p->c2sPackCount==0 ? 0:p->c2sByteCount/p->c2sPackCount);
    printf("S2C Bytes/Pkts    :%d\n", p->s2cPackCount==0 ? 0:p->s2cByteCount/p->s2cPackCount);
    printf("\n");

}

///* 调试 输出*/
static void print_st(ST_qqGroup* p, const char*hash_key)
{
    if(0 == g_config.debug_qq_voip)
    {
        return;
    }

    if(NULL == p || NULL == hash_key)
    {
        return;
    }

    print_session(p, hash_key);
}


static void* phone_watch_init(void)
{
    char phone_number[16];
    int  safelen;
    void* hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if(NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    if(0 != memcmp(g_config.wx_phone_watch, "null", 4))
    {
        int    i = 0;
        char*p   = (char*)&g_config.wx_phone_watch;
        int size = sizeof(g_config.wx_phone_watch);
        printf("QQ话单监视手机列表[%s]\n", g_config.wx_phone_watch);

        char *pLeft  = p;
        char *pRight = pLeft;
        while('\0' != *p)
        {
            pRight = strchr(pLeft, ',');
            if(NULL == pRight)
            {
                // 探测是不是只有一个手机号，末尾没有逗号
                int len = strlen(pLeft);
                if(len >= 13 && len <15)//一个手机号就是13个字符
                {
                    char *pStr = pLeft;
                    g_hash_table_insert((GHashTable *)hash, g_strdup(pStr), g_strdup(pStr));
                    printf("QQ话单监视手机[%s]\n", pStr);
                }
                break;
            }
            safelen = sizeof(phone_number) < (unsigned)(pRight - pLeft) ? sizeof(phone_number) : (unsigned)(pRight - pLeft);
            memset(phone_number, 0, sizeof(phone_number));
            memcpy(phone_number, pLeft, safelen);
            g_hash_table_insert((GHashTable *)hash, g_strdup(phone_number), g_strdup(phone_number));
            pLeft = ++pRight;
            printf("QQ话单监视手机[%s]\n", phone_number);
        }
    }
    else
    {
        return NULL;
    }
    return hash;
}

static int phone_watch_find(void *hash, const char*pStr)
{
    if(NULL == hash)
    {
        return -1;
    }

    gpointer p = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if(NULL == p)
    {
        return -1;
    }
    return 1;
}

static void phone_watch_fini(void* hash)
{
    if(NULL == hash)
    {
        return;
    }
    g_hash_table_destroy((GHashTable *)hash);
}


// 会话线程 代码段
static void *qq_voice_session_thread(void *arg)
{
#define MAX_TIME_OUT_NODE 1024*256
 
    // Hash init
    session_hash = wx_session_hash_init();
    if(NULL == session_hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create qq hash");
        exit(-1);
    }

    person_hash = hash_init(person_hash, &qq_person_rwlock);
    if(NULL == person_hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create person hash");
        exit(-1);
    }
    void* hash = session_hash;

    // API init
    wxc_handle handle      = NULL;

    if (g_config.qq_voip_port > 0 && g_config.qq_voip_ip[0] > '0')                                     //如果qq有端口号
    {
        wxc_init(&handle, g_config.qq_voip_ip,  g_config.qq_voip_port);
    }
    else
    {
        wxc_init(&handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    }

    if(NULL == handle)
    {
        printf("handle is NULL!\n");
        return NULL;
    }
    qq_handle = handle;

    // 监控手机号 init
    void* hash_phone_num = phone_watch_init();

    while(1) // should I exit ?
    {
        sleep(g_config.wx_session_timeloop);

        int   hash_count= 0;
        int   KeyTop    = 0;
        int   p2p       = 0;
        int   group     = 0;

        void* Stack[MAX_TIME_OUT_NODE];// 用来存储超时的 hash node

        char*      key   = NULL;
        QQ_session* value = NULL;

        FILE *qq_log_file = fopen("qq_voip_log.txt", "w");
        fprintf(qq_log_file, "QQ session stat:\t %lu\n"
                             "program group   \t %lu\t%.2f%%\n"
                             "real group      \t %lu\t%.2f%%\n", 
                all_qq_session, qq_group_session, (float)100*qq_group_session/all_qq_session, 
                real_qq_group_session, (float)100*real_qq_group_session/all_qq_session);
        fclose(qq_log_file);
        // 遍历所有 session node
        pthread_rwlock_rdlock(&qq_rwlock);     //读锁.锁.锁.锁.锁.锁.锁.
        GHashTableIter iter;
        uint32_t   speed     = 0;

        g_hash_table_iter_init(&iter, (GHashTable *) hash);//使用迭代器遍历哈希表
        while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&value))
        {
            hash_count++;
            // 发送所有的 session node
            if ( NULL != handle && NULL != value )
            {
                uint64_t now = time(NULL);
                if (now - value->person_info.lastActiveTime > g_config.wx_session_timeout)
                {
                    value->is_time_out = 1;
                }

                if (value->is_time_out == 1)
                {    // Glib 支持 同时遍历并删除节点
                    // 记录需要删除的 超时session node
                    if(KeyTop < MAX_TIME_OUT_NODE && NULL != key)
                    {
                        Stack[KeyTop++] = key; // 记录待删除的key
                    }
                }
                if (value->person_info.groupId > 0)
                {
                    if ( (value->get_02 == 1 && value->get_5b == 0)
                        && (value->person_info.otherQQNums[0] > 9999) )
                    {
                        dpi_TrailerUpdateTS(&value->person_info.trailer);
                        if (value->is_time_out == 1)
                        {
                            ++ qq_group_session;
                            // value->person_info.isTimeout = 1;
                        }
                        wxc_sendMsg(handle, (const unsigned char*)&(value->person_info), sizeof(ST_qqGroup), WXCS_QQ_GROUP);
                        ++group;
                    }
                } else if ((value->get_28 == 1) || (value->get_48 == 1)) {
                    ST_qqSingle qq_sig = {
                        .c2sByteCount = value->person_info.c2sByteCount,
                        .c2sPackCount = value->person_info.c2sPackCount,
                        .s2cByteCount = value->person_info.s2cByteCount,
                        .s2cPackCount = value->person_info.s2cPackCount,
                        .srcIp = value->person_info.srcIp,
                        .dstIp = value->person_info.dstIp,
                        .srcPort = value->person_info.srcPort,
                        .dstPort = value->person_info.dstPort,
                        .firstActiveTime = value->person_info.firstActiveTime,
                        .lastActiveTime = value->person_info.lastActiveTime,
                        .selfQQNum = value->person_info.selfQQNum,
                        .trailer = value->person_info.trailer,
                        .otherQQNum = value->person_info.otherQQNums[0],
                        .isTimeout = value->person_info.isTimeout,
                        .pubSrcIP = value->person_info.pubSrcIP,
                        .qqSession[0] = value->person_info.qqSession[0],
                        .qqSession[1] = value->person_info.qqSession[1],
                        .sessionType = value->get_02 == 1 ? WXCS_SESSION_CHAT_SINGLE_VIDEO : WXCS_SESSION_CHAT_SINGLE_AUDIO,
                    };
                    dpi_TrailerUpdateTS(&qq_sig.trailer);
                    wxc_sendMsg(handle, (const unsigned char*)&qq_sig, sizeof(ST_qqSingle), WXCS_QQ_SINGLE);
                    ++ p2p;
                }
                //printf("state:%x\n",value->recorder.state);
                print_st(&value->person_info, (const char*)key);
            }
            // 监控手机号 find
            char phone_num[20];
            snprintf(phone_num, 20, "%ld", value->person_info.trailer.MSISDN);
            if (1 == phone_watch_find(hash_phone_num, phone_num))
            {
                printf("find_phone        :%s\n", phone_num);
                print_session(&value->person_info, key);
            }
        }
        pthread_rwlock_unlock(&qq_rwlock);     //解锁.锁.锁.锁.锁.锁.锁.

        // 打印 hash 信息
        char strtime[26];
        time_t timer;
        time(&timer);
        struct tm* tm_info = localtime(&timer);
        strftime(strtime, 26, "%Y-%m-%d %H:%M:%S", tm_info);
        printf("QQSesionHash:[%s] delete/p2p/group/total=[%d/%d/%d/%d]\n", strtime, KeyTop, p2p, group, hash_count);

        // 开始删除超时的 Hash node
        while(KeyTop--)
        {
            wx_session_hash_delete(hash, Stack[KeyTop]);
        }
        
        // 清除可能是误识别的
        key = NULL;
        QQ_person *key_value = NULL;
        uint32_t key_index = 0;
        void * key_set[MAX_TIME_OUT_NODE];

        pthread_rwlock_rdlock(&qq_person_rwlock);
        g_hash_table_iter_init(&iter, (GHashTable *)person_hash);
        while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&key_value))
        {
            if(NULL != key && NULL != key_value)
            {
                uint64_t now = time(NULL);
                if (now - key_value->last_active_time > g_config.wx_session_timeout)
                {
                    key_value->is_time_out = 1;
                    key_value->judge = QQ_UNUSED;
                }

                if (key_value->is_time_out == 1 && key_index < MAX_TIME_OUT_NODE)
                {
                    key_set[key_index++] = key;
                }
            }
        }
        pthread_rwlock_unlock(&qq_person_rwlock);

        while(key_index --)
        {
            hash_delete(person_hash, key_set[key_index], &qq_person_rwlock);
        }

    }

    // Fini
    if(NULL != handle)
    {
        wxc_fini(handle);
        handle = NULL;
    }

    if(NULL != session_hash)
    {
        wx_session_hash_destory(session_hash);
        session_hash = NULL;
    }

    if(NULL != person_hash)
    {
        hash_destory(person_hash, &qq_person_rwlock);
        person_hash = NULL;
    }

    if(NULL != hash_phone_num)
    {
        phone_watch_fini(hash_phone_num);
        hash_phone_num = NULL;
    }

    return NULL;
}

int init_dissector_qq_sesion_thread(void)
{
    if (g_config.protocol_switch[PROTOCOL_QQ_VOIP] == 0)
    {
        return 0;
    }

    pthread_t qq_voip_session;

    int status = pthread_create(&qq_voip_session, NULL, qq_voice_session_thread, NULL);
    pthread_setname_np(qq_voip_session, "thread_QQ");
    if(status != 0)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create qq_voice_session thread");
        exit(-1);
    }

    //return pthread_join(weixin_voice_session, NULL); // 此处不能阻塞等待
    return 0;// 返回到上一层
}

typedef struct st_media_chat
{
    int       FlagPacketC2S;
    int       FlagPacketS2C;
    int       SessionType;
    uint32_t  GroupFlowSeq;
    uint32_t  GroupIdExcept;
    int       count_except;
} ST_qqMediaChat;

static ST_qqMediaChat* WX_FlowSessionInit(struct flow_info *flow)
{
    if(NULL == flow)
    {
        return NULL;
    }

    if(NULL == flow->app_session)
    {
        ST_qqMediaChat* pst_MediaChat;
        pst_MediaChat= malloc(sizeof(ST_qqMediaChat));
        if(NULL == pst_MediaChat)
        {
            DPI_LOG(DPI_LOG_ERROR, "error on malloc ST_qqMediaChat");
            exit(-1);
        }
        memset(pst_MediaChat, 0, sizeof(ST_qqMediaChat));
        flow->app_session = pst_MediaChat;
    }

    return flow->app_session;
}

static int WX_FlowSessionSetFlags(struct flow_info *flow, int C2S)
{
    if(NULL == flow)
    {
        return -1;
    }

    if(NULL == flow->app_session)
    {
        WX_FlowSessionInit(flow);
    }

    ST_qqMediaChat* pst_MediaChat;
    pst_MediaChat = flow->app_session;
    if(1 == C2S)
    {
        pst_MediaChat->FlagPacketC2S++;
    }
    else
    {
        pst_MediaChat->FlagPacketS2C++;
    }
    return 0;
}

static int WX_FlowSessionGetFlags(struct flow_info *flow, int C2S)
{
    if(NULL == flow)
    {
        return 0;
    }

    if(NULL == flow->app_session)
    {
        return 0;
    }

    ST_qqMediaChat* pst_MediaChat;
    pst_MediaChat = flow->app_session;
    if(1 == C2S)
    {
        return pst_MediaChat->FlagPacketC2S;
    }
    else
    {
        return pst_MediaChat->FlagPacketS2C;
    }
}

static int WX_FlowSessionRemove(struct flow_info *flow)
{
    if(NULL == flow)
    {
        return 0;
    }

    if(NULL != flow->app_session)
    {
        dpi_free(flow->app_session);
        flow->app_session = NULL;
    }
    return 1;
}

static int check_flow_seq(struct flow_info *flow, uint32_t seq, int C2S)                                            //检测udp流序号连续性
{
    if(NULL == flow)
    {
        return 0;
    }
    if(NULL == flow->app_session)
    {
        return 0;
    }
    if(!C2S)
    {
        return 3;
    }

    ST_qqMediaChat *flow_state = (ST_qqMediaChat *)flow->app_session;

    if(flow_state->count_except == 0)
    {
        flow_state->count_except ++;                                                                                //报序号异常帧状态检测
        return 2;
    }
    else if(flow_state->count_except < 10)
    {
        return 1;
    }
    else
    {
        return 0;
    }

    if(seq > flow_state->GroupFlowSeq)
    {
        if(flow_state->GroupFlowSeq == 0)
        {
            flow_state->GroupFlowSeq = seq;
            return 1;
        }
        else if( seq - flow_state->GroupFlowSeq > 0 &&  seq - flow_state->GroupFlowSeq < g_config.qq_session_seq_jitter)  //报序号小于阈值则正常
        {
            flow_state->GroupFlowSeq = seq;
            return 1;
        }
        else
        {
            flow_state->GroupFlowSeq = seq;
            flow_state->count_except ++;
            return 1;
        }
    }
    else if(seq < 10 && flow_state->GroupFlowSeq != seq)
    {
        flow_state->GroupFlowSeq = seq;
        return 1;
    }
    else
    {
        return 0;
    }

}

static int check_group_id(struct flow_info *flow)
{
    if (NULL == flow)
    {
        return 0;
    }

    if(NULL == flow->app_session)
    {
        return 0;
    }

    ST_qqMediaChat *flow_state = (ST_qqMediaChat *)flow->app_session;

    if (flow_state->GroupIdExcept > g_config.qq_session_seq_jitter)
    {
        return 0;
    }
    else
    {
        flow_state->GroupIdExcept ++;
    }
    return 1;
}
// 解析线程 代码段
static int dissect_qq_voip_chat(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
#define  SESSION_PERSON  2
#define  SESSION_GROUP   1

    if (g_config.protocol_switch[PROTOCOL_QQ_VOIP] == 0)
    {
        return 0;
    }

    // 如果pkt 小于 wx_session_pkt_size， 则不计入统计
    //if(payload_len <= g_config.wx_session_pkt_size)
    //{
    //    return 0;
    //}

    // 初始化 app_session
    if (NULL == flow->app_session)
    {
        WX_FlowSessionInit(flow);
    }

    const uint8_t *pdata = payload;
    uint32_t data_len    = payload_len;
    uint32_t     sessionID = 0;
    // waiting for hash init ok...
    while(NULL == session_hash);
    while(NULL == person_hash);

    // 获取当前IP的 五元组信息
    uint32_t ip_src      = 0;
    uint32_t ip_dst      = 0;
    uint16_t port_src    = 0;
    uint16_t port_dst    = 0;
    uint32_t client_ip   = 0;
    uint16_t client_port = 0;
    uint32_t cli_qq      = 0;

    int      is_28       = 0;
    int      is_02       = 0;
    int      is_5b       = 0;
    int      is_person   = 0;
    int      is_48       = 0;
    // 以客户端为第一人称，取出客户端的源末IP，Port
    if(FLOW_DIR_SRC2DST == direction) // 正向流
    {
        port_dst  = ntohs(flow->tuple.inner.port_dst);
        port_src  = ntohs(flow->tuple.inner.port_src);
        ip_src    =       flow->tuple.inner.ip_src.ip4;
        ip_dst    =       flow->tuple.inner.ip_dst.ip4;
    }
    else if(FLOW_DIR_DST2SRC == direction) // 反向流
    {
        port_dst  = ntohs(flow->tuple.inner.port_src);
        port_src  = ntohs(flow->tuple.inner.port_dst);
        ip_dst    =       flow->tuple.inner.ip_src.ip4;
        ip_src    =       flow->tuple.inner.ip_dst.ip4;
    }
    // 判断 当前报文的端口
    int C2S = 0;
    if ((0x1700 == get_uint16_t(payload, 7) && 0 == get_uint32_t(payload, 9)) || 8000 == port_dst || 8001 == port_dst ||
        8800 == port_dst || 18000 == port_dst || 19003 == port_dst || 0b11110011 == port_dst >> 5 ||
        ((0x28 == payload[0] || (*(uint64_t *)payload << 16) == 281784214487040) &&
            FLOW_DIR_SRC2DST == direction))  // 如果这个报文发往 Server
    {
        C2S=1;
        client_ip = ip_src;// 取自己的 源IP
        client_port = port_src;
    } else {
        C2S=0;
        client_ip = ip_dst; //取 客户端 源IP
        client_port = port_dst;
    }

    WX_FlowSessionSetFlags(flow, C2S);
    /* 聊天 类型判断  */
    if( !(NULL != flow && NULL != flow->app_session) )
    {
        return 0;
    }
    ST_qqMediaChat *flow_state = (ST_qqMediaChat *)flow->app_session;
    if (C2S && 0x28 == payload[0])  // 获取p2p客户端qq号
    {
        is_28 = 1;
        char    *json_str = NULL;
        uint16_t mark = get_uint16_t(payload, 1);
        uint32_t len = get_uint32_ntohl(payload, 1);
        if (mark != 0 || len > payload_len) {
            return 0;
        }
        if (decode_protobuf((char *)(payload + 9), len, &json_str)) {
            cJSON *root = dpi_cjson_parse_json(json_str);
            if (root) {
                char *qq_str = dpi_cjson_get_string_field(root, "2");
                //ip与qq绑定
                if (qq_str) {
                    cli_qq = atoi(qq_str);
                    free(qq_str);
                }
                dpi_cjson_free_json(root);
            }
        }
    } else if (0x02 == payload[0])  //02包会有两种情况：1对1聊天时的单纯的02包流及群聊天时的先28后02流
    {
        if ((0x001700 == get_uint16_t(payload, 7) || 0x1800 == get_uint16_t(payload, 7)) && 0 == get_uint32_t(payload, 9)) {
            flow_state->SessionType = SESSION_GROUP;
            is_02 = 1;
        } else if (get_uint16_ntohs(payload, 1) == payload_len) {
            //02004800010000
            is_person = 1;
            if (payload_len == 0x48) {
                is_48 = 1;
            }
            flow_state->SessionType = SESSION_PERSON;
        } else {
            return 0;
        }
    } else if (0x5b == payload[0]) {
        if (WX_FlowSessionGetFlags(flow, C2S) > 0 || WX_FlowSessionGetFlags(flow, !C2S) > 0)
        {
            flow_state->SessionType = SESSION_PERSON;
            sessionID = get_uint32_t(payload, 9);
            is_5b = 1;
        }
        else
        {
            return 0;
        }
    } else {
        return 0;
    }

    QQ_person *person_value = NULL;
    //根据聊天类型提取 session_id
    char Hash_Key[256];
    memset(Hash_Key, 0, 256);

    ST_trailer tmp_trailer;
    memset(&tmp_trailer, 0, sizeof(ST_trailer));

    dpi_TrailerParser(&tmp_trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析

    uint64_t msisdn = tmp_trailer.MSISDN;

    if (flow_state->SessionType == SESSION_PERSON) {
        char isdn_or_cliIP_hashkey[256];
        memset(isdn_or_cliIP_hashkey, 0, 256);
        if (1 == flow->has_trailer && msisdn > 0)  //如果有RTL标签且手机号大于0
        {
            snprintf(isdn_or_cliIP_hashkey, sizeof(isdn_or_cliIP_hashkey), "%lx", msisdn);
        } else {
            snprintf(isdn_or_cliIP_hashkey, sizeof(isdn_or_cliIP_hashkey), "%x", client_ip);
        }

        person_value = (QQ_person *)hash_find(person_hash, isdn_or_cliIP_hashkey, &qq_person_rwlock);

        if (NULL == person_value) {
            QQ_person base_tmp_value;
            init_qq_person_info(&base_tmp_value, msisdn, client_ip, client_port);
            person_value = hash_insert_check(person_hash, g_strdup(isdn_or_cliIP_hashkey),
                g_memdup((QQ_person *)&base_tmp_value, sizeof(QQ_person)), &qq_person_rwlock);
        }

        if (NULL == person_value) {
            DPI_LOG(DPI_LOG_ERROR, "hash insert failed!\n");
            return 0;
        }
        person_value->last_active_time = time(NULL);
        if (is_person && get_uint32_ntohl(payload, 19) > 9999 && get_uint32_ntohl(payload, 31) > 9999) {
            //02004800
            if (person_value->judge == QQ_UNUSED) {
                return 0;
            } else if (person_value->judge == QQ_START)  //个人qq报文第一次来
            {
                person_value->judge = get_uint8_t(payload, 25);  //记录标志位值
                //登记双方qq号
                if (person_value->qq_session[0] == 0) {
                    if (get_uint32_ntohl(payload, 15) != 0 && get_uint32_ntohl(payload, 27) != 0) {
                        return 0;
                    }
                    person_value->qq_session[0] = get_uint32_ntohl(payload, 19);
                    person_value->qq_session[1] = get_uint32_ntohl(payload, 31);
                }
            }
            if(person_value->self_qq != 0){
                person_value->other_qq =
                    (cli_qq == person_value->qq_session[0]) ? person_value->qq_session[1] : person_value->qq_session[0];
            }
        } else if (1 == is_28 && cli_qq) {
            person_value->self_qq = cli_qq;
            if (person_value->qq_session[0] != 0) {
                person_value->other_qq =
                    (cli_qq == person_value->qq_session[0]) ? person_value->qq_session[1] : person_value->qq_session[0];
            }
        }
    }

    int ret = 0;
    if (flow_state->SessionType == SESSION_GROUP) {
        if (1 == flow->has_trailer && msisdn > 0)  //如果有RTL标签且手机号大于0
        {
            //ret += BinToHex(flow->trailer + 16, 7, Hash_Key+ret, 256 - ret);  // flow->trailer+16是手机号, 7代表长度
            ret = snprintf(Hash_Key, sizeof(Hash_Key), "%lx%x", msisdn, client_port);
        } else {
            //ret += BinToHex((const unsigned char*)&client_ip, sizeof(client_ip), Hash_Key+ret, 256 -ret); // 将client_ip 作为HASH key 的一部分
            ret = snprintf(Hash_Key, sizeof(Hash_Key), "%x%x", client_ip, client_port);
        }
    } else {
        if (1 == flow->has_trailer && msisdn > 0)  //如果有RTL标签且手机号大于0
        {
            ret = snprintf(Hash_Key, sizeof(Hash_Key), "%lx", msisdn);
        }else {
            ret = snprintf(Hash_Key, sizeof(Hash_Key), "%x", client_ip);
        }
    }

    // 哈希查找Session
    QQ_session* qq_session = wx_session_hash_find(session_hash, Hash_Key); // 查询机制待修改 先用手机号查询，若无结果，再用ip查询
    if (is_28 == 1 || is_02 || is_person)
    {
        char hashkey[256];
        memset(hashkey, 0, 256);
        if (1 == flow->has_trailer && msisdn > 0) //如果有RTL标签且手机号大于0
        {
            snprintf(hashkey, sizeof(hashkey), "%lx", msisdn);
        }
        else
        {
            snprintf(hashkey, sizeof(hashkey), "%x", client_ip);
        }

        QQ_person *bvalue = (QQ_person *)hash_find(person_hash, hashkey, &qq_person_rwlock);

        // if (NULL != bvalue && bvalue->another_port > 0 && bvalue->another_port != client_port)
        // {
        //     char hashkey2[256];
        //     snprintf(hashkey2, sizeof(hashkey2), "%s%x", hashkey, bvalue->another_port);
        //     QQ_session* old_value = wx_session_hash_find(session_hash, hashkey2);

        //     if (NULL != old_value)
        //     {
        //         old_value->person_info.isTimeout = 1;
        //         old_value->is_time_out = 1;
        //     }
        // }

        if(NULL == qq_session)
        {
            // 创建
            qq_session = malloc(sizeof(QQ_session));

            if(NULL == qq_session)
            {
                DPI_LOG(DPI_LOG_ERROR, "[qqav Session] not enough memory");
                return -1;
            }
            memset(qq_session, 0, sizeof(QQ_session));

            // 设置建联 相关 参数
            dpi_TrailerParser(&qq_session->person_info.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
            dpi_TrailerGetMAC(&qq_session->person_info.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
            dpi_TrailerGetHWZZMAC(&qq_session->person_info.trailer, (const char *)flow->ethhdr);

            dpi_TrailerSetDev(&qq_session->person_info.trailer, g_config.devname);        // 解析板号
            dpi_TrailerSetOpt(&qq_session->person_info.trailer, g_config.operator_name);  // 运营商
            dpi_TrailerSetArea(&qq_session->person_info.trailer, g_config.devArea);       // 设定 地域名

            // Set 五元组
            if(1 == C2S)
            {
                qq_session->person_info.srcIp          = ip_src;
                qq_session->person_info.dstIp          = ip_dst;
                qq_session->person_info.srcPort        = port_src;
                qq_session->person_info.dstPort        = port_dst;
            }
            else
            {
                qq_session->person_info.srcIp          = ip_dst;
                qq_session->person_info.dstIp          = ip_src;
                qq_session->person_info.srcPort        = port_dst;
                qq_session->person_info.dstPort        = port_src;
            }

            qq_session->person_info.firstActiveTime    = time(NULL);

            // 存入 Hash表
            wx_session_hash_insert(session_hash, g_strdup(Hash_Key), (void*)qq_session); // Hash_Key的内容需要全程维护
        }
    }

    if (qq_session == NULL)
    {
        return 0;
    }

    if (is_02 && qq_session->get_02 == 0) {
        qq_session->get_02 = is_02;  //视频
    }
    if (is_28 && qq_session->get_28 == 0) {
        qq_session->get_28 = is_28;
    }
    if (is_5b == 1 && qq_session->get_28 == 1) {
        qq_session->get_5b = is_5b;  //音频
    }
    if (is_person && qq_session->get_48 == 0) {
        qq_session->get_48 = 1;

    }
    if (flow_state->SessionType == SESSION_GROUP && payload[0] == 0x02 && get_uint32_ntohl(payload, 13) > 9999) {
        uint32_t qq = get_uint32_ntohl(payload, 13);
        uint32_t qq_group_session_id = get_uint32_ntohl(payload, 17);
        if (payload_len > 34)
        {
            if (qq_session->person_info.groupId == 0)
            {
                qq_session->person_info.groupId = qq_group_session_id;
            }
            else if(qq_session->person_info.groupId != qq_group_session_id)
            {
                if (qq_session->person_info.groupId != 0 && qq_group_session_id != 0)
                {
                    // printf("QQ GROUP SESSION[%u] PERSON [%u] NOT MATCH SESSION[%u] PERSON [%lu]\n",qq_group_session_id,qq,person->person_info.groupId,person->person_info.selfQQNum);
                    wx_session_hash_delete(session_hash, Hash_Key);
                    WX_FlowSessionRemove(flow);
                    return 0;
                }
            }
        }

        if (C2S)
        {
            if(payload_len > 34 && qq_group_session_id > 0)
            {
                if(!check_flow_seq(flow, get_uint32_ntohl(payload, 3), C2S))
                {
                    wx_session_hash_delete(session_hash, Hash_Key);
                    WX_FlowSessionRemove(flow);
                    return 0;
                }
            }

            if ( qq_session->person_info.selfQQNum == 0 || (qq_session->person_info.otherQQNums[0] == qq_session->person_info.selfQQNum && qq_session->person_info.otherQQNums[0] != qq) )
            {
                qq_session->person_info.selfQQNum = qq;
            }
        }
        else
        {
            if (qq != qq_session->person_info.selfQQNum && qq_group_session_id > 0)
            {
                for (int i = 0; i < QQ_GROUP_MAX_DISSECT_MEMBER_COUNT; i++)
                {
                    if (qq_session->person_info.otherQQNums[i] == qq)
                    {
                        break;
                    }
                    if ((qq_session->person_info.otherQQNums[i] == 0 || qq_session->person_info.selfQQNum == qq_session->person_info.otherQQNums[i]) && qq_session->person_info.otherQQNums[i-1] != qq )
                    {
                        qq_session->person_info.otherQQNums[i] = qq;
                        break;
                    }
                }
            }

        }
    }

    if (flow_state->SessionType == SESSION_PERSON && (payload[0] == 0x5b || payload[0] == 0x28 || is_person))
    {
        char hashkey2[256];
        memset(hashkey2, 0, 256);
        if (is_48 && qq_session->person_info.pubSrcIP == 0) {
            qq_session->person_info.pubSrcIP = ip_dst;
        }

        if (1 == flow->has_trailer && msisdn > 0)  //如果有RTL标签且手机号大于0
            snprintf(hashkey2, sizeof hashkey2, "%lx", qq_session->person_info.trailer.MSISDN);
        else
            snprintf(hashkey2, sizeof hashkey2, "%x", qq_session->person_info.srcIp);

        QQ_person *person_info = (QQ_person *)hash_find(person_hash, hashkey2, &qq_person_rwlock);
        if (person_info && person_info->judge != QQ_END) {
            if (qq_session->person_info.selfQQNum == 0 && person_info->self_qq) {
                qq_session->person_info.selfQQNum = person_info->self_qq;
            }
            if (qq_session->person_info.otherQQNums[0] == 0 && person_info->other_qq) {
                qq_session->person_info.otherQQNums[0] = person_info->other_qq;
            }
            if(qq_session->person_info.qqSession[0] ==0){
              qq_session->person_info.qqSession[0] = person_info->qq_session[0];
              qq_session->person_info.qqSession[1] = person_info->qq_session[1];
            }

        }
    }
    //存疑
    if (flow_state->SessionType == SESSION_GROUP && (payload[0] != 0x02 && payload[0] != 0x28))
    {
        -- all_qq_session;
        -- qq_group_session;
        // printf("SESSION_GROUP NOT 0x02 || 0x28 DELETE SESSION\n");
        wx_session_hash_delete(session_hash, Hash_Key);
        WX_FlowSessionRemove(flow);
        return 0;
    }

    //存疑
    if (flow_state->SessionType == SESSION_PERSON && (payload[0] != 0x5b && payload[0] != 0x28 && payload[0] != 0x02))
    {
        // printf("SESSION_PERSON NOT 0x5b || 0x28 || 0x02 DELETE SESSION\n");
        wx_session_hash_delete(session_hash, Hash_Key);
        WX_FlowSessionRemove(flow);
        return 0;
    }

    // 更新
    if(1 == C2S)
    {
        qq_session->person_info.c2sPackCount ++;
        qq_session->person_info.c2sByteCount += payload_len;
    }
    else
    {
        qq_session->person_info.s2cPackCount ++;
        qq_session->person_info.s2cByteCount += payload_len;
    }

    // 刷一下存在感
    qq_session->person_info.lastActiveTime = time(NULL);

    // 收工!
    return PKT_OK;
}

static void identify_qq_voip_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_QQ_VOIP] == 0)
    {
        return;
    }

    if (payload_len < 20)
    {
        return;
    }

    /* 判断报文的目标端口  */
    int port_src = ntohs(flow->tuple.inner.port_src);
    int port_dst = ntohs(flow->tuple.inner.port_dst);

    if (8000 == port_src || 8000 == port_dst ||
        port_src >> 5 == 0b11110011 || port_dst >> 5 == 0b11110011 ||
        8001 == port_dst || 8001 == port_src ||
        8800 == port_dst || 8800 == port_src ||
        18000 == port_dst || 18000 == port_src ||
        19003 == port_dst || 19003 == port_src) {
        /* 判断UDP报文的前缀 */
        if( ( (payload[0] == 0x28 && payload[payload_len-1] == 0x29)
           || (payload[0] == 0x5b)
           || (payload[0] == 0x02 && payload[payload_len-1] == 0x03) ) && payload_len > 20)
        {
            flow->real_protocol_id = PROTOCOL_QQ_VOIP;
            return;
        }
    }
    if ((*(uint64_t *)payload<< 16 )== 281784214487040) { //0x1004800020000
        flow->real_protocol_id = PROTOCOL_QQ_VOIP;
        return;
    }
    return;
}

void init_qq_voip_chat_dissector(void)
{
    port_add_proto_head(IPPROTO_UDP, 8000, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_UDP, 8001, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_UDP, 8800, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_UDP, 18000, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_UDP, 19003, PROTOCOL_QQ_VOIP);

    udp_detection_array[PROTOCOL_QQ_VOIP].proto         = PROTOCOL_QQ_VOIP;
    udp_detection_array[PROTOCOL_QQ_VOIP].identify_func = identify_qq_voip_chat;
    udp_detection_array[PROTOCOL_QQ_VOIP].dissect_func  = dissect_qq_voip_chat;
    port_add_proto_head(IPPROTO_TCP, 8000, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_TCP, 8001, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_TCP, 8800, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_TCP, 18000, PROTOCOL_QQ_VOIP);
    port_add_proto_head(IPPROTO_TCP, 19003, PROTOCOL_QQ_VOIP);

    tcp_detection_array[PROTOCOL_QQ_VOIP].proto         = PROTOCOL_QQ_VOIP;
    tcp_detection_array[PROTOCOL_QQ_VOIP].identify_func = identify_qq_voip_chat;
    tcp_detection_array[PROTOCOL_QQ_VOIP].dissect_func  = dissect_qq_voip_chat;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_QQ_VOIP].excluded_protocol_bitmask, PROTOCOL_QQ_VOIP);
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_QQ_VOIP].excluded_protocol_bitmask, PROTOCOL_QQ_VOIP);

    return;
}

/*
static __attribute((constructor)) void before_init_qq_voip_chat(void)
{
    register_tbl_array(TBL_LOG_QQ_VOIP, 0, "QQVoipChat", init_qq_voip_chat_dissector);
}
*/
