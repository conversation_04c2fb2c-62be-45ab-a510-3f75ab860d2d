#include "dpi_lua_adapt.h"
#include "dpi_protorecord.h"

#include <dirent.h>
#include <sys/stat.h>
#include <unistd.h>

#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_utils.h"
extern struct global_config g_config;
// static atomic_int adapt_record = 0;

padapt_controller_t *g_padapt_controller;
uint8_t              g_adapt_init_flag = 0;  // 是否遍历标记
#define COMMON_LONG_STRING       1024

/*输出适配后的字段表*/
void dpi_pschema_dump_adapt_proto_schemas(const char *pschema_output_dir) {
  pschema_db_t *adapt_db = padapt_controller_get_adapted_schema_db(g_padapt_controller);
  char          common_buff[COMMON_LONG_STRING] = {0};
  int           ret = 0;
  pschema_t    *common_schema = pschema_get_proto(adapt_db, "common");
  if(!common_schema){
    return;
  }
  for (pfield_desc_t *fdesc = pschema_fdesc_get_first(common_schema); fdesc != NULL;
       fdesc = pschema_fdesc_get_next(common_schema, fdesc)) {
    const char *field_name = pfdesc_get_name(fdesc);
    ret += snprintf(common_buff + ret , sizeof(common_buff) - ret, "%s\n", field_name);
  }
  for (pschema_t *schema = pschema_get_first(adapt_db); schema != NULL; schema = pschema_get_next(adapt_db, schema)) {
    const char *proto_name = pschema_get_proto_name(schema);
    if (strcmp(proto_name, "common") == 0) {
      continue;
    }
    char pschema_file_path[PATH_MAX] = {0};
    snprintf(pschema_file_path, sizeof pschema_file_path, "%s/%s_f.txt", pschema_output_dir, proto_name);
    FILE *pschema_file = fopen(pschema_file_path, "w");
    if (NULL == pschema_file) {
      continue;
    }
    fprintf(pschema_file, "%s", common_buff);

    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc != NULL; fdesc = pschema_fdesc_get_next(schema, fdesc)) {
      const char *field_name = pfdesc_get_name(fdesc);

      fprintf(pschema_file, "%s\n", field_name);
    }
    fclose(pschema_file);
  }
}

bool dpi_padapt_init(pschema_db_t* pschema_db)
{
    if (NULL == pschema_db) {
      return false;
    }
    g_padapt_controller = padapt_controller_create(pschema_db);
    return true;
}

static bool ends_with(const char *str, const char *suffix) {
    // 计算字符串和子串的长度
    size_t str_len = strlen(str);
    size_t suffix_len = strlen(suffix);

    // 如果子串长度大于字符串长度，直接返回 false
    if (suffix_len > str_len) {
        return false;
    }

    // 使用 strstr() 函数查找子串在字符串中的位置
    const char *result = strstr(str + (str_len - suffix_len), suffix);

    // 如果结果为 NULL，则表示子串不在字符串中，返回 false；否则返回 true
    return result != NULL;
}

static void _padapt_load_script(const char * file_path)
{
  int   ret = 1;
  char *filename;
  const char* err_msg = NULL;
  if (file_path == NULL) return;

  filename = strrchr(file_path, '/');
  if (!filename) return;
  filename += 1;

  // 确保文件是 .lua 结尾，并且以 adapt_ 开头
  if (ends_with(filename, ".lua") && strncmp(filename, "adapt_", 6) == 0 && ends_with(g_config.lua_adapt_script_type,".lua")) {
    ret = padapt_controller_load_adapt_script(g_padapt_controller, file_path);
    printf("adapt proto file = %s\n", filename);
  }else if (ends_with(filename, ".yalua") && strncmp(filename, "adapt_", 6) == 0 && ends_with(g_config.lua_adapt_script_type,".yalua")) {
    ret = padapt_controller_load_bytecode_adapt_script(g_padapt_controller, file_path);
    printf("adapt proto file = %s\n", filename);
  }
  if (ret < 0) {
    err_msg = precord_misc_get_last_error();
    printf("load lua adapt file %s failed, error: %s\n", file_path, err_msg);
  }
}

static void _list_files_recursive(const char *path) {
    DIR *dir;
    struct dirent *entry;
    struct stat statbuf;

    // 打开目录
    if ((dir = opendir(path)) == NULL) {
        perror("opendir");
        return;
    }

    // 读取目录中的每个项
    while ((entry = readdir(dir)) != NULL) {
        char filepath[1024];
        snprintf(filepath, sizeof(filepath), "%s/%s", path, entry->d_name);

        // 获取文件信息
        if (lstat(filepath, &statbuf) == -1) {
            perror("lstat");
            continue;
        }

        // 如果是目录，则递归遍历该目录
        if (S_ISDIR(statbuf.st_mode)) {
            // 忽略 . 和 .. 目录
            if (strcmp(entry->d_name, ".") != 0 && strcmp(entry->d_name, "..") != 0) {
                _list_files_recursive(filepath);
            }
        } else {
            _padapt_load_script(filepath);
            // 如果是文件，则打印文件路径
            printf("load lua adapt file %s\n", filepath);
        }
    }

    closedir(dir);
}

bool dpi_padapt_load_script(const char * dir)
{
  char init_file[256]  = {0};
  char proto_dir[256] = {0};

  if (dir == NULL) return false;
  snprintf(init_file, sizeof(init_file), "%s/init.lua", dir);
  if (!access(init_file, F_OK)) {
    printf("adapt init lua = %s\n", init_file);
    padapt_controller_load_init_script(g_padapt_controller, init_file);
  }

  dpi_utils_traverse_dir(dir, _padapt_load_script);
  dpi_pschema_dump_adapt_proto_schemas(g_config.dpi_field_dir);
  return true;
}

padapt_engine_t *dpi_padapt_engine_create()
{
    return padapt_engine_create(g_padapt_controller);
}

void dpi_padapt_engine_destroy(padapt_engine_t * engine)
{
    if (engine == NULL) return;

    padapt_engine_destroy(engine);
}

int dpi_padapt_record_adapt(padapt_engine_t *engine, precord_t **to_record, precord_t * from_record)
{
    return padapt_engine_record_check_and_adapt(engine, to_record, from_record);
}

void dpi_padapt_print_all_schema()
{
  printf("dpi_padapt_print_all_schema start -------------------------------------------------\n");
    pschema_db_t * db = padapt_controller_get_adapted_schema_db(g_padapt_controller);
    for (pschema_t* schema = pschema_get_first(db); schema != NULL; schema = pschema_get_next(db, schema)) {
        printf("adapt schema name: %s\n", pschema_get_proto_name(schema));
        for (pfield_desc_t * desc = pschema_fdesc_get_first(schema); desc != NULL; desc = pschema_fdesc_get_next(schema, desc)) {
            printf("adapt field name = %s\n", pfdesc_get_name(desc));
        }
    }
  printf("dpi_padapt_print_all_schema end -------------------------------------------------\n");
}