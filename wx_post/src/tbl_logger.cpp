/****************************************************************************************
 * 文 件 名 : tbl_logger.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2019-08-06
* 编    码 : zhangsx      '2019-08-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "tbl_logger.h"
#include "tbl_config.h"
#include "tbl_utils.h"

#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/async.h>

void TblLogger::createLogger(bool bLogToFile)
{
    std::string && strLogDir = CFG.getCurrentConfigValue({"log_conf", "log_dir"}, std::string("logs"));

    std::string && tblLogDir = CFG.getCurrentConfigValue({"log_conf", "tbl_log_dir"}, std::string("logs"));
    
    ensureDirExist(strLogDir.c_str());

    ensureDirExist(tblLogDir.c_str());

    /*创建tbl日志     这里async_factory是创建异步日志库的意思 */
    auto tblLogger           = spdlog::rotating_logger_mt<spdlog::async_factory>("tbl_log",     tblLogDir + "/tbl_log.txt",         CFG.getCurrentConfigValue({"log_conf", "log_max_size"}, 104857600), 3);

    /*创建运行时日志*/
    if (bLogToFile)
    {
        auto runLogger       = spdlog::rotating_logger_mt<spdlog::async_factory>("run_log",     strLogDir + "/log.txt",             CFG.getCurrentConfigValue({"log_conf", "log_max_size"}, 104857600), 3);
        spdlog::set_default_logger(runLogger);
    }
    else
    {
        auto consoleLogger   = spdlog::stdout_color_mt("interest");
        spdlog::set_default_logger(consoleLogger);
    }

    // log gloable level
    spdlog::set_level(spdlog::level::from_str( CFG.getCurrentConfigValue({"log_conf", "log_level"}, std::string("info")) ) );

    // logger 每次遇到 warn 级别日志均 flush
    spdlog::default_logger()->flush_on(spdlog::level::warn);
    
    spdlog::get("tbl_log")->flush_on(spdlog::level::warn);
    /* 这里计划以后将tbl日志等级改为与默认日志库等级不一致的版本 */
    spdlog::get("tbl_log")->set_level(spdlog::level::from_str( CFG.getCurrentConfigValue({"log_conf", "log_level"}, std::string("info")) ) ); 
}

std::shared_ptr<spdlog::logger> TblLogger::defaultLogger()
{
    return spdlog::default_logger();
}

std::shared_ptr<spdlog::logger> TblLogger::tblLogger()
{
    return spdlog::get("tbl_log");
}
