/****************************************************************************************
 * 文 件 名 : dpi_weixin_info_mini.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq         2021/05/19
 修改: 李春利         2021/05/19
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <iconv.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"
#include "dpi_wx_info.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
const char g_mini_flag[] = {0x08, 0x08, 0x0d, 0x0d, 0x0d, '\0'};


static int8_t wx_info_find_blocks(dpi_str_t * data, uint32_t * arrs, uint16_t arr_max_len)
{
    if (data->data == NULL) {
        return -1;
    }

    int32_t pos = 0;
    uint16_t index = 0;
    while (1) {
        int32_t tmp = dpi_strstr(data->data + pos, data->len - pos , g_mini_flag, strlen(g_mini_flag));
        if (tmp > 0) {
            if (arr_max_len < index){
                return -1;
            }
            arrs[index] = pos + tmp;
            index++;
            pos = pos + tmp + strlen(g_mini_flag);
            // pos 加一，防止查找重复子串
            // pos += strlen(g_mini_flag);
        } else {
            break;
        }
    }

    return index;
}

static  int8_t
get_suffix_len(dpi_str_t * data, uint32_t pos, uint8_t pos_len) {
    uint8_t suffix_len;
    if (data->len < pos) {
        return -1;
    }

    uint8_t wxflag0 = get_uint8_t(data->data, pos + pos_len);
    if (wxflag0 == 0x77 || wxflag0 == 0x67) {
        return 0;
    }

    uint16_t wxflag1 = get_uint16_ntohs(data->data, pos + pos_len);
    if (wxflag1 == 0x0077 || wxflag1 == 0x0067) {
        return 1;
    }

    if (wxflag1 == 0x000d || wxflag1 == 0x330d || wxflag1 == 0x350d) {
        return 2;
    }

    uint32_t wxflag2 = (get_uint32_ntohl(data->data, pos + pos_len) >> 4);
    if (wxflag2 == 0x0d0077 || wxflag2 == 0x0d0067) {
        return 2;
    }


    return -1;
}


static uint8_t
wx_info_cut_datablock(dpi_str_t *data, uint32_t flag_pos, uint8_t suffix_len, MiniBlock * block)
{
    if (data == NULL || data->data == NULL) {
        return false;
    }

    if (data->len < flag_pos) {
        return false;
    }

    if (flag_pos < 15 + WX_INFO_TYPE_MAX_OFFSET) {
        return false;
    }

    uint32_t i = flag_pos  - 15;    // 从标记为前 15 个字节开始查找
    if (i > data->len) {
        return false;
    }
    uint8_t shift = 0;
    for (;i > flag_pos - WX_INFO_TYPE_MAX_OFFSET; --i) {
        if (data->data[i] == (flag_pos - i + suffix_len + strlen(g_mini_flag))) {
            block->type_len = data->data[i];
            block->type_pos = i;
            // block->data.len = ((data->data[i - 3] ^ 128) << 7) | data->data[i - 2];
            block->data.len = data->len - i;
            block->data.data = data->data + i;
            return true;
        }
    }

    return false;
}

static uint32_t
wx_info_trans_len(uint32_t len)
{
    // 转换对应关系
    //   | type value       |  len
    //   | 0                |  0
    //   | N = 1,2,3,4      |  N
    //   | 5                |  6
    //   | 6                |  8
    //   | 7                |  8
    //   | 8                |  0
    //   | 9                |  0
    //   | 10,11            |  N/A
    //   | N >= 12 && 偶数  |  (N - 12) / 2
    //   | N >= 13 && 奇数  |  (N - 13) / 2
    if (len <= 4 ) {
        return len;
    } else if (len == 8 || len == 9 || len == 10 || len == 11) {
        return 0;
    } else if (len == 5) {
        return 6;
    } else if (len == 6 || len == 7) {
        return 8;
    } else if (len >= 12 && (len % 2) == 0) {
        return (len - 12) / 2;
    } else if (len >= 13 && (len % 2) == 1) {
        return (len - 13) / 2;
    }

    return 0;
}

// 解析 type 为 13 的内部数据块
static
int wx_info_nest_block(dpi_str_t *data, WXInfo *info)
{
    uint32_t offset = 0;
    if (data->data[0] != 0x7b) {
        return -1;
    }
    // 0x7b
    offset += 1;
    // 46 固定字节
    offset += 46;

    uint16_t flag0 = get_uint16_ntohs(data->data, offset);
    uint16_t flag1 = get_uint16_ntohs(data->data, offset + 2);

    if ((flag0 == 0x0000 || flag0 == 0x0001) && (flag1 == 0x0000 || flag1 == 0x0001)) {
        return -1;
    }
    if ((flag0 == 0x0000 || flag0 == 0x0001) && flag1 != 0x0000 && flag1 != 0x0001) {
        offset += 2;
    }

    uint8_t index = 0;
    while (offset < data->len) {
        uint16_t elem_len = get_uint16_ntohs(data->data, offset);
        offset += 2;
        if (offset + elem_len > data->len) {
            return -1;
        }
        // char arrr[128];
        snprintf(info->nest_elems[index], elem_len + 1, "%s", data->data + offset);
        index++;
        info->nelts++;
        offset += elem_len;

        if (get_uint16_ntohs(data->data, offset) == 0x0000) {
            break;
        }
    }

    // 16 固定字节
    offset += 16;


    flag0 = get_uint16_ntohs(data->data, offset);
    flag1 = get_uint16_ntohs(data->data, offset + 2);

    if ((flag0 == 0x0000 || flag0 == 0x0001) && (flag1 == 0x0000 || flag1 == 0x0001)) {
        return -1;
    }
    if ((flag0 == 0x0000 || flag0 == 0x0001) && flag1 != 0x0000 && flag1 != 0x0001) {
        offset += 2;
    }

    while (offset < data->len) {
        uint16_t elem_len = get_uint16_ntohs(data->data, offset);
        offset += 2;
        if (offset + elem_len > data->len) {
            return -1;
        }
        snprintf(info->nest_elems[index], elem_len + 1, "%s", data->data + offset);
        index++;
        info->nelts++;
        offset += elem_len;

        if (get_uint16_ntohs(data->data, offset) == 0x0000) {
            break;
        }
    }

    return 0;
}

static
int wx_info_ghinfo(dpi_str_t *data, WXInfo *info)
{


    return 0;
}

static
int wx_info_person_info(dpi_str_t *data, WXInfo *info)
{

    return 0;
}


static
void wx_info_split_person_block(WXInfo *info)
{
    struct {
        uint8_t index;
        char * elem;
    }elems [] = {
        {0, info->wxPersonSign},
        {1, NULL},
        {2, NULL},
        {3, info->wxLocSimple},
        {0, NULL}
    };

    if (info->nelts == 4) {
        int i = 0;
        for (; i < 4; ++i) {
            if (elems[i].elem != NULL) {
                strcpy(elems[i].elem, info->nest_elems[i]);
            }
        }
    }
}


static
void wx_info_split_gh_block(WXInfo *info)
{
    struct {
        uint8_t index;
        char * elem;
    }elems [] = {
        {0, info->wxGHAbout},
        {1, NULL},
        {2, NULL},
        {3, NULL},
        {4, info->wxLocSimple},
        {0, NULL}
    };

    if (info->nelts == 5) {
        int i = 0;
        for (; i < 4; ++i) {
            if (elems[i].elem != NULL) {
                strcpy(elems[i].elem, info->nest_elems[i]);
            }
        }
    }
}


static
int dissect_wxinfo_mini_block(MiniBlock * block, WXInfo * info)
{
    struct {
        uint8_t index;
        char * elem;
    }elems [] = {
        {0, info->wxid},
        {1, info->weChatId},
        {2, info->remarkName},
        {3, NULL},
        {4, info->wxNickName},
        {5, info->wxNickNameSimple},
        {6, info->wxNickNameFull},
        {7, info->remarkNameFull},
        {8, NULL},
        {9, NULL},
        {10, NULL},
        {11, NULL},
        {12, info->remarkNameSimple},
        {13, NULL},
        {0, NULL}
    };

    const char * type_block = (const char *)block->data.data + 1;
    dpi_str_t data_block;
    data_block.data = block->data.data + block->type_len;
    data_block.len = block->data.len - block->type_len;
    uint8_t index = 0;
    uint32_t data_pos = block->type_len;
    for (int i = 0; i < block->type_len - 1; ++i) {
        // 目前只发现前 14 个类型中有价值数据
        if (index >= 14) break;

        uint32_t t_len = 0;
        while(((*type_block) & 128) == 128) {
            t_len = t_len << 7;
            uint8_t tmp = *type_block ^ 128;
            t_len = t_len | tmp;
            type_block++;
            ++i;
        }
        t_len = t_len << 7;
        t_len = t_len | (*type_block);
        type_block++;

        uint32_t len_value = wx_info_trans_len(t_len);


        if (elems[index].elem != NULL) {
            if (len_value > data_block.len || len_value >= 256) {
                break;
            }
            snprintf(elems[index].elem, len_value + 1, "%s", data_block.data);
        }

        if (index == 13) {
            dpi_str_t child_block_data;
            child_block_data.data = data_block.data;
            child_block_data.len = len_value < data_block.len ? len_value : data_block.len;
            // char elems[WX_INFO_MINI_BLOCK_MAX_LEN][128] = { 0 };
            uint8_t arr_size = 0;

            wx_info_nest_block(&child_block_data, info);

            if (info->wxid[0] == '\0') {
                return -1 ;
            }
            if (strstr(info->wxid, "wxid_") != NULL) {
                wx_info_split_person_block(info);
            } else if (strstr(info->wxid, "gh_") != NULL) {
                wx_info_split_gh_block(info);
            } else {

            }

            int i = 0;
            for (i = 0; i < info->nelts; ++i) {
                snprintf(info->resv1 + strlen(info->resv1), 256,"%s;", info->nest_elems[i]);
            }
            if (info->resv1[strlen(info->resv1) - 1] != '\0') {
                info->resv1[strlen(info->resv1) - 1] = '\0';
            }


        }

        data_block.data += len_value;
        data_block.len  -= len_value;

        ++index;
    }

    return 0;
}

int dissect_weixin_info_mini(struct flow_info *flow, int direction, uint32_t seq,
    const uint8_t *payload,  const uint32_t payload_len, uint8_t flag)
{

    if (payload_len <= 200) {
        return 0;
    }

    uint32_t offset = 0;
     /* 每帧数据以d6 开头 */
    if (payload[0] != WX_INFO_IDENT_0) {
        return 0;
    }

    offset += 1;
    /* unknown bytes */
    offset += 4;

    uint32_t head_len = get_uint32_ntohl(payload, offset);
    offset += 4;

    if (offset + head_len > payload_len) {
        return 0;
    }
    uint16_t identify = get_uint16_ntohs(payload, offset + head_len - 2);
    offset += head_len;

    dpi_str_t data;
    memset(&data, 0, sizeof(dpi_str_t));
    data.data =(uint8_t *)( payload + offset);
    data.len = payload_len - offset;

    uint32_t pos_arrs[16] = {0};
    uint8_t block_num = wx_info_find_blocks(&data, pos_arrs, 16);        // 查找有几个数据块
    if (block_num <= 0) {
        return -1;
    }
    for (int i = 0; i < block_num; ++i) {
        int8_t suffix_len = get_suffix_len(&data, pos_arrs[i], strlen(g_mini_flag));
        if (suffix_len < 0) {
            continue;
        }

        MiniBlock block;
        memset(&block, 0, sizeof(MiniBlock));


        wx_info_cut_datablock(&data, pos_arrs[i], suffix_len, &block);

        WXInfo info;
        memset(&info, 0, sizeof(WXInfo));

        dissect_wxinfo_mini_block(&block, &info);
        strcpy(info.infotype, "mini");

        write_wx_info_log(flow, direction, &info);
    }

    return 0;
}
