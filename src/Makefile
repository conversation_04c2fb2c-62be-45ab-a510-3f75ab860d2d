#   BSD LICENSE
#
#   Copyright(c) 2010-2014 Intel Corporation. All rights reserved.
#   All rights reserved.
#
#   Redistribution and use in source and binary forms, with or without
#   modification, are permitted provided that the following conditions
#   are met:
#
#     * Redistributions of source code must retain the above copyright
#       notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright
#       notice, this list of conditions and the following disclaimer in
#       the documentation and/or other materials provided with the
#       distribution.
#     * Neither the name of Intel Corporation nor the names of its
#       contributors may be used to endorse or promote products derived
#       from this software without specific prior written permission.
#
#   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
#   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
#   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
#   A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
#   OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
#   SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT
#   LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
#   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
#   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
#   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#   OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

ifeq ($(RTE_SDK),)
$(error "Please define RTE_SDK environment variable")
endif

# Default target, can be overridden by command line or environment
RTE_TARGET ?= x86_64-native-linuxapp-gcc


include $(RTE_SDK)/mk/rte.vars.mk

# binary name
#APP=../../run/yaWxDpi_$(shell git describe --tag $(shell git rev-list --tags --max-count=1))
APP=../../run/yaWxDpi_$(shell git describe --abbrev=0 --tags)

# all source are stored in SRCS-y

SRCS-y := dpi_main.c dpi_detect.c dpi_proto_ids.c  dpi_tbl_log.c charsets.c\
	dpi_tcp_reassemble.c  dpi_vtysh.c dpi_thread_timer.c dpi_log.c dpi_common.c  dpi_tcp_rsm.c\
	dpi_conversation.c ip2region.c \
	post.c \
	dpi_http.c dpi_http_sub.c\
	dpi_weixin_voice.c \
	dpi_zoom_conference.c \
	dpi_trailer.c \
	dpi_qq_voip.c \
	dpi_weixin.c dpi_weixin_sub.c\
	dpi_wx_group_head.c \
	dpi_wx_msg.c\
	dpi_weixin_relation.c\
	dpi_wx_info.c \
	dpi_wx_info_mini.c \
	dpi_skype_media.c	\
	dpi_qq_event.c \
	dpi_wx_voice_peers.c \
	dpi_wx_location_sharing.c \
	dpi_wxmisc.c \
	dpi_string.c \
	dpi_rtp.c dpi_douyin.c\
	dpi_wxid.c \
	dpi_wxpay.c dpi_wxpay_manager.c\
	dpi_gquic.c \
	dpi_protorecord.c\
	dpi_precord_writer.c\
	dpi_utils.c\
	dpi_tbl_record_log.c\
	dpi_lua_adapt.c
#SRCS-y := *.c


CFLAGS += -std=c11 -g -D_GNU_SOURCE -pthread -I$(SRCDIR)/. -I$(SRCDIR)/../include/ -I/usr/include/glib-2.0/ -I/usr/lib64/glib-2.0/include
CFLAGS += $(WERROR_FLAGS)   -Wno-unused-parameter -Wno-unused-variable -Wno-unused-function -Wno-unused-but-set-variable
# define PROJECT_VERSION_STR macro for dpi_main.o
dpi_main.o: GIT_TAG       := $(shell git describe --abbrev=0 --tags)
dpi_main.o: GIT_COMMITID  := $(shell git log -1 --pretty=format:%h)
dpi_main.o: GIT_BRANCH    := $(shell git rev-parse --abbrev-ref HEAD)
dpi_main.o: GIT_DATA      := $(shell date +%Y_%m_%d)
dpi_main.o: BUILD_STR     := $(GIT_TAG)_$(GIT_COMMITID)_$(GIT_BRANCH)_$(GIT_DATA)
dpi_main.o: CFLAGS += -DPROJECT_VERSION_STR=\"$(BUILD_STR)\"

LDLIBS += -L$(SRCDIR)/../lib
LDLIBS += -lpthread  -liniparser -lglib-2.0 -lwxc -lz -lmuduo_net_cpp11 -lmuduo_base_cpp11 -lstdc++ -lssl -lcrypto -lyaProtoRecord -lyaFtypes -lcjson -llua

# 读取环境变量是否添加编译参数
ifneq ($(BUILD_DEBUG),)
CFLAGS += -O0 -fsanitize=address -fno-omit-frame-pointer -static-libasan
endif


include $(RTE_SDK)/mk/rte.extapp.mk
dpi_wxpay_manager.o: dpi_wxpay_manager.c
	g++ -x c++ -o dpi_wxpay_manager.o -c ../dpi_wxpay_manager.c -std=c++11 -lpthread

dpi_precord_writer.o:dpi_precord_writer.c
	g++ -x c++ -o dpi_precord_writer.o -c ../dpi_precord_writer.c -I$(SRCDIR)/../include/ -std=c++11 -lpthread -ldpdk -lyaProtoRecord -lyaFtypes -lcjson -g
