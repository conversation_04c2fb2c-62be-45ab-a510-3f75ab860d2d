#include <stdint.h>
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <inttypes.h>

#include <string.h>

#include "glib.h"

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_weixin.h"
#include "openssl/md5.h"


void  *g_wxrela_hash  = NULL;
pthread_rwlock_t  wxrela_rwlock = PTHREAD_RWLOCK_INITIALIZER; //定义和初始化读写锁
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

#define SUB_COPY_VALUE(a, ptr, len, release)      \
do {                                              \
    /* 如果已有值且需要释放，先释放它 */           \
    if ((a).value != NULL && (a).free != NULL) {  \
        (a).free((a).value);                      \
    }                                             \
                                                  \
    /* 复制新值 */                                \
    if (ptr != NULL && len > 0) {                 \
        (a).value = memdup(ptr, len);             \
        (a).value_len = len;                      \
        (a).free = release;                       \
    } else {                                      \
        (a).value = NULL;                         \
        (a).value_len = 0;                        \
        (a).free = NULL;                          \
    }                                             \
} while(0)  /* 注意：这里没有分号 */


weixin_field_t wx_relation_array_f[] = {
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_TIME,        EM_F_TYPE_STRING,     "time"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_VAL1,   EM_F_TYPE_STRING,     "resv_val1"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_VAL2,   EM_F_TYPE_STRING,     "resv_val2"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_1,      EM_F_TYPE_STRING,     "resv_1"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_2,      EM_F_TYPE_STRING,     "resv_2"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_3,      EM_F_TYPE_STRING,     "resv_3"),
  WEIXIN_FIELD_ARGS(EM_WX_RELATION_RESV_4,      EM_F_TYPE_STRING,     "resv_4"),
};


static
int write_wx_reation_log(WxRealtionInfo  *info)
{
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;
    char str[64] = { 0 };


    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    char buff[20]={0};
    for(i=0;i<EM_WX_RELATION_MAX;i++)
    {
        switch(wx_relation_array_f[i].index)
        {
            case EM_WX_RELATION_TIME:
              get_now_datetime(str, sizeof(str));
              write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
              break;
            case EM_WX_RELATION_RESV_VAL1:
              write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxnum_base64, strlen(info->wxnum_base64));
              break;
            case EM_WX_RELATION_RESV_VAL2:
              write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->url_wxnum_base64, strlen(info->url_wxnum_base64));
              break;
            case EM_WX_RELATION_RESV_1:
              write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxnum);
              break;
            case EM_WX_RELATION_RESV_2:
              write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->url_wxnum);
              break;
            case EM_WX_RELATION_RESV_3:
              write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->videocdnmsg_wxnum);
              // write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxnum_hex, strlen(info->wxnum_hex));
              break;
            case EM_WX_RELATION_RESV_4:
              write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxvideo_flag);
              break;
            default:
              write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
              break;
        }
    }

    log_ptr->type         = TBL_LOG_RELATION;
    log_ptr->len          = idx;
    // log_ptr->tid          = flow->thread_id;

    if(tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


static
gboolean foreach_callback(gpointer key, gpointer value, gpointer user_data)
{
  uint32_t now = time(NULL);
  WxRealtionInfo * data = (WxRealtionInfo *)value;

  if (data->wxnum == data->url_wxnum || data->wxnum == data->videocdnmsg_wxnum ) {
    return TRUE;   // 关联自己的直接删除
  }

  if (now - data->time < 180) {
    return FALSE;
  }

  write_wx_reation_log(data);

  return TRUE;
}

static
void *wx_relation_thread(void *args)
{
  uint32_t wheel = time(NULL);
  time_t now;
  while (1) {


    sleep(60);
    now = time(NULL);
    // 每隔 60s
    if (now - wheel  >= 60) {
      // 清空超时人数
      pthread_rwlock_rdlock(&wxrela_rwlock);
      g_hash_table_foreach_remove(g_wxrela_hash, foreach_callback, NULL);
      pthread_rwlock_unlock(&wxrela_rwlock);     //解锁
      wheel = now;
    }
  }

  return NULL;
}


void base64_encode_rela(const char * in, char * out)
{
  char *encode_out;
  encode_out = g_malloc0(BASE64_ENCODE_OUT_SIZE(strlen(in)));
  base64_encode((const u_char *)in, strlen(in), encode_out);
  memcpy(out, encode_out, strlen(encode_out));

  g_free(encode_out);
}


int dissect_weixin_relation(struct flow_info * flow, weixin_info_t * line_info)
{
  /* 提取version */
  const char *value=NULL;
  int  value_len=0;
  // time_t now;
  char tmp_str[64] = { 0 };
  char msg[512] = { 0 };
  char key_str[64] = { 0 };
  char wxnum_hex[64] = { 0 };
  gchar       **elems = NULL;
  uint8_t       elem_cnt = 0;
  const char   *delim = "_";
  uint64_t      wxnum;
  int i = 0;
  uint8_t   field_type = 0;
  const char * cdnmsg_str = NULL;
  const char * url_str = NULL;
  int           url_str_len = 0;
  const char * fileurl_str = NULL;
  int           fileurl_str_len = 0;
  struct {
    const char *data;
    int len;
    uint8_t  type;
  }rela_str[3] = {{ 0 }};
  uint8_t index = 0;
  uint64_t val = 0;
  uint8_t  vflag = 0;


  if (line_info[EM_WX_VIDEOCDNMSG].value != NULL) {
    elems = g_strsplit((char *)line_info[EM_WX_VIDEOCDNMSG].value, delim, 0);
    for (i = 0; elems[i]; ++i) {
      elem_cnt++;
    }
    if (elem_cnt >= 3) {
      cdnmsg_str = elems[2];
      rela_str[index].data = elems[2];
      rela_str[index].len = strlen(elems[2]);
      rela_str[index].type = EM_WX_RELA_FIELD_VIDEOCDNMSG;
      index += 1;
    }
  }

  if (line_info[EM_WX_URL].value != NULL) {
    url_str=dissect_wx_url_key((const char*)line_info[EM_WX_URL].value,
                             (int)line_info[EM_WX_URL].value_len,
                             "storeid=",&url_str_len);
    rela_str[index].data = dissect_wx_url_key((const char*)line_info[EM_WX_URL].value,
                             (int)line_info[EM_WX_URL].value_len,
                             "storeid=",&rela_str[index].len);
    rela_str[index].type = EM_WX_RELA_FIELD_URL;
    index += 1;
  }

  if (line_info[EM_WX_FILEURL].value != NULL){
    fileurl_str = dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "storeid=",&fileurl_str_len);
    rela_str[index].data = dissect_wx_url_key((const char*)line_info[EM_WX_URL].value,
                             (int)line_info[EM_WX_URL].value_len,
                             "storeid=",&rela_str[index].len);
    rela_str[index].type = EM_WX_RELA_FIELD_FILEURL;
    index += 1;
  }


  // 遍历结构体数组，直到提取出的第一个 wxnum 大于0，跳出循环
  for (i = 0; i < index; ++i) {
    if (rela_str[i].data == NULL)  continue;
    if (rela_str[i].len < 38 || rela_str[i].data[0] == 51) continue;
    strncpy(wxnum_hex, rela_str[i].data + 17,8);
    if (isHEX(wxnum_hex, strlen(wxnum_hex)) != 1) continue;
    val = hex_to_decimal(wxnum_hex, 8);

    if (val > 0) {
      field_type = rela_str[i].type;
      break;
    } else {
      continue;
    }
  }

  if (line_info[EM_WX_WEIXINNUM].value != NULL) {
    wxnum = atoll((char *)line_info[EM_WX_WEIXINNUM].value);
  } else if (line_info[EM_WX_PYQ_UIN].value != NULL) {
    wxnum = atoll((char *)line_info[EM_WX_PYQ_UIN].value);
  }

  // 视频号判定
  {
    const char video_flag[] = "/251/";
    char * url_flag = NULL;
    char * fileurl_flag = NULL;
    if (line_info[EM_WX_URL].value != NULL && line_info[EM_WX_URL].value_len != 0) {
      url_flag = strstr((char *)line_info[EM_WX_URL].value, video_flag);
    }
    if (line_info[EM_WX_FILEURL].value != NULL && line_info[EM_WX_FILEURL].value_len != 0) {
      fileurl_flag = strstr((char *)line_info[EM_WX_FILEURL].value, video_flag);
    }
    if (url_flag != NULL || fileurl_flag != NULL) {
      vflag = 2;
    }
  }

  //视频号回填
  SUB_COPY_VALUE(line_info[EM_WX_RESV4], NULL,vflag, free);

  if (val == 0 || wxnum == 0) return -1;
  //wxnum回填
  char wxnum_base64[64]={0};
  sprintf(tmp_str,"%lu", strtoul(wxnum_hex, NULL, 16));
  base64_encode_rela(tmp_str, wxnum_base64);
  SUB_COPY_VALUE(line_info[EM_WX_RESV3], wxnum_base64, strlen(wxnum_base64), free);
  if(strlen(wxnum_base64)==0||strlen(tmp_str)==0||line_info[EM_WX_RESV3].value==NULL)
   printf("wxnum_base64=0！！！！！！！！！");
  snprintf(key_str, sizeof(key_str), "%"PRIu64"%"PRIu64, wxnum, val);

  pthread_rwlock_wrlock(&wxrela_rwlock);     //写锁
  WxRealtionInfo * info = (WxRealtionInfo * )g_hash_table_lookup(g_wxrela_hash, key_str);
  pthread_rwlock_unlock(&wxrela_rwlock);     //解锁
  if (info == NULL) {
    info = g_malloc0(sizeof(WxRealtionInfo));
    info->time = time(NULL);
    info->wxnum = wxnum;
    snprintf(tmp_str, sizeof(tmp_str), "%"PRIu64, info->wxnum);
    base64_encode_rela(tmp_str, info->wxnum_base64);

    snprintf(info->wxnum_hex, sizeof(info->wxnum_hex), "%s", wxnum_hex);
    info->wxvideo_flag = vflag;

    switch(field_type){
    case EM_WX_RELA_FIELD_URL:
    case EM_WX_RELA_FIELD_FILEURL:
      info->url_wxnum = val;
      snprintf(tmp_str, sizeof(tmp_str), "%"PRIu64, info->url_wxnum);
      base64_encode_rela(tmp_str, info->url_wxnum_base64);
      break;
    case EM_WX_RELA_FIELD_VIDEOCDNMSG:
      info->videocdnmsg_wxnum = val;
      snprintf(tmp_str, sizeof(tmp_str), "%"PRIu64, val);
      base64_encode_rela(tmp_str, info->videocdnmsg_wxnum_base64);
      break;
    default:
      break;
    }

    pthread_rwlock_wrlock(&wxrela_rwlock);     //写锁
    g_hash_table_insert(g_wxrela_hash, g_strdup(key_str), (void *)info);
    pthread_rwlock_unlock(&wxrela_rwlock);     //解锁
  }


  g_strfreev(elems);

  return 0;
}


static
int write_wx_relation_field_txt(weixin_field_t *proto_array,const char *proto_name)
{
    if(proto_name==NULL || proto_array==NULL){return 0;}

    int i;
    char file_path[TBL_PATH_LENGTH ]={0};
    snprintf(file_path,TBL_PATH_LENGTH ,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);
    FILE *fp=fopen(file_path,"w+");
    if (fp) {
      for(i=0;i<EM_WX_RELATION_MAX;i++)
      {
          fwrite(proto_array[i].field, proto_array[i].field_len,1,fp);
          fwrite("\n",1,1,fp);
      }

      fclose(fp);
    }

    return 1;
}

void init_wxpyq_relation(void)
{
  pthread_t wx_relation;
  g_wxrela_hash =  g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);


  int status = pthread_create(&wx_relation, NULL, wx_relation_thread, NULL);
  if(status != 0)
  {
      DPI_LOG(DPI_LOG_ERROR, "error on create weixin_voice_session thread");
      exit(-1);
  }
}

void init_wx_relation_dissector(void)
{
  write_wx_relation_field_txt(wx_relation_array_f, "relation");
}