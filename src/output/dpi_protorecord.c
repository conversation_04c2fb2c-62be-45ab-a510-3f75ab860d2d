#include "dpi_protorecord.h"
#include "dpi_lua_adapt.h"

#include <stdio.h>
#include <stdlib.h>

#define PSCHEMA_COMMON "pschema_common"

static pschema_db_t *g_weixin_db;
static DpiProto *ya_proto_head = NULL;
extern __thread ya_allocator_t *th_alloc;
// static atomic_int creat_record = 0;
// static atomic_int destroy_record = 0;
int ya_record_register_proto(const char *template, const char *proto_name, const char *proto_full_name) {
  pschema_register_proto(g_weixin_db, template, proto_name, proto_full_name);
  return 0;
}

/* 声明 libyaProtoRecord 中的隐藏接口 */
pschema_t *ya_record_get_proto(const char *proto_name) { return pschema_get_proto(g_weixin_db, proto_name); }

precord_t *ya_create_record(const char *proto_name) {
  // atomic_fetch_add_explicit(&creat_record, 1, memory_order_relaxed);
  precord_t *record = precord_fast_create(th_alloc, g_weixin_db, PRECORD_FLAG_ARENA_ALLOC);
  player_t  *curr_layer = NULL;

  curr_layer = precord_layer_put_new_layer(record, "common");
  if (curr_layer == NULL) {
    printf("---- put common layer failed \n");
  }
  curr_layer = precord_layer_put_new_layer(record, proto_name);
  if (curr_layer == NULL) {
    printf("---- put proto_name \"%s\"layer failed \n", proto_name);
  }
  return record;
}
void ya_destroy_record(precord_t *record) {
  // atomic_fetch_add_explicit(&destroy_record, 1, memory_order_relaxed);
  return precord_destroy(record);
}

pfield_t *dpi_precord_fvalue_put_by_name(precord_t *record, const char *field_name, ya_fvalue_t *field_value) {
  return precord_fvalue_put(record, field_name, field_value);
}
pfield_t *precord_fvalue_put_by_index(precord_t *record, int fieldIndex, ya_fvalue_t *field_value);

pfield_t *dpi_precord_fvalue_put_by_index(precord_t *record, int fieldIndex, ya_fvalue_t *field_value) {
  precord_fvalue_put_by_index(record, fieldIndex, field_value);
  return NULL;
}
pfield_t *dpi_precord_fvalue_put_by_index_increment(precord_t *record, int *fieldIndex, ya_fvalue_t *field_value) {
  precord_fvalue_put_by_index(record, *fieldIndex, field_value);
  (*fieldIndex)++;
  return NULL;
}
static ya_ftenum_t dpi_ftype_to_ya_ftype(int dpi_ftype) { return YA_FT_STRING; }

/*协议注册初始化接口*/
/****************************************************************************************/
/*使用本接口，需要在回调函数内完成注册字段*/
void dpi_proto_register(const char *proto_name, void (*proto_register)(void)) {
  struct dpi_proto *proto = (struct dpi_proto *)malloc(sizeof(struct dpi_proto));
  proto->name = proto_name;
  proto->func = proto_register;
  proto->next = NULL;

  if (ya_proto_head == NULL) {
    ya_proto_head = proto;
  } else {
    struct dpi_proto *p = ya_proto_head;
    while (p->next != NULL) {
      p = p->next;
    }
    p->next = proto;
  }
}
pschema_t * dpi_pschema_get_proto(const char * proto_name)
{
    if (proto_name == NULL) {
        return NULL;
    }

    return pschema_get_proto(g_weixin_db, proto_name);
}
/*使用这个接口，协议直接传入proto_array完成注册*/
int dpi_pschema_register_proto_field(
    const char *proto_name, dpi_record_field_table *field_table, int field_count, int with_common_schema) {

  if (pschema_get_proto(g_weixin_db, proto_name))
  {
      printf("重复注册 proto schema: <%s>\n", proto_name);
      return 0;
  }

  pschema_t *schema = pschema_register_proto(
      g_weixin_db, NULL, proto_name, "no protocol desc.");

  for (int i = 0; i < field_count; i++) {
    pschema_register_field(schema, field_table[i].field_name, dpi_ftype_to_ya_ftype(field_table[i].type), "");
  }

  return 0;
}
/*********************************************************************************/

/*注册所有协议*/
static void dpi_register_proto_all(void) {
  struct dpi_proto *p = ya_proto_head;
  while (p != NULL) {
    p->func();
    p = p->next;
  }
}

/*注册公共字段*/
static int dpi_pschema_register_base(void) {
  dpi_record_field_table *common_field_table = NULL;
  int                     common_field_count = dpi_pschema_get_common_field(&common_field_table);

  pschema_t *schema_base = pschema_register_base(g_weixin_db, "common", "base schema", "#NONE");
  for (int i = 0; i < common_field_count; ++i) {
    pschema_register_field(schema_base, common_field_table[i].field_name, common_field_table[i].type, "");
    }

  return 0;
}

/*写协议字段表*/
int dpi_pschema_dump_proto_schema_of(pschema_t *schema, const char *pschema_output_dir) {
  const char *proto_name = pschema_get_proto_name(schema);
  char pschema_file_path[PATH_MAX] = {0};

  snprintf(pschema_file_path, sizeof pschema_file_path, "%s/%s_f.txt", pschema_output_dir, proto_name);
  FILE *pschema_file = fopen(pschema_file_path, "w");
  if (NULL == pschema_file) {
    return -1;
  }

  for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc != NULL; fdesc = pschema_fdesc_get_next(schema, fdesc)) {
    const char *field_name = pfdesc_get_name(fdesc);

    fprintf(pschema_file, "%s\n", field_name);
  }

  fclose(pschema_file);
  return 0;
}

void dpi_record_print_all_schema()
{
  printf("dpi_record_print_all_schema start -------------------------------------------------\n");
    for (pschema_t* schema = pschema_get_first(g_weixin_db); schema != NULL; schema = pschema_get_next(g_weixin_db, schema)) {
        printf("adapt schema name: %s\n", pschema_get_proto_name(schema));
        for (pfield_desc_t * desc = pschema_fdesc_get_first(schema); desc != NULL; desc = pschema_fdesc_get_next(schema, desc)) {
            printf("adapt field name = %s\n", pfdesc_get_name(desc));
        }
    }
  printf("dpi_record_print_all_schema end -------------------------------------------------\n");
}
/*初始化协议字段表*/
int dpi_pschema_dump_proto_schemas(const char *pschema_output_dir) {
  if (!is_file_exist(pschema_output_dir)) {
    mkdirs(pschema_output_dir);
  }

  for (pschema_t *schema = pschema_get_first(g_weixin_db); schema != NULL; schema = pschema_get_next(g_weixin_db, schema)) {
    dpi_pschema_dump_proto_schema_of(schema, pschema_output_dir);
  }
  dpi_pschema_dump_adapt_proto_schemas(pschema_output_dir);
  return 0;
}
/*初始化record*/
void ya_record_init(void) {
  ya_ftypes_initialize();

  g_weixin_db = pschema_db_create();
  if (g_weixin_db == NULL) {
    printf("%s\n", "Schemadb init failed!!");
    exit(-1);
  }
  /* 注册共性字段作为 schema 模板 */
  dpi_pschema_register_base();

  dpi_register_proto_all();
  dpi_padapt_init(g_weixin_db);
  // dpi_record_print_all_schema();
}

void dpi_distory_schemaDB(void) {
  // DPI_LOG(DPI_LOG_ERROR, "creat_record num %d\n", creat_record);
  // DPI_LOG(DPI_LOG_ERROR, "destroy_record num %d\n", destroy_record);

  if (g_weixin_db != NULL)
    pschema_db_destroy(g_weixin_db);
}

void printf_record(precord_t *record) {
  for (player_t *layer = precord_layer_get_first(record); layer != NULL; layer = precord_layer_get_next(record, layer)) {
    const char *layer_name = precord_layer_get_layer_name(layer);
    precord_layer_move_cursor(record, layer_name);
    printf("record layer---------%s----------------------------\n", layer_name);
    for (pfield_t *field = precord_field_get_first(record); field != NULL; field = precord_field_get_next(record, field)) {
      pfield_desc_t *fdesc = precord_field_get_fdesc(field);
      ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
      printf("record field---------:%s\n", pfdesc_get_name(fdesc));

      if (fvalue == NULL) {
        printf("       value---------:NULL\n");

        continue;
      }
      printf("       value---------:%s\n", ya_fvalue_to_string_repr(fvalue, BASE_NONE));
    }
  }
}


