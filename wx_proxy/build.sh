#!/bin/bash

pwd=`pwd`
dir_build="chunli"
exe="wx_proxy"
rm -rf $dir_build

branch=$(git  rev-parse --abbrev-ref HEAD)
version=$(git log -1 --pretty=format:%h)
tag=$(git describe --abbrev=0 --tags)
date=$(date +%Y_%m_%d)
function release()
{
    dir=yaTcpMirror_${tag}_release
    rm -rf   ${dir}*
    mkdir -p ${dir}

    cp ${dir_build}/${exe} ${dir}/yaTcpMirror_${tag}
    #git show HEAD:./config.ini >   ${dir}/config.ini_${tag}    #只从提交提取
    cat config.ini >   ${dir}/config.ini_${tag}    #只从提交提取

    ln -sfr   ${dir}/config.ini_${tag}  ${dir}/config.ini
    ln -sfr   ${dir}/yaTcpMirror_${tag} ${dir}/yaTcpMirror

    tar -zcf ${dir}.${date}.tar.gz     ${dir}
}

rm -rf yaTcpMirror*
rm -rf   $dir_build
mkdir -p $dir_build
cd       $dir_build
cmake ..
make

cd $pwd
release

rm -rf $dir_build
