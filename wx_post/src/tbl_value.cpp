#include <string.h>
#include <memory>
#include <iostream>

#include "tbl_value.h"
#include "tbl_utils.h"
#include "tbl_config.h"

#include "tbl_value_PersonULI.h"

std::map<std::string, TblFieldValue *> TblFieldValue::TblFieldManager;

TblFieldValue::TblFieldValue(const std::string & field_name)
{
    field_name_ = field_name;

    for(auto &it: CFG.getKeyArray({"field_conf", "field_extra", field_name_}))
    {
        for(auto & iter :CFG.getCurrentArray({"field_conf", "field_extra", field_name_, it}))
        {
            base_format[it].emplace(iter);                                                           // 初始化字段格式配置
        }
    }
    if(TblFieldManager.find(field_name) == TblFieldManager.end())
    {
        TblFieldManager.emplace(field_name, this);
    }
}

TblFieldValue::~TblFieldValue()
{
}

void TblFieldValue::init()
{
    RegistSpecialField(); // 注册自定义类实现的对象 

    for(auto &it: CFG.getKeyArray({"field_conf", "field_extra"})) // 注册非自定义，但用户有格式需求的对象
    {
        if(TblFieldManager.find(it) == TblFieldManager.end())
        {
            TblFieldManager.emplace(it, new TblFieldValue(it));
        }
    }
}
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
 *     name: getField(const std::string & field_name)
 * function: 获取字段名对应的指针
 *  
 *  
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
TblFieldValue * TblFieldValue::getField(const std::string & field_name)
{
    if(TblFieldManager.find(field_name) == TblFieldManager.end())
    {
        //TblFieldManager.emplace(field_name, new TblFieldValue(field_name));
        return nullptr;
    }
    return TblFieldManager[field_name];
}

std::string TblFieldValue::getMatchFormat(const std::string &proto) 
{
    if(field_name_ == CFG.getCurrentConfigValue({"work_conf", proto, "field_name"}, std::string()))
    {
        return CFG.getCurrentConfigValue({"work_conf", proto, "field_type"}, std::string(""));
    }
    if(!base_format.empty())
    {
        return base_format.begin()->first;
    }
    return "";
}

bool TblFieldValue::format_check(const std::string &type, const std::string & tbl_value)
{
    if(tbl_value.empty())
    {
        return false;
    }

    if(type.empty())
    {
        return true;
    }

    if(base_format.find(type) == base_format.end())       // 配置类型配错了，与先前配置类型对不上
    {
        LOG_DEF->error("error tbl type {} of field {}" ,type ,field_name_);
        return false;
    }
    else
    {
        for(auto & it : base_format[type])
        {
            if(strBeginwith(tbl_value, it))
            {
                return true;
            }
        }
    }
    
    return false;
}

bool TblFieldValue::FormatCheck(const std::string &proto_name, const std::string &tbl_value)
{
    if(proto_format_map.find(proto_name) != proto_format_map.end())
    {
        return format_check(proto_format_map[proto_name], tbl_value);
    }
    return false;
}
/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * 
  name:      getRequestFieldValue(std::string &tbl_value, int index)
  function:  从tbl原始数据中获取指定字段的值

   
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
std::string TblFieldValue::getRequestFieldValue(std::string & proto, std::string &tbl_data, int index)
{
    std::string field_value = getStrNthChar(tbl_data, '|', index);

    return field_value;
}

void TblFieldValue::setFieldAndProto(const std::string& field_name, const std::string& proto_name)
{
    proto_format_map.emplace(proto_name, getMatchFormat(proto_name));
}

void TblFieldValue::setFieldTable(std::vector<std::pair<std::string, int>> &)
{
    return;
}

void TblFieldValue::destoryItem()
{
    delete this;
}


void TblFieldValue::RegistSpecialField()
{
    static TblValuePersonULI person_uli("Person_RT_ULI", "Person_RT_BS");
    static TblValuePersonULI rt_uli("RT_ULI", "RT_BS");
}

void TblFieldValue::destory()
{
    for(auto & it:TblFieldManager)
    {
        it.second->destoryItem();
    }
}
