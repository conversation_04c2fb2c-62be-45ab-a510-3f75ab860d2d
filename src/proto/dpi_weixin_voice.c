/****************************************************************************************
 * 文 件 名 : dpi_weixin_voice.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: 李春利         2018/12/27
 修改: 李春利         2020/01/08
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <arpa/inet.h>

#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "wxcs_def.h"
#include "jhash.h"

#define                     LEAST_DROP_VALUE      48
#define                     WXA_PKT_TCP           0XEC
#define                     DROP              1<< 10
#define                     MAX_TIME_OUT_NODE 1024*256
#define                     PACK              __attribute__((packed))


#define WXA_PERSON_VIOCE_TYPE   0x76
#define WXA_PERSON_VIDEO_TYPE   0x77
#define WXA_GROUP_VOICE_TYPE    0X10
#define WXA_GROUP_VIDEO_TYPE    0X20
#define WXA_PERSON_VIDEO_TYPE_1   0x21

#define WXA_D5_FLAG             0x1800


typedef struct voiceattr_
{
    uint8_t     callflag;       // 主被叫标识
    uint8_t     flowflag;       // 多流标识
    uint8_t     calltype;       // 会话类型  video voice
    uint16_t    pkts;           // 数据包中包计数
    uint16_t    voice_pkts;     // 数据包中语音包计数
    uint16_t    video_pkts;     // 数据包中视频包计数
}VoiceAttr;


typedef struct voice_count_
{
    uint64_t total;     // 总人数
    uint64_t contact;   // 建联人数
    uint64_t del;       // 超时人数
}GVoiceCnt;

typedef enum  {
    // 75 76 77 95 96 97 98
    MEDIA_TYPE_75 = 0,
    MEDIA_TYPE_76 = 1,
    MEDIA_TYPE_77 = 2,
    MEDIA_TYPE_95 = 3,
    MEDIA_TYPE_96 = 4,
    MEDIA_TYPE_97 = 5,
    MEDIA_TYPE_98 = 6,
    MEDIA_TYPE_MAX,
} MediaType;

extern struct global_config g_config;
pthread_rwlock_t            rwlock       = PTHREAD_RWLOCK_INITIALIZER;
void                       *global_hash  = NULL;
void                       *g_phone_hash = NULL;
wxc_handle                  g_handle     = NULL;
// 话单计数
GVoiceCnt                   g_voice_cnt;
static void
weixin_voip_timeout(struct flow_info *flow, void *ptr);

static void* wx_session_hash_init(void)
{
    pthread_rwlock_wrlock(&rwlock);
    if(NULL == global_hash)
    {
        global_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    }
    pthread_rwlock_unlock(&rwlock);

    return global_hash;
}

// 返回   1  代表 OK
// 返回  -1  代表 ERR
static int wx_session_hash_insert(void *hash, char *key, void *value)
{
    void *exists = NULL;
    int   ret    = -1;

    if(NULL == hash || NULL==key || NULL==value)
    {
        return -1;
    }

    pthread_rwlock_wrlock(&rwlock);
    exists = g_hash_table_lookup ((GHashTable *)hash, (gconstpointer)key);
    if(NULL == exists)
    {
        ret = g_hash_table_insert ((GHashTable *)hash, key, value);
    }
    pthread_rwlock_unlock(&rwlock);
    return  ret;
}

static void* wx_session_hash_find(void *hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return NULL;
    }

    pthread_rwlock_rdlock(&rwlock);
    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&rwlock);
    return ret;
}

static int wx_session_hash_delete(void *hash, char *pStr)
{
    if(NULL == hash || NULL == pStr)
    {
        return -1;
    }

    pthread_rwlock_wrlock(&rwlock);
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&rwlock);
    return 0;
}

static void wx_session_hash_destory(void *hash)
{
    if(NULL == hash)
    {
        return;
    }

    pthread_rwlock_wrlock(&rwlock);
    g_hash_table_destroy((GHashTable *)hash);
    pthread_rwlock_unlock(&rwlock);
}

static int BinToHex(const unsigned char *inPut, unsigned int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    char * pOrigin = OutBuffer;
    unsigned int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
        OutBuffer+=2;
    }
    return OutBuffer - pOrigin;
}


static int
bin_to_hex(const char* data, int len, char* out, int size)
{
    const char map[] = "0123456789abcdef";
    if(len * 2 > size)
    {
        return -1;
    }

    char * begin = out;
    char * end  = out;
    int i = 0;
    for(i = 0; i < len; i++)
    {
        unsigned char H = 0x0f & (data[i] >> 4);
        unsigned char L = 0x0f & data[i];
        out[i * 2 + 0] = map[H];
        out[i * 2 + 1] = map[L];
        end += 2;
    }
    // return out;
    return end - begin;
}

static void print_session(ST_wxAudioSessionAlive *p)
{
    int  ret = 0;
    char sessionID[64];
    char client_ip[64];
    char server_ip[64];

    ret = BinToHex(p->SessionID, sizeof(p->SessionID), sessionID, sizeof(sessionID));
    sessionID[ret] = 0;

    if(4 == p->ip_version)
    {
        inet_ntop(AF_INET, &p->client_ip.ipv4, client_ip, sizeof(client_ip));
        inet_ntop(AF_INET, &p->server_ip.ipv4, server_ip, sizeof(server_ip));
    }

    if(6 == p->ip_version)
    {
        inet_ntop(AF_INET6, p->client_ip.ipv6, client_ip, sizeof(client_ip));
        inet_ntop(AF_INET6, p->server_ip.ipv6, server_ip, sizeof(server_ip));
    }

    dpi_TrailerDump(&p->trailer);
    printf("sessionID         :%-16s\n", sessionID);

    printf("ip_version        :%u\n", p->ip_version);
    printf("client_ip         :%-25s", client_ip);
    printf("server_ip         :%s\n", server_ip);
    printf("client_port       :%-25u", p->client_port);
    printf("server_port       :%u\n", p->server_port);

    if(p->Person_pkt_75)
    {
        printf("Packets_75        :%d\n", p->Person_pkt_75);
    }

    if(p->Person_pkt_76)
    {
        printf("Packets_76        :%d\n", p->Person_pkt_76);
    }

    if(p->Person_pkt_77)
    {
        printf("Packets_77        :%d\n", p->Person_pkt_77);
    }

    if(p->Person_pkt_95)
    {
        printf("Packets_95        :%d\n", p->Person_pkt_95);
    }

    if(p->Person_pkt_96) {
        printf("Packets_96        :%d\n", p->Person_pkt_96);
    }

    if(p->Person_pkt_97) {
        printf("Packets_97        :%d\n", p->Person_pkt_97);
    }
    if(p->Person_pkt_98) {
        printf("Packets_98        :%d\n", p->Person_pkt_98);
    }
    if(p->PersonC2S_pkt_tcp) {
        printf("PersonC2S_pkt_tcp :%d\n", p->PersonC2S_pkt_tcp);
    }

    if(p->PersonS2C_pkt_tcp) {
        printf("PersonS2C_pkt_tcp :%d\n", p->PersonS2C_pkt_tcp);
    }

    printf("C2STransPackets   :%-25d", p->PersonC2STransPackets);
    printf("S2CTransPackets   :%d\n", p->PersonS2CTransPackets);

    printf("C2S_D5_Pcakets    :%-25d", p->PersonC2S_D5_Pcaket);
    printf("S2C_D5_Pcakets    :%d\n", p->PersonS2C_D5_Pcaket);

    printf("C2SVideoPackets   :%-25d", p->PersonC2SVideoPackets);
    printf("S2CVideoPackets   :%d\n", p->PersonS2CVideoPackets);

    printf("C2STransBytes     :%-25d", p->PersonC2STransBytes);
    printf("S2CTransBytes     :%d\n", p->PersonS2CTransBytes);

    printf("C2S Bytes/Pkts    :%-25d", p->PersonC2STransPackets==0 ? 0:p->PersonC2STransBytes/p->PersonC2STransPackets);
    printf("S2C Bytes/Pkts    :%d\n", p->PersonS2CTransPackets==0 ? 0:p->PersonS2CTransBytes/p->PersonS2CTransPackets);

    printf("FirstActiveTime   :%-25d", p->PersonFirstActiveTime);
    printf("LastActiveTime    :%d\n", p->PersonLastActiveTime);

    printf("群组通话          :%-25s", p->SessionType==WXA_SESSION_GROUP?                     "yes":"no");
    printf("视频通话          :%s\n", p->c2s_video_pkts>5&&p->s2c_video_pkts>5?"yes":"no");
    // printf("视频通话          :%s\n", p->PersonC2SVideoPackets>5&&p->PersonS2CVideoPackets>5?"yes":"no");
    printf("是否主叫          :%-25s", p->SessionCalling==CallTYpe_Calling?                   "yes":"no");
    printf("是否接听          :%s\n", p->PersonIsAnswered==1?                                "yes":"no");
    printf("响铃时长          :%-25d", p->PersonRingTime);
    printf("通话时长          :%d\n", p->PersonLastActiveTime - p->PersonFirstActiveTime);
    printf("通话结束          :%d\n", p->isTimeout);
    printf("\n");
}

static void print_st(ST_wxAudioSessionAlive *p)
{
    if(0 == g_config.debug_weixin_voice)
    {
        return;
    }

    if(NULL == p)
    {
        return;
    }

    print_session(p);
}

static void* phone_watch_init(void)
{
    char  phone_number[16];
    int   safelen;
    void* hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if(NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    if(0 != memcmp(g_config.wx_phone_watch, "null", 4))
    {
        int  i   = 0;
        char*p   = (char*)g_config.wx_phone_watch;
        int size = sizeof(g_config.wx_phone_watch);
        printf("WX话单监视手机列表[%s]\n", g_config.wx_phone_watch);

        char *pLeft  = p;
        char *pRight = pLeft;
        while('\0' != *p)
        {
            pRight = strchr(pLeft, ',');
            if(NULL == pRight)
            {
                // 探测是不是只有一个手机号，末尾没有逗号
                int len = strlen(pLeft);
                if(len >= 13 && len <15) //一个手机号就是13个字符
                {
                    char *pStr = pLeft;
                    printf("WX话单监视手机[%s]\n", pStr);
                    g_hash_table_insert((GHashTable *)hash, g_strdup(pStr), g_strdup(pStr));
                }

                break;
            }
            safelen = sizeof(phone_number) < (unsigned)(pRight - pLeft) ? sizeof(phone_number) : (unsigned)(pRight - pLeft);
            memset(phone_number, 0, sizeof(phone_number));
            memcpy(phone_number, pLeft, safelen);
            printf("WX话单监视手机[%s]\n", phone_number);
            g_hash_table_insert((GHashTable *)hash, g_strdup(phone_number), g_strdup(phone_number));
            pLeft = ++pRight;
        }
    }
    else
    {
        return NULL;
    }
    return hash;
}

static int phone_watch_find(void *hash, const char *pStr)
{
    if(NULL == hash)
    {
        return -1;
    }

    gpointer p = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if(NULL == p)
    {
        return -1;
    }
    return 1;
}

static void phone_watch_fini(void *hash)
{
    if(NULL == hash)
    {
        return;
    }
    g_hash_table_destroy((GHashTable *)hash);
}

static void *wexin_voice_session_thread(void *arg)
{
    uint32_t wheel = time(NULL);

    // wxc_handle handle      = 0;
    wxc_init(&g_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    // if(NULL == handle)
    // {
    //     return NULL;
    // }


    g_phone_hash = phone_watch_init();
    // 命运的轮回...
    while(1) // should I exit ?
    {
        int   hash_count              = 0;
        int   KeyTop                  = 0;
        int   send                    = 0;
        int   relation                = 0;
        char* key                     = NULL;
        ST_wxAudioSessionAlive* value = NULL;
        void* Stack[MAX_TIME_OUT_NODE];
        time_t now;



        sleep(g_config.wx_session_timeloop);
        now = time(NULL);
        // 每隔 60s
        if (now - wheel  >= 60) {
            // 清空超时人数
            ATOMIC_ZERO(&g_voice_cnt.del);

            wheel = now;
        }

        char buffer[26];
        time_t timer;
        time(&timer);
        struct tm* tm_info = localtime(&timer);
        strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
        printf("WechatOnline:[%s] 已超时(60s轮询)/已建联/总人数=[%lu/%lu/%lu]\n", buffer, g_voice_cnt.del, g_voice_cnt.contact, g_voice_cnt.total);

        /////////////////////////////////////////////////////////////////////////
        struct st_tcp_reassemble_obj *obj = g_config.reassemble_info.obj;
        size_t time_dur = time(NULL) - g_config.reassemble_info.weixin_start;
        printf("微信还原状态:[%s] MP4[震荡/成功/期望][%zd/%zd/%zd %zd%%]\n", buffer,
                obj[WX_PYQ_MP4].error,
                obj[WX_PYQ_MP4].success,
                obj[WX_PYQ_MP4].expect,
                obj[WX_PYQ_MP4].expect==0?0:obj[WX_PYQ_MP4].success * 100 /obj[WX_PYQ_MP4].expect);
        printf("微信还原状态:[%s] JPG[震荡/成功/期望][%zd/%zd/%zd %zd%%]\n", buffer,
                obj[WX_PYQ_JPG].error,
                obj[WX_PYQ_JPG].success,
                obj[WX_PYQ_JPG].expect,
                obj[WX_PYQ_JPG].expect==0?0:obj[WX_PYQ_JPG].success * 100 /obj[WX_PYQ_JPG].expect);
        printf("微信还原状态:[%s] 转储:总量%zd(MB),速率%zd(MB/min)\n", buffer,
                g_config.reassemble_info.weixin_total/1024/1024,
                0==time_dur?0:((g_config.reassemble_info.weixin_size*60/time_dur)/1024/1024));
        /////////////////////////////////////////////////////////////////////////
        printf("\n");

    }

    return NULL;
}

int init_dissector_wx_sesion_thread(void);
int init_dissector_wx_sesion_thread(void)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_MEDIA_CHAT] == 0)
    {
        return 0;
    }

    pthread_t weixin_voice_session;

    int status = pthread_create(&weixin_voice_session, NULL, wexin_voice_session_thread, NULL);
    if(status != 0)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create weixin_voice_session thread");
        exit(-1);
    }

    return 0;
}

typedef struct st_media_chat
{
    ST_trailer  trailer;
    int FlagPacketC2S;
    int FlagPacketS2C;
    int SequenceC2S;
    int SequenceS2C;

    int sessionType;
    int RingTimeStart;
    int RingTimeLast;
    int FirstActiveTime;// 会话接通时间戳
    int isAnswered;     // 是否已经接听
    int pkt_75;
    int pkt_76;
    int pkt_77;
    int pkt_95;
    int pkt_96;
    int pkt_97;
    int pkt_drop;

    uint32_t lasttime;      // 上一次更新的时间戳

    uint8_t     callflag;       // 主被叫标识
    uint8_t     flowflag;       // 多流标识
    uint8_t     calltype;       // 会话类型  video voice
    uint16_t    pkts;           // 数据包中包计数
    uint16_t    c2s_voice_pkts;     // 数据包中语音包计数
    uint16_t    s2c_voice_pkts;     // 数据包中语音包计数
    uint16_t    c2s_video_pkts;     // 数据包中视频包计数
    uint16_t    s2c_video_pkts;     // 数据包中视频包计数
} __attribute__((packed)) ST_MediaChat;

static ST_wxAudioSessionAlive* WX_FlowSessionInit(struct flow_info *flow)
{
    if(NULL == flow)
    {
        return NULL;
    }

    if(NULL == flow->app_session)
    {
        ST_wxAudioSessionAlive* pst_MediaChat;
        pst_MediaChat= malloc(sizeof(ST_wxAudioSessionAlive));
        if(NULL == pst_MediaChat)
        {
            DPI_LOG(DPI_LOG_ERROR, "error on malooc ST_MediaChat");
            exit(-1);
        }
        memset(pst_MediaChat, 0, sizeof(ST_wxAudioSessionAlive));
        flow->app_session = pst_MediaChat;
        flow->flow_EOF = weixin_voip_timeout;
        pst_MediaChat->SessionType = WXA_SESSION_NOT_SURE;
        pst_MediaChat->SessionCalling = CALLTYPE_UNSET;
        pst_MediaChat->PersonFirstActiveTime = g_config.g_now_time;
    }

    return flow->app_session;
}

static int WX_FlowSessionSetFlags(struct flow_info *flow, int C2S)
{
    ST_MediaChat* pst_MediaChat;
    pst_MediaChat = flow->app_session;
    if(1 == C2S)
    {
        pst_MediaChat->FlagPacketC2S++;
    }
    else
    {
        pst_MediaChat->FlagPacketS2C++;
    }
    return 0;
}

static int WX_FlowSessionGetFlags(struct flow_info *flow, int C2S)
{
    if(NULL == flow)
    {
        return 0;
    }

    if(NULL == flow->app_session)
    {
        return 0;
    }

    ST_MediaChat* pst_MediaChat;
    pst_MediaChat = flow->app_session;
    if(1 == C2S)
    {
        return pst_MediaChat->FlagPacketC2S;
    }
    else
    {
        return pst_MediaChat->FlagPacketS2C;
    }
}

// update callflag
//    uint32_t   stat_callflag_96_0;               // 96 c2s 主叫统计 0
    // uint32_t   stat_callflag_96_1;               // 96 c2s 被叫统计 1
    // uint32_t   stat_callflag_98_0;               // 98 c2s 主叫统计 0
    // uint32_t   stat_callflag_98_1;               // 98 c2s 被叫统计 1
static void update_callflag(struct flow_info *flow, MediaType type, int callflag)
{
    if(NULL == flow->app_session)
    {
        return;
    }

    ST_wxAudioSessionAlive* value = (ST_wxAudioSessionAlive*)flow->app_session;

    switch(type)
    {
    case MEDIA_TYPE_96:
        if (0 == callflag) {
            value->stat_callflag_96_0++;
        } else {
            value->stat_callflag_96_1++;
        }
        break;
    case MEDIA_TYPE_98:
        if (0 == callflag) {
            value->stat_callflag_98_0++;
        } else {
            value->stat_callflag_98_1++;
        }
        break;
    default:
        break;
    }
}

// 更新响铃 状态
static int RingTimeUpdate(struct flow_info *flow, int sessionType)
{
    int              now;

    if(NULL == flow->app_session)
    {
        return -1;
    }

    /*
     * 很难 判断 D5 类型的包到底是不是 响铃伴随包
     * IOS 发起音视频群聊时, 在没人接听的情况下, UDP流中 只有D5包
     *
     * 规则如下:
     * 1 流中报文, 第1个是 d5 包, 此时不能确定 是什么类型的会话
     * 2 出现75    可以确定是个人对个人的通话
     * 3 出现76 77 可以确定是微信群的通话
     * 4 对于已确定的会话类型, 不再采集D5数据包
     */

    ST_wxAudioSessionAlive* pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    if(1 == pst_MediaChat->PersonIsAnswered)
    {
        return 0;
    }

    // 如果开始响铃时戳未设置
    now = time(NULL);
    if(0 == pst_MediaChat->RingTimeStart)
    {
        pst_MediaChat->RingTimeStart = now;
        pst_MediaChat->RingTimeLast  = now;
    }

    switch(sessionType)
    {
        case WXA_SESSION_PERSON   :
        case WXA_SESSION_GROUP    :
            // 更新始响铃时间
            pst_MediaChat->RingTimeLast    = now;
            pst_MediaChat->PersonFirstActiveTime = now; //真正的会话尚未开始, 复位操作

            // 标记Session类型
            pst_MediaChat->SessionType = sessionType;
            break;

        case WXA_SESSION_NOT_SURE : // 不确定的类型(当只有D5数据包, 其他数据包还未到达时, 无法确定 SessionType)
            // 会话类型 是否已经 成功标记过
            if(WXA_SESSION_NOT_SURE != pst_MediaChat->SessionType)
            {
                return 0;
            }

            // 会话类型 没有被标记过, 更新...
            pst_MediaChat->RingTimeLast    = now;
            pst_MediaChat->PersonFirstActiveTime = now; // 真正的会话尚未开始,复位操作
            break;

        default:
            break;
    }
    return 0;
}

static int RingTimeEnd(struct flow_info *flow, int sessionType)
{
    if(NULL == flow->app_session) {
        return -1;
    }

    ST_wxAudioSessionAlive* pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    if(1 == pst_MediaChat->PersonIsAnswered) {
        return 0;
    }

    if(WXA_SESSION_PERSON == sessionType) {
        pst_MediaChat->SessionType = WXA_SESSION_PERSON;
        if(0 == pst_MediaChat->PersonIsAnswered) {
            pst_MediaChat->PersonIsAnswered       = 1;
            pst_MediaChat->PersonFirstActiveTime = time(NULL);
        }
    } else if(WXA_SESSION_GROUP == sessionType) {
        pst_MediaChat->SessionType = WXA_SESSION_GROUP;
        if(0 == pst_MediaChat->PersonIsAnswered) {
            pst_MediaChat->PersonIsAnswered       = 1;
            pst_MediaChat->PersonFirstActiveTime = time(NULL);
        }
    }

    return 0;
}

static int GetFirstActiveTime(struct flow_info *flow)
{
    if(NULL == flow->app_session)
    {
        return -1;
    }
    ST_MediaChat* pst_MediaChat = (ST_MediaChat*)flow->app_session;
    return pst_MediaChat->FirstActiveTime;
}

static int GetisAnswered(struct flow_info *flow)
{
    if(NULL == flow->app_session)
    {
        return -1;
    }
    ST_MediaChat* pst_MediaChat = (ST_MediaChat*)flow->app_session;
    return pst_MediaChat->isAnswered;
}

static int GetRingTime(struct flow_info *flow)
{
    if(NULL == flow->app_session)
    {
        return -1;
    }
    ST_MediaChat* pst_MediaChat = (ST_MediaChat*)flow->app_session;
    return pst_MediaChat->RingTimeLast - pst_MediaChat->RingTimeStart;
}

static int check_sequence_num(struct flow_info *flow, int seq, int C2S)
{
    if(NULL == flow->app_session)
    {
        return -1;
    }

    ST_wxAudioSessionAlive* pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    if(1 == C2S)
    {
        if(pst_MediaChat->SequenceC2S > 65500 && seq < pst_MediaChat->SequenceC2S)
        {
            pst_MediaChat->SequenceC2S = seq;
        }

        int diff = seq > pst_MediaChat->SequenceC2S ? seq-pst_MediaChat->SequenceC2S : pst_MediaChat->SequenceC2S-seq;
        if( (unsigned int)diff > g_config.wx_session_seq_jitter)
        {
            return -1;
        }

        pst_MediaChat->SequenceC2S = seq;
    } else {
        if(pst_MediaChat->SequenceS2C > 65500 && seq < pst_MediaChat->SequenceS2C) {
            pst_MediaChat->SequenceS2C = seq;
        }

        int diff = seq > pst_MediaChat->SequenceS2C ? seq-pst_MediaChat->SequenceS2C : pst_MediaChat->SequenceS2C-seq;
        if( (unsigned int)diff > g_config.wx_session_seq_jitter) {
            return -1;
        }

        pst_MediaChat->SequenceS2C = seq;
    }

    return 0;
}

static unsigned int
process_voip_75(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID, VoiceAttr *attr)
{
    struct st
    {
        char    prefix;
        short   seq;
        int     reserve;
        int     sessionID;
        char    index;
        uint8_t type;
        uint16_t typeseq;
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = ntohs(pkt->seq);

    if(0x75 != pkt->prefix)
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    if(check_sequence_num(flow, tmp_seq, C2S) < 0)
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if((g_config.wx_session_drop == 1) && (0 != pkt->reserve))
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    /*
     * 微信1对1通话(音视) 是否接通检测(IOS Android):
     * 通话接通前, 全是0x75开头, UDP包长为51字节的报文
     * 通话接通后, 出现0x75开头, UDP包长大于51的报文, UDP包长为51的报文伴随出现
     */

    if(len <= (int)g_config.wx_session_ring_size) {
        RingTimeUpdate(flow, WXA_SESSION_PERSON);
    } else {
        RingTimeEnd(flow, WXA_SESSION_PERSON);
    }

    pst_MediaChat->Person_pkt_75++;

    // *index     = (1 == C2S) ? (!!pkt->index):(!pkt->index);

    *type      = WXA_SESSION_PERSON;
    *sessionID = pkt->sessionID;
    attr->callflag = pkt->index;
    // attr->flowflag = pkt->flowflag;
    attr->pkts  = ntohs(pkt->seq);
    if (pkt->type == WXA_PERSON_VIOCE_TYPE) {
        attr->voice_pkts = ntohs(pkt->typeseq);
    } else if (pkt->type == WXA_PERSON_VIDEO_TYPE) {
        attr->video_pkts = ntohs(pkt->typeseq);
    }
    return 0;
}



static unsigned int
process_voip_97(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID, VoiceAttr * attr)
{
    struct st
    {
        uint16_t    prefix;
        uint32_t    reserve;
        uint32_t    sessionID;
        uint8_t     index;          // 主被叫标识
        uint8_t     flowflag;       // 流标记
        uint16_t    seq;            // 序号
        uint8_t     type;           // 通话类型：视频(0x77)、语音(0x76)
        uint16_t    typeseq;        // 各类型序号
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = ntohs(pkt->seq);
    if(0x9711 != ntohs(pkt->prefix))
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    /* 保留前 64 个报文，后面的做 16:1 过滤，过滤条件  (seq & 0x0F) != 0 */
    if (g_config.wx_session_filter == 1 && (tmp_seq >= 64) && (tmp_seq & 0x0F)) {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if(check_sequence_num(flow, tmp_seq, C2S) < 0)
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if((g_config.wx_session_drop == 1) && (0 != pkt->reserve))
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }


    if(len <= (int)g_config.wx_session_ring_size) {
        RingTimeUpdate(flow, WXA_SESSION_PERSON);
    } else {
        RingTimeEnd(flow, WXA_SESSION_PERSON);
    }

    pst_MediaChat->Person_pkt_97++;

    if (g_config.wxa_recognition == 2) {
        if (pkt->type != 0x77 && pkt->type != 0x76) return 0;
        if (pkt->index != 0x00 && pkt->index != 0x01) return 0;
    }

    // *index     = (1 == C2S) ? (!!pkt->index):(!pkt->index);
    *type      = WXA_SESSION_PERSON;
    *sessionID = pkt->sessionID;
    if (C2S == 1) {
        attr->callflag = pkt->index;
        update_callflag(flow, MEDIA_TYPE_97, pkt->index);
    }

    attr->flowflag = pkt->flowflag;
    attr->pkts  = ntohs(pkt->seq);
    if (pkt->type == WXA_PERSON_VIOCE_TYPE) {
        attr->voice_pkts = ntohs(pkt->typeseq);
    } else if (pkt->type == WXA_PERSON_VIDEO_TYPE) {
        attr->video_pkts = ntohs(pkt->typeseq);
    }
    return 0;
}

static unsigned int
process_voip_98(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID, VoiceAttr * attr)
{
    struct st
    {
        uint16_t    prefix;
        uint64_t    sessionID;
        uint8_t     index;          // 主被叫标识
        uint8_t     flowflag;       // 网络环境指示
        uint16_t    seq;            // 序号
        uint8_t     type;           // 通话类型：0x10（语音）
        uint16_t    typeseq;        // 2字节计数，与前面1字节类型值相关
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = pkt->seq;
    if(0x9815 != ntohs(pkt->prefix))
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;

    /* 保留前 64 个报文，后面的做 16:1 过滤，过滤条件  (seq & 0x0F) != 0 */
    if (g_config.wx_session_filter == 1 && (tmp_seq >= 64) && (tmp_seq & 0x0F)) {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if(check_sequence_num(flow, tmp_seq, C2S) < 0)
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if(len <= (int)g_config.wx_session_ring_size) {
        RingTimeUpdate(flow, WXA_SESSION_PERSON);
    } else {
        RingTimeEnd(flow, 8);
    }

    pst_MediaChat->Person_pkt_98++;

    if (g_config.wxa_recognition == 2) {
        if (pkt->type != 0x10) return 0;
        if (pkt->index != 0x00 && pkt->index != 0x01) return 0;
    }

    // *index     = (1 == C2S) ? (!!pkt->index):(!pkt->index);
    *type      = WXA_SESSION_PERSON;
    *sessionID = pkt->sessionID;

    if (C2S == 1) {
        attr->callflag = pkt->index;
        update_callflag(flow, MEDIA_TYPE_98, pkt->index);
    }
    attr->flowflag = pkt->flowflag;
    attr->pkts  = ntohs(pkt->seq);
    return 0;
}

static unsigned int
process_voip_76(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID)
{
    struct st
    {
        short   prefix;
        short   seq;
        int     sessionID;
        char    index;
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = ntohs(pkt->seq);

    if(0x7603 != ntohs(pkt->prefix))
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;
    if(check_sequence_num(flow, tmp_seq, C2S) < 0 )
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    /* IOS
     * 微信群通话(音视), 是否接通检测:
     * 通话接通前, 全是0xd5开头, UDP包长为90~120字节
     * 通话接通后, 出现0x76开头, UDP音视频包大量出现, d5报文伴随出现
     *
     * Android
     * 持续的76 UDP数据包, 包长70 ~ 180, 波动很大
     * 对端接听前, 只有本机发往服务器的 76 数据包
     * 对端接听后, 才有服务端发往本机的 76 77 数据包
     */

    if(0 == C2S && 0 == GetisAnswered(flow))
    {
        // IOS 发起通话2秒内被接, 无法识别
        if(abs(pst_MediaChat->SequenceC2S - tmp_seq) <= LEAST_DROP_VALUE && GetRingTime(flow) <=2)
        {
            pst_MediaChat->RingTimeLast  = 0;
            pst_MediaChat->RingTimeStart = 0;
        }
    }

    if(0 == C2S) {
        RingTimeEnd(flow, WXA_SESSION_GROUP);
    }

    RingTimeUpdate(flow, WXA_SESSION_GROUP);

    pst_MediaChat->Person_pkt_76++;

    *type      = WXA_SESSION_GROUP;
    *sessionID = pkt->sessionID;
    return 0;
}


static unsigned int
process_voip_77(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID)
{
    struct st
    {
        short   prefix;
        short   seq;
        int     sessionID;
        char    index;
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = ntohs(pkt->seq);

    if(0x7703 != ntohs(pkt->prefix))
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;
    if(check_sequence_num(flow, tmp_seq, C2S) < 0 )
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    /* IOS
     * 微信群通话(音视), 是否接通检测:
     * 通话接通前, 全是0xd5开头, UDP包长为90~120字节
     * 通话接通后, 出现0x76开头, UDP音视频包大量出现, d5报文伴随出现
     *
     * Android
     * 持续的76 UDP数据包, 包长70 ~ 180, 波动很大
     * 对端接听前, 只有本机发往服务器的 76 数据包
     * 对端接听后, 才有服务端发往本机的 76 77 数据包
     */

    if(0 == C2S && 0 == GetisAnswered(flow))
    {
        // IOS 发起通话2秒内被接, 无法识别
        if(abs(pst_MediaChat->SequenceC2S - tmp_seq) <= LEAST_DROP_VALUE && GetRingTime(flow) <=2)
        {
            pst_MediaChat->RingTimeLast  = 0;
            pst_MediaChat->RingTimeStart = 0;
        }
    }

    if(0 == C2S)
    {
        RingTimeEnd(flow, WXA_SESSION_GROUP);
    }

    RingTimeUpdate(flow, WXA_SESSION_GROUP);

    pst_MediaChat->Person_pkt_77++;

    *type      = WXA_SESSION_GROUP;
    *sessionID = pkt->sessionID;
    return 0;
}

static unsigned int
process_voip_95(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID)
{
    struct st
    {
        unsigned char    prefix;
        short            seq;
        int              reserve_1;
        unsigned int     sessionID;
        int              reserve_2;
        char             index;
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = ntohs(pkt->seq);

    if(0x95 != pkt->prefix)
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;
    if(check_sequence_num(flow, tmp_seq, C2S) < 0 )
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if(0 == C2S && 0 == GetisAnswered(flow))
    {
        // IOS 发起通话2秒内被接, 无法识别
        if(abs(pst_MediaChat->SequenceC2S - tmp_seq) <= LEAST_DROP_VALUE && GetRingTime(flow) <=2)
        {
            pst_MediaChat->RingTimeLast  = 0;
            pst_MediaChat->RingTimeStart = 0;
        }
    }

    if(0 == C2S)
    {
        RingTimeEnd(flow, WXA_SESSION_GROUP);
    }

    pst_MediaChat->Person_pkt_95++;

    RingTimeUpdate(flow, WXA_SESSION_GROUP);
    *type      = WXA_SESSION_GROUP;
    *sessionID = pkt->sessionID;
    return 0;
}

static unsigned int
process_voip_96(const char *data, int len, int C2S, struct flow_info *flow, int *type, uint64_t *sessionID, VoiceAttr * attr)
{
    struct st
    {
        unsigned short  prefix;
        uint64_t        sessionID;
        uint8_t         index;
        uint8_t         reserve;
        unsigned short  seq;
        uint8_t         type;           // 通话类型：群:视频(0x20)、语音(0x10)  个人：视频(0x21)、
        uint16_t        typeseq;        // 各类型序号
    } PACK const *pkt = (const struct st*)data;

    uint16_t tmp_seq = pkt->seq;

    if(0x9612 != ntohs(pkt->prefix) && 0x9613 != ntohs(pkt->prefix))
    {
        return 0;
    }

    ST_wxAudioSessionAlive  *pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;
    if(check_sequence_num(flow, tmp_seq, C2S) < 0 )
    {
        pst_MediaChat->Person_pkt_drop++;
        return DROP;
    }

    if(0 == C2S && 0 == GetisAnswered(flow))
    {
        // 过滤掉 响铃2秒以内的群被叫接听，防误识别。 当群成员的响铃时长大于 N秒， 即认定为是群主叫。
        if(abs(pst_MediaChat->SequenceC2S - tmp_seq) <= LEAST_DROP_VALUE && GetRingTime(flow) <=2)
        {
            pst_MediaChat->RingTimeLast  = 0;
            pst_MediaChat->RingTimeStart = 0;
        }
    }

    if(0 == C2S)
    {
        RingTimeEnd(flow, WXA_SESSION_GROUP);
    }

    pst_MediaChat->Person_pkt_96++;

    if (g_config.wxa_recognition == 2) {
        // type 中还包含 0x30 0x50 0x61 0x70 等，但是这些都不作为音视频识别的主要特征，直接进行包计数即可返回。
        if (pkt->type != 0x10 && pkt->type != 0x21) return 0;
    }

    RingTimeUpdate(flow, WXA_SESSION_GROUP);
    *sessionID = pkt->sessionID;
    attr->pkts  = pkt->seq;
    if (pkt->type == WXA_GROUP_VOICE_TYPE) {
        *type      = WXA_SESSION_GROUP;
        attr->voice_pkts = pkt->typeseq;
    } else if (pkt->type == WXA_GROUP_VIDEO_TYPE) {
        *type      = WXA_SESSION_GROUP;
        attr->video_pkts = pkt->typeseq;
    }  else if (pkt->type ==WXA_PERSON_VIDEO_TYPE_1 ){
        *type = pst_MediaChat->Person_pkt_98 ? WXA_SESSION_PERSON : WXA_SESSION_GROUP;
        attr->video_pkts = pkt->typeseq;
        if (C2S == 1) {
            attr->callflag = pkt->index;
            update_callflag(flow, MEDIA_TYPE_96, pkt->index);
        }
    }
    return 0;
}

static unsigned int
process_voip_d5(const char *data, int len, int C2S, struct flow_info *flow)
{
    if(0xD5 != (unsigned char)data[0])
    {
        return 0;
    }
    ST_wxAudioSessionAlive* pst_MediaChat = (ST_wxAudioSessionAlive*)flow->app_session;
    if(1 == C2S) {
        pst_MediaChat->PersonC2S_D5_Pcaket++;
    } else {
        pst_MediaChat->PersonS2C_D5_Pcaket++;
    }

    RingTimeUpdate(flow, WXA_SESSION_NOT_SURE);
    return DROP;
}

static int
get_hashkey(struct flow_info *flow, int client_ip, int sessionID, char *Hash_Key, int size)
{
    int ret = 0;

    if(1 == flow->has_trailer)
    {
        ST_trailer tr = {0};
        dpi_TrailerParser(&tr, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
        ret += BinToHex((const unsigned char*)&tr.IMSI, sizeof(tr.IMSI), Hash_Key+ret, size-ret);
    }
    else
    {
        ret += BinToHex((const unsigned char*)&client_ip, sizeof(client_ip), Hash_Key+ret, size-ret);
    }

    BinToHex((const unsigned char*)&sessionID, sizeof(sessionID), Hash_Key+ret, size-ret);
    return 0;
}


/**
 *  新版话单哈希表 key 的哈希值，考虑到同一通会话，多条流的情况，仅仅手机号和 sessionid 作为key是不够的
 *  多条流端口不一样，所以现同时把端口作为 key 的一部分添加上去
 *  同时取消 imsi 作为 key， 统一使用 clientip + clientport
 */
static int
get_hashkey_v2(struct sonwden * _sonwden, uint64_t sessionID, char *Hash_Key, int size)
{
    int ret = 0;
    ret += bin_to_hex((const char *)&_sonwden->client_ip, sizeof(_sonwden->client_ip), Hash_Key+ret, size-ret);
    ret += bin_to_hex((const char *)&_sonwden->client_port, sizeof(_sonwden->client_port), Hash_Key + ret, size-ret);
    bin_to_hex((const char *)&sessionID, sizeof(sessionID), Hash_Key+ret, size-ret);
    return 0;
}

static void
weixin_voip_timeout(struct flow_info *flow, void *ptr)
{
    int                     C2S           = 0;
    char                    Hash_Key[32];
    int                     salt          = 0;
    void                   *hash          = NULL;
    ST_MediaChat           *s             = NULL;
    ST_wxAudioSessionAlive *value = (ST_wxAudioSessionAlive *)ptr;
    struct sonwden          sonwden;

    // s = (ST_MediaChat*)ptr;

    // 检测是不是IOS发起了群主叫
    // IOS 发起群主叫, 无人接听时, 没有76,77. 只有双向的D5
    if(value->SessionType == WXA_SESSION_NOT_SURE &&
      value->PersonS2C_D5_Pcaket > g_config.wx_session_flag_pkt &&
      value->PersonC2S_D5_Pcaket > g_config.wx_session_flag_pkt)
    {
        // 只有双向 d5 没有 sessionID 创建一个随机的 sessionID
        salt = sonwden.client_ip.ipv4 ^ sonwden.server_ip.ipv4 ^ sonwden.client_port ^ sonwden.server_port;
        memcpy(value->SessionID, &salt, sizeof(salt));
        memcpy(((char*)value->SessionID)+4, "\xFF\xFF\xFF\xFF", 4);

        value->PersonLastActiveTime = value->PersonFirstActiveTime;
        value->SessionCalling       = 0;
        value->SessionType          = WXA_SESSION_GROUP;
        value->PersonRingTime       = value->RingTimeLast - value->RingTimeStart;

        // 只有双向 d5 的报文，在解析阶段不会进入 trailer 解析
        dpi_TrailerParser(&value->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
        dpi_TrailerGetMAC(&value->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
        dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);
        dpi_TrailerSetDev(&value->trailer, g_config.devname);        // 解析板号
        dpi_TrailerSetOpt(&value->trailer, g_config.operator_name);  // 运营商
        dpi_TrailerSetArea(&value->trailer,g_config.devArea);
    } else {
        if (value->send_flag == 1) {
            ATOMIC_SUB_FETCH(&g_voice_cnt.total);
            if (value->trailer.MSISDN && value->trailer.IMSI &&value->trailer.IMEI) {
                ATOMIC_SUB_FETCH(&g_voice_cnt.contact);
            }
        }

        ATOMIC_ADD_FETCH(&g_voice_cnt.del);
    }

    value->isTimeout = 1;

    // 发送给聚合
    dpi_TrailerUpdateTS(&value->trailer);
    print_st(value);
    wxc_sendMsg(g_handle, (const unsigned char*)value, sizeof(ST_wxAudioSessionAlive), WXCS_MEDIA_CHAT);

    return;
}


static uint32_t
GetSeqTotal(uint32_t original, uint16_t new) {
    uint32_t seq = original >> 16;
    // 流中计数字段为两字节循环计数，因此当前后两帧序号差距较大的时候，可以判定为开始下一轮计数
    // 允许乱序抖动值
    // printf("old:%d,new:%d\n", original, new);
    if (abs(original - new) > 60000 && new > 50) {
        // printf("seq:%d-",seq);
        seq += 1;
        // printf("%d-",seq);
        seq = (seq << 16) + new;
    } else {
        // uint32_t tmp = (original & 0xFF00);
        uint32_t tmp = (original & 0xFFFF);
        // printf("tmp:%d-",tmp);
        tmp = tmp > new ? tmp : new;
        // printf("%d-",tmp);
        seq = (original & 0xf0000) + tmp;
    }


    // printf("%d\n", seq);
    return seq;
}

static int
dissect_weixin_media_chat(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    void          *hash          = NULL;
    int            C2S           = 0;
    int            type          = WXA_SESSION_NOT_SURE;
    uint64_t       sessionID     = 0;
    int            index         = 0;
    int            st            = 0;
    int            ret           = 0;
    char           Hash_Key[64];
    int            len           = 0;
    const char    *data          = NULL;
    ST_wxAudioSessionAlive* value= NULL;
    struct sonwden sonwden;

    data            = (const char*)payload;
    len             = payload_len;

    memset(Hash_Key, 0, sizeof(Hash_Key));
    memset(&sonwden, 0, sizeof(sonwden));

    if (g_config.protocol_switch[PROTOCOL_WEIXIN_MEDIA_CHAT] == 0)
    {
        return 0;
    }

    if(payload_len <= g_config.wx_session_pkt_size) {
        return  0;
    }

    WX_FlowSessionInit(flow);
    value   = (ST_wxAudioSessionAlive*)flow->app_session;


    uint8_t flowflag = 0;
    uint32_t now;
    VoiceAttr voice_attr;
    memset(&voice_attr, 0, sizeof(VoiceAttr));
    voice_attr.callflag = CALLTYPE_UNSET;


    get_ip_port(&flow->pkt, &sonwden, &C2S);
    st |= process_voip_75(data, len, C2S, flow, &type, &sessionID, &voice_attr);    // 发现于 2019.01.01 寒冬的夜晚
    st |= process_voip_97(data, len, C2S, flow, &type, &sessionID, &voice_attr);    // 发现于 2020.01.20 Android Ver 7.0.12
    st |= process_voip_98(data, len, C2S, flow, &type, &sessionID, &voice_attr);    // 发现于 2024.11.07
    st |= process_voip_96(data, len, C2S, flow, &type, &sessionID, &voice_attr);    // 发现于 2020.09.29 测试现场
    st |= process_voip_76(data, len, C2S, flow, &type, &sessionID);                 // 发现于 2019.01.01 寒冬的夜晚
    st |= process_voip_77(data, len, C2S, flow, &type, &sessionID);                 // 发现于 2019.01.01 寒冬的夜晚
    st |= process_voip_95(data, len, C2S, flow, &type, &sessionID);                 // 发现于 2020.01.20 Android Ver 7.0.12
    st |= process_voip_d5(data, len, C2S, flow);

    value->ip_version            = sonwden.ip_ver;
    memcpy(value->client_ip.ipv6,  sonwden.client_ip.ipv6, 16);
    memcpy(value->server_ip.ipv6,  sonwden.server_ip.ipv6, 16);
    value->client_port           = sonwden.client_port;
    value->server_port           = sonwden.server_port;

    if(st & DROP || 0 == sessionID) {
        return 0;
    }
    // get_hashkey(flow, sonwden.client_ip.ipv4, sessionID, Hash_Key, sizeof(Hash_Key));
    get_hashkey_v2(&sonwden, sessionID, Hash_Key, sizeof(Hash_Key));

    if (value->trailer.MSISDN == 0) {
        dpi_TrailerParser(&value->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
        dpi_TrailerGetMAC(&value->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
        dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);
        dpi_TrailerSetDev(&value->trailer, g_config.devname);        // 解析板号
        dpi_TrailerSetOpt(&value->trailer, g_config.operator_name);  // 运营商
        dpi_TrailerSetArea(&value->trailer,g_config.devArea);
    }

    if(1 == C2S) {
        value->PersonC2STransPackets ++;
        value->PersonC2STransBytes   += payload_len;
        value->PersonC2SVideoPackets += payload_len >= VIDEO_PKT_SIZE ? 1 : 0;

        value->c2s_pkts         = GetSeqTotal(value->c2s_pkts, voice_attr.pkts);
        value->c2s_voice_pkts   = GetSeqTotal(value->c2s_voice_pkts,voice_attr.voice_pkts);
        value->c2s_video_pkts   = GetSeqTotal(value->c2s_video_pkts, voice_attr.video_pkts);
    } else {
        value->PersonS2CTransPackets ++;
        value->PersonS2CTransBytes   += payload_len;
        value->PersonS2CVideoPackets += payload_len >= VIDEO_PKT_SIZE ? 1 : 0;

        value->s2c_pkts         = GetSeqTotal(value->s2c_pkts, voice_attr.pkts);
        value->s2c_voice_pkts   = GetSeqTotal(value->s2c_voice_pkts,voice_attr.voice_pkts);
        value->s2c_video_pkts   = GetSeqTotal(value->s2c_video_pkts, voice_attr.video_pkts);
    }

    value->PersonC2S_pkt_tcp    += ((WXA_PKT_TCP == flag) && (1 == C2S)) ? 1 : 0;
    value->PersonS2C_pkt_tcp    += ((WXA_PKT_TCP == flag) && (0 == C2S)) ? 1 : 0;


    char buffer[16] = { 0 };
    if (C2S == 1) {
        snprintf(buffer, sizeof(buffer), "%d", voice_attr.callflag);
        if (strstr(value->callflag, buffer) == NULL) {
            if (value->callflag[0] != '\0' &&
            value->callflag[strlen(value->callflag) - 1] != ',') {
                strcat(value->callflag, ",");
            }
            strcat(value->callflag, buffer);
        }
    }

    buffer[0] = '\0';
    snprintf(buffer,  sizeof(buffer), "%d", voice_attr.flowflag);
    if (strstr(value->flowflag, buffer) == NULL) {
        if (value->flowflag[0] != '\0' &&
          value->flowflag[strlen(value->flowflag) -1] != ',') {
            strcat(value->flowflag, ",");
        }
        strcat(value->flowflag, buffer);
    }


    // 获取当前时间
    now = time(NULL);
    value->PersonLastActiveTime  = now;
    if (value->SessionCalling == CALLTYPE_UNSET && C2S == 1 && voice_attr.callflag != CALLTYPE_UNSET) {
        value->SessionCalling = voice_attr.callflag;
    }
     // printf("port = %d , sessioncalling = %d, callflag = %d\n",value->client_port, value->SessionCalling, voice_attr.callflag);
    value->SessionType           = type;
    value->Session_hash          = jhash(&sessionID, sizeof(sessionID), 31);

    memcpy(&value->SessionID, &sessionID, sizeof(sessionID));
    value->PersonRingTime        = value->RingTimeLast - value->RingTimeStart;
    // 根据配置时间间隔 循环发送数据到聚合
    if (now - value->lasttime >= g_config.wx_session_timeloop) {
        // 发送给聚合
        dpi_TrailerUpdateTS(&value->trailer);
        wxc_sendMsg(g_handle, (const unsigned char*)value, sizeof(ST_wxAudioSessionAlive), WXCS_MEDIA_CHAT);

        // 原子操作，更新计数
        if (value->send_flag != 1) {
            ATOMIC_ADD_FETCH(&g_voice_cnt.total);

            if (value->trailer.MSISDN && value->trailer.IMEI && value->trailer.IMSI) {
                ATOMIC_ADD_FETCH(&g_voice_cnt.contact);
            }
            value->send_flag = 1;
        }

        // 打印布控
        print_st(value);
        char phone_num[20];
        snprintf(phone_num, 20, "%ld", value->trailer.MSISDN);
        if(1 == phone_watch_find(g_phone_hash, phone_num))
        {
            printf("find_phone        :%s\n", phone_num);
            print_session(value);
        }

        value->lasttime = now;    // 更新时间戳
    }

    return 0;
}


// TCP 切割机
static int
dissect_weixin_voip_tcp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    int offset = 0;
    int len    = 0;

    if(DISSECT_PKT_ORIGINAL != flag)
    {
        return 0;
    }

    while(0xD8 == *(const unsigned char*)(payload + offset))
    {
        offset += 1;

        len =  ntohl(*(const int*)(payload + offset));
        if((unsigned int)len > 1500)
        {
            break;
        }
        offset += 4;
        len    -= 4;

        if(0 != memcmp("\x00\x08\x00\x01", payload + offset, 4))
        {
            break;
        }
        offset += 4;
        len    -= 4;

        if(len + offset <= (int)payload_len)
        {
            dissect_weixin_media_chat(flow, direction, seq, payload + offset,  len, WXA_PKT_TCP);
            offset += len;
        }
        else
        {
            break;
        }
    }
    return 0;
}


static gboolean
identify_weixin_media_chat_d5(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    uint16_t offset = 0;

    if (payload[offset] != 0xd5) return FALSE;
    offset += 1;

    // unused
    offset += 5;

    //length
    offset += 1;

    // unused
    offset += 1;

    uint8_t len = get_uint8_t(payload, offset);

    offset += 1;
    offset += len;

    // unused
    offset += 1;

    // session id
    offset += 4;

    uint16_t flag = get_uint16_ntohs(payload, offset);
    if (flag != WXA_D5_FLAG) return FALSE;


    return TRUE;

}

static void
identify_weixin_media_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_MEDIA_CHAT] == 0)
    {
        return;
    }

    if(payload_len < 20)
    {
        return;
    }

    /* 判断报文的目标端口  */
    int port_src = ntohs(flow->tuple.inner.port_src);
    int port_dst = ntohs(flow->tuple.inner.port_dst);
    if(     80     == port_src
         || 8000   == port_src
         || 8080   == port_src
         || 16285  == port_src

         || 80     == port_dst
         || 8000   == port_dst
         || 8080   == port_dst
         || 16285  == port_dst
      )
    {
        /* 判断UDP报文的前缀 */
        if(0x75 == payload[0] || 0x76 == payload[0] || 0x77 == payload[0] ||
           0x95 == payload[0] || 0x96   == payload[0] || 0x97   == payload[0] || 0x98   == payload[0]) {
            //当前这路流, 是微信话单.
            flow->real_protocol_id = PROTOCOL_WEIXIN_MEDIA_CHAT;
        }

        // wx 共享实时位置与 d5 冲突，排除 共享实时位置的影响
        if (0xd5 == payload[0]) {
            if (identify_weixin_media_chat_d5(flow, payload, payload_len)) {
                flow->real_protocol_id = PROTOCOL_WEIXIN_MEDIA_CHAT;
            }
        }

    }

    //当前这路流, 不是微信话单. 自我标识.
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_WEIXIN_MEDIA_CHAT);
    return;
}


static void
identify_weixin_voip_tcp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_MEDIA_CHAT] == 0)
    {
        return;
    }

    if(payload_len < 20)
    {
        return;
    }

    /* 判断报文的目标端口  */
    int port_src = ntohs(flow->tuple.inner.port_src);
    int port_dst = ntohs(flow->tuple.inner.port_dst);
    if(          80   == port_src
            || 8000   == port_src
            || 8080   == port_src
            || 16285  == port_src

            || 80     == port_dst
            || 8000   == port_dst
            || 8080   == port_dst
            || 16285  == port_dst
      )
    {
        if(0 == memcmp(payload, "\xd8\x00\x00", 3) && 0 == memcmp(payload+5, "\x00\x08\x00\x01", 4))
        {
            flow->real_protocol_id = PROTOCOL_WEIXIN_MEDIA_CHAT;
        }
    }
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_WEIXIN_MEDIA_CHAT);
    return;
}

void init_WeixinMediaChat_dissector(void)
{
    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_WEIXIN_MEDIA_CHAT
    port_add_proto_head(IPPROTO_UDP, 80,    PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_UDP, 8000,  PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_UDP, 8080,  PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_UDP, 16285, PROTOCOL_WEIXIN_MEDIA_CHAT);

    // 协议为 PROTOCOL_WEIXIN_MEDIA_CHAT 的回调
    udp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].proto         = PROTOCOL_WEIXIN_MEDIA_CHAT;
    udp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].identify_func = identify_weixin_media_chat;
    udp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].dissect_func  = dissect_weixin_media_chat;

    port_add_proto_head(IPPROTO_TCP, 80,    PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_TCP, 8000,  PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_TCP, 8080,  PROTOCOL_WEIXIN_MEDIA_CHAT);
    port_add_proto_head(IPPROTO_TCP, 16285, PROTOCOL_WEIXIN_MEDIA_CHAT);

    tcp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].proto         = PROTOCOL_WEIXIN_MEDIA_CHAT;
    tcp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].identify_func = identify_weixin_voip_tcp;
    tcp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].dissect_func  = dissect_weixin_voip_tcp;

    // 自我标识.当前结构体的协议类型. 辅助流识别.
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_WEIXIN_MEDIA_CHAT].excluded_protocol_bitmask, PROTOCOL_WEIXIN_MEDIA_CHAT);
    return;
}

