/****************************************************************************************
 * 文 件 名 : dpi_tcp_rsm.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.2.0
 * 设    计 : chunli              2020/05/11
*****************************************************************************************/

#ifndef _DPI_TCP_RSM_H_
#define _DPI_TCP_RSM_H_

#include <sys/types.h>
#include <stdint.h>
#include "list.h"

#define      TCP_RSM_DISABLE   (void*)-1
#define      TCP_LEN_MAX        1500
extern struct rte_mempool *tcp_reassemble_mempool;

struct tcp_rsm_node
{
    struct   list_head node;
    uint32_t            seq;
    uint16_t            len;
    uint8_t             p[TCP_LEN_MAX];
};

struct tcp_status
{
    uint32_t  seq_start;     // 记录单向TCP SEQ起始
    uint32_t  seq_end;       // 记录单向TCP SEQ终止

    uint32_t  ack;           // TCP重组 累计收到 纯ACK  报文
    uint32_t  dated;         // TCP重组 累计收到 重复   报文
    uint32_t  wait;          // TCP重组 累计    确定报文有序
    uint32_t  exp;           // TCP重组 遇到期望报文的个数
    uint32_t  jitter;        // TCP重组 遇到抖动报文的个数
    uint64_t  count;         // TCP重组 总包数

    uint32_t  first;         // 确定报文有序的序号
    uint32_t  interrupt;     // 缺包中断次数
    uint32_t  miss;          // 缺包块数
    uint32_t  miss_len;      // 缺包总长(字节)
    uint32_t  rsm;           // 重组报文数
    uint64_t  rsm_len;       // 重组总长
    uint32_t  fail;          // 入队列失败 计数
    uint32_t  fail_len;      // 入队列失败 总长
    uint32_t  fin;           // TCP 传输完成
    uint32_t  rst;           // TCP 传输异常
};

enum TCP_FLAG
{
    TCP_FIN  =   1 << 0,
    TCP_SYN  =   1 << 1,
    TCP_RST  =   1 << 2,
    TCP_PSH  =   1 << 3,
    TCP_ACK  =   1 << 4,
};

typedef void TCP_RSM;

#ifdef __cplusplus
extern "C" {
#endif

TCP_RSM *tcp_rsm_init     (int (*pkt)  (void *user, int C2S, const uint8_t *p, uint16_t l),
                           int (*miss) (void *user, int C2S, uint32_t len),
                                        void *user, uint32_t jitter);

int   tcp_rsm_add         (TCP_RSM *tcp, int C2S, uint32_t seq, uint32_t ack, uint8_t tcp_flag, const uint8_t *p, uint32_t l);
int   tcp_rsm_status      (TCP_RSM *tcp, int C2S, struct tcp_status *status);
int   tcp_rsm_port_reuse  (TCP_RSM *tcp, int C2S, uint32_t ack);
void  tcp_rsm_dump        (TCP_RSM *tcp);
void  tcp_rsm_free        (TCP_RSM *tcp);

#ifdef __cplusplus
}
#endif /* __cplusplus */


#endif
