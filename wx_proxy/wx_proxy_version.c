/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <stdio.h>

#include <event2/bufferevent_ssl.h>
#include <event2/listener.h>
#include <openssl/ssl.h>
#include <openssl/rand.h>

#include <glib.h>

#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_version.h"
#include "wx_proxy_log.h"

#define  DATE        "2019-11-02"
#define  VERSION     "0.1.2"
#define  SUBVERSION  "3828"

/*****************************************************************
*Function    : show_version
*Description : printf proxy server version
*Input       : neno
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void show_version(void)
{
    printf("Version: build date:%s, version:%s, subversion:%s\n", DATE, VERSION, SUBVERSION);
}
