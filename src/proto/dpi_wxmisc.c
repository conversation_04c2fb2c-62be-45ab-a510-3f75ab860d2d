/****************************************************************************************
 * 文 件 名 : dpi_wxmisc.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq  		    2022/03/01
编码: chenzq			2022/03/01
修改: chenzq            2022/03/01
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <iconv.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"


#include "dpi_wxmisc.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

dpi_field_table weixin_misc_array_f[] = {
    DPI_FIELD_D(EM_WX_MISC_DATA,                EM_F_TYPE_STRING,                "MiscData"),
    
    // DPI_FIELD_D(EX_WX_MISC_NICK_NAME,                EM_F_TYPE_STRING,                "wxNickName"),
    // DPI_FIELD_D(EM_WX_MISC_WXID,                    EM_F_TYPE_STRING,                 "wxid"),
    // DPI_FIELD_D(EX_EX_MISC_REMARK_NAME,             EM_F_TYPE_STRING,                 "wxRemarkName"),
    // DPI_FIELD_D(EM_WX_MISC_WECHAT_ID,               EM_F_TYPE_STRING,                 "weChatId"),
    // DPI_FIELD_D(EM_WX_MISC_NICKNAME_FULL,           EM_F_TYPE_STRING,                 "wxNickNameFull"),
    // DPI_FIELD_D(EX_WX_MISC_REMARK_NAME_SIMPLE,      EM_F_TYPE_STRING,                 "wxRemarkNameSimple"),
    // DPI_FIELD_D(EM_WX_MISC_REMARK_NAME_FULL,        EM_F_TYPE_STRING,                 "wxRemarkNameFull"),
    // DPI_FIELD_D(EM_WX_MISC_HEAD_THUM_ATTR,          EM_F_TYPE_STRING,                 "wxHeadThumAttr"),
    // DPI_FIELD_D(EM_WX_MISC_HEAD_THUM_URL,           EM_F_TYPE_STRING,                 "wxHeadThumUrl"),
    // DPI_FIELD_D(EM_WX_MISC_HEAD_ATTR,               EM_F_TYPE_STRING,                 "wxHeadAttr"),
    // DPI_FIELD_D(EM_WX_MISC_HEAD_URL,                EM_F_TYPE_STRING,                 "wxHeadUrl"),
    // DPI_FIELD_D(EM_WX_MISC_COUNTRY,                 EM_F_TYPE_STRING,                 "wxCountry"),
    // DPI_FIELD_D(EM_WX_MISC_PROVINCE_FULL,           EM_F_TYPE_STRING,                 "wxProvinceFull"),
    // DPI_FIELD_D(EM_WX_MISC_CITY_FULL,               EM_F_TYPE_STRING,                 "wxCityFull"),
    // DPI_FIELD_D(EM_WX_MISC_PERSON_SIGN,             EM_F_TYPE_STRING,                 "wxPersonSign"),
    // DPI_FIELD_D(EM_WX_MISC_PYQ_MSG_ID,              EM_F_TYPE_STRING,                 "wxPyqMsgId"),
    // DPI_FIELD_D(EM_WX_MISC_PYQ_BG_IMG,              EM_F_TYPE_STRING,                 "wxPyqBgImg"),

};
#if 1
static int write_wx_misc_log(struct flow_info *flow, int direction, WXMisc *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    // struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_WX_MISC_MAX; i++)
    {
        switch(weixin_misc_array_f[i].index)
        {
        case EM_WX_MISC_DATA:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->misc_data, strlen(info->misc_data));
            break;
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    log_ptr->type        = TBL_LOG_WEIXIN_MISC;
    log_ptr->len         = idx;
    log_ptr->tid          = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

#endif

static 
void wxmisc_utf8_decode(const uint8_t *payload, const uint32_t payload_len, WXMisc * info)
{
    uint32_t offset = 0;
    uint32_t i = 0;
    int j = 0;
    uint32_t len = 0;
    const uint8_t *p = payload;
    char str[1024] = { 0 };
    char split = ' ';
    uint8_t end_flag = 0;
    for (i = 0; i < payload_len;) {
        if(isprint(*p) > 0) {
            p++;
            len++;
            i++;
            if (i < payload_len) 
                continue;
            else 
                end_flag = 1;
        } else if((0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) ) {
            p = p + 3;
            len += 3;
            i += 3;
            if (i < payload_len) 
                continue;
            else 
                end_flag = 1;
        } else if((0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) 
                && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) ) {
            p = p + 4;
            len += 4;
            i += 4;
            if (i < payload_len) 
                continue;
            else 
                end_flag = 1;
        } else {
            
            i++;
            p++;
            offset += 1;
        }

        if ( len > 0 ) {
            if (len > 6 && len < 1024) {
                if (info->flag) split = ';';
                memset(&str, 0, sizeof(str));
                if (end_flag) {
                    strncpy(str, (const char *)(payload + offset), len);
                } else {
                    strncpy(str, (const char *)(payload + offset - 1), len);
                }
                
                snprintf(info->misc_data + strlen(info->misc_data), strlen(str) + 2, "%c%s", split, str);
                info->flag = 1;
            
            }
            offset += len;
            len = 0;
        }
        

        
    }

    // printf("%s\n", info->misc_data);
}

static
void wxmisc_save_data_debug(struct flow_info *flow, const uint8_t * payload, const uint32_t payload_len) 
{
    char srcip[128] = { 0 };
    char dstip[128] = { 0 };
    uint16_t srcport = 0;
    uint16_t dstport = 0;
    char prefix [124] = { 0 };
    const char path[] = "/tmp/tbls/wx_misc_data";
    // char filename[128] = { 0 };
    char file[256] = { 0 };

    struct timeval tv;

    if (flow->ip_version == 4) {
        get_ip4string(srcip, sizeof(srcip), flow->pkt.src_ip.ipv4);
        get_ip4string(dstip, sizeof(dstip), flow->pkt.dst_ip.ipv4);
    } else {
        get_ip6string(srcip, sizeof(srcip), (const uint8_t *)flow->pkt.src_ip.ipv6);
        get_ip6string(dstip, sizeof(dstip), (const uint8_t *)flow->pkt.dst_ip.ipv6);
    }
    srcport = ntohs(flow->pkt.src_port);
    dstport = ntohs(flow->pkt.dst_port);

    snprintf(prefix, sizeof(prefix), "%s_%d_%s_%d", srcip, srcport, dstip, dstport);

    gettimeofday(&tv, NULL);
    snprintf(file, sizeof(file), "%s/%s_%06ld.bin", path, prefix, tv.tv_usec);

    struct stat st = { 0 };
    if (stat(path, &st) == -1) {
        mkdir(path, 0644);
    }

    FILE * fp = fopen(file, "w+");
    fwrite(prefix, strlen(prefix), 1, fp);
    fwrite("\n", 1, 1, fp);
    fwrite(payload, payload_len, 1, fp);
    fwrite("\n", 1, 1, fp);
    // fwrite(proto_array[i].field_name,strlen(proto_array[i].field_name),1,fp);

    fclose(fp);
    
    /*
    gettimeofday(&tv, NULL);
		snprintf(tbl_log_array[type].tbl[tid].filename, sizeof(tbl_log_array[type].tbl[tid].filename), "%s/%s/%s_%06ld_%s_%03d",
				g_config.tbl_out_dir,
                tbl_log_array[type].protoname,
                time_to_datetime(tv.tv_sec),
                tv.tv_usec,
                tbl_log_array[type].protoname,
                tid);
    */

}

int dissect_weixin_misc(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, 
                            const uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_MISC] == 0) {
        return 0;
    }

    uint8_t c2s = 0;

    if (ntohs(flow->tuple.inner.port_src) > ntohs(flow->tuple.inner.port_dst)) {
        c2s = 1;
    } else {
        c2s = 0;
    }

    if (!c2s) return 0;

    WXMisc info;
    memset(&info, 0, sizeof(info));

    wxmisc_utf8_decode(payload, payload_len, &info);

    if (info.misc_data[0] == '\0') {
        return 0;
    }
    write_wx_misc_log(flow, direction, &info);
    if (g_config.wx_misc_payload_save) {
        wxmisc_save_data_debug(flow, payload, payload_len);
    }

    return 0;
}

void init_weixin_misc_dissector(void)
{

    write_proto_field_tab(weixin_misc_array_f, EM_WX_MISC_MAX, "wx_misc");

    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_WEIXIN_MEDIA_CHAT
    // port_add_proto_head(IPPROTO_UDP, 80,    PROTOCOL_WEIXIN_RELA);

    // 协议为 PROTOCOL_WEIXIN_MEDIA_CHAT 的回调
    udp_detection_array[PROTOCOL_WEIXIN_MISC].proto         = PROTOCOL_WEIXIN_MISC;
    udp_detection_array[PROTOCOL_WEIXIN_MISC].dissect_func  = dissect_weixin_misc;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN_MISC].excluded_protocol_bitmask, PROTOCOL_WEIXIN_MISC);

    return;
}
