/****************************************************************************************
 * 文 件 名 : dpi_gquic.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: chenzq         2022/06/16
编码: chenzq         2022/06/16
修改: chenzq         2022/06/16
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include "dpi_gquic.h"


#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_weixin.h"


extern struct global_config   g_config;
extern struct rte_mempool    *tbl_log_mempool;
// static uint16_t   wxf_filter_array[WEIXIN_FILTER_MAX_NUM]={0};
static uint16_t               g_gquic_wxf_filter[WEIXIN_FILTER_MAX_NUM];
extern weixin_field_t         weixin_array_f[EM_WX_MAX];
extern struct rte_hash       *wx_handle;


#define COPY_VALUE(a,ptr,len,release)             \
do                                                \
{                                                 \
    if((a).free && (a).value_len > 0)             \
    {                                             \
        break;                                    \
    }                                             \
    if((a).value==NULL){                          \
        (a).value     = memdup(ptr, len);         \
    }                                             \
    (a).value_len = len;                          \
    (a).free      = (ptr) ? release : NULL;       \
}while(0);

/* 专门用于更新rangeend的值 */
#define UPDATE_VALUE(a,ptr,len,release)                                \
do                                                                     \
{                                                                      \
    if((a).value==NULL){                                               \
        (a).value     = memdup(ptr, len);                              \
    }else{                                                             \
        (a).free((a).value);                                           \
        (a).value     = memdup(ptr, len);                              \
    }                                                                  \
    (a).value_len = len;                                               \
    (a).free      = (ptr) ? release : NULL;                            \
}while(0);



weixin_field_t gquic_array_f[] = {
    WEIXIN_FIELD_ARGS(EM_GQUIC_CID,                         EM_F_TYPE_STRING,                 "gquic_cid")
};


static
int write_gquic_wxf_log(struct flow_info *flow, int direction,  weixin_info_t  *line_info)
{
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;

    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    char buff[20]={0};
    for(i=0;i<EM_GQUIC_MAX;i++)
    {
        switch(weixin_array_f[i].index)
        {
            case EM_WX_LOCALNAME:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;

            case EM_WX_FILEID:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;
            case EM_WX_THUMBDATA:
			case EM_WX_MIDIMGDATA:
            case EM_WX_FILEDATA:
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_PYQ_MUL_URL:
                if(line_info[i].value_len>7){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_WX_PYQ_UIN:
                if(line_info[i].value_len>4){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            default:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, weixin_array_f[i].type, line_info[i].value, line_info[i].value_len);
                break;
        }

        if(line_info[i].free && line_info[i].value)
        {
            line_info[i].free((void*)line_info[i].value);
            line_info[i].value = NULL;
            line_info[i].free  = NULL;
        }

    }

    log_ptr->type         = TBL_LOG_WEIXIN;
    log_ptr->len          = idx;
    log_ptr->tid          = flow->thread_id;

    if(tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


static
int write_gquic_wx_pyq_log(struct flow_info *flow, int direction,  weixin_info_t  *line_info)
{
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;

    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);
    dissect_referer_key_value(line_info);
    dissect_fileurl_key_value(line_info);

    char buff[20]={0};
    for(i=0;i<EM_WX_MAX;i++)
    {
        switch(weixin_array_f[i].index)
        {
            case EM_WX_LOCALNAME:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;

            case EM_WX_FILEID:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;
            case EM_WX_THUMBDATA:
			case EM_WX_MIDIMGDATA:
            case EM_WX_FILEDATA:
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_PYQ_MUL_URL:
                if(line_info[i].value_len>7){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_WX_PYQ_UIN:
                if(line_info[i].value_len>4){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            default:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, weixin_array_f[i].type, line_info[i].value, line_info[i].value_len);
                break;
        }

        if(line_info[i].free && line_info[i].value)
        {
            line_info[i].free((void*)line_info[i].value);
            line_info[i].value = NULL;
            line_info[i].free  = NULL;
        }

    }

    log_ptr->type         = TBL_LOG_WEIXIN_PYQ;
    log_ptr->len          = idx;
    log_ptr->tid          = flow->thread_id;

    if(tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}



static int
gquic_wx_get_filter(weixin_info_t *line_info)
{
    if(line_info==NULL){
        return 0;
    }
    int i=0;

    for(i=0;i<WEIXIN_FILTER_MAX_NUM;i++){
      if(g_gquic_wxf_filter[i]>0 && g_gquic_wxf_filter[i]<EM_GQUIC_MAX){
          if(line_info[g_gquic_wxf_filter[i]].value!=NULL){
              return 1;
          }
      }else{
          break;
      }
    }

    return 0;
}

static
int find_three_zero_position(const uint8_t *payload, uint32_t payload_len)
{
	uint32_t i;
	if (payload_len < 4)
		return -1;
	for (i = 0; i <= payload_len -4; i++) {
		if (payload[i]==0x00 && payload[i+1]==0x00 && payload[i+2]==0x00)
			return (i);
	}

	return -1;
}


static
uint8_t is_gquic_wxf(struct flow_info *flow, weixin_info_t * line_info)
{

  return 0;
}


static
uint8_t is_gquic_wxpyq(struct flow_info *flow, weixin_info_t * line_info)
{
  int i = 0;

  // 查找 filetype 是否有 pyq 类型标识
  struct {
    int index;
    char *value;
  } ftype[] = {
    {0, "20202"},    // video
    {1, "20201"},   // jpg upload
    {2, "20302"},   // jpg upload
    {3, "20204"},   // jpg upload
    {4, "20250"},   // jpg upload
    {5, "20205"},   // jpg upload
    {-1, NULL},
  };

  if (line_info[EM_WX_FILETYPE].value != NULL) {
    weixin_info_t *info = &line_info[EM_WX_FILETYPE];
    for (i = 0; ftype[i].index != -1; ++i) {
      if (memmem(info->value, info->value_len, ftype[i].value, strlen(ftype[i].value)) ) {
        return 1;
      }
    }
  }


  // 查找 url 中是否有 wx_pyq 标识
  struct {
    int index;
    char *value;
  } wxurl[] = {
    {0, "videodownload"},
    {1, "snscosdownload"},
    {2, "stodownload"},
    {3, "snssvpdownload"},
    {4, "mmsns"},
    {-1, NULL}
  };

  if (line_info[EM_WX_URL].value != NULL) {
    weixin_info_t *info = &line_info[EM_WX_URL];
    for (i = 0; ftype[i].index != -1; ++i) {
      if (memmem(info->value, info->value_len, ftype[i].value, strlen(ftype[i].value)) ) {
        return 1;
      }
    }
  }

  return 0;
}


static
int gquic_flow_timeout(struct flow_info *flow)
{
  int i = 0;
  ST_weixin_file_session *session = NULL;
  GquicWXSession *gquic_session = NULL;
  weixin_info_t *line_info;
  /* 解析结果存放于 session中         */
  session   = (ST_weixin_file_session*)flow->app_session;
  gquic_session = &session->gquic_session;

  for (i = 0; i < gquic_session->cnt; ++i) {
    line_info = gquic_session->line_info[i];

    // 判断 pyq 和 wxf
    if (is_gquic_wxpyq(flow, line_info) == 1) {
      if ((line_info[EM_WX_URL].value != NULL ||
          line_info[EM_WX_FILEURL].value != NULL ||
          line_info[EM_WX_VIDEOCDNMSG].value != NULL) &&
          (line_info[EM_WX_WEIXINNUM].value != NULL || line_info[EM_WX_PYQ_UIN].value != NULL)) {
        dissect_weixin_relation(flow, line_info);
      }
      write_weixin_log_pyq(flow, 0, line_info);
      // write_gquic_wx_pyq_log(flow, 0, line_info);
    }
    //  else {
    //   /* 如果开启了过滤[filemd5][url][fileid], 则不输出 */
    //   if (g_config.gquic_wxf_filter > 0 && gquic_wx_get_filter(line_info) != 1) {
    //     free_info_ref(line_info);
    //     return;
    //   }
    //   write_gquic_wxf_log(flow, 0, line_info);
    // }
    //memmem(line_info[EM_WX_FILETYPE].value, line_info[EM_WX_FILETYPE].value_len, str_type, strlen(str_type))


  }

  for (i = 0; i < gquic_session->cnt; ++i) {
    line_info = gquic_session->line_info[i];
    free_info_ref(line_info);
  }
  free_info_ref(session->line_info);

  return 0;
}


static
int gquic_dissect_weixin(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag, GquicInfo *info)
{
  int      ret = 0;
  const uint8_t  *wxf_start  = payload;
  uint32_t wxf_max_len = payload_len;
  uint32_t wxf_len=0;
  int      count=0;
  int      index=0;
  weixin_info_t   *line_info = NULL;
  uint16_t i = 0;
  uint16_t offset = 0;
  uint32_t ver_len;

  ST_weixin_file_session *session = NULL;
  struct timeval tv;

  /* 解析结果存放于 session中         */
  session   = (ST_weixin_file_session*)flow->app_session;
  line_info = session->gquic_session.line_info[info->cid_index];

  // line_info = session->line_info;


  // for(i=0;i<EM_GQUIC_MAX;i++){
  //   line_info[i].free=NULL;
  //   line_info[i].value=NULL;
  //   line_info[i].value_len=0;
  // }


  COPY_VALUE(line_info[EM_WX_RESV6], info->gquic_cid, info->gquic_cid_len, free);
  // offset = GQUIC_WX_HEAD_LEN;

  // tlv 第一个值长度
  ver_len = get_uint32_ntohl(payload, offset + GQUIC_WX_HEAD_LEN);
  if (ver_len == 3) {
    offset += GQUIC_WX_HEAD_LEN;
  } else {
    index = find_three_zero_position(wxf_start,wxf_max_len);

    offset += index;
  }

  ret = parse_weixin_packet_info(flow, direction, payload+ offset, payload_len - offset, line_info);

  weixin_process_fileid(line_info);

  return 0;
}


static
void gquic_session_init(struct flow_info *flow)
{
  //
  if (flow == NULL) {
    return;
  }

  if(NULL == flow->app_session) {
    ST_weixin_file_session *session = NULL;

    flow->app_session = malloc(sizeof(ST_weixin_file_session));
    if(NULL == flow->app_session)
    {
        DPI_LOG(DPI_LOG_ERROR, "WEIXIN SESSION Malloc ERROR");
        return;
    }
    memset(flow->app_session, 0, sizeof(ST_weixin_file_session));
    session        = (ST_weixin_file_session*)flow->app_session;

    session->tid        = flow->thread_id;
    int i, j = 0;
    for (i = 0; i < GQUIC_WX_LINEINFO_MAX; ++i) {
      for(j = 0; j < EM_WX_MAX; j++){
        session->gquic_session.line_info[i][j].free = NULL;
        session->gquic_session.line_info[i][j].value = NULL;
        session->gquic_session.line_info[i][j].value_len = 0;
      }
    }

  }
}

static
int dissect_gquic(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
  if(g_config.protocol_switch[PROTOCOL_GQUIC_WEIXIN] == 0)
  {
    return 0;
  }

  uint32_t offset = 0;
  uint16_t wx_start = 0;
  uint16_t i = 0;
  GquicInfo info;
  ST_weixin_file_session   *session = NULL;
  GquicWXSession *          gquic_session = NULL;
  memset(&info, 0, sizeof(GquicInfo));

  gquic_session_init(flow);

  session = (ST_weixin_file_session*)flow->app_session;
  gquic_session = &session->gquic_session;

  // GquicWXSession *gquic_session = flow->app_sessio

  offset += 1;

  for (i = 0; i < 8; ++i) {
    sprintf(info.gquic_cid + i * 2, "%02x", payload[offset + i]);
  }
  info.gquic_cid_len = 16;
  // info.cid = get_uint64_t(payload, offset);
  for (i = 0; i < GQUIC_WX_LINEINFO_MAX; ++i) {
    if (strncmp(info.gquic_cid, (char *)gquic_session->cid[i], info.gquic_cid_len) == 0) {
      info.cid_index = i;
      break;
    }
    if (gquic_session->cid[i][0] == '\0') {
      snprintf((char *)gquic_session->cid[i], sizeof(gquic_session->cid[i]), "%s", info.gquic_cid);
      info.cid_index = i;
      gquic_session->cnt += 1;
      break;
    }
  }


  offset += 8;

  for (i = 0; i < GQUIC_HEAD_MAX_LEN; ++i) {
    if (offset + GQUIC_WX_PAYLOAD_MIN_LEN > payload_len) return -1;

    wx_start = get_uint16_ntohs(payload, offset);
    if (wx_start == GQUIC_WX_START) {

      uint16_t pos = 0;
      // ab = head lklv
      uint32_t first_key_len = get_uint32_ntohl(payload, offset + GQUIC_WX_HEAD_LEN);
      if (first_key_len == 3) {
        pos = offset + GQUIC_WX_HEAD_LEN + 4;
        if(0 == memcmp(payload + pos,"ver",3) || 0 == memcmp(payload + pos,"seq",3)) {
          break;
        }
      }

      pos = offset + GQUIC_WX_HEAD_LEN;
      if (payload[pos]==0x08 && payload[pos + 1]==0x01 && payload[pos + 2]==0x10) {
        break;
      }
    }
    offset++;
  }

  if (wx_start != GQUIC_WX_START) return -1;


  gquic_dissect_weixin(flow, direction, seq, payload + offset, payload_len - offset, flag, &info);

  return 0;
}


static
void identify_gquic(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
  if(g_config.protocol_switch[PROTOCOL_GQUIC_WEIXIN] == 0)
  {
      return;
  }

  uint16_t offset = 0;
  uint8_t flag =  0;
  uint16_t i = 0;
  uint8_t gquic_first = get_uint8_t(payload, offset);
    // 上行
  if (ntohs(flow->pkt.src_port) > ntohs(flow->pkt.dst_port)) {
    if (gquic_first == GQUIC_UPSTREAM_FLAG_0C ||
        gquic_first == GQUIC_UPSTREAM_FLAG_0D ||
        gquic_first == GQUIC_UPSTREAM_FLAG_1C) {
      flag = 1;
    }
  } else { // 下行
    if (gquic_first == GQUIC_DOWNSTREAM_FLAG_08 || gquic_first == GQUIC_DOWNSTREAM_FLAG_18) {
      flag = 1;
    }
  }

  if (flag != 1) return;
  offset += 1;
  // gquic CID
  offset += 8;

  for (; i < GQUIC_HEAD_MAX_LEN; ++i) {
    if (offset + GQUIC_WX_PAYLOAD_MIN_LEN > payload_len) return;

    uint16_t wx_start = get_uint16_ntohs(payload, offset);
    if (wx_start == GQUIC_WX_START) {

      uint16_t pos = 0;
      // ab = head lklv
      uint32_t first_key_len = get_uint32_ntohl(payload, offset + GQUIC_WX_HEAD_LEN);
      if (first_key_len == 3) {
        pos = offset + GQUIC_WX_HEAD_LEN + 4;
        if(0 == memcmp(payload + pos,"ver",3) || 0 == memcmp(payload + pos,"seq",3)) {
          flow->real_protocol_id = PROTOCOL_GQUIC_WEIXIN;
          return;
        }
      }

      pos = offset + GQUIC_WX_HEAD_LEN;
      if (payload[pos]==0x08 && payload[pos + 1]==0x01 && payload[pos + 2]==0x10) {
        flow->real_protocol_id = PROTOCOL_GQUIC_WEIXIN;
        return;
      }
    }
    offset++;
  }


  return ;
}


static int write_gquic_field_txt(weixin_field_t *proto_array,const char *proto_name)
{
    if(proto_name==NULL || proto_array==NULL){return 0;}

    int i;
    char file_path[TBL_PATH_LENGTH ]={0};
    snprintf(file_path,TBL_PATH_LENGTH ,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);
    FILE *fp=fopen(file_path,"w+");
    if(fp)
    {
        /* common field */
        for(i=0;i<EM_COMMON_MAX;i++)
        {
            fwrite(dpi_common_field[i].field_name,strlen(dpi_common_field[i].field_name),1,fp);
            fwrite("\n",1,1,fp);
        }

        for(i=0;i<EM_GQUIC_MAX;i++)
        {
            fwrite(proto_array[i].field, proto_array[i].field_len,1,fp);
            fwrite("\n",1,1,fp);
        }

        fclose(fp);
    }

    return 1;
}


static
void gquic_wxf_filter_init(void)
{
  int i = 0, j = 0;
  char    buff[64] = { 0 };
  char   *result = NULL;
  uint8_t count = 0;
  for(i=0;i<WEIXIN_FILTER_MAX_NUM;i++){
    g_gquic_wxf_filter[i]=0;
  }
  weixin_field_t *wx_field=NULL;
  result = strtok(g_config.gquic_wxf_filter_str, ",");
  while (result) {
      memset(buff,0,MAX_FIELD_LEN);
      snprintf(buff,MAX_FIELD_LEN,"%s",result);
      count++;
      if(count>=WEIXIN_FILTER_MAX_NUM){
          break;
      }
      int pos = rte_hash_lookup_data(wx_handle, buff, (void **)&wx_field);
      if(pos>0){
          g_gquic_wxf_filter[j]=wx_field->index;
          j++;
      }
      result = strtok(NULL, ",");
  }
}


void init_gquic_dissector(void)
{

    int       i = 0;
    int       j = 0;
    int       count = 0;
    char      tmp_buff[MAX_FIELD_LEN]={0};
    char      *tmp=NULL;


    port_add_proto_head(IPPROTO_UDP, GQUIC_PORT_0, PROTOCOL_GQUIC_WEIXIN);
    port_add_proto_head(IPPROTO_UDP, GQUIC_PORT_1, PROTOCOL_GQUIC_WEIXIN);

    udp_detection_array[PROTOCOL_GQUIC_WEIXIN].proto         = PROTOCOL_GQUIC_WEIXIN;
    udp_detection_array[PROTOCOL_GQUIC_WEIXIN].identify_func = identify_gquic;
    udp_detection_array[PROTOCOL_GQUIC_WEIXIN].dissect_func  = dissect_gquic;
    udp_detection_array[PROTOCOL_GQUIC_WEIXIN].flow_timeout  = gquic_flow_timeout;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_GQUIC_WEIXIN].excluded_protocol_bitmask, PROTOCOL_GQUIC_WEIXIN);


    return;
}
