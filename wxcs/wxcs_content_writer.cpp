#include "wxcs_content_writer.h"

#include <unistd.h>
#include "wxcs_utils.h"

std::string WxcsContentWriter::getSuffix(WxcsContentType fileType)
{
    switch (fileType)
    {
    case WXCS_CONTENT_JPG:
        return ".jpg";
        break;
    default:
        break;
    }

    return "";
}

bool WxcsContentWriter::writeFile(const char *fileDir, const char *fileBaseName, WxcsContentType fileType, const uint8_t *data, uint32_t len)
{
    if(!fileDir || !fileBaseName) {
		return false;
	}

    if (access(fileDir, F_OK) !=0 ) {
        makeDir(fileDir);
    }
    
    std::string suffix = getSuffix(fileType);
    if (suffix.empty()) {
        return false;
    }

    char filePath[1024] = {0};
    sprintf(filePath, "%s/%s%s", fileDir, fileBaseName, suffix.c_str());
    if (access(filePath, F_OK) == 0) {
        return true;
    }

    FILE* fp = fopen(filePath, "wb");
    if (!fp) {
        return false;
    }
    fwrite(data, len, 1, fp);
    fflush(fp);
    fclose(fp);
    return true;
}