function adapt_weixin_pyq(to, from)

to["WXF_totalLen"] = from["WXF_totalLen"]
to["WXF_resv1"] = from["WXF_resv1"]
to["WXF_resv2"] = from["WXF_resv2"]
to["WXF_resv3"] = from["WXF_resv3"]
to["WXF_resv4"] = from["WXF_resv4"]
to["WXF_msgLen"] = from["WXF_msgLen"]
to["WXF_unknown"] = from["WXF_unknown"]
to["Http-Method"] = from["Http-Method"]
to["Http-Uri"] = from["Http-Uri"]
to["Http-Version"] = from["Http-Version"]
to["Http-ContentLength"] = from["Http-ContentLength"]
to["ver"] = from["ver"]
to["weixinnum"] = from["weixinnum"]
to["seq"] = from["seq"]
to["clientversion"] = from["clientversion"]
to["clientostype"] = from["clientostype"]
to["touser"] = from["touser"]
to["wxchattype"] = from["wxchattype"]
to["nettype"] = from["nettype"]
to["apptype"] = from["apptype"]
to["lastip"] = from["lastip"]
to["wxmsgflag"] = from["wxmsgflag"]
to["localname"] = from["localname"]
to["scene"] = from["scene"]
to["url"] = from["url"]
to["fileid"] = from["fileid"]
to["filemd5"] = from["filemd5"]
to["filekey"] = from["filekey"]
to["filetype"] = from["filetype"]
to["filecrc"] = from["filecrc"]
to["filebitmap"] = from["filebitmap"]
to["offset"] = from["offset"]
to["totalsize"] = from["totalsize"]
to["rawtotalsize"] = from["rawtotalsize"]
to["blockmd5"] = from["blockmd5"]
to["rawfilemd5"] = from["rawfilemd5"]
to["filedatamd5"] = from["filedatamd5"]
to["datacheckmd5"] = from["datacheckmd5"]
to["datachecksum"] = from["datachecksum"]
to["fileurl"] = from["fileurl"]
to["refererurl"] = from["refererurl"]
to["compresstype"] = from["compresstype"]
to["recvlen"] = from["recvlen"]
to["srcsize"] = from["srcsize"]
to["hasthumb"] = from["hasthumb"]
to["needthumbflag"] = from["needthumbflag"]
to["thumbflag"] = from["thumbflag"]
to["thumbtotalsize"] = from["thumbtotalsize"]
to["thumbcrc"] = from["thumbcrc"]
to["thumbdata"] = from["thumbdata"]
to["thumburl"] = from["thumburl"]
to["rawthumbsize"] = from["rawthumbsize"]
to["rawthumbmd5"] = from["rawthumbmd5"]
to["encthumbcrc"] = from["encthumbcrc"]
to["hittype"] = from["hittype"]
to["existflag"] = from["existflag"]
to["enablehit"] = from["enablehit"]
to["existancecheck"] = from["existancecheck"]
to["rangestart"] = from["rangestart"]
to["rangeend"] = from["rangeend"]
to["imgheight"] = from["imgheight"]
to["imgwidth"] = from["imgwidth"]
to["jpegscancount"] = from["jpegscancount"]
to["jpegscanlist"] = from["jpegscanlist"]
to["jpegcrclist"] = from["jpegcrclist"]
to["midimglen"] = from["midimglen"]
to["midimgrawsize"] = from["midimgrawsize"]
to["midimgtotalsize"] = from["midimgtotalsize"]
to["midimgchecksum"] = from["midimgchecksum"]
to["midimgmd5"] = from["midimgmd5"]
to["midimgdata"] = from["midimgdata"]
to["rsppicformat"] = from["rsppicformat"]
to["setofpicformat"] = from["setofpicformat"]
to["largesvideo"] = from["largesvideo"]
to["videoformat"] = from["videoformat"]
to["smallvideoflag"] = from["smallvideoflag"]
to["advideoflag"] = from["advideoflag"]
to["mp4identify"] = from["mp4identify"]
to["droprateflag"] = from["droprateflag"]
to["sourceflag"] = from["sourceflag"]
to["authkey"] = from["authkey"]
to["safeproto"] = from["safeproto"]
to["rsaver"] = from["rsaver"]
to["rsavalue"] = from["rsavalue"]
to["nocheckaeskey"] = from["nocheckaeskey"]
to["clientrsaver"] = from["clientrsaver"]
to["clientrsaval"] = from["clientrsaval"]
to["bfrsaver"] = from["bfrsaver"]
to["bfrsavalue"] = from["bfrsavalue"]
to["skeyresp"] = from["skeyresp"]
to["skeybuf"] = from["skeybuf"]
to["aeskey"] = from["aeskey"]
to["sessionbuf"] = from["sessionbuf"]
to["isoverload"] = from["isoverload"]
to["isgetcdn"] = from["isgetcdn"]
to["isstorevideo"] = from["isstorevideo"]
to["isretry"] = from["isretry"]
to["retrycnt"] = from["retrycnt"]
to["retrysec"] = from["retrysec"]
to["x_ClientIp"] = from["x-ClientIp"]
to["X_snsvideoflag"] = from["X-snsvideoflag"]
to["X_encflag"] = from["X-encflag"]
to["X_enclen"] = from["X-enclen"]
to["ipseq"] = from["ipseq"]
to["acceptdupack"] = from["acceptdupack"]
to["wxautostart"] = from["wxautostart"]
to["signal"] = from["signal"]
to["redirect"] = from["redirect"]
to["redirectfail"] = from["redirectfail"]
to["lastretcode"] = from["lastretcode"]
to["retcode"] = from["retcode"]
to["retmsg"] = from["retmsg"]
to["wxmsgsignature"] = from["wxmsgsignature"]
to["elst"] = from["elst"]
to["etl"] = from["etl"]
to["dynamicetl"] = from["dynamicetl"]
to["substituteftype"] = from["substituteftype"]
to["view"] = from["view"]
to["filedata"] = from["filedata"]



end


-- 注册 adapt_weixin_pyq 协议适配器, 将 weixin_pyq 协议适配到 weixin_pyq 协议
yalua_register("weixin_pyq", "weixin_pyq", adapt_weixin_pyq)
