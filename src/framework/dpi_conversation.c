/****************************************************************************************
 * 文 件 名 : dpi_conversation.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/08/20
编码: wangy			2018/08/20
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
//#include <linux/if_ether.h>

#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"

#define CONVERSATION_ONCE_TIMEOUT_NUM 10000

struct rte_hash *conversation_hash_exact;
struct rte_hash *conversation_hash_no_addr2;
struct rte_hash *conversation_hash_no_port2;

extern struct global_config g_config;

int session_protocol_st_size[PROTOCOL_MAX]={0};

static struct rte_hash_parameters conversation_hash_params = {
	.key_len = sizeof(struct conversation_tuple),
	.hash_func = rte_jhash,
	.hash_func_init_val = 0,
	.socket_id = 0,
	.extra_flag = RTE_HASH_EXTRA_FLAGS_MULTI_WRITER_ADD
};
	
struct conversation_value * find_conversation(struct conversation_tuple *tuple, uint8_t options)
{
 	struct conversation_value *conversation = NULL;
	struct conversation_tuple tuple_reverse;
	struct conversation_tuple tuple_original;

	if (!g_config.conversation_switch)
		return NULL;

	int pos;
	
	memcpy(&tuple_original, tuple, sizeof(tuple_original));

	tuple_reverse.port_dst = tuple->port_src;
	tuple_reverse.port_src = tuple->port_dst;
	memcpy(&tuple_reverse.ip_dst, &tuple->ip_src, sizeof(tuple_reverse.ip_dst));
	memcpy(&tuple_reverse.ip_src, &tuple->ip_dst, sizeof(tuple_reverse.ip_src));
	tuple_reverse.proto = tuple->proto;

	if (!(options & (NO_ADDR_B|NO_PORT_B))) {
		/*
		* Neither search address B nor search port B are wildcarded,
		* start out with an exact match.
		*/
		
		pos = rte_hash_lookup_data(conversation_hash_exact, &tuple_original, (void **)&conversation);
		if (pos < 0) {
			pos = rte_hash_lookup_data(conversation_hash_exact, &tuple_reverse, (void **)&conversation);
		}

		if (pos >= 0 && conversation != NULL)
			return conversation;
	}
	
	if (!(options & NO_ADDR_B)) {
		tuple_original.port_dst = 0;
		pos = rte_hash_lookup_data(conversation_hash_no_port2, &tuple_original, (void **)&conversation);
		if (pos < 0) {
			tuple_reverse.port_dst = 0;
			pos = rte_hash_lookup_data(conversation_hash_no_port2, &tuple_reverse, (void **)&conversation);
		}

		if (pos >= 0 && conversation != NULL)
			return conversation;
	}

	if (!(options & NO_PORT_B)) {
		tuple_original.port_dst = tuple->port_dst;
		memset(&tuple_original.ip_dst, 0, sizeof(tuple_original.ip_dst));
		pos = rte_hash_lookup_data(conversation_hash_no_addr2, &tuple_original, (void **)&conversation);
		if (pos < 0) {
			tuple_reverse.port_dst = tuple->port_src;
			memset(&tuple_reverse.ip_dst, 0, sizeof(tuple_reverse.ip_dst));
			pos = rte_hash_lookup_data(conversation_hash_no_addr2, &tuple_reverse, (void **)&conversation);
		}

		if (pos >= 0 && conversation != NULL)
			return conversation;
	}


	return NULL;
}

struct conversation_value *find_or_create_conversation(struct conversation_tuple *tuple, uint8_t options, uint16_t protocol, void *session)
{
	struct conversation_value *conv=NULL;
	struct rte_hash *hash;

	if (!g_config.conversation_switch)
		return NULL;

	if((conv = find_conversation(tuple, options)) != NULL) {
		return conv;
	} else {

		if (!(options & (NO_ADDR_B|NO_PORT_B))) {
			hash = conversation_hash_exact;
		} else if (!(options & NO_ADDR_B)) {
			hash = conversation_hash_no_port2;
		} else if (!(options & NO_PORT_B)) {
			hash = conversation_hash_no_addr2;
		} else
			return NULL;

		conv = (struct conversation_value *)dpi_malloc(sizeof(struct conversation_value));
		if (conv == NULL)
			return NULL;
		conv->protocol = protocol;
		conv->createtime = g_config.g_now_time;
		conv->conv_session = session;

		int retval = rte_hash_add_key_data(hash, tuple, conv);

		if (retval < 0) {
			DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
			dpi_free(conv);
		}
	}

	return conv;
}


static void _timeout_conversation_hash(struct rte_hash *hash)
{
	const void *next_key;
	void *next_data;
	uint32_t iter = 0;
	int i = 0;
	const struct conversation_tuple *tuple;
	const struct conversation_value *conv;

	const struct conversation_tuple *array_timeout[CONVERSATION_ONCE_TIMEOUT_NUM];
	const struct conversation_value *array_timeout_v[CONVERSATION_ONCE_TIMEOUT_NUM];

	while (rte_hash_iterate(hash, &next_key, &next_data, &iter) >= 0) {
		tuple = (const struct conversation_tuple *)next_key;
		conv = (const struct conversation_value *)next_data;

		if (conv->createtime + CONVERSATION_TIMEOUT < g_config.g_now_time) {	
			array_timeout[i] = tuple;
			array_timeout_v[i] = conv;
			i++;
		}

		if (i == CONVERSATION_ONCE_TIMEOUT_NUM)
			break;
	}

	while (i > 0) {
		i--;
		if (array_timeout_v[i])
			dpi_free((void *)(size_t)array_timeout_v[i]);
		
		int retval = rte_hash_del_key(hash, array_timeout[i]);
		if (retval < 0)
			DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple_reverse to hash");
	}

	return;
}

void timeout_conversation_hash(void)
{
	_timeout_conversation_hash(conversation_hash_exact);
	_timeout_conversation_hash(conversation_hash_no_addr2);
	_timeout_conversation_hash(conversation_hash_no_port2);
}

void init_conversation(void)
{
    /* set up config */
    conversation_hash_params.entries = g_config.max_conversation_hash_node;

	conversation_hash_params.name = "conversation_hash_exact";
	conversation_hash_exact = rte_hash_create(&conversation_hash_params);
	if (conversation_hash_exact == NULL) {
		DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_exact failed");
		exit(-1);
	}
	conversation_hash_params.name = "conversation_hash_no_addr2";
	conversation_hash_no_addr2 = rte_hash_create(&conversation_hash_params);
	if (conversation_hash_no_addr2 == NULL) {
		DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_no_addr2 failed");
		exit(-1);
	}
	conversation_hash_params.name = "conversation_hash_no_port2";
	conversation_hash_no_port2 = rte_hash_create(&conversation_hash_params);
	if (conversation_hash_no_port2 == NULL) {
		DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_no_port2 failed");
		exit(-1);
	}
}
