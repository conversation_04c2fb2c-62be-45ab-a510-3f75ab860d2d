/****************************************************************************************
 * 文 件 名 : dpi_wxid.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/10/27
 修改: chenzq          2022/10/27
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include "dpi_wxid.h"

#include <rte_mempool.h>

#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_log.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table  wxid_field_array[] = {
    DPI_FIELD_D(EM_WXID_DATA,                    EM_F_TYPE_STRING,                 "wxid"),
};


static int write_wxid_log(struct flow_info *flow, int direction, WXIDInfo *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    // struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_WXID_MAX; i++)
    {
        switch(wxid_field_array[i].index)
        {
        case EM_WXID_DATA:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->data, strlen(info->data));
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    log_ptr->type        = TBL_LOG_WXID;
    log_ptr->len         = idx;
    log_ptr->tid          = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}



static
int dissect_wxid(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
    const uint32_t payload_len, uint8_t flag)
{
  if(g_config.protocol_switch[PROTOCOL_WXID] == 0) {
      return 0;
  }

  int i = 0;
  DpiUtf8Info utf8_info;
  WXIDInfo    info;
  char tmp_str[64] = { 0 };
  memset(&utf8_info, 0, sizeof(DpiUtf8Info));
  memset(&info, 0, sizeof(WXIDInfo));

  char find_str[] = "wxid";
  int  find_flag = 0;


  dpi_get_utf8_arr(payload, payload_len, &utf8_info, 8);

  for (i = 0; i < utf8_info.num; ++i) {
    snprintf(tmp_str, utf8_info.len[i], "%s", payload + utf8_info.offset[i]);
    find_flag = dpi_strstr((const uint8_t *)tmp_str, strlen(tmp_str), find_str, strlen(find_str));
    if (find_flag < 0)
      continue;
    snprintf(info.data + strlen(info.data), sizeof(info.data), "%s;", tmp_str);
  }

  if (strlen(info.data) == 0 )
    return 0;

  int len = strlen(info.data);
  if (info.data[len - 1] == ';') {
    info.data[len - 1] = '\0';
  }

  write_wxid_log(flow, direction, &info);

  // char tmp_str[256] = { 0 };
  // for (i = 0; i < utf8_info.num; ++i) {
  //   snprintf(tmp_str, utf8_info.len[i], "%s", payload + utf8_info.offset[i]);
  //   printf("index = %d, offset = %d, len = %d\n", i, utf8_info.offset[i], utf8_info.len[i]);
  //   printf("content = %s\n", tmp_str);
  // }

  // dissect_dy_data(flow, direction, payload, payload_len);

  return 0;
}


static
void identify_wxid(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{

  if(g_config.protocol_switch[PROTOCOL_WXID] == 0) {
      return;
  }

  struct dpi_pkt_st pkt;
  uint16_t  flag_0 = 0;
  uint64_t  flag_1 = 0;


  pkt.payload = payload;
  pkt.payload_len = payload_len;



  dpi_get_be16(&pkt, 0, &flag_0);

  if (flag_0 != WXID_FLAG_0)
    return;

  dpi_get_be48(&pkt, 4, &flag_1);
  if (flag_1 == WXID_FLAG_1) {
    flow->real_protocol_id = PROTOCOL_WXID;
  }

  return;
}



void init_wxid_dissector(void)
{
  // fp = fopen(path, "w+");
  //   // int       i = 0;
  //   // int       j = 0;
  //   // int       count = 0;
  //   // char      tmp_buff[MAX_FIELD_LEN]={0};
  //   // char      *tmp=NULL;

  write_proto_field_tab(wxid_field_array, EM_WXID_MAX, "wxid");

  //   // init_wx_relation_dissector();

  port_add_proto_head(IPPROTO_TCP, WXID_PORT_0, PROTOCOL_WXID);
  port_add_proto_head(IPPROTO_TCP, WXID_PORT_1, PROTOCOL_WXID);
  port_add_proto_head(IPPROTO_TCP, WXID_PORT_2, PROTOCOL_WXID);

  tcp_detection_array[PROTOCOL_WXID].proto         = PROTOCOL_WXID;
  tcp_detection_array[PROTOCOL_WXID].identify_func = identify_wxid;
  tcp_detection_array[PROTOCOL_WXID].dissect_func  = dissect_wxid;

  //   // DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN].excluded_protocol_bitmask, PROTOCOL_WEIXIN);


    return;
}



static __attribute((constructor)) void     before_init_wxid(void){
    register_tbl_array(TBL_LOG_WXID, 0, "wxid", init_wxid_dissector);
}