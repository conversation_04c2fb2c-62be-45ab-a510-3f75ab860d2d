/****************************************************************************************
 * 文 件 名 : dpi_thread_timer.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_lcore.h>
#include <rte_cycles.h>
#include <rte_timer.h>
#include <rte_ethdev.h>

#include "dpi_detect.h"
#include "dpi_tbl_log.h"
#include "dpi_conversation.h"

uint64_t g_inc_flow[TRAFFIC_NUM];
struct traffic_stats stat_dpdk[DEV_MAX_NUM][TRAFFIC_NUM];
uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];

static struct rte_timer thread_timer;

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;

rte_atomic64_t test_pkts1;
rte_atomic64_t test_pkts2;
rte_atomic64_t test_pkts3;
rte_atomic64_t test_pkts4;

void init_thread_timer(void);

static void _stat_inc_flow(void)
{
	unsigned int i;
	for (i = 0; i < TRAFFIC_NUM - 1; i++) {
		g_inc_flow[TRAFFIC_NUM - i - 1] = g_inc_flow[TRAFFIC_NUM - i - 2];
	}
	g_inc_flow[0] = 0;

	for (i = 0; i < g_config.dissector_thread_num; i++) {
		g_inc_flow[0] += flow_thread_info[i].stats.inc_flow_num;
	}
}

/*
*目前timer只用在每秒统计收包速率，和处理速率, tbl速率
*/
static void thread_timer_cb(__attribute__((unused)) struct rte_timer *tim,
      __attribute__((unused)) void *arg)
{
	unsigned int i;
	unsigned int j;
	uint16_t idx_port = 0;
	struct rte_eth_stats stat_info;
	int stat;
	_stat_inc_flow();

	for (i = 0; i < TRAFFIC_NUM - 1; i++) {
		RTE_ETH_FOREACH_DEV(idx_port) {
			if (idx_port >= DEV_MAX_NUM) break;
			stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].bytes = stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].bytes;
			stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].pkts = stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].pkts;
			stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].imissed = stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].imissed;
			stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ierrors = stat_dpdk[idx_port][TRAFFIC_NUM - i - 2].ierrors;
		}

		for (j = 0; j < g_config.dissector_thread_num; j++) {
			flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].bytes = flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].bytes;
			flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 1].pkts = flow_thread_info[j].stats.traffic[TRAFFIC_NUM - i - 2].pkts;
		}
	}

	idx_port = 0;
	RTE_ETH_FOREACH_DEV(idx_port) {
		if (idx_port >= DEV_MAX_NUM) break;
		stat_dpdk[idx_port][0].bytes = 0;
		stat_dpdk[idx_port][0].pkts = 0;
		stat_dpdk[idx_port][0].imissed = 0;
		stat_dpdk[idx_port][0].ierrors = 0;
	}

	idx_port = 0;
	RTE_ETH_FOREACH_DEV(idx_port) {
		if (idx_port >= DEV_MAX_NUM) break;
		stat = rte_eth_stats_get(idx_port, &stat_info);
		if (stat == 0) {
			stat_dpdk[idx_port][0].bytes += stat_info.ibytes;
			stat_dpdk[idx_port][0].pkts += stat_info.ipackets;
			stat_dpdk[idx_port][0].imissed += stat_info.imissed;
			stat_dpdk[idx_port][0].ierrors += stat_info.ierrors;
		}
	}

	for (j = 0; j < g_config.dissector_thread_num; j++) {
		flow_thread_info[j].stats.traffic[0].bytes = flow_thread_info[j].stats.total_wire_bytes;
		flow_thread_info[j].stats.traffic[0].pkts = flow_thread_info[j].stats.raw_packet_count;
	}

	for (i = 0; i < TRAFFIC_NUM - 1; i++) {
		for (j = 0; j < TBL_LOG_MAX; j++)
			log_total[j][TRAFFIC_NUM - i - 1] = log_total[j][TRAFFIC_NUM - i - 2];
	}

	timeout_conversation_hash();

	uint64_t hz = rte_get_timer_hz();
	unsigned lcore_id = rte_lcore_id();
	rte_timer_reset_sync(tim, hz, SINGLE, lcore_id,
			thread_timer_cb, tim);

//	printf("pkt1: %llu        pk2:%llu         pkt3:%llu   pkt4:%llu\n",
//			(unsigned long long)rte_atomic64_read(&test_pkts1),
//			(unsigned long long)rte_atomic64_read(&test_pkts2),
//			(unsigned long long)rte_atomic64_read(&test_pkts3),
//			(unsigned long long)rte_atomic64_read(&test_pkts4));
}

void init_thread_timer(void)
{
	uint64_t hz;
	unsigned lcore_id;

	rte_timer_subsystem_init();

	/* init timer structures */
	rte_timer_init(&thread_timer);

	/* load timer0, every second, on master lcore, reloaded automatically */
	hz = rte_get_timer_hz();
	lcore_id = rte_lcore_id();
	rte_timer_reset(&thread_timer, hz, SINGLE, lcore_id, thread_timer_cb, NULL);

}
