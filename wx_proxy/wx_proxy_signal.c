/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#define _POSIX_SOURCE
#include <signal.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <event2/listener.h>
#include <glib.h>

#include "wx_proxy_log.h"
#include "wx_proxy_common.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_thread.h"

/*****************************************************************
*Function    : handler
*Description : catch signal callback, stop server service.
*Input       : signum, proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
static void
handler(int signo, short events, void* arg)
{
    struct context* ctx;
    ctx        = arg;

    fprintf(stderr, "\n");
    log_write(INFO, "wait ...");
    proxy_thread_loopexit(ctx);
    event_base_loopexit(ctx->master_base, NULL);
    log_exit();
}

/*****************************************************************
*Function    : signal_init
*Description : init signal used libevent
*Input       : none
*Output      : none
*Return      : 0:OK, others:ERROR
*Others      : none
*****************************************************************/
int
signal_init(struct context* ctx)
{
    what(ctx == NULL, pexit(-1), "ERROR signal_init ctx=%p", ctx);

    ctx->sig_SIGINT  = evsignal_new(ctx->master_base, SIGINT, handler, ctx);
    evsignal_add(ctx->sig_SIGINT, NULL);

    ctx->sig_SIGQUIT  = evsignal_new(ctx->master_base, SIGQUIT, handler, ctx);
    evsignal_add(ctx->sig_SIGQUIT, NULL);

    ctx->sig_SIGTSTP  = evsignal_new(ctx->master_base, SIGTSTP, handler, ctx);
    evsignal_add(ctx->sig_SIGTSTP, NULL);

    struct sigaction act;
    act.sa_handler = SIG_IGN;
    act.sa_flags   = 0;
    sigemptyset(&act.sa_mask);
    sigaction(SIGPIPE, &act, NULL);

    return 0;
}

/*****************************************************************
*Function    : signal_free
*Description : signal free used by libevent
*Input       : none
*Output      : none
*Return      : [0:OK, othres:ERROR]
*Others      : none
*****************************************************************/
int
signal_free(struct context* ctx)
{
    what(NULL == ctx, pexit(-1), "ERROR signal_free ctx=%p", ctx);

    evsignal_del(ctx->sig_SIGINT );
    evsignal_del(ctx->sig_SIGQUIT);
    evsignal_del(ctx->sig_SIGTSTP);

    return 0;
}


