#include "dpi_precord_writer.h"
#include "dpi_precord_writer_IF.h"
#include "dpi_utils.h"
#include <iostream>

extern "C" {
#include "dpi_detect.h"
  extern struct global_config g_config;
#include "dpi_common.h"  // for mkdirs
}

#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include "cJSON.h"
#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <yyjson.h>

ProtoRecordWriter::ProtoRecordWriter(ProtoRecordWriterKeeper *keeper, const std::string &strProto,
    const std::string &strOutputRootPath, const std::string &strOutputFormat)
    : keeper_(keeper), protoName_(strProto), outputRootPath_(strOutputRootPath), outputFormat_(strOutputFormat) {}

#define BUFFER_SIZE 4096 // 定义缓冲区大小
int ProtoRecordWriter::writeOneRecord_in_tbl(precord_t *record) {
    char buffer[BUFFER_SIZE];
    size_t buffer_index = 0;
    int idx= 0;
    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
        layer           = precord_layer_get_next(record, layer)) {
        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);
        for (pfield_t *field = precord_field_get_first(record); field != NULL; field = precord_field_get_next(record, field)) {
            // pfield_desc_t *desc =  precord_field_get_fdesc(field);
          ya_fvalue_t *fvalue = precord_field_get_fvalue(field);
          if (fvalue == NULL) {
            if (buffer_index + 1 >= BUFFER_SIZE) {
              fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
              buffer_index = 0;
            }
            if (g_config.tbl_out_quote) {
              memcpy(buffer + buffer_index, "\"\"", 2);
              buffer_index+= 2;
            }
            buffer[buffer_index++] = '|';
            continue;
          }
          char *fvalue_str = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
          size_t len = strlen(fvalue_str);
          int i = 0;
          while(fvalue_str[i]!= '\0')
          {
            if (fvalue_str[i] ==  '|'){
            fvalue_str[i] = '_';
            }
            if(fvalue_str[i]  == '\n'||fvalue_str[i] == '\r')
                fvalue_str[i] = ' ';

            i++;
          }

          if (len >= BUFFER_SIZE) {
            fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
            buffer_index = 0;
            if (g_config.tbl_out_quote){
                fwrite("\"", sizeof(char), 1, currentFileHandle_);
            }
            fwrite(fvalue_str, sizeof(char), len, currentFileHandle_);
            if (g_config.tbl_out_quote){
                fwrite("\"", sizeof(char), 1, currentFileHandle_);
            }
            fwrite("|", sizeof(char), 1, currentFileHandle_);
          } else {
            if (buffer_index + len + 1 >= BUFFER_SIZE) {
              fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
              buffer_index = 0;
            }
            if (g_config.tbl_out_quote) {
              memcpy(buffer + buffer_index, "\"", 1);
              buffer_index++;
            }
            memcpy(buffer + buffer_index, fvalue_str, len);
            buffer_index += len;
            if (g_config.tbl_out_quote) {
            memcpy(buffer + buffer_index, "\"", 1);
            buffer_index++;
            }
            buffer[buffer_index++] = '|';
          }

          ya_fvalue_free_string_repr(fvalue_str);
        }
    }
    buffer[buffer_index++] = '\n';

    if (buffer_index > 0) {
      fwrite(buffer, sizeof(char), buffer_index, currentFileHandle_);
    }

  fflush(currentFileHandle_);
  return 0;
}

int ProtoRecordWriter::writeOneRecord_in_json(precord_t *record) {
   char * value_string = NULL;
    char * elems[2048];
    int   index = 0;
    yyjson_mut_doc * doc = yyjson_mut_doc_new(NULL);
    yyjson_mut_val * root = yyjson_mut_obj(doc);
    yyjson_mut_doc_set_root(doc, root);

    const char *proto_name = precord_get_proto_name(record);
    yyjson_mut_val * proto_key = yyjson_mut_str(doc, proto_name);
    yyjson_mut_val * proto_root = yyjson_mut_obj(doc);
    yyjson_mut_obj_add(root, proto_key, proto_root);

    player_t* layer_top = precord_layer_get_top(record);
    const char *layer_top_name = precord_layer_get_layer_name(layer_top);
    if (strcmp(layer_top_name,"common") == 0) {
    return 0 ;
    }
    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
         layer           = precord_layer_get_next(record, layer)) {

        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);

        for (pfield_t *field = precord_field_get_first(record);
            field != NULL;
            field = precord_field_get_next(record, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);

            yyjson_mut_val * field_name = yyjson_mut_str(doc, pfdesc_get_name(fdesc));

            if (fvalue == NULL ) {
                    yyjson_mut_obj_add(proto_root, field_name, yyjson_mut_strn(doc, "", 0));
                continue;
            }


            elems[index] = ya_fvalue_to_string_repr(fvalue, BASE_NONE);

            // yyjson_mut_val * val  = yyjson_mut_strcpy(doc, value_string);
            yyjson_mut_val * val  = yyjson_mut_str(doc, elems[index]);
            yyjson_mut_obj_add(proto_root, field_name, val);
            // ya_fvalue_free_string_repr(value_string);
            index++;
        }

    }
    static const yyjson_write_flag write_flags = YYJSON_WRITE_ALLOW_INVALID_UNICODE; // YYJSON_WRITE_ALLOW_INVALID_UNICODE;

    // yyjson_write_err err;

    yyjson_mut_write_fp(currentFileHandle_, doc, write_flags, NULL, NULL);
    // char *json_str = yyjson_mut_write_opts(doc, write_flags, NULL, NULL, &err);
    // if (json_str == NULL) {
    //     DPI_LOG(DPI_LOG_ERROR, "[%s] yyjson write error[%u]: %s\n", protoName_.c_str(), err.code, err.msg);
    //     yyjson_mut_doc_free(doc);
    //     return -1;
    // }

    // fprintf(currentFileHandle_, "%s\n", json_str);
    fwrite("\n", 1, 1, currentFileHandle_);

    // free(json_str);
    yyjson_mut_doc_free(doc);

    for (int i = 0 ; i < index; ++i) {
        ya_fvalue_free_string_repr(elems[i]);
    }

    return 0;
}

std::string ProtoRecordWriter::generateOutputParentDirPath() {
  char buff[1024] = {0};
  char time_buff[10] = {0};

  dpi_utils_strftime(time_buff, sizeof time_buff, NULL);

  // 输出格式: 协议名/日期
  snprintf(buff, sizeof buff, "%s/%s",
      outputRootPath_.c_str(),  // 根路径
      protoName_.c_str()        // 协议名
                                //  time_buff                // 日期
  );

  if (access(buff, F_OK) != 0) {
    mkdirs(buff);
  }

  return buff;
}

std::string ProtoRecordWriter::generateOutputFileName() {
  char buff[1024] = {0};
  char time_buff[24] = {0};

  dpi_utils_strftime(time_buff, sizeof time_buff, "%Y%m%d%H%M%S");

  // 设备名_协议名_生成时间_IP地址_线程号_流水号.txt
  snprintf(buff, sizeof buff, "%s_%s_%03d.tbl",
      time_buff,           // 生成时间
      protoName_.c_str(),  // 协议名
      // keeper_->getExtraProperty("dev_ip").c_str(),     // IP地址
      std::stoi(keeper_->getExtraProperty("thread_id").c_str())); // 线程号
      // keeper_->getNewFileSerialNumber());

  return buff;
}

std::string ProtoRecordWriter::generateOutputFilePath() {
  outputParentDirPath_ = generateOutputParentDirPath();  // TODO: parentDir 一天才变化一次, 可以优化
  currentFileName_ = generateOutputFileName();
  currentFileNameWriting_ = currentFileName_ + ".writing";
  currentFilePath_ = outputParentDirPath_ + "/" + currentFileNameWriting_;

  return currentFilePath_;
}

int ProtoRecordWriter::openNewOutputFile() {
  currentFilePath_ = generateOutputFilePath();
  currentFileHandle_ = fopen(currentFilePath_.c_str(), "w");
  if (NULL == currentFileHandle_) {
    // TODO: log
    fprintf(stderr, "openNewOutputFile %s error %s\n", currentFilePath_.c_str(), strerror(errno));
    return -1;
  }

  currentFileCreateTime_ = time(NULL);
  currentFileWriteRecordCounts_ = 0;
  return 0;
}

bool ProtoRecordWriter::currentFileShouldRotate(uint64_t time_now) {
  if (currentFileWriteRecordCounts_ >= recordCountsMaxPerFile_) {
    return true;
  }

  // 至少写入了一条记录之后发生了"超时"
  if (currentFileWriteRecordCounts_ > 0 && (time_now - lastWriteInTime_) > writingTimeout_inSeconds_) {
    // TODO: log
    return true;
  }

  return false;
}

int ProtoRecordWriter::closeCurrentFile() {
  if (NULL == currentFileHandle_ || access(currentFilePath_.c_str(), F_OK) != 0)  // 文件不存在，可能被意外删除
  {
    return 0;
  }
  fclose(currentFileHandle_);
  currentFileHandle_ = NULL;

  std::string currentFileFinalPath = outputParentDirPath_ + "/" + currentFileName_;
  if (rename(currentFilePath_.c_str(), currentFileFinalPath.c_str()) < 0) {
    return -1;
  }

  return 0;
}

int ProtoRecordWriter::writeRecord(precord_t *record) {
  if ((NULL == currentFileHandle_ || access(currentFilePath_.c_str(), F_OK) != 0)  // 文件不存在，可能被意外删除
      && openNewOutputFile() < 0) {
    // TODO: log
    return -1;
  }
  if (strcmp(outputFormat_.c_str(), "json") == 0) {
    writeOneRecord_in_json(record);
  } else if (strcmp(outputFormat_.c_str(), "tbl") == 0) {
    writeOneRecord_in_tbl(record);
  }

  // 更新计数与写入时间
  currentFileWriteRecordCounts_++;
  lastWriteInTime_ = time(NULL);

  // 文件需要进行滚动，此时仅关闭旧文件，
  // 新文件会在下次有数据到来时才创建,否则会产生空文件;
  if (currentFileShouldRotate(lastWriteInTime_)) {
    closeCurrentFile();
  }

  return 0;
}

int ProtoRecordWriter::onIdleCheck(uint64_t time_now) {
  if (currentFileShouldRotate(time_now)) {
    closeCurrentFile();
  }

  return 0;
}

int ProtoRecordWriter::onProcessGoingToTerminate() {
  closeCurrentFile();
  return 0;
}

int ProtoRecordWriter::setRecordCountPerFile(uint32_t recordCount) {
  recordCountsMaxPerFile_ = recordCount;
  return 0;
}

std::vector<std::unique_ptr<ProtoRecordWriterKeeper>> ProtoRecordWriterKeeper::cs_perThreadProtoRecordWriterKeeper;

ProtoRecordWriterKeeper::ProtoRecordWriterKeeper(const std::string &strOutputRootPath, const std::string &strOutputFormat)
    : outputRootPath_(strOutputRootPath), outputFormat_(strOutputFormat) {}

ProtoRecordWriter *ProtoRecordWriterKeeper::getProtoRecordWriterOf(const std::string &strProtoName) {
  auto findIter = mapping_protoName2RecordWriter_.find(strProtoName);
  if (findIter != mapping_protoName2RecordWriter_.end()) {
    return findIter->second.get();
  }

  ProtoRecordWriter *newRecordWriter = new ProtoRecordWriter(this, strProtoName, outputRootPath_, outputFormat_);
  mapping_protoName2RecordWriter_.emplace(strProtoName, std::unique_ptr<ProtoRecordWriter>(newRecordWriter));

  return newRecordWriter;
}

int ProtoRecordWriterKeeper::createSomeProtoRecordWriterKeeper(
    uint32_t count, const std::string &strOutputRootPath, const std::string &strOutputFormat) {
  for (uint32_t i = 0; i < count; i++) {
    ProtoRecordWriterKeeper *newKeeper = new ProtoRecordWriterKeeper(strOutputRootPath, strOutputFormat);
    cs_perThreadProtoRecordWriterKeeper.push_back(std::unique_ptr<ProtoRecordWriterKeeper>(newKeeper));
  }

  return 0;
}

ProtoRecordWriterKeeper *ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(uint32_t index) {
  if (index >= cs_perThreadProtoRecordWriterKeeper.size()) {
    return NULL;
  }

  return cs_perThreadProtoRecordWriterKeeper[index].get();
}

uint64_t ProtoRecordWriterKeeper::getNewFileSerialNumber() { return protoFileSerialNumber_++; }

int ProtoRecordWriterKeeper::setExtraProperty(const std::string &name, const std::string &value) {
  extraPropertyMap_.emplace(name, value);
  return 0;
}

std::string ProtoRecordWriterKeeper::getExtraProperty(const std::string &name) {
  auto findIter = extraPropertyMap_.find(name);
  if (findIter == extraPropertyMap_.end()) {
    return "[" + name + "_" + "noValue" + "]";
  }

  return findIter->second;
}

int ProtoRecordWriterKeeper::foreachRecordWriter(std::function<int(ProtoRecordWriter *)> func) {
  for (auto &pair : mapping_protoName2RecordWriter_) {
    func(pair.second.get());
  }

  return 0;
}

int dpi_output_create_writer_keeper_for_n_thread(
    uint32_t write_thread_count, const char *output_root_path, const char *output_format) {
  return ProtoRecordWriterKeeper::createSomeProtoRecordWriterKeeper(write_thread_count, output_root_path, output_format);
}

static ProtoRecordWriter *dpi_output_get_writer_of(uint32_t write_thread_index, precord_t *record) {
  ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
  if (NULL == keeper) {
    return NULL;
  }

  const char *proto_to_write = precord_get_proto_name(record);
  ProtoRecordWriter *writer = keeper->getProtoRecordWriterOf(proto_to_write);
  if (NULL == writer) {
    return NULL;
  }

  return writer;
}

int dpi_output_write_one_record(uint32_t write_thread_index, precord_t *record) {
  ProtoRecordWriter *writer = dpi_output_get_writer_of(write_thread_index, record);
  if (NULL == writer) {
    return -1;
  }

  return writer->writeRecord(record);
}

int dpi_output_do_idle_check(uint32_t write_thread_index, uint64_t time_now) {
  ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
  if (NULL == keeper) {
    return -1;
  }
  keeper->foreachRecordWriter([=](ProtoRecordWriter *writer) {
    writer->onIdleCheck(time_now);
    return 0;
  });

  return 0;
}

int dpi_output_on_process_going_to_terminate(uint32_t write_thread_index) {
  printf("process will terminate.\n");
  return 0;
}

int dpi_output_set_writer_property(uint32_t write_thread_index, const char *name, const char *value) {
  ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
  if (NULL == keeper) {
    return -1;
  }

  if (value && strlen(value) > 0) {
    keeper->setExtraProperty(name, value);
    return 0;
  }

  // NULL value
  std::string str_value = "[";
  str_value += name;
  str_value += "_noValue]";
  keeper->setExtraProperty(name, str_value);

  return 0;
}

int dpi_output_set_writer_property_number(uint32_t write_thread_index, const char *name, uint64_t number) {
  ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
  if (NULL == keeper) {
    return -1;
  }

  keeper->setExtraProperty(name, std::to_string(number));
  return 0;
}

int dpi_output_set_writer_record_count_per_file(uint32_t write_thread_index, uint32_t record_count) {
  ProtoRecordWriterKeeper *keeper = ProtoRecordWriterKeeper::getProtoRecordWriterKeeperByIndex(write_thread_index);
  if (NULL == keeper) {
    return -1;
  }

  keeper->foreachRecordWriter([=](ProtoRecordWriter *writer) {
    writer->setRecordCountPerFile(record_count);
    return 0;
  });

  return 0;
}
