#vDateTime
#string(TIMESTAMP vDateTime "%Y%m%d_%H%M%S")
string(TIMESTAMP vDateTime "%Y_%m_%d")

#vProjectDir
execute_process(
  COMMAND basename ${CMAKE_CURRENT_SOURCE_DIR}
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  OUTPUT_VARIABLE vProjectDir
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

#vGitBranch
execute_process(
  COMMAND git rev-parse --abbrev-ref HEAD
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  OUTPUT_VARIABLE vGitBranch
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

#vGitCommit
execute_process(
  COMMAND git log -1 --pretty=format:%h
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  OUTPUT_VARIABLE vGitCommit
  OUTPUT_STRIP_TRAILING_WHITESPACE
)


#vGitTagID
#execute_process(
#  COMMAND git rev-list --tags --max-count=1
#  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
#  OUTPUT_VARIABLE vGitTagID
#  OUTPUT_STRIP_TRAILING_WHITESPACE
#)

#vGitTag
#execute_process(
#  COMMAND git describe --tag ${vGitTagID}
#  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
#  OUTPUT_VARIABLE vGitTag
#  OUTPUT_STRIP_TRAILING_WHITESPACE
#)

execute_process(
  COMMAND git describe --abbrev=0 --tags
  WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  OUTPUT_VARIABLE vGitTag
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

set(vFileName "${CMAKE_CURRENT_SOURCE_DIR}/version.h")

file(WRITE ${vFileName} "\r\n#ifndef VERSION_H\r\n#define VERSION_H\r\n" )
file(APPEND ${vFileName} "const char* version=\"${vGitTag}_${vGitCommit}_${vGitBranch}_${vDateTime}\";")
file(APPEND ${vFileName} "\r\n#endif")

