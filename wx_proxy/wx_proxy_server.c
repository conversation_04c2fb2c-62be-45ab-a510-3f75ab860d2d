/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <unistd.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <event2/listener.h>

#include <glib.h>

#include "wx_proxy_common.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_thread.h"
#include "wx_proxy_timer.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_server.h"
#include "wx_proxy_log.h"
#include "wx_proxy_process.h"

struct connection_t*
slave_connection_init(struct sockaddr *sa, struct mirror_t *mirror)
{
    struct sockaddr_in  *sock = (struct sockaddr_in*)sa;
    struct bufferevent  *bev = NULL;
    struct connection_t *connection = NULL;
    struct event_base   *base = NULL;
    int                 ret = 0;
    int                 socklen =0;
    char                buff[64];

    base = mirror->base;

    format_sockaddr(sa, buff, sizeof(buff));
    log_write(INFO, "connecting [%s:%u] bind base[%p]", buff, ntohs(sock->sin_port), mirror->base);

    socklen = sizeof(struct sockaddr);
    bev = bufferevent_socket_new(base, -1, BEV_OPT_CLOSE_ON_FREE|BEV_OPT_DEFER_CALLBACKS);
    what(NULL == bev, goto ERROR, "Error in bufferevent_socket_new");

    ret = bufferevent_socket_connect(bev, sa, socklen);
    what(ret < 0, goto ERROR, "can't connect");

    connection          = connection_malloc();
    connection->mirror  = mirror;
    connection->bev     = bev;
    connection->readcb  = slave_readcb;
    connection->writecb = slave_writecb;
    connection->eventcb = slave_eventcb;
    bufferevent_setcb (bev, connection->readcb, NULL, connection->eventcb, connection);
    bufferevent_enable(bev, EV_READ|EV_WRITE|EV_CLOSED);
    return connection;

ERROR:
    log_write(ERROR, "connecting [%s:%u] FAIL", buff, ntohs(sock->sin_port));
    connection_free(connection);
    return NULL;
}

struct connection_t*
master_connection_init(int client_fd, struct mirror_t *mirror)
{
    struct connection_t *connection = NULL;
    struct bufferevent  *bev = NULL;
    struct event_base   *base = NULL;

    base = mirror->base;

    log_write(INFO, "bind client fd %d with base %p", client_fd, base);
    bev = bufferevent_socket_new(base, client_fd, BEV_OPT_CLOSE_ON_FREE|BEV_OPT_DEFER_CALLBACKS);
    what(NULL == bev,  goto ERROR, "ERROR client fd bind bufferevent");

    connection          = connection_malloc();
    connection->mirror  = mirror;
    connection->bev     = bev;
    connection->readcb  = master_readcb;
    connection->writecb = master_writecb;
    connection->eventcb = master_eventcb;

    bufferevent_setcb (bev, connection->readcb, NULL, connection->eventcb, connection);
    bufferevent_enable(bev, EV_READ|EV_WRITE|EV_CLOSED);
    bufferevent_setwatermark(bev, EV_READ|EV_WRITE, 0, 4096); //READ BUFF 超过N值之后, 暂停收包

    log_write(DEBUG, "mirror->master connection=%p", connection);
    return connection;

ERROR:
    connection_free(connection);
    return NULL;
}
/*****************************************************************
*Function    : accept_cb
*Description : this function need run in worker thread, bind with worker thread event base
*Input       : socket addr, thread_info
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
static void
accept_cb(struct evconnlistener *listener, int client_fd, struct sockaddr *client, int client_size, struct thread_info* thread)
{
    struct mirror_t         *mirror = NULL;
    struct context          *ctx = NULL;
    int                      ret = 0;
    char                     buff[1024];

    ctx         = thread->ctx;

    /**** Print Client info ****/
    if (client->sa_family == AF_INET)
    {
        const struct sockaddr_in *sin = (const struct sockaddr_in*)client;
        log_write(INFO, "new client:[%s] port:[%u] on thread [%d]",
                inet_ntop(AF_INET, &sin->sin_addr.s_addr, buff, sizeof(buff)), ntohs(sin->sin_port), thread->index);
    }

    /**** create mirror session  ****/
    mirror = mirror_create(ctx);
    what(NULL == mirror, return, "ERROR session_new");
    log_write(INFO, "create session successful mirror=%p", mirror);

    /**** set session  ****/
    mirror->base              = thread->base;
    mirror->thread            = thread;

    /**** push current session to a thread_array ****/
    log_write(DEBUG, "add session to thread %d session manager", thread->index);
    g_ptr_array_add(thread->session_manager, mirror);

    /**** Get client fd, and bind to a thread_event ****/
    mirror->master = master_connection_init(client_fd, mirror);

    /* connection to SLAVE_1 */
    mirror->slave_1 = slave_connection_init(&ctx->conf.slave_1, mirror);
    what(0 != ret,  goto ERROR, "ERROR SLAVE_1 init");
    log_write(DEBUG, "mirror->slave_1 connection %p", mirror->slave_1);

    /* connection to SLAVE_2 */
    mirror->slave_2 = slave_connection_init(&ctx->conf.slave_2, mirror);
    what(0 != ret,  goto ERROR, "ERROR SLAVE_2 init");
    log_write(DEBUG, "mirror->slave_2 connection %p", mirror->slave_2);

    return;

ERROR:

    if(mirror)
    {
        mirror_free(mirror);
        mirror = NULL;
        log_write(DEBUG, "init slave ERROR, free mirror session");
    }
}

/*****************************************************************
*Function    : accept_enqueue
*Description : this function need run in master thread, enqueue worker thread, and wakeup it
*Input       : socket addr, proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
static void
accept_enqueue(struct evconnlistener *listener, int client_fd, struct sockaddr *sa, int salen, void *global_ctx)
{
    struct context          *ctx;
    struct new_client       *client;
    struct thread_info      *thread;

    ctx     = global_ctx;
    thread  = proxy_thread_get_least_conn(ctx);

    log_write(INFO, "client enqueue, push thread %d", thread->index);
    client = accept_push(thread);
    if(NULL == client)
    {
        log_write(ERROR, "accept client enqueue is full");
        close(client_fd);
        return;
    }

    client->accept     = accept_cb;
    client->client_fd  = client_fd;
    client->listener   = listener;
    client->sa         = sa;
    client->salen      = salen;
    thread->wakeup(thread);
}

/*****************************************************************
*Function    : create_server
*Description : this function need run in master thread, create TCP server use master event_base
*Input       : port, proxy ctx
*Output      : none
*Return      : listener
*Others      : none
*****************************************************************/
struct evconnlistener*
create_server(int listen_port, struct context *ctx)
{
    struct evconnlistener *listener;
    struct sockaddr_in     sin;
    int                    socklen;

    /**** set_sockaddr  ****/
    socklen = sizeof(sin);
    memset(&sin, 0, socklen);

    sin.sin_port           = htons(listen_port);
    sin.sin_addr.s_addr    = htonl(INADDR_ANY);
    sin.sin_family         = AF_INET;

    /**** register accept  callback ****/
    listener = evconnlistener_new_bind(ctx->master_base, accept_enqueue, ctx,
            LEV_OPT_CLOSE_ON_FREE|LEV_OPT_CLOSE_ON_EXEC|LEV_OPT_REUSEABLE|LEV_OPT_THREADSAFE,
            1024, (struct sockaddr*)&sin, socklen);
    return listener;
}

