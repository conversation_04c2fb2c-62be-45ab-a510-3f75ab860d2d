/****************************************************************************************
 * 文 件 名 : wxcs_session.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-14
* 编    码 : root      '2019-01-14
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_SESSION_H_
#define _WXCS_SESSION_H_

#include "wxcs_def.h"
#include "wxcs_utils.h"
#include "wxcs_config.h"
#include "wxcs_logger.h"
#include "wxcs_person.h"
#include "wxcs_dpi_node.h"

#include <muduo/net/Endian.h>

#include <map>
#include <iostream>
#include <set>
#include <algorithm>

using namespace muduo;
using namespace muduo::net;

#define SESSION_PERSON_COUNT_MAX	  (10)
#define SESSION_GROUP_PERSON_COUNT_MAX	(50)
#define SESSION_POSITION_PERSON_COUNT_MAX	(20)
#define SESSION_LS_PERSON_COUNT_MAX     20


std::string WriteTrailerCommon(int i, char sep);

template <typename PersonType>
class WxcsSessionBase
{
public:
    WxcsSessionBase(){}

    WxcsSessionBase(char *byteSessionId, int len, uint8_t sessionType, uint32_t sessionStartTime)
        : byteSessionId_(byteSessionId, len)
        , sessionType_(sessionType)
        , sessionStartTime_(sessionStartTime)
        , sessionStopTime_(0)
        , sessionLastActiveTime_(0)
    {
        generateSessionCode();
    }

public:
    inline uint8_t GetSessionType() const { return sessionType_; }
    inline void    SetSessionType(uint8_t session_type) { sessionType_ = session_type; }
    PersonPtr<PersonType> findPerson(const PersonPtr<PersonType> &person)
    {
        std::lock_guard<std::mutex> lck(personMtx_);
        auto iter = personMap_.find(person->getPersonID());

        if (iter == std::end(personMap_))
        {
            return NULL;
        }

        return iter->second;
    }

    int addNewPerson(PersonPtr<PersonType> &person, bool bInterestingPerson)
    {
        if (bInterestingPerson)
        {
            LOG_INTST->warn("新目标:{}, session:{}, 解析板:{}, 运营商:{}",
                    person->getPersonID(), getPrintableSessionId(), person->getDevName(), person->getOperator());
        }
        else
        {
            LOG_DEF->debug("新用户:{}, session:{}, 解析板:{}, 运营商:{}",
                    person->getPersonID(), getPrintableSessionId(), person->getDevName(), person->getOperator());
        }

        std::lock_guard<std::mutex> lck(personMtx_);
        sessionLastActiveTime_  = person->getLastActiveTime();
        personMap_.emplace(person->getPersonID(), person);
        return 0;
    }

    int updatePersonInfo(const PersonPtr<PersonType> &person, bool bInterestingPerson)
    {
        auto pOldPersonInfo = findPerson(person);
        if (!pOldPersonInfo)
        {
            return -1;
        }

        if (bInterestingPerson)
        {
            LOG_INTST->warn("活跃目标:{}, session:{}, 解析板:{}, 运营商:{}",
                           person->getPersonID(), getPrintableSessionId(),
                           person->getDevName(), person->getOperator());
        }
        else
        {
            LOG_DEF->debug("活跃用户:{}, session {}, 解析板:{}, 运营商:{}",
                           person->getPersonID(), getPrintableSessionId(),
                           person->getDevName(), person->getOperator());
        }

        std::lock_guard<std::mutex> lck(personMtx_);
        sessionLastActiveTime_  = person->getLastActiveTime();
        personMap_[person->getPersonID()] = person;

        return 0;
    }

    void generateSessionCode(std::string field = std::string())
    {
        static int maxCode = CFG->GetValueOf<int>("TBL_FILE_NAME_MAX_FORMAT_CODE", 1);
        const std::string& tmp = field.empty() ? byteSessionId_ : field;
        sessionCode_ = maxCode > 1 ? hashFunc_(tmp) % maxCode : 0;
    }

public:

    int getPersonCount() const
    {
        return personMap_.size();
    }

    const std::string &getSessionId() const
    {
        return byteSessionId_;
    }

    bool wasDeadSession(int activeDiffMax) const
    {
        // 1 wxdpi 脱机时长 检测
        for (auto &kv : personMap_)
        {
            if(time(NULL) - get_wxcs_timestamp(kv.second->getDPI_NodeID()) > activeDiffMax)
            {
                return true;
            }
        }

        // 2 Session 超时检测
        bool isTimeout = true;
        for (auto &kv : personMap_)
        {
            if(get_node_timestamp(kv.second->getDPI_NodeID()) - kv.second->getLastActiveTime() < activeDiffMax)
            {
                isTimeout = false;
                break;
            }
        }
        if (isTimeout) {
            return true;
        }

        return false;
    }

public:
    std::string getPrintableSessionId() const
    {
        return   bytes_to_hexstring((uint8_t *)byteSessionId_.data(), byteSessionId_.length());
    }

    std::string getPersonList() const
    {
        std::string strPersonList;
        for (auto kv : personMap_)
        {
            strPersonList += kv.second->getPrintablePersonID() + ',';
        }

        return strPersonList;
    }

    uint16_t getSessionCode() const
    {
        return sessionCode_;
    }

    // need specialization
    std::string toStrRecordLine(char sep) const;

    static std::string getColumnList(char sep);


protected: // session info
    std::string byteSessionId_;
    uint8_t     sessionType_;
    uint32_t    sessionStartTime_;
    uint32_t    sessionStopTime_;
    uint32_t    sessionLastActiveTime_; // 会话最近活跃时间，由前端报告得来

protected:
    std::hash<std::string> hashFunc_;   // hash函数，固定key类型，需要其他类型的话统一转std::string
    uint16_t sessionCode_;              // 参与tbl文件名编码

public: // person map, MSISDN -> Person
    std::map<uint64_t, PersonPtr<PersonType> > personMap_;
    std::mutex personMtx_;
};

// WxcsSession
template <typename PersonType>
class WxcsSession : public WxcsSessionBase<PersonType>
{
public:
    using WxcsSessionBase<PersonType>::WxcsSessionBase;
};

template <typename PersonType>
using SessionPtr = std::shared_ptr<WxcsSession<PersonType> >;

// WxcsSession for WxcsPositionPerson
template<>
class WxcsSession<WxcsPositionPerson> : public WxcsSessionBase<WxcsPositionPerson>
{
public:
    using WxcsSessionBase<WxcsPositionPerson>::WxcsSessionBase;

public:
    // 特化基类方法的满足不了需求，这里选择在子类重写
    std::string toStrRecordLine(char sep) const;

    std::string getLongitude() const {
        return personMap_.begin()->second->getLongitude();
    }

    std::string getLatitude() const {
        return personMap_.begin()->second->getLatitude();
    }
};

// WxcsSession for WxcsQQfilePerson
template<>
class WxcsSession<WxcsQQFilePerson> : public WxcsSessionBase<WxcsQQFilePerson>
{
public:
    using WxcsSessionBase<WxcsQQFilePerson>::WxcsSessionBase;

public:
    std::string toStrRecordLine(char sep) const ;

    bool get_ispictupe() const
    {
        return m_ispictupe;
    }

    void set_ispictupe(int v)
    {
        m_ispictupe = v;
    }

    void set_pictupe_w(std::string str)
    {
        pictupe_w = str;
    }

    std::string get_pictupe_w() const
    {
        return pictupe_w;
    }

    void set_pictupe_h(std::string str)
    {
        pictupe_h = str;
    }

    std::string get_pictupe_h() const
    {
        return pictupe_h;
    }

private:
    bool m_ispictupe;
    std::string  pictupe_w;
    std::string  pictupe_h;
};

// WxcsSession for WxcsQQGroupPerson
template<>
class WxcsSession<WxcsQQGroupPerson> : public WxcsSessionBase<WxcsQQGroupPerson>
{
public:
    using WxcsSessionBase<WxcsQQGroupPerson>::WxcsSessionBase;

public:
    // 特化基类方法的满足不了需求，这里选择在子类重写
    std::string toStrRecordLine(char sep) const;

    std::string getPersonList() const;
    std::string getPersonListMaxMember() const;

    void handleExtraQQList(uint64_t selfQQ, uint64_t* otherQQs, int len);

private:
    std::set<uint64_t> extraQQSet_;
};

// WxcsSession for WxcsQQSinglePerson
template<>
class WxcsSession<WxcsQQSinglePerson> : public WxcsSessionBase<WxcsQQSinglePerson>
{
public:
    using WxcsSessionBase<WxcsQQSinglePerson>::WxcsSessionBase;

    bool wasDeadSession(int activeDiffMax) const {
        // 1 dpi 离线检测, 只要任意一个 DPI离线, 相关这个Session则超时掉
        for (auto &kv : personMap_) {
            if(time(NULL) - get_wxcs_timestamp(kv.second->getDPI_NodeID()) > activeDiffMax)
            {
                return true;
            }
        }

        // 2 isTimeout 加速
        bool isTimeout = true;
        for (auto& kv : personMap_) {
            if(!kv.second->isTimeout_) {
                isTimeout = false;
                break;
            }
        }
        if(isTimeout) {
            return true;
        }

        // 3 Session 超时检测
        isTimeout = true;
        for (auto& kv : personMap_) {
            if(get_node_timestamp(kv.second->getDPI_NodeID()) - kv.second->getLastActiveTime() < activeDiffMax) {
                isTimeout = false;
                break;
            }
        }
        if (isTimeout) {
            return true;
        }

        return false;
    }

    std::string getPrintableSessionId() const {
        return  byteSessionId_;
    }

public:
    std::string toStrRecordLine(char sep) const;
    std::string getPersonList() const;
    std::string get48PersonList() const;
    void setOtherQQNum(uint64_t qq) { otherQQNum_ = qq;}
    void setOtherQQPubIP(uint64_t pub_ip) { QQPubIP_ = pub_ip;}
    void updateStartTime(uint32_t time)
    {
        if (time < sessionStartTime_)
            sessionStartTime_ = time;
    }
    void setonly48(uint8_t status){
      if (!only48_) {
        return;
      }
      only48_ = status;
    }
private:
    uint64_t otherQQNum_;
    uint64_t QQPubIP_;
    uint8_t  only48_ = 1;
};

template<>
class WxcsSession<WxcsGroupHeadPerson> : public WxcsSessionBase<WxcsGroupHeadPerson>
{
public:
    using WxcsSessionBase<WxcsGroupHeadPerson>::WxcsSessionBase;

    std::string toStrRecordLine(char sep) const;

    void setGroupPictureName(const std::string& picture) {
        if (!picture.empty() && groupHeadPicture.empty()) {
            groupHeadPicture = picture;
        }
    }

    void setGroupPictureNameMd5(const std::string& md5) {
        if (!md5.empty() && groupHeadPictureMd5.empty()) {
            groupHeadPictureMd5 = md5;
        }
    }

    bool hasMd5() {
        return !groupHeadPictureMd5.empty();
    }

private:
    std::string groupHeadPicture;
    std::string groupHeadPictureMd5;
};


// WxcsSession for WxcsSkypePerson
template<>
class WxcsSession<WxcsSkypePerson> : public WxcsSessionBase<WxcsSkypePerson>
{
public:
    using WxcsSessionBase<WxcsSkypePerson>::WxcsSessionBase;

public:
    uint32_t getTotalSessionC2STransPackets() const
    {
        uint32_t sessionC2STransPacket = 0;

        for (auto kv : personMap_)
        {
            sessionC2STransPacket += kv.second->getC2STransPackets();
        }

        return sessionC2STransPacket;
    }

    bool wasDeadSession(int activeDiffMax) const
    {
        // 1 dpi 离线检测, 只要任意一个 DPI离线, 相关这个Session则超时掉
        for (auto &kv : personMap_)
        {
            if(time(NULL) - get_wxcs_timestamp(kv.second->getDPI_NodeID()) > activeDiffMax)
            {
                return true;
            }
        }

        // 2 isTimeout 加速
        bool all_timeout = true;
        for (auto &kv : personMap_)
        {
            if(1 != kv.second->isTimeout)
            {
                all_timeout = false;
                break;
            }
        }
        if(all_timeout)//如果全部已标记超时
        {
            return true;
        }

        // 3 Session 超时检测
        all_timeout = true;
        for (auto &kv : personMap_)
        {
            if(get_node_timestamp(kv.second->getDPI_NodeID()) - kv.second->getLastActiveTime() < activeDiffMax)
            {
                all_timeout = false;
                break;
            }
        }
        if(all_timeout)//如果所有Person已超时
        {
            return true;
        }

        return false;
    }
};
#endif /* _WXCS_SESSION_H_ */
