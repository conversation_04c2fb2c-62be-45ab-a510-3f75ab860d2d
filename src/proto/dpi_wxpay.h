/****************************************************************************************
 * 文 件 名 : dpi_wxpay.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/11/04
 修改: chenzq          2022/11/04
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#ifndef DPI_WXPAY_H
#define DPI_WXPAY_H

#include <stdint.h>

#include "dpi_detect.h"

enum wxpay_index_em{
  EM_WXPAY_TEID,
  EM_WXPAY_TAGTYPE,
  EM_WXPAY_MSISDN,
  EM_WXPAY_IMSI,
  EM_WXPAY_IMEI,
  EM_WXPAY_TAC,
  EM_WXPAY_OPERATOR,
  EM_WXPAY_DEVNAME,
  EM_WXPAY_AREA,
  EM_WXPAY_HW_BFLAGS,
  EM_WXPAY_HW_APN,
  EM_WXPAY_HW_NCODE,
  EM_WXPAY_HW_ECGI,
  EM_WXPAY_HW_LAC,
  EM_WXPAY_HW_SAC,
  EM_WXPAY_HW_CI,
  EM_WXPAY_RT_PLMN_ID,
  EM_WXPAY_RT_ULI,
  EM_WXPAY_RT_BS,
  EM_WXPAY_RT_DNS,
  EM_WXPAY_CAPDATE,
  EM_WXPAY_SRCIP,
  EM_WXPAY_DSTIP,
  EM_WXPAY_SRCPORT,
  EM_WXPAY_DSTPORT,
  EM_WXPAY_TYPE,
  EM_WXPAY_RESV0,
  EM_WXPAY_RESV1,
  EM_WXPAY_RESV2,
  EM_WXPAY_RESV3,
  EM_WXPAY_RESV4,
  EM_WXPAY_MAX
};


typedef struct
{
  ST_trailer trailer;
  struct sonwden sonwden;
  uint8_t type;
  uint32_t timestamp;
  uint32_t thread_id;
  uint8_t direction;

  char url[256];
  char host[64];
}WxpayInfo;


typedef struct
{
  char url[256];
  char host[64];
}WxpayHeader;

void init_wxpay();
int dissect_wxpay_from_http(struct flow_info *flow, int direction,
                            WxpayHeader *http_header);

typedef int (*RmFunc)(void *key, void *data, void *user_data);
typedef void (*FreeFunc)(void *data);
#ifdef __cplusplus
extern "C" {
#endif

void  wxpay_manager_init(RmFunc r_func, FreeFunc f_func);

void wxpay_manager_add(void *key, void *data);
void * wxpay_manager_find(void *key);

#ifdef __cplusplus
}
#endif

#endif