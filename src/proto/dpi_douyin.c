/****************************************************************************************
 * 文 件 名 : dpi_douyin.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/10/24
 修改: chenzq          2022/10/24
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/

#include "dpi_douyin.h"

#include <time.h>
#include <glib.h>

#include <rte_mempool.h>

#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_log.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table  dy_field_array[] = {
    DPI_FIELD_D(EM_DY_DATA,                    EM_F_TYPE_STRING,                 "data"),
};


static int write_dy_log(struct flow_info *flow, int direction, DyInfo *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    // struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_DY_MAX; i++)
    {
        switch(dy_field_array[i].index)
        {
        case EM_DY_DATA:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->data, strlen(info->data));
            break;
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    log_ptr->type        = TBL_LOG_DOUYIN;
    log_ptr->len         = idx;
    log_ptr->tid          = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


FILE * fp = NULL;
char path[64] = "/tmp/tbls/data.txt";

static
void debug_utf8_str(const uint8_t *data, const uint32_t data_len, DyUtf8Info *out)
{
  char path[64] = { 0 };
  char tmp_str[1024] = { 0 };
  char tmp_info[256] = { 0 };
  int i = 0;
  for (i = 0; i < out->num; ++i) {
    if (out->len[i] == 0 || out->offset[i] + out->len[i] > data_len)
      continue;
    snprintf(tmp_info, sizeof(tmp_info), "offset = %d, len = %d\n", out->offset[i], out->len[i]);
    fwrite(tmp_info, strlen(tmp_info), 1, fp);
    snprintf(tmp_str, out->len[i], "%s", data + out->offset[i]);
    fwrite(tmp_str, strlen(tmp_str), 1, fp);
  }
}

static
void debug_du_info(DyUtf8Info *o, DyUtf8Info *t)
{
  int i = 0;
  for (i = 0; i < o->num; ++i) {
    printf("begin parse json : index = %d, len = %d\n", o->offset[i], o->len[i]);
    printf("after parse json : index = %d, len = %d\n", t->offset[i], t->len[i]);
  }
}

static
int is_utf8_encode(const uint8_t *data, const uint32_t data_len, DyUtf8Info *out)
{
  uint32_t index = 0;
  uint32_t offset = 0;

  if (data == NULL || data_len == 0)
    return -1;

  const uint8_t *p = data;
  const uint8_t *str = NULL;
  char tmp_str[1024] = { 0 };
  uint32_t len = 0;

  // fwrite(split, strlen(split), 1, fp);
  while (index < data_len) {

    if(isprint(*p) > 0) {
      p++;
      if (str == NULL)
        str = p;
      len++;
      index++;

    } else if((0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) ) {
      p = p + 3;
      if (str == NULL)
        str = p;
      len += 3;
      index += 3;

    } else if((0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0))
            && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) ) {
      p = p + 4;
      if (str == NULL)
        str = p;
      len += 4;
      index += 4;
    } else {
        if (str != NULL) {
          // memcpy(tmp_str, data + offset, len);
          // // snprintf(tmp_str, sizeof(tmp_str), "%s", p);
          // char s_len[64] = { 0 };
          // snprintf(s_len, sizeof(s_len), "len = %d, index = %d, offset = %d\n", len, index, offset);
          // fwrite(s_len, strlen(s_len), 1, fp);
          // fwrite(tmp_str, strlen(tmp_str), 1, fp);
          // fwrite("\n", 1, 1, fp);
          // fflush(fp);
          if (len > DY_JSON_MIN_LEN) {
            out->len[out->num] = len;
            out->offset[out->num] = offset;
            out->num++;
          }

          offset = offset + len ;
          len = 0;
          str = NULL;
          // memset(tmp_str, 0, sizeof(tmp_str));
        }
        p++;
        index++;
        offset++;
    }
  }
  // fwrite(split, strlen(split), 1, fp);

  return index;
}


static
int get_json_str(const uint8_t *payload, uint32_t payload_len,
                 DyUtf8Info *in, DyUtf8Info *out)
{

  int i = 0, j = 0;
  int flag = -1;
  const uint8_t *p = NULL;
  GQueue *q_char = NULL;
  for (i = 0; i < in->num; ++i) {
    if (in->offset[i] + in->len[i] > payload_len)
      continue;

    p = payload + in->offset[i];
    q_char = g_queue_new();
    int begin_index = 0;
    int data_len = 0;

    for (uint32_t j = 0; j < in->len[i]; ++j,p++) {
      // guint q_len = g_queue_get_length(q_char);
      if (g_queue_is_empty(q_char) && *p == '{') {
        g_queue_push_head(q_char, (gpointer)&*p);
        begin_index = j;
        data_len = 1;
      } else if (!g_queue_is_empty(q_char)) {
        data_len++;
        char *c = (char *)g_queue_peek_head(q_char);
        if (*c == '{' && *p == '}') {
          g_queue_pop_head(q_char);
          if (g_queue_is_empty(q_char)) {
            out->len[i] = data_len;
            out->offset[i] = in->offset[i] + begin_index;
            // data_len = 0;
            // begin_index = 0;
            break;
          }
        } else if (*p == '{' || *p == '}') {
          g_queue_push_head(q_char, (gpointer)&*p);
        }
      }
   }

  g_queue_free(q_char);
 }

  return 0;
}


static
int dissect_dy_data(struct flow_info * flow, int direction,
                    const uint8_t *payload, const uint32_t payload_len)
{
  uint32_t offset = 0;
  int i = 0;
  DyInfo info;
  DyUtf8Info du_info;
  DyUtf8Info du_json_info;
  memset(&du_info, 0, sizeof(DyUtf8Info));
  memset(&du_json_info, 0, sizeof(DyUtf8Info));
  memset(&info, 0, sizeof(DyInfo));
  char find_flag[] = "ktv_sei";
  char tmp_str[512] = { 0 };
  int   flag = 0;

  is_utf8_encode(payload, payload_len, &du_info);
  du_json_info.num = du_info.num;
  // debug_utf8_str(payload, payload_len, &du_info);
  get_json_str(payload, payload_len, &du_info, &du_json_info);

  // debug_du_info(&du_info, &du_json_info);
  // debug_utf8_str(payload, payload_len, &du_json_info);


  for (i = 0; i < du_json_info.num; ++i) {
    if (du_json_info.len[i] == 0 ||
        du_json_info.len[i] + du_json_info.offset[i] > payload_len) {
      continue;
    }
    // snprintf(tmp_str, du_json_info.len[i], "%s", payload + du_json_info.offset[i]);
    flag = dpi_strstr(payload + du_json_info.offset[i], du_json_info.len[i], find_flag, strlen(find_flag));
    // flag = dpi_strstr(tmp_str, strlen(tmp_str), find_flag, strlen(find_flag));
    if (flag < 0)
      continue;
    // snprintf(info.data + strlen(info.data), sizeof(info.data), "%s;", tmp_str);
    snprintf(info.data + info.data_len, du_json_info.len[i] + 1, "%s;", payload + du_json_info.offset[i]);
    info.data_len += du_json_info.len[i];
  }

  printf("len = %d\n", info.data_len);
  printf("%s\n", info.data);
  if (strlen(info.data) == 0) {
    return 0;
  }

  write_dy_log(flow, direction, &info);
  return 0;
}


int dissect_dy_from_rtp(struct flow_info *flow,
                        int direction, uint32_t seq,
                        const uint8_t *payload,
                        const uint32_t payload_len,
                        uint8_t flag)
{
  uint8_t   flag_0 = 0;
  uint32_t  flag_1 = 0;
  uint16_t  flag_2 = 0;
  uint32_t  flag_3 = 0;
  uint32_t  offset = 0;
  struct dpi_pkt_st pkt;
  struct sonwden sonw;
  int       c2s = 0;
  if (payload == NULL || payload_len <  DY_RTP_PAYLOAD_LEN_MIN)
    return 0;

  pkt.payload = payload;
  pkt.payload_len = payload_len;

  dpi_get_uint8(&pkt, offset, &flag_0);

  dpi_get_be24(&pkt, offset + 12, &flag_1);

  if (flag_0 != DY_RTP_FLAG_0 || flag_1 != DY_RTP_FLAG_1)
    return 0;

  get_ip_port(&flow->pkt, &sonw, &c2s);

  if (sonw.server_port == DY_RTP_MORE_PORT_0 ||
      sonw.server_port == DY_RTP_MORE_PORT_1 ) {
    dpi_get_be16(&pkt, offset + 16, &flag_2);
    dpi_get_be24(&pkt, offset + 20, &flag_3);

    if (flag_2 != DY_RTP_FLAG_2 || flag_3 != DY_RTP_FLAG_3)
      return 0;
    dissect_dy_data(flow, direction, payload, payload_len);
  } else {
    dissect_dy_data(flow, direction, payload, payload_len);
  }

  return 0;
}


int dissect_dy_from_http(struct flow_info *flow, int direction,
                        const uint8_t *payload, const uint32_t payload_len)
{

  char ident[] = "ktv_sei";

  if (dpi_strstr(payload, payload_len, ident, strlen(ident)) <= 0)
    return 0;

  dissect_dy_data(flow, direction, payload, payload_len);

  return 0;
}


static
int dissect_douyin(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
    const uint32_t payload_len, uint8_t flag)
{
  if(g_config.protocol_switch[PROTOCOL_DOUYIN] == 0) {
      return 0;
  }


  dissect_dy_data(flow, direction, payload, payload_len);

  return 0;
}


static
void identify_douyin(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{

  if(g_config.protocol_switch[PROTOCOL_DOUYIN] == 0) {
      return;
  }

  struct dpi_pkt_st pkt;
  uint32_t  flag_0 = 0;
  uint64_t  flag_1 = 0;


  pkt.payload = payload;
  pkt.payload_len = payload_len;



  dpi_get_be32(&pkt, 4, &flag_0);

  if (flag_0 != DY_FLAG_0_0 && flag_0 != DY_FLAG_0_1)
    return;

  dpi_get_be48(&pkt, 14, &flag_1);
  if (flag_1 == DY_FLAG_1) {
    flow->real_protocol_id = PROTOCOL_DOUYIN;
  }

  return;
}


void init_douyin_dissector(void)
{
  // fp = fopen(path, "w+");
    // int       i = 0;
    // int       j = 0;
    // int       count = 0;
    // char      tmp_buff[MAX_FIELD_LEN]={0};
    // char      *tmp=NULL;

    write_proto_field_tab(dy_field_array, EM_DY_MAX, "douyin");
    // write_weixn_field_txt(weixin_array_f,"weixin_pyq");

    // init_wx_relation_dissector();

    port_add_proto_head(IPPROTO_UDP, DY_PORT_0, PROTOCOL_DOUYIN);
    // port_add_proto_head(IPPROTO_TCP, WEIXIN_PORT_1, PROTOCOL_WEIXIN);
    // port_add_proto_head(IPPROTO_TCP, WEIXIN_PORT_2, PROTOCOL_WEIXIN);

    udp_detection_array[PROTOCOL_DOUYIN].proto         = PROTOCOL_DOUYIN;
    udp_detection_array[PROTOCOL_DOUYIN].identify_func = identify_douyin;
    udp_detection_array[PROTOCOL_DOUYIN].dissect_func  = dissect_douyin;

    // DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN].excluded_protocol_bitmask, PROTOCOL_WEIXIN);


    return;
}



static __attribute((constructor)) void     before_init_rtp(void){
    register_tbl_array(TBL_LOG_DOUYIN, 0, "douyin", init_douyin_dissector);
}

