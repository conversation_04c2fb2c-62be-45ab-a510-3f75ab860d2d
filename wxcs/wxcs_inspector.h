/****************************************************************************************
 * 文 件 名 : wxcs_inspector.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 : inspector
 * 功    能 : 以 http 接口对外提供程序内部状态查询功能
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019-09-06
* 编    码 : zhengsw      '2019-09-06
* 修    改 : 
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_INSPECTOR_H_
#define _WXCS_INSPECTOR_H_

#include <muduo/base/Types.h>
#include <muduo/net/http/HttpRequest.h>
#include <muduo/net/inspect/Inspector.h>

#include "wxcs_server.h"

class WxcsInspector
{
public:
    WxcsInspector(muduo::net::EventLoop* loop, const muduo::net::InetAddress& httpAddr, WxcsServer *pServer);
    ~WxcsInspector();
    
public:
    muduo::string queryQQByMsisdn(muduo::net::HttpRequest::Method, const muduo::net::Inspector::ArgList &);
    muduo::string queryQQListByMsisdn(HttpRequest::Method, const Inspector::ArgList &args);
    muduo::string queryAllMsisdnQQ(HttpRequest::Method , const Inspector::ArgList& args);

private:
    void RegisterCommands(muduo::net::Inspector* ins);
    
private:
    muduo::net::Inspector ins_;
    WxcsServer *pServer_;
};

#endif /* _WXCS_INSPECTOR_H_ */
