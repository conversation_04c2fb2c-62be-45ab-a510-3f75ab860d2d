/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_LOG_H
#define WX_PROXY_LOG_H

#include "glib.h"

#define what(boolean, action, fmt, ...) if(boolean){log_write(ERROR, fmt, ##__VA_ARGS__); action;}

enum
{
    LOG_MIN,
    DEBUG   ,
    INFO    ,
    WARN ,
    ERROR   ,
    STOP   ,
    LOG_MAX,
} LOG_LEVEL;

#ifdef __cplusplus
extern "C" {
#endif

int
log_init(int level, const char *filepath, int max_size, int max_num);

void
log_exit(void);

int
log_write(int level, const char *format, ...);

#ifdef __cplusplus
}
#endif

#endif

