/****************************************************************************************
 * 文 件 名 : tbl_logger.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#pragma once

#include <memory>
#include <string>
#include <atomic>
#include <map>
#include <spdlog/spdlog.h>

namespace spdlog
{
    class logger;
}

class TblLogger
{
public:
    static void createLogger(bool bLogToFile);

    static std::shared_ptr<spdlog::logger> defaultLogger();

    static std::shared_ptr<spdlog::logger> tblLogger();
};

#define LOG_DEF   TblLogger::defaultLogger()
#define LOG_TBL   TblLogger::tblLogger()

