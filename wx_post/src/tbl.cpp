#include "tbl_config.h"
#include "tbl_reader.h"
#include "tbl_utils.h"
#include "tbl_writer.h"
#include "tbl_scanner.h"
#include "tbl_worker.h"
#include "tbl_logger.h"
#include "tbl_value.h"
#include "tbl_manager.h"
#include "tbl_statistics.h"

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>

/* Parse the argument given in the command line of the application */


std::map<std::string, TblWorker *> workers;

void finishWork(int sig)
{
    CFG.setWorkMode(0);
    
    //exit(0);
}

int main(int argc, char *argv[])
{
    int lSts = 0;

    lSts = CFG.ParseCmdLineOpts(argc, argv);
    if (lSts < 0 )               // parse 出错
    {   
        return lSts;
    }
    std::vector<std::thread> thread_manager;

    // init config
    CFG.init();
    // register signal handler
    signal(SIGINT, finishWork);
    // set logger
    TblLogger::createLogger( CFG.getLoggerOut());
    // set Value and Special Value
    TblFieldValue::init();

    LOG_ST.init();

    LOG_ST.startWork();

    TblReader tbl_reader;
    
    for( uint16_t i = 0; i < CFG.getWorkThreadNum(); i ++ )
    {
        LOG_DEF->info("{} thread init.", i);
        
        thread_manager.emplace_back([&tbl_reader](){
            
            TblWorkerManager tbl_worker_manager;

            while (CFG.getWorkMode() > 0)
            {
                tbl_reader.work(&tbl_worker_manager);
            }
            
        });
    }

    TblScanner::getInstance().init();
    
    LOG_DEF->info("main work mode:{}",CFG.getWorkMode());

    LOG_ST.finishWork();
 
    TblScanner::getInstance().work(); 

    for(auto & it:thread_manager)
    {
        if(it.joinable())
        {
            it.join();
        }
    }

    TblFieldValue::destory();

    LOG_DEF->info("TblClassicification exiting!");
}