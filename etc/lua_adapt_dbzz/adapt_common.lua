function adapt_common(to, from)
    to["TAGTYPE"] = from["TAGTYPE"]
    to["TEID"] = from["TEID"]
    to["MSISDN"] = from["MSISDN"]
    to["IMSI"] = from["IMSI"]
    to["IMEI"] = from["IMEI"]
    to["TAC"] = from["TAC"]
    to["OPERATOR"] = from["OPERATOR"]
    to["DEVNAME"] = from["DEVNAME"]
    to["AREA"] = from["AREA"]
    to["HW_BFALGS"] = from["HW_BFALGS"]
    to["HW_APN"] = from["HW_APN"]
    to["HW_NCODE"] = from["HW_NCODE"]
    to["HW_ECGI"] = from["HW_ECGI"]
    to["HW_LAC"] = from["HW_LAC"]
    to["HW_SAC"] = from["HW_SAC"]
    to["HW_CI"] = from["HW_CI"]
    to["RT_PLMN_ID"] = from["RT_PLMN_ID"]
    to["RT_ULI"] = from["RT_ULI"]
    to["RT_BS"] = from["RT_BS"]
    to["RT_DNS"] = from["RT_DNS"]
    to["DevNo"] = from["DevNo"]
    to["LineNo"] = from["LineNo"]
    to["LinkLayerType"] = from["LinkLayerType"]
    to["isIPv6"] = from["isIPv6"]
    to["isMPLS"] = from["isMPLS"]
    to["nLabel"] = from["nLabel"]
    to["innerLabel"] = from["innerLabel"]
    to["otherLabel"] = from["otherLabel"]
    to["resv1"] = from["resv1"]
    to["resv2"] = from["resv2"]
    to["resv3"] = from["resv3"]
    to["resv4"] = from["resv4"]
    to["resv5"] = from["resv5"]
    to["resv6"] = from["resv6"]
    to["resv7"] = from["resv7"]
    to["resv8"] = from["resv8"]
    to["CapDate"] = from["CapDate"]
    to["SrcIp"] = from["SrcIp"]
    to["SrcCountry"] = from["SrcCountry"]
    to["SrcArea"] = from["SrcArea"]
    to["SrcCity"] = from["SrcCity"]
    to["SrcCarrier"] = from["SrcCarrier"]
    to["DstIp"] = from["DstIp"]
    to["DstCountry"] = from["DstCountry"]
    to["DstArea"] = from["DstArea"]
    to["DstCity"] = from["DstCity"]
    to["DstCarrier"] = from["DstCarrier"]
    to["SrcPort"] = from["SrcPort"]
    to["DstPort"] = from["DstPort"]
    to["C2S"] = from["C2S"]
    to["Proto"] = from["Proto"]
    to["TTL"] = from["TTL"]
	  to["IPFlag"] = from["IPFlag"]
end
yalua_register("common", "common", adapt_common)
