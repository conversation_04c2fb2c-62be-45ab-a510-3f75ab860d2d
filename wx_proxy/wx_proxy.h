/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_H
#define WX_PROXY_H

struct context
{
    int                cpus;
    int                init;
    struct event_base *master_base;

    GArray            *thread_manager;

    int                work_thread_num;
    int                connection_timeout;
    int                connect_auto_reconn;
    void*            (*create)(void*p);
    struct event      *master_timer;
    struct timeval     master_tv;

    const char        *conf_file_path;
    struct conf        conf;

    struct event      *sig_SIGINT;
    struct event      *sig_SIGQUIT;
    struct event      *sig_SIGTSTP;

    struct log        *log;

};

#endif
