#include "dpi_http.h"
#include "dpi_tbl_log.h"
#include <rte_mbuf.h>

#include "dpi_wx_msg.h"
#include "dpi_weixin.h"
#include "dpi_common.h"
#include "dpi_douyin.h"
#include "dpi_wxpay.h"
static wxc_handle h_handle = NULL;
static wxc_handle p_handle = NULL;
static wxc_handle f_handle = NULL;

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;
extern struct rte_mempool *tbl_log_record_mempool;
extern dpi_field_table  http_field_array[EM_HTTP_MAX];

static void _find_hash_write_log_delete(struct http_request_info *line_info, const char *header_name, struct tbl_log *log_ptr, int *idx)
{
    gpointer _value;
    struct header_value *value;

    if(line_info->table==NULL){
         write_n_empty_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, 1);
         return ;
    }

    _value = g_hash_table_lookup(line_info->table, header_name);
    value = (struct header_value *)_value;
    if (!value)
        write_n_empty_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, 1);
    else
        write_one_str_reconds(log_ptr->log_content, idx, TBL_LOG_MAX_LEN, (const char *)value->ptr, value->len);
    g_hash_table_remove(line_info->table, header_name);
}

static void _find_hash_write_record_delete(struct http_request_info *line_info, const char *header_name, const char *field_name, precord_t* record)
{
    gpointer _value;
    struct header_value *value;
    ya_fvalue_t *new_value = NULL;

    if(line_info->table==NULL){
         return ;
    }

    _value = g_hash_table_lookup(line_info->table, header_name);
    value = (struct header_value *)_value;
    if (value){
     gpointer _value=NULL;
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)value->ptr, value->len);
    dpi_precord_fvalue_put_by_name(record, field_name, new_value);
    }
    g_hash_table_remove(line_info->table, header_name);
}

static wxc_handle init_session_handle(char *ip_addr, uint16_t port, wxc_handle handle)
{
    uint16_t u_port    = g_config.wx_voice_port;
    char*    u_ip_addr = g_config.wx_voice_ip;

    if (NULL == handle)
    {
        wxc_init(&handle, u_ip_addr, u_port);
    }

    return handle;
}


static
void init_session_group_handle(char *ip_addr, uint16_t port)
{
    h_handle = init_session_handle(ip_addr, port, h_handle);
}


static
void init_session_position_handle(char *ip_addr, uint16_t port)
{
    p_handle = init_session_handle(ip_addr, port, p_handle);
}


static
void init_session_QQ_file_handle(char *ip_addr, uint16_t port)
{
    f_handle = init_session_handle(ip_addr, port, f_handle);
}


static
void  print_session(ST_wxGroupHead* p)
{
    if(0 == g_config.debug_group_head)
    {
        return;
    }

    printf("phone_num      :%lu\n", p->trailer.MSISDN);
    printf("Host           :%s\n", p->PersonHost);
    printf("URL            :%s\n", p->PersonURL);
    printf("Uin            :%s\n", p->PersonUin);
    printf("Ref            :%s\n", p->PersonRef);
    printf("Agent          :%s\n", p->PersonAgent);
    printf("AcceptEncoding :%s\n", p->PersonAcceptEncoding);
    printf("AcceptLanguag  :%s\n", p->PersonAcceptLanguage);
    printf("SrcPort        :%u\n", p->PersonSrcPort);
    printf("DstPort        :%u\n", p->PersonDstPort);

    printf("src_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->PersonSrcIp)+0),
            *((uint8_t*)(&p->PersonSrcIp)+1),
            *((uint8_t*)(&p->PersonSrcIp)+2),
            *((uint8_t*)(&p->PersonSrcIp)+3));

    printf("dst_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->PersonDstIp)+0),
            *((uint8_t*)(&p->PersonDstIp)+1),
            *((uint8_t*)(&p->PersonDstIp)+2),
            *((uint8_t*)(&p->PersonDstIp)+3));

    printf("LastActiveTim  :%d\n", p->PersonLastActiveTime);
    printf("\n");
}

void write_wx_http_post_tbl(struct flow_info *flow, int direction,const uint8_t *payload, const uint32_t payload_len,struct http_request_info *line_info)
{
      if (g_config.protocol_switch[PROTOCOL_WEIXIN_HTTP_POST] == 0)
    {
        return ;
    }
    if(NULL == h_handle || NULL == line_info || NULL == flow ||payload ==NULL ||payload_len < 16)
    {
        return;
    }

    struct http_session *session = (struct http_session *)flow->app_session;
    /* 是否为 cgi-bin */
    const char     *Prefix = "/cgi-bin/";
    size_t          Prefix_len = strlen(Prefix);
    struct tbl_log_record *log_ptr;

    if (Prefix_len > line_info->uri_val_len) {
        return;  // 如果不是微信 则退出
    }

    if (0 != memcmp(line_info->PROTOCOL_VAL_PTR(uri), Prefix, strlen(Prefix))) {
        return;
    }

    uint32_t wxnum = 0;
    if (payload[0] != 0xbf && ((payload[2] & 0x0f) != 0x0f || (payload[2] & 0x0f) != 0x00))
        return;
    wxnum = get_uint32_ntohl(payload,7);
    if (rte_mempool_get(tbl_log_record_mempool, (void **)&log_ptr) < 0) {
      DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_record_mempool");
      return ;
    }
    precord_t *record = ya_create_record("wx_http_post");
    write_tbl_log_common_by_record(flow, direction, record);
    log_ptr->record             = record;
    log_ptr->thread_id          = flow->thread_id;
    precord_layer_move_cursor(record, "wx_http_post");
    ya_fvalue_t *fvalue;
    if (line_info->uri_val_ptr) {
        fvalue = ya_fvalue_new_stringn(YA_FT_STRING, (char *)line_info->uri_val_ptr, line_info->uri_val_len);
        dpi_precord_fvalue_put_by_name(record, "URI", fvalue);
    }
    fvalue = ya_fvalue_new_stringn(YA_FT_STRING, session->host, strlen(session->host));
    dpi_precord_fvalue_put_by_name(record, "Host", fvalue);

    if (line_info->table != NULL) {
        gpointer             _value;
        struct header_value *value;
        _value = g_hash_table_lookup(line_info->table, "user-agent");
        value = (struct header_value *)_value;
        if (value) {
          int  i = 0, j = 0;
          char tmp[value->len * 2 + 1];
          memset(tmp, 0, value->len * 2 + 1);
          for (i = 0; i < value->len && j < value->len * 2 + 1; i++) {
            if (!isprint(value->ptr[i])) {
              sprintf(tmp + j, "%2x", value->ptr[i]);
              j++;
            } else
              tmp[j] = value->ptr[i];
            j++;
          }
          fvalue = ya_fvalue_new_stringn(YA_FT_STRING, tmp, strlen(tmp));
          dpi_precord_fvalue_put_by_name(record, "User-Agent", fvalue);
        }
    }

    fvalue = ya_fvalue_new_uinteger(YA_FT_UINT32, wxnum);
    dpi_precord_fvalue_put_by_name(record, "wxnum", fvalue);
    if(tbl_record_log_enqueue(log_ptr) != 1) {
        ya_destroy_record(record);
        rte_mempool_put(tbl_log_record_mempool, (void *)log_ptr);
    }
    record = NULL;
}
static
void send_group_head(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_GROUP_HEAD] == 0)
    {
        return ;
    }

    if(NULL == h_handle || NULL == line_info || NULL == flow)
    {
        return;
    }

    struct http_session *session = (struct http_session *)flow->app_session;
    /* 是否为 微信 群图像 */
    const char* Prefix = "/mmcrhead/";
    size_t  Prefix_len = strlen(Prefix);

    if(Prefix_len > line_info->uri_val_len)
    {
        return; // 如果不是微信 则退出
    }

    if(0 != memcmp(line_info->PROTOCOL_VAL_PTR(uri), Prefix, strlen(Prefix)))
    {
        return ;
    }

    /* 数据整合 */
    ST_wxGroupHead aGroupHeadPerson;
    memset(&aGroupHeadPerson, 0, sizeof(ST_wxGroupHead));

    //设置建联 相关 参数
    dpi_TrailerParser(&aGroupHeadPerson.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
    dpi_TrailerGetMAC(&aGroupHeadPerson.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&aGroupHeadPerson.trailer, (const char *)flow->ethhdr);
    dpi_TrailerSetDev(&aGroupHeadPerson.trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&aGroupHeadPerson.trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&aGroupHeadPerson.trailer, g_config.devArea);       // 地域名

    /* Get HTTP Head */
    typedef struct
    {
        const char*k;
        char*      v;
        int        s;
    } ST_kv;

    ST_kv WX_GroupHead[] = {
        {"host",            aGroupHeadPerson.PersonHost          ,sizeof(aGroupHeadPerson.PersonHost          )},
        {"user-agent",      aGroupHeadPerson.PersonAgent         ,sizeof(aGroupHeadPerson.PersonAgent         )},
        {"referer",         aGroupHeadPerson.PersonRef           ,sizeof(aGroupHeadPerson.PersonRef           )},
        {"accept-language", aGroupHeadPerson.PersonAcceptLanguage,sizeof(aGroupHeadPerson.PersonAcceptLanguage)},
        {"accept-encoding", aGroupHeadPerson.PersonAcceptEncoding,sizeof(aGroupHeadPerson.PersonAcceptEncoding)},
        {NULL, NULL, 0},
    };

    for(int i = 0; NULL != WX_GroupHead[i].k; i++)
    {
        struct header_value* value = (struct header_value*)g_hash_table_lookup(line_info->table, WX_GroupHead[i].k);
        if(NULL != value)
        {
            int safelen = value->len < WX_GroupHead[i].s - 1 ? value->len : WX_GroupHead[i].s - 1;
            memcpy(WX_GroupHead[i].v, value->ptr, safelen);
        }
    }


    char  uri_buff[2048] = {0};
    char *pleft  = NULL;
    char *pright = NULL;

    /* Get Uri , 检测是不是UTF8编码*/
    if(line_info->uri_val_len != (size_t)isUTF8((const char*)line_info->uri_val_ptr, line_info->uri_val_len))
    {
        return;
    }

    /* 将 Host 与 URI, 合成 URL */
    memcpy(aGroupHeadPerson.PersonHost, session->host, strlen(session->host));
    memcpy(uri_buff, line_info->uri_val_ptr, line_info->uri_val_len);
    snprintf(aGroupHeadPerson.PersonURL, sizeof(aGroupHeadPerson.PersonURL), "http://%s%s", aGroupHeadPerson.PersonHost, uri_buff);

    /* 去除IOS Android WinPC 平台差异导致的 URL 后缀不一样, 截取URL中一样的部分 */
    char* psplit = g_strrstr(aGroupHeadPerson.PersonURL, "/0");
    if(NULL == psplit)
    {
        return; //URL中没有 "/0", 不符合群图像URL规则
    }
    *(psplit+2) = '\0'; //将URL中出现的 '/0?tp=wxpic'从'?'位置截断!

    /* Get Uin */
    if( 0!= get_uint32_ntohl(aGroupHeadPerson.PersonRef, 0)) // 如果 ref 不为空
    {
        pleft  = aGroupHeadPerson.PersonRef;
        // 有 \0 结尾的, 可以使用strstr
        if(NULL != (pleft  = strstr(pleft, "uin=")) && NULL != (pright = strstr(pleft+1, "&")))
        {
            pleft += 4;
            memcpy(aGroupHeadPerson.PersonUin, pleft, pright-pleft);
        }
    }

    int C2S = 0;
    Snowden snowden;

    get_ip_port(&flow->pkt, &snowden, &C2S);
    aGroupHeadPerson.PersonDstPort        = ntohs(flow->tuple.inner.port_dst);
    aGroupHeadPerson.PersonSrcPort        = ntohs(flow->tuple.inner.port_src);
    aGroupHeadPerson.PersonSrcIp          = flow->tuple.inner.ip_src.ip4;
    aGroupHeadPerson.PersonDstIp          = flow->tuple.inner.ip_dst.ip4;
    aGroupHeadPerson.ip_version          = snowden.ip_ver;
    memcpy(aGroupHeadPerson.client_ip.ipv6,  snowden.client_ip.ipv6, 16);
    memcpy(aGroupHeadPerson.server_ip.ipv6,  snowden.server_ip.ipv6, 16);
    aGroupHeadPerson.PersonLastActiveTime = time(NULL);

    if (session) {
        snprintf(session->wx_group_head_last_status.session_url, sizeof(session->wx_group_head_last_status.session_url), "%s", aGroupHeadPerson.PersonURL);
        // 获取图片名
        snprintf(aGroupHeadPerson.GroupHeadPictureName, sizeof(aGroupHeadPerson.GroupHeadPictureName), "%s", session->wx_group_head_last_status.pic_uri);
    }

    //* 发送*/
    dpi_TrailerUpdateTS(&aGroupHeadPerson.trailer);
    wxc_sendMsg(h_handle, (const unsigned char*)&aGroupHeadPerson, sizeof(aGroupHeadPerson), WXCS_GROUP_HEAD);
    print_session(&aGroupHeadPerson);

    session->has_sub = 1;
}


static
int isHex(char c)
{
    if(isdigit(c))
    {
        return 1;
    }

    switch (c)
    {
        case 'A':
        case 'B':
        case 'C':
        case 'D':
        case 'E':
        case 'F':

        case 'a':
        case 'b':
        case 'c':
        case 'd':
        case 'e':
        case 'f':
        return 1;

        default:
        return 0;
    }
    return 0;
}


static
int getFilecode(const char*p, int len, char*fielcode, int size)
{
#define FILE_CODE_LENGTH 36
    char FileCode[FILE_CODE_LENGTH];
    int  FileCodeIndex = 0;
    int index = 0;

    while(index < len)
    {
        if('-' == p[index] || isHex(p[index]))
        {
            FileCode[FileCodeIndex] = p[index];
            FileCodeIndex++;
            index++;
        }
        else
        {
            FileCodeIndex=0;
            index++;
        }
        if(FILE_CODE_LENGTH == FileCodeIndex)
        {
            memcpy(fielcode, FileCode, FileCodeIndex);
            fielcode[FileCodeIndex] = '\0';
            return 0;
        }
    }

    return -1;
}


static int get_wx_uin(const char*str, int len, char*out, int size)
{
    int              safe   = 0   ;
    const char      *p      = NULL;
    const char      *find   = NULL;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"&", 1},
        {";", 1},
        {" ", 1},
        {"=", 1},
        {NULL,0},
    };

    if(NULL == str || len <= 0)
    {
        return 0;
    }

    if(0 == *(const uint32_t*)str)
    {
        return 0;
    }

    find  = strstr(str, "uin=");
    if(NULL == find)
    {
        return 0;
    }
    len -= (str - find);

    p      = (const char*)find + 4;
    len   -= 4;

    for(int i = 0; 0 != magic[i].len && len > magic[i].len; i++)
    {
        find = memmem(p, len, magic[i].prefix,  magic[i].len);
        if(find)
        {
            len = (int)(find - p) < size ? (int)(find - p) : size-1;
            memcpy(out, p, len);
            out[len] = '\0';
            return 0;
        }
    }
    return 0;
}


static int write_http_wxph_log(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    char                 uin[32]  = {0};
    int                  idx      =  0 ;
    int                  i        =  0 ;
    struct tbl_log      *log_ptr  = NULL;
    struct http_session *session  = (struct http_session *)flow->app_session;
    struct header_value *refer    = NULL;

    if(line_info->table)
    {
        refer = (struct header_value *)g_hash_table_lookup(line_info->table, "referer");
        if(refer)
        {
            get_wx_uin((const char*)refer->ptr, refer->len, uin, sizeof(uin));
        }
    }

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_HTTP_H_X_NUMBER+1;i++)
    {
        switch(http_field_array[i].index)
        {
        case EM_HTTP_METHOD:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
            break;
        case EM_HTTP_URI:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            break;
        case EM_HTTP_VERSION:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
            break;
        case EM_HTTP_STATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
            break;
        case EM_HTTP_RESPONSESTATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
            break;
        case EM_HTTP_CACHE_CONTROL:
            _find_hash_write_log_delete(line_info, "cache-control", log_ptr, &idx);
            break;
        case EM_HTTP_CONNECTION:
            _find_hash_write_log_delete(line_info, "connection", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE:
            _find_hash_write_log_delete(line_info, "cookie", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE2:
            _find_hash_write_log_delete(line_info, "cookie2", log_ptr, &idx);
            break;
        case EM_HTTP_DATE:
            _find_hash_write_log_delete(line_info, "date", log_ptr, &idx);
            break;
        case EM_HTTP_PRAGMA:
            _find_hash_write_log_delete(line_info, "pragma", log_ptr, &idx);
            break;
        case EM_HTTP_TRAILER:
            _find_hash_write_log_delete(line_info, "trailer", log_ptr, &idx);
            break;
        case EM_HTTP_TRANSFER_ENCODING:
            _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_UPGRADE:
            _find_hash_write_log_delete(line_info, "upgrade", log_ptr, &idx);
            break;
        case EM_HTTP_VIA:
            _find_hash_write_log_delete(line_info, "via", log_ptr, &idx);
            break;
        case EM_HTTP_WARNING:
            _find_hash_write_log_delete(line_info, "warning", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT:
            _find_hash_write_log_delete(line_info, "accept", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_CHARSET:
            _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_ENCODING:
            _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "accept-language", log_ptr, &idx);
            break;
        case EM_HTTP_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "authorization", log_ptr, &idx);
            break;
        case EM_HTTP_EXPECT:
            _find_hash_write_log_delete(line_info, "expect", log_ptr, &idx);
            break;
        case EM_HTTP_FROM:
            _find_hash_write_log_delete(line_info, "from", log_ptr, &idx);
            break;
        case EM_HTTP_HOST:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            // if (direction == FLOW_DIR_SRC2DST)
            //     _find_hash_write_log_delete(line_info, "host", log_ptr, &idx);
            // else if (session){
            //     g_hash_table_remove(line_info->table, "host");
            //     write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            // } else {
            //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            // }
            break;
        case EM_HTTP_IF_MATCH:
            _find_hash_write_log_delete(line_info, "if-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_MODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, &idx);
            break;
        case EM_HTTP_IF_NONE_MATCH:
            _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_RANGE:
            _find_hash_write_log_delete(line_info, "if-range", log_ptr, &idx);
            break;
        case EM_HTTP_IF_UNMODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, &idx);
            break;
        case EM_HTTP_MAX_FORWARDS:
            _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, &idx);
            break;
        case EM_HTTP_RANGE:
            _find_hash_write_log_delete(line_info, "range", log_ptr, &idx);
            break;
        case EM_HTTP_REFERER:
            _find_hash_write_log_delete(line_info, "referer", log_ptr, &idx);
            break;
        case EM_HTTP_TE:
            _find_hash_write_log_delete(line_info, "te", log_ptr, &idx);
            break;
        case EM_HTTP_USER_AGENT:
            _find_hash_write_log_delete(line_info, "user-agent", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_RANGES:
            _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, &idx);
            break;
        case EM_HTTP_AGE:
            _find_hash_write_log_delete(line_info, "age", log_ptr, &idx);
            break;
        case EM_HTTP_ETAG:
            _find_hash_write_log_delete(line_info, "etag", log_ptr, &idx);
            break;
        case EM_HTTP_LOCATION:
            _find_hash_write_log_delete(line_info, "location", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_RETRY_AFTER:
            _find_hash_write_log_delete(line_info, "retry-after", log_ptr, &idx);
            break;
        case EM_HTTP_SERVER:
            _find_hash_write_log_delete(line_info, "server", log_ptr, &idx);
            break;
        case EM_HTTP_VARY:
            _find_hash_write_log_delete(line_info, "vary", log_ptr, &idx);
            break;
        case EM_HTTP_WWW_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_ALLOW:
            _find_hash_write_log_delete(line_info, "allow", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_ENCODING:
            _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "content-language", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LENGTH:
            _find_hash_write_log_delete(line_info, "content-length", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LOCATION:
            _find_hash_write_log_delete(line_info, "content-location", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_MD5:
            _find_hash_write_log_delete(line_info, "content-md5", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_RANGE:
            _find_hash_write_log_delete(line_info, "content-range", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_TYPE:
            _find_hash_write_log_delete(line_info, "content-type", log_ptr, &idx);
            break;
        case EM_HTTP_EXPIRES:
            _find_hash_write_log_delete(line_info, "expires", log_ptr, &idx);
            break;
        case EM_HTTP_LAST_MODIFIED:
            _find_hash_write_log_delete(line_info, "last-modified", log_ptr, &idx);
            break;
        case EM_HTTP_X_FORWARDED_FOR:
            _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE:
            _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE2:
            _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, &idx);
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }

    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 2);                //K00,V00
    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, uin, strlen(uin)); //K01
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);                //V01
    //K02,V02 --- K50,V50 NULL
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (HTTP_UNKNOWN_LINE_NUM_MAX-1) * 2);
    // write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (100-2)*2);        //K02,V02 ~ K99,V99

    log_ptr->type        = TBL_LOG_HTTP_WXPH;
    log_ptr->len         = idx;

    write_http_log_v51(flow, direction, line_info, log_ptr);

    if (tbl_log_enqueue(log_ptr) != 1) {
        if (log_ptr->content_len > 0 && tbl_log_content_mempool_256k) {
            rte_mempool_put(tbl_log_content_mempool_256k, (void *)log_ptr->content_ptr);
        }

        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

// 如果 HOST_URI 符合 WX 规则, 输出 TBL
static int write_http_wx_tbl(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP_WXPH] == 0)
    {
        return -1;
    }
    struct header_value* host = NULL;
    struct http_session*session = (struct http_session *)flow->app_session;
    if (g_config.http_wx_host == 1) {
        //是否存在 Host
        struct header_value* host = (struct header_value*)g_hash_table_lookup(line_info->table, "host");
        if(NULL == host) {
            return -1;
        }
    }


    //是否存在 URI
    if( (line_info->uri_val_len <=0)  || NULL == line_info->uri_val_ptr)
    {
        return -1;
    }

    //匹配
    for(int i = 0; (0 != g_config.http_wx[i].host[0] && 0 != g_config.http_wx[i].uri[0]); i++)
    {
        if(line_info->uri_val_len < strlen(g_config.http_wx[i].uri))
        {
            continue;
        }



        if(0 != memcmp(line_info->uri_val_ptr, g_config.http_wx[i].uri,  strlen(g_config.http_wx[i].uri)))
        {
            continue;
        }

        if (g_config.http_wx_host == 1) {
            if(host->len < strlen(g_config.http_wx[i].host)) {
                continue;
            }
            if(0 != memcmp(host->ptr, g_config.http_wx[i].host, strlen(g_config.http_wx[i].host))) {
                continue;
            }

        }

        session->has_sub = 1;
        write_http_wxph_log(flow, direction, line_info);
        return 0;
    }

    return -1;
}

static int write_http_qq_log(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_HTTP_H_X_NUMBER+1;i++)
    {
        switch(http_field_array[i].index)
        {
        case EM_HTTP_METHOD:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
            break;
        case EM_HTTP_URI:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            break;
        case EM_HTTP_VERSION:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
            break;
        case EM_HTTP_STATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
            break;
        case EM_HTTP_RESPONSESTATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
            break;
        case EM_HTTP_CACHE_CONTROL:
            _find_hash_write_log_delete(line_info, "cache-control", log_ptr, &idx);
            break;
        case EM_HTTP_CONNECTION:
            _find_hash_write_log_delete(line_info, "connection", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE:
            _find_hash_write_log_delete(line_info, "cookie", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE2:
            _find_hash_write_log_delete(line_info, "cookie2", log_ptr, &idx);
            break;
        case EM_HTTP_DATE:
            _find_hash_write_log_delete(line_info, "date", log_ptr, &idx);
            break;
        case EM_HTTP_PRAGMA:
            _find_hash_write_log_delete(line_info, "pragma", log_ptr, &idx);
            break;
        case EM_HTTP_TRAILER:
            _find_hash_write_log_delete(line_info, "trailer", log_ptr, &idx);
            break;
        case EM_HTTP_TRANSFER_ENCODING:
            _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_UPGRADE:
            _find_hash_write_log_delete(line_info, "upgrade", log_ptr, &idx);
            break;
        case EM_HTTP_VIA:
            _find_hash_write_log_delete(line_info, "via", log_ptr, &idx);
            break;
        case EM_HTTP_WARNING:
            _find_hash_write_log_delete(line_info, "warning", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT:
            _find_hash_write_log_delete(line_info, "accept", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_CHARSET:
            _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_ENCODING:
            _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "accept-language", log_ptr, &idx);
            break;
        case EM_HTTP_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "authorization", log_ptr, &idx);
            break;
        case EM_HTTP_EXPECT:
            _find_hash_write_log_delete(line_info, "expect", log_ptr, &idx);
            break;
        case EM_HTTP_FROM:
            _find_hash_write_log_delete(line_info, "from", log_ptr, &idx);
            break;
        case EM_HTTP_HOST:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            // if (direction == FLOW_DIR_SRC2DST)
            //     _find_hash_write_log_delete(line_info, "host", log_ptr, &idx);
            // else if (session){
            //     g_hash_table_remove(line_info->table, "host");
            //     write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            // } else {
            //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            // }
            break;
        case EM_HTTP_IF_MATCH:
            _find_hash_write_log_delete(line_info, "if-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_MODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, &idx);
            break;
        case EM_HTTP_IF_NONE_MATCH:
            _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_RANGE:
            _find_hash_write_log_delete(line_info, "if-range", log_ptr, &idx);
            break;
        case EM_HTTP_IF_UNMODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, &idx);
            break;
        case EM_HTTP_MAX_FORWARDS:
            _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, &idx);
            break;
        case EM_HTTP_RANGE:
            _find_hash_write_log_delete(line_info, "range", log_ptr, &idx);
            break;
        case EM_HTTP_REFERER:
            _find_hash_write_log_delete(line_info, "referer", log_ptr, &idx);
            break;
        case EM_HTTP_TE:
            _find_hash_write_log_delete(line_info, "te", log_ptr, &idx);
            break;
        case EM_HTTP_USER_AGENT:
            _find_hash_write_log_delete(line_info, "user-agent", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_RANGES:
            _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, &idx);
            break;
        case EM_HTTP_AGE:
            _find_hash_write_log_delete(line_info, "age", log_ptr, &idx);
            break;
        case EM_HTTP_ETAG:
            _find_hash_write_log_delete(line_info, "etag", log_ptr, &idx);
            break;
        case EM_HTTP_LOCATION:
            _find_hash_write_log_delete(line_info, "location", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_RETRY_AFTER:
            _find_hash_write_log_delete(line_info, "retry-after", log_ptr, &idx);
            break;
        case EM_HTTP_SERVER:
            _find_hash_write_log_delete(line_info, "server", log_ptr, &idx);
            break;
        case EM_HTTP_VARY:
            _find_hash_write_log_delete(line_info, "vary", log_ptr, &idx);
            break;
        case EM_HTTP_WWW_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_ALLOW:
            _find_hash_write_log_delete(line_info, "allow", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_ENCODING:
            _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "content-language", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LENGTH:
            _find_hash_write_log_delete(line_info, "content-length", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LOCATION:
            _find_hash_write_log_delete(line_info, "content-location", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_MD5:
            _find_hash_write_log_delete(line_info, "content-md5", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_RANGE:
            _find_hash_write_log_delete(line_info, "content-range", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_TYPE:
            _find_hash_write_log_delete(line_info, "content-type", log_ptr, &idx);
            break;
        case EM_HTTP_EXPIRES:
            _find_hash_write_log_delete(line_info, "expires", log_ptr, &idx);
            break;
        case EM_HTTP_LAST_MODIFIED:
            _find_hash_write_log_delete(line_info, "last-modified", log_ptr, &idx);
            break;
        case EM_HTTP_X_FORWARDED_FOR:
            _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE:
            _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE2:
            _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, &idx);
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }

    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 100*2);  //K00,V00 ~ K99,V99

    log_ptr->type        = TBL_LOG_HTTP_QQACC;
    log_ptr->len         = idx;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


// 如果 HOST_URI 符合 QQ 规则, 输出 TBL
static int write_http_qq_tbl(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP_QQACC] == 0)
    {
        return -1;
    }

    struct http_session*session = (struct http_session *)flow->app_session;
    //是否存在 Host
    struct header_value* host = (struct header_value*)g_hash_table_lookup(line_info->table, "host");
    if(NULL == host)
    {
        return -1;
    }

    //是否存在 URI
    if( (line_info->uri_val_len <=0)  || NULL == line_info->uri_val_ptr)
    {
        return -1;
    }

    //匹配
    int uri_len = (line_info->uri_val_len < 20)?line_info->uri_val_len : 20;
    for(int i = 0; (0 != g_config.http_qq[i].host[0] && 0 != g_config.http_qq[i].uri[0]); i++)
    {
        if(line_info->uri_val_len < strlen(g_config.http_qq[i].uri))
        {
            continue;
        }

        if(host->len < strlen(g_config.http_qq[i].host))
        {
            continue;
        }

        if(0 != memcmp(line_info->uri_val_ptr, g_config.http_qq[i].uri,  strlen(g_config.http_qq[i].uri)))
        {
            continue;
        }

        if(0 != memcmp(host->ptr, g_config.http_qq[i].host, strlen(g_config.http_qq[i].host)))
        {
            continue;
        }

        session->has_sub = 1;
        write_http_qq_log(flow, direction, line_info);

        return 0;
    }

    return -1;
}

static int write_http_wx_msg_log(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_HTTP_H_X_NUMBER+1;i++)
    {
        switch(http_field_array[i].index)
        {
        case EM_HTTP_METHOD:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
            break;
        case EM_HTTP_URI:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            break;
        case EM_HTTP_VERSION:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
            break;
        case EM_HTTP_STATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
            break;
        case EM_HTTP_RESPONSESTATUS:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[i].type,line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
            break;
        case EM_HTTP_CACHE_CONTROL:
            _find_hash_write_log_delete(line_info, "cache-control", log_ptr, &idx);
            break;
        case EM_HTTP_CONNECTION:
            _find_hash_write_log_delete(line_info, "connection", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE:
            _find_hash_write_log_delete(line_info, "cookie", log_ptr, &idx);
            break;
        case EM_HTTP_COOKIE2:
            _find_hash_write_log_delete(line_info, "cookie2", log_ptr, &idx);
            break;
        case EM_HTTP_DATE:
            _find_hash_write_log_delete(line_info, "date", log_ptr, &idx);
            break;
        case EM_HTTP_PRAGMA:
            _find_hash_write_log_delete(line_info, "pragma", log_ptr, &idx);
            break;
        case EM_HTTP_TRAILER:
            _find_hash_write_log_delete(line_info, "trailer", log_ptr, &idx);
            break;
        case EM_HTTP_TRANSFER_ENCODING:
            _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_UPGRADE:
            _find_hash_write_log_delete(line_info, "upgrade", log_ptr, &idx);
            break;
        case EM_HTTP_VIA:
            _find_hash_write_log_delete(line_info, "via", log_ptr, &idx);
            break;
        case EM_HTTP_WARNING:
            _find_hash_write_log_delete(line_info, "warning", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT:
            _find_hash_write_log_delete(line_info, "accept", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_CHARSET:
            _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_ENCODING:
            _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "accept-language", log_ptr, &idx);
            break;
        case EM_HTTP_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "authorization", log_ptr, &idx);
            break;
        case EM_HTTP_EXPECT:
            _find_hash_write_log_delete(line_info, "expect", log_ptr, &idx);
            break;
        case EM_HTTP_FROM:
            _find_hash_write_log_delete(line_info, "from", log_ptr, &idx);
            break;
        case EM_HTTP_HOST:
            if (direction == FLOW_DIR_SRC2DST)
                _find_hash_write_log_delete(line_info, "host", log_ptr, &idx);
            else if (session){
                g_hash_table_remove(line_info->table, "host");
                write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
            } else {
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case EM_HTTP_IF_MATCH:
            _find_hash_write_log_delete(line_info, "if-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_MODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, &idx);
            break;
        case EM_HTTP_IF_NONE_MATCH:
            _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, &idx);
            break;
        case EM_HTTP_IF_RANGE:
            _find_hash_write_log_delete(line_info, "if-range", log_ptr, &idx);
            break;
        case EM_HTTP_IF_UNMODIFIED_SINCE:
            _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, &idx);
            break;
        case EM_HTTP_MAX_FORWARDS:
            _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHORIZATION:
            _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, &idx);
            break;
        case EM_HTTP_RANGE:
            _find_hash_write_log_delete(line_info, "range", log_ptr, &idx);
            break;
        case EM_HTTP_REFERER:
            _find_hash_write_log_delete(line_info, "referer", log_ptr, &idx);
            break;
        case EM_HTTP_TE:
            _find_hash_write_log_delete(line_info, "te", log_ptr, &idx);
            break;
        case EM_HTTP_USER_AGENT:
            _find_hash_write_log_delete(line_info, "user-agent", log_ptr, &idx);
            break;
        case EM_HTTP_ACCEPT_RANGES:
            _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, &idx);
            break;
        case EM_HTTP_AGE:
            _find_hash_write_log_delete(line_info, "age", log_ptr, &idx);
            break;
        case EM_HTTP_ETAG:
            _find_hash_write_log_delete(line_info, "etag", log_ptr, &idx);
            break;
        case EM_HTTP_LOCATION:
            _find_hash_write_log_delete(line_info, "location", log_ptr, &idx);
            break;
        case EM_HTTP_PROXY_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_RETRY_AFTER:
            _find_hash_write_log_delete(line_info, "retry-after", log_ptr, &idx);
            break;
        case EM_HTTP_SERVER:
            _find_hash_write_log_delete(line_info, "server", log_ptr, &idx);
            break;
        case EM_HTTP_VARY:
            _find_hash_write_log_delete(line_info, "vary", log_ptr, &idx);
            break;
        case EM_HTTP_WWW_AUTHENTICATE:
            _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, &idx);
            break;
        case EM_HTTP_ALLOW:
            _find_hash_write_log_delete(line_info, "allow", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_ENCODING:
            _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LANGUAGE:
            _find_hash_write_log_delete(line_info, "content-language", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LENGTH:
            _find_hash_write_log_delete(line_info, "content-length", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_LOCATION:
            _find_hash_write_log_delete(line_info, "content-location", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_MD5:
            _find_hash_write_log_delete(line_info, "content-md5", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_RANGE:
            _find_hash_write_log_delete(line_info, "content-range", log_ptr, &idx);
            break;
        case EM_HTTP_CONTENT_TYPE:
            _find_hash_write_log_delete(line_info, "content-type", log_ptr, &idx);
            break;
        case EM_HTTP_EXPIRES:
            _find_hash_write_log_delete(line_info, "expires", log_ptr, &idx);
            break;
        case EM_HTTP_LAST_MODIFIED:
            _find_hash_write_log_delete(line_info, "last-modified", log_ptr, &idx);
            break;
        case EM_HTTP_X_FORWARDED_FOR:
            _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE:
            _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, &idx);
            break;
        case EM_HTTP_SET_COOKIE2:
            _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, &idx);
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 51*2);  //K00,V00 ~ K50,V50
    // const char *p = (const char *)line_info->PROTOCOL_VAL_PTR(content_decode);
    // int         l = line_info->PROTOCOL_VAL_LEN(content_decode);


    // if (p && l > 0 && (0 != memcmp(p, "ERR_", 4) && 0 != memcmp(p, "HTTP_BODY_FORMAT", 15)))
    // {
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K51
    //     write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, l);//V51
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K52
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V52
    // }
    // else
    // if (p && -1 == l && (0 == memcmp(p, "ERR_", 4) || 0 == memcmp(p, "HTTP_BODY_FORMAT", 15)))
    // {
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K51
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V51
    //     write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));//K52
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V52
    // }
    // else
    // {
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K51
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V51
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K52
    //     write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V52
    // }


    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K51
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V51
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //K52
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);   //V52

    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 47*2);  //K53,V53 ~ K99,V99

    log_ptr->type        = TBL_LOG_HTTP_WX_MSG;
    log_ptr->len         = idx;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}



// 如果 HOST_URI 符合 QQ 规则, 输出 TBL
static int write_http_wx_msg(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP_WX_MSG] == 0)
    {
        return -1;
    }

    struct http_session*session = (struct http_session *)flow->app_session;
    //报文内是否存在 Host
    struct header_value* host = (struct header_value*)g_hash_table_lookup(line_info->table, "host");
    if(NULL == host)
    {
        return -1;
    }

    //报文内是否存在 URI
    if( (line_info->uri_val_len <=0)  || NULL == line_info->uri_val_ptr)
    {
        return -1;
    }

    //轮巡匹配
    int uri_len = (line_info->uri_val_len < 20)?line_info->uri_val_len : 20;
    for(int i = 0; (0 != g_config.http_wx_msg[i].host[0] && 0 != g_config.http_wx_msg[i].uri[0]); i++)
    {
        if(line_info->uri_val_len < strlen(g_config.http_wx_msg[i].uri))
        {
            continue;
        }

        if(host->len < strlen(g_config.http_wx_msg[i].host))
        {
            continue;
        }

        if(0 != memcmp(line_info->uri_val_ptr, g_config.http_wx_msg[i].uri,  strlen(g_config.http_wx_msg[i].uri)))
        {
            continue;
        }

        if(0 != memcmp(host->ptr, g_config.http_wx_msg[i].host, strlen(g_config.http_wx_msg[i].host)))
        {
            continue;
        }

        struct http_session*session = (struct http_session *)flow->app_session;
        write_http_wx_msg_log(flow, direction, line_info);
        return 0;
    }

    return -1;
}


static
void print_QQ_File(ST_QQ_File_MSG *p)
{
    if(0 == g_config.debug_qq_file)
    {
        return;
    }

    dpi_TrailerDump(&p->trailer);
    printf("ip_version    :%d\n", p->ip_version);
    printf("client_ip     :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->client_ip.ipv4)+0),
            *((uint8_t*)(&p->client_ip.ipv4)+1),
            *((uint8_t*)(&p->client_ip.ipv4)+2),
            *((uint8_t*)(&p->client_ip.ipv4)+3));

    printf("server_ip     :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->server_ip.ipv4)+0),
            *((uint8_t*)(&p->server_ip.ipv4)+1),
            *((uint8_t*)(&p->server_ip.ipv4)+2),
            *((uint8_t*)(&p->server_ip.ipv4)+3));

    printf("client_port   :%d\n", p->client_port);
    printf("server_port   :%d\n", p->server_port);
    printf("isPicture     :%d\n", p->isPicture);
    printf("picture_width :%s\n", p->picture_width);
    printf("picture_height:%s\n", p->picture_height);
    printf("isGroup       :%d\n", p->isGroup);
    printf("isSender      :%d\n", p->isSender);
    printf("FileEncode    :%s\n", p->FileEncode);
    printf("Host          :%s\n", p->Host);
    printf("URL           :%s\n", p->URL);
    printf("Method        :%s\n", p->Method);
    printf("User_Agent    :%s\n", p->User_Agent);
    printf("AcceptEncoding:%s\n", p->AcceptEncoding);
    printf("AcceptLanguage:%s\n", p->AcceptLanguage);
    printf("Connection    :%s\n", p->Connection);
    printf("ContentLength :%s\n", p->ContentLength);
    printf("CacheControl  :%s\n", p->CacheControl);
    printf("Cookie        :%s\n", p->Cookie);
    printf("NetType       :%s\n", p->NetType);
    printf("\n");
}


static void send_QQ_File(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    const char* URI_Prefix      = "/cgi-bin/httpconn?";
    size_t      URI_Prefix_len  = strlen(URI_Prefix);
    int         safelen         = 0;
    struct http_session*session = (struct http_session *)flow->app_session;

    if (g_config.protocol_switch[PROTOCOL_QQ_FILE] == 0)
    {
        return ;
    }


    if(NULL == line_info || NULL == flow)
    {
        return;
    }

    if(URI_Prefix_len > line_info->uri_val_len || (0 != memcmp(line_info->PROTOCOL_VAL_PTR(uri), URI_Prefix, strlen(URI_Prefix) ) ) )
    {
        return ;
    }

    if(line_info->uri_val_len != (size_t)isUTF8((const char*)line_info->uri_val_ptr, line_info->uri_val_len))
    {
        return;
    }

    ST_QQ_File_MSG a;
    memset(&a, 0, sizeof(a));

    typedef struct
    {
        const char*k;
        char*      v;
        int        s;
    } ST_kv;

    ST_kv list[] = {
        { "host"           , a.Host           , sizeof(a.Host          )},
        { "user-agent"     , a.User_Agent     , sizeof(a.User_Agent    )},
        { "accept-language", a.AcceptLanguage , sizeof(a.AcceptLanguage)},
        { "accept-encoding", a.AcceptEncoding , sizeof(a.AcceptEncoding)},
        { "connection"     , a.Connection     , sizeof(a.Connection    )},
        { "content-length" , a.ContentLength  , sizeof(a.ContentLength )},
        { "cookie"         , a.Cookie         , sizeof(a.Cookie        )},
        { "net-type"       , a.NetType        , sizeof(a.NetType       )},
        { "cache-control"  , a.CacheControl   , sizeof(a.CacheControl  )},
        { NULL             , NULL             , 0             },
    };

    for(int i = 0; NULL != list[i].k; i++)
    {
        struct header_value* value = (struct header_value*)g_hash_table_lookup(line_info->table, list[i].k);
        if(NULL != value)
        {
            int safelen = value->len < list[i].s -1 ? value->len : list[i].s -1 ;
            memcpy(list[i].v, value->ptr, safelen);
        }
    }


    safelen = line_info->method_val_len < sizeof(a.Method) -1 ? line_info->method_val_len : sizeof(a.Method) -1 ;
    memcpy(a.Method, line_info->method_val_ptr, safelen);

    if(0 == memcmp("POST", a.Method, 4))
    {
        a.isSender = 1;
        getFilecode((const char*)line_info->content_val_ptr, line_info->content_val_len, (char*)a.FileEncode, sizeof(a.FileEncode));
    }
    else
    if(0 == memcmp("GET", a.Method, 3))
    {
        a.isSender = 0;
        const char*fname = "fname=";
        const char*fcode = g_strstr_len((const char*)line_info->uri_val_ptr, line_info->uri_val_len, fname);
        if(NULL == fcode)
        {
            // No fileID
            return;
        }

        fcode = fcode ? fcode + strlen(fname) : fcode;
        const char*fcodeEnd = g_strstr_len(fcode ? fcode+1 : "", line_info->uri_val_len, "&");
        int fcode_length = (fcode && fcodeEnd) ? (fcodeEnd - fcode) : 0;

        if(fcode && fcode_length <= 40)//有时出现前缀为 /
        {
            getFilecode(fcode, fcode_length, (char*)a.FileEncode, sizeof(a.FileEncode));
        }
        else
        if(fcode && fcode_length > 36 && fcode_length<= 74) //有时出现前缀为 2F
        {
            hextobin(fcode, fcode_length, a.FileEncode, sizeof(a.FileEncode));
            if('/' == a.FileEncode[0])
            {
                memmove(a.FileEncode, a.FileEncode+1, sizeof(a.FileEncode)-2);
            }
        }


        if(        g_strstr_len((const char*)line_info->uri_val_ptr, line_info->uri_val_len, "pictype=")
                && g_strstr_len((const char*)line_info->uri_val_ptr, line_info->uri_val_len, "size="))
        {
            a.isPicture  = 1;

            const char* width = g_strstr_len((const char*)line_info->uri_val_ptr,line_info->uri_val_len, "size=");
            width = width? width + strlen("size=") : "";

            const char* height = g_strstr_len(width, line_info->uri_val_len, "*");
            height = height ? height + 1 : "";

            unsigned int width_len  = (g_strstr_len(width, line_info->uri_val_len, "*") - width);
            const char *str1 = g_strstr_len(height, line_info->uri_val_len, "&");
            const char *str2 = g_strstr_len(height, line_info->uri_val_len, " ");
            unsigned int height_len = 0;
            height_len = ((0 == height_len && str1) ? str1 - height : height_len);
            height_len = ((0 == height_len && str2) ? str2 - height : height_len);
            width_len  = width_len < sizeof(a.picture_width)-1 ?width_len:sizeof(a.picture_width)-1;
            height_len = height_len< sizeof(a.picture_height)-1?height_len:sizeof(a.picture_height)-1;
            memcpy(a.picture_width,  width,  width_len);
            memcpy(a.picture_height, height, height_len);
        }


    }


    dpi_TrailerParser(&a.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
    dpi_TrailerGetMAC(&a.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&a.trailer, (const char *)flow->ethhdr);

    dpi_TrailerSetDev(&a.trailer, g_config.devname);
    dpi_TrailerSetOpt(&a.trailer, g_config.operator_name);
    dpi_TrailerSetArea(&a.trailer, g_config.devArea);          // 地域名

    char  uri_buff[2048] = {0};
    memcpy(uri_buff, line_info->uri_val_ptr, line_info->uri_val_len);
    snprintf(a.URL, sizeof(a.URL), "http://%s%s", a.Host, uri_buff);

    a.ip_version     = flow->tuple.inner.ip_version;
    a.client_port    = ntohs(flow->tuple.inner.port_src);
    a.server_port    = ntohs(flow->tuple.inner.port_dst);
    a.client_ip.ipv4 =       flow->tuple.inner.ip_src.ip4;
    a.server_ip.ipv4 =       flow->tuple.inner.ip_dst.ip4;
    a.isGroup        = QQ_FILE_TYPE_GROUP;
    a.LastActiveTime = time(NULL);
    while(NULL == f_handle);
    dpi_TrailerUpdateTS(&a.trailer);
    wxc_sendMsg(f_handle, (const unsigned char*)&a, sizeof(a), WXCS_QQ_FILE);
    session->has_sub = 1;
    print_QQ_File(&a);
}
/******************* ADD_E_By chunli ************************************/


/************************************ ADD By zhangsx ************************************/
static
void  print_share(ST_wxPosition* p)
{
    if (0 == g_config.debug_position_share)
    {
        return;
    }

    printf("phone_num      :%lu\n", p->trailer.MSISDN);
    printf("Host           :%s\n", p->PersonHost);
    printf("URL            :%s\n", p->PersonURL);
    printf("Agent          :%s\n", p->PersonAgent);
    printf("AcceptEncoding :%s\n", p->PersonAcceptEncoding);
    printf("SrcPort        :%u\n", p->PersonSrcPort);
    printf("DstPort        :%u\n", p->PersonDstPort);

    printf("src_ip         :%d.%d.%d.%d\n",
        *((uint8_t*)(&p->PersonSrcIp) + 0),
        *((uint8_t*)(&p->PersonSrcIp) + 1),
        *((uint8_t*)(&p->PersonSrcIp) + 2),
        *((uint8_t*)(&p->PersonSrcIp) + 3));

    printf("dst_ip         :%d.%d.%d.%d\n",
        *((uint8_t*)(&p->PersonDstIp) + 0),
        *((uint8_t*)(&p->PersonDstIp) + 1),
        *((uint8_t*)(&p->PersonDstIp) + 2),
        *((uint8_t*)(&p->PersonDstIp) + 3));

    printf("LastActiveTim  :%d\n", p->PersonLastActiveTime);
    printf("\n");
}


static void send_position(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_POSITION] == 0)
    {
        return ;
    }

    gpointer _value;
    struct header_value *value;
    struct http_session*session = (struct http_session *)flow->app_session;

    if(NULL == p_handle || NULL == line_info || NULL == flow)
    {
        return;
    }
    // 检测微信坐标

    const char* Prefix     = "/api?size=";
    size_t Prefix_len      = sizeof("/api?size=") - 1;

    const char* Suffix     = "=weixin";
    size_t Suffix_len      = sizeof("=weixin") - 1;

    if (Prefix_len > line_info->uri_val_len)
    {
        return; // 如果不是坐标 则退出
    }

    if (0 != memcmp(line_info->PROTOCOL_VAL_PTR(uri), Prefix, strlen(Prefix)))
    {
        return;
    }

    ST_wxPosition aPositionPerson;
    memset(&aPositionPerson, 0, sizeof(ST_wxPosition));

    // 设置建联 相关 参数
    dpi_TrailerParser(&aPositionPerson.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
    dpi_TrailerGetMAC(&aPositionPerson.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&aPositionPerson.trailer, (const char *)flow->ethhdr);
    dpi_TrailerSetDev(&aPositionPerson.trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&aPositionPerson.trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&aPositionPerson.trailer, g_config.devArea);       // 地域名


     typedef struct
    {
        const char*phead;
        char*      pvalue;
        int        size;
    } ST_kv;

    ST_kv WX_Position[] = {
        {"host",            aPositionPerson.PersonHost           , sizeof(aPositionPerson.PersonHost          )},
        {"user-agent",      aPositionPerson.PersonAgent          , sizeof(aPositionPerson.PersonAgent         )},
        {"request-uri",     aPositionPerson.PersonURL            , sizeof(aPositionPerson.PersonURL           )},
        {"accept-encoding", aPositionPerson.PersonAcceptEncoding , sizeof(aPositionPerson.PersonAcceptEncoding)},
        {NULL, NULL, 0},
    };

    for(int i = 0; NULL != WX_Position[i].phead; i++)
    {
        struct header_value* value = (struct header_value*)g_hash_table_lookup(line_info->table, WX_Position[i].phead);
        if(NULL != value)
        {
            int safelen = value->len < WX_Position[i].size -1 ? value->len : WX_Position[i].size -1;
            memcpy(WX_Position[i].pvalue, value->ptr, safelen);
        }
    }
    char  uri_buff[2048] = { 0 };
    char *pleft          = NULL;
    char *pright         = NULL;

    /* Get Uri */
    if (line_info->uri_val_len != (size_t)isUTF8((const char*)line_info->uri_val_ptr, line_info->uri_val_len))
    {
        return;
    }

    memcpy(uri_buff, line_info->uri_val_ptr, line_info->uri_val_len);
    snprintf(aPositionPerson.PersonURL, sizeof(aPositionPerson.PersonURL), "http://%s%s", aPositionPerson.PersonHost, uri_buff);

    aPositionPerson.PersonDstPort        = ntohs(flow->tuple.inner.port_dst);
    aPositionPerson.PersonSrcPort        = ntohs(flow->tuple.inner.port_src);
    aPositionPerson.PersonSrcIp          = flow->tuple.inner.ip_src.ip4;
    aPositionPerson.PersonDstIp          = flow->tuple.inner.ip_dst.ip4;
    aPositionPerson.PersonLastActiveTime = time(NULL);

    //* 发送*/
    dpi_TrailerUpdateTS(&aPositionPerson.trailer);
    wxc_sendMsg(p_handle, (const unsigned char*)&aPositionPerson, sizeof(aPositionPerson), WXCS_POSITION);
    session->has_sub = 1;
    print_share(&aPositionPerson);
}
/************************************ ADD_End By zhangsx ************************************/


static
void wx_msg_red_packet(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    gpointer _value;
    struct http_session *session;
    struct header_value *value;
    char content_length[20] = { 0 };
    uint16_t fsport = 0, fdport = 0;
    uint32_t fsip = 0, fdip = 0;
    uint8_t c2s = 0;
    uint8_t fc2s = 0;

    if (g_config.wx_red_packet_switch == 0)
        return;

    WxActionInfo info;
    memset(&info, 0, sizeof(WxActionInfo));

    fsport = ntohs(flow->tuple.inner.port_src);
    fdport = ntohs(flow->tuple.inner.port_dst);

    if (fsport < fdport) {
        fsip = ntohl(flow->tuple.inner.ip_dst.ip4);
        fdip = ntohl(flow->tuple.inner.ip_src.ip4);
        fc2s = 0;
    } else {
        fsip = ntohl(flow->tuple.inner.ip_src.ip4);
        fdip = ntohl(flow->tuple.inner.ip_dst.ip4);
        fc2s = 1;
    }

    info.hash_key = fsip;

    if (direction == FLOW_DIR_SRC2DST) {
       // ip_number   = fsip;
        info.c2s    = fc2s;
    } else {
      //  ip_number   = fdip;
        info.c2s    = !fc2s;
    }

    if(NULL == line_info || NULL == flow)
    {
        return;
    }


    const char *rp_prefix = "/mmtls";
    size_t rp_prefix_len = strlen(rp_prefix);

    if (rp_prefix_len > line_info->uri_val_len)
    {
        return;
    }

    //判断指针是否为空
    if (line_info->PROTOCOL_VAL_PTR(uri) == NULL)
        return;

    if (0 != memcmp(line_info->PROTOCOL_VAL_PTR(uri), rp_prefix, rp_prefix_len))
    {
        return;
    }


    typedef struct
    {
        const char*phead;
        char*      pvalue;
        int        size;
    } ST_kv;

    ST_kv WX_Position[] = {
        {"content-length",  content_length , sizeof(content_length)},
        {NULL, NULL, 0},
    };

    for(int i = 0; NULL != WX_Position[i].phead; i++)
    {
        struct header_value* value = (struct header_value*)g_hash_table_lookup(line_info->table, WX_Position[i].phead);
        if(NULL != value)
        {
            int safelen = value->len < WX_Position[i].size -1 ? value->len : WX_Position[i].size -1;
            memcpy(WX_Position[i].pvalue, value->ptr, safelen);
        }
    }

    info.content_length =  atoi(content_length);
    if (info.content_length < 500 || info.content_length > 600) {
        return;
    }

    /* Get Uri */
    // if (line_info->uri_val_len != (size_t)isUTF8((const char*)line_info->uri_val_ptr, line_info->uri_val_len))
    // {
    //     return;
    // }

    memcpy(info.uri_buff, line_info->uri_val_ptr, line_info->uri_val_len);
    //snprintf(aPositionPerson.PersonURL, sizeof(aPositionPerson.PersonURL), "http://%s%s", aPositionPerson.PersonHost, uri_buff);
    info.time_stamp = time(NULL);
    info.action_flag = EM_SEND_RED_PACKET;


    wx_action_red_packet(flow, direction, info.hash_key, &info);
}


void http_douyin(struct flow_info *flow, int direction, const uint8_t *payload,
                 const uint32_t payload_len, struct http_request_info *line_info)
{
  char url_cont1[] = "douyincdn.com/radio/stream-";
  char url_cont2[] = "/radio/stream-";

  char content_type[] = "video/x_flv";

  struct header_value * value = NULL;

  if (g_config.protocol_switch[PROTOCOL_DOUYIN] == 0)
    return;

  struct http_session* session = flow->app_session;


  // value = (struct header_value*)g_hash_table_lookup(line_info->table, "method");
  // if(NULL == value)
  //   return;
  // if (memcmp("GET", value->ptr, 3) != 0)
  //   return;

  int find_flag1 = -1;
  int find_flag2 = -1;

  if (session->uri == NULL)
    return;

  find_flag1 = dpi_strstr((const uint8_t *)session->uri, strlen(session->uri),
                          url_cont1, strlen(url_cont1));
  find_flag2 = dpi_strstr((const uint8_t *)session->uri, strlen(session->uri),
                          url_cont2, strlen(url_cont2));

  if (find_flag1 < 0 && find_flag2 < 0)
    return;

  dissect_dy_from_http(flow, direction, payload, payload_len);
}

int write_alipay_log_by_recorder(struct flow_info *flow, int direction, precord_t *record) {
    int idx = 0, i;
    struct tbl_log_record *log_ptr;
    const char *str = NULL;

    if (rte_mempool_get(tbl_log_record_mempool, (void **)&log_ptr) < 0) {
      DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_record_mempool");
      return PKT_OK;
    }
    write_tbl_log_common_by_record(flow,direction,record);
    log_ptr->record             = record;
    log_ptr->thread_id          = flow->thread_id;
    precord_layer_move_cursor(record, "alipay");

    if(tbl_record_log_enqueue(log_ptr) != 1) {
        ya_destroy_record(record);
        rte_mempool_put(tbl_log_record_mempool, (void *)log_ptr);
    }
    record = NULL;
    struct rte_mempool *pool = NULL;
    uint32_t pool_len = 0;


    return 0;
}

static char *alipay_command[]={
"个人发送图片消息",
"个人发送语音消息",
"个人发送视频消息",
"个人发送位置消息",
"群发送图片消息",
"群发送语音消息",
"群发送视频消息",
"群发送位置消息",
};
void http_alipay(struct flow_info *flow, int direction, const uint8_t *payload,
                 const uint32_t payload_len, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_ALIPAY] == 0)
    {
        return ;
    }

    precord_t *alipay_record = ya_create_record("alipay");
    ya_fvalue_t *new_value = NULL;
    gpointer _value;
    struct header_value *value;
    char buff[256] = {0};
    char sender[128] = {0};
    char reciver[128] = {0};
    uint32_t sender_len = 0, reciver_len = 0;
    char suffix[16] = "bin";
    _value = g_hash_table_lookup(line_info->table, "x-mmup-biztype");
    if (_value) {
        value = (struct header_value *)_value;
        if(value->ptr && value->len){
            memcpy(buff, (char *)value->ptr, value->len<(sizeof(buff)-1)?value->len:(sizeof(buff)-1));
            if(!memcmp(buff,"social_chat_",12)) {
                if(buff[13] == '_') {//android
                    char *p1 = strchr(buff+14, '_');
                    if(p1) {
                       sender_len = p1 - buff - 14;
                       reciver_len = buff + strlen(buff) - p1 - 1;
                        memcpy(sender, buff+14, sender_len);
                        memcpy(reciver, p1+1, reciver_len);
                    }
                } else {//ios
                    char *p1 = strchr(buff+12, '_');
                    char *p2 = strrchr(buff, '_');
                    if(p1 && p2){
                        sender_len = p1 - buff - 12;
                        reciver_len = p2 - p1 - 1;
                        memcpy(sender, (char *)value->ptr+12, sender_len);
                        memcpy(reciver, p1+1, reciver_len);
                    }
                }
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, sender, sender_len);
                dpi_precord_fvalue_put_by_name(alipay_record, "sendUID", new_value);
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, reciver, reciver_len);
                if(sender_len == reciver_len)
                    dpi_precord_fvalue_put_by_name(alipay_record, "reciveUID", new_value);
                else
                    dpi_precord_fvalue_put_by_name(alipay_record, "groupUID", new_value);
            }
        }
    }
    _value = g_hash_table_lookup(line_info->table, "x-arup-timestamp");
    if (_value) {
        value = (struct header_value *)_value;
        if(value->ptr && value->len){
            memset(buff, 0, sizeof(buff));
            memcpy(buff, (char *)value->ptr, value->len<(sizeof(buff)-1)?value->len:(sizeof(buff)-1));
			timet_to_datetime(atol(buff)/1000, buff, sizeof(buff));
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, buff, strlen(buff));
            dpi_precord_fvalue_put_by_name(alipay_record, "sendFileTime", new_value);
        }
    }
    _value = g_hash_table_lookup(line_info->table, "x-arup-file-name");
    if (_value) {
        value = (struct header_value *)_value;
        if(value->ptr && value->len){
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, (char *)value->ptr, value->len);
            dpi_precord_fvalue_put_by_name(alipay_record, "sourceFilename", new_value);
        }
    }
    _value = g_hash_table_lookup(line_info->table, "x-mmup-file-ext");
    if (_value) {
        value = (struct header_value *)_value;
        if(value->ptr && value->len){
            uint32_t suffix_len = value->len < (sizeof(suffix)-1) ? value->len : (sizeof(suffix)-1);
            memcpy(suffix, (char *)value->ptr, value->len);
            suffix[value->len] = '\0';
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, (char *)value->ptr, value->len);
            dpi_precord_fvalue_put_by_name(alipay_record, "filetype", new_value);
            if(!memcmp((char *)value->ptr,"jpg", value->len) && sender_len == reciver_len){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[0], strlen(alipay_command[0]));
            } else if(!memcmp((char *)value->ptr,"amr", value->len) && sender_len == reciver_len){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[1], strlen(alipay_command[1]));
            } else if(!memcmp((char *)value->ptr,"mp4", value->len) && sender_len == reciver_len){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[2], strlen(alipay_command[2]));
            } else if(!memcmp((char *)value->ptr,"jpg", value->len)){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[4], strlen(alipay_command[4]));
            } else if(!memcmp((char *)value->ptr,"amr", value->len)){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[5], strlen(alipay_command[5]));
            } else if(!memcmp((char *)value->ptr,"mp4", value->len)){
                new_value = ya_fvalue_new_stringn(YA_FT_STRING, alipay_command[6], strlen(alipay_command[6]));
            }
            dpi_precord_fvalue_put_by_name(alipay_record, "command", new_value);
        }
    }
    if(line_info->PROTOCOL_VAL_PTR(content) && line_info->PROTOCOL_VAL_LEN(content) > 4){
        struct timeval tv;
        gettimeofday(&tv, NULL);
        char local_filename[512] = {0};
        snprintf(local_filename, sizeof(local_filename), "%s/alipay/%s_%06ld_alipay_%03d.%s", g_config.tbl_out_dir, dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, flow->thread_id, suffix);
        FILE *fp = fopen(local_filename, "w");
        if(fp){
            fwrite(line_info->PROTOCOL_VAL_PTR(content), line_info->PROTOCOL_VAL_LEN(content), 1, fp);
            fclose(fp);
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, local_filename, strlen(local_filename));
            dpi_precord_fvalue_put_by_name(alipay_record, "localFilename", new_value);
        }
    }
    write_alipay_log_by_recorder(flow, direction, alipay_record);
}

void wx_gh_hash_insert(char *key, void* value);
int wx_gh_hash_has_key(char *key);
static void send_group_head_pic_data(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_GROUP_HEAD_CONTENT] == 0) {
        return ;
    }

    if(!h_handle || !line_info || !flow) {
        return;
    }

    struct http_session* session = flow->app_session;
    if (!session || !line_info->content_val_ptr || strlen(session->wx_group_head_last_status.session_url) == 0
        || strlen(session->wx_group_head_last_status.pic_uri) == 0) {
        return;
    }

    if (wx_gh_hash_has_key(session->wx_group_head_last_status.pic_uri)) {
        rte_atomic32_inc(&g_config.reassemble_info.content_statics[EM_CONTENT_WXGH].repeat_number);
        return;
    }

    int dataLen =  line_info->content_val_len;
    if(dataLen>=REASSEMBLE_LEN_MAX && dataLen<=0){
        return;
    }
    int totalLen = dataLen + sizeof(ST_wxGroupHeadPicData);
    ST_wxGroupHeadPicData* data = (ST_wxGroupHeadPicData*)malloc(totalLen);
    if (!data) {
        DPI_LOG(DPI_LOG_WARNING, "malloc failed");
        return;
    }
    memset(data, 0, totalLen);
    snprintf(data->SessionUrl, sizeof(data->SessionUrl)-1, "%s", session->wx_group_head_last_status.session_url);
    snprintf(data->GroupHeadPictureName, sizeof(data->GroupHeadPictureName), "%s", session->wx_group_head_last_status.pic_uri);
    data->PictureDataLen = dataLen;
    memcpy(data->PictureData, line_info->content_val_ptr, dataLen);

    // 发送数据
    wxc_sendMsg(h_handle, (const unsigned char*)data, totalLen, WXCS_WX_GROUP_HEAD_DATA);
    rte_atomic32_inc(&g_config.reassemble_info.content_statics[EM_CONTENT_WXGH].s_wxcs_number);

    uint32_t t = time(NULL);
    char str[16] = {0};
    snprintf(str, sizeof(str)-1, "%u", t);
    wx_gh_hash_insert(strdup(session->wx_group_head_last_status.pic_uri), strdup(str));

    // 清空上次状态
    session->wx_group_head_last_status.session_url[0] = '\0';
    session->wx_group_head_last_status.pic_uri[0] = '\0';

    session->has_sub = 1;
    free(data);
}

int put_value_http_weixin_pyq_record(struct flow_info *flow, int direction,  struct http_request_info *line_info,struct header_value *referer_value){
    ST_weixin_file_session *session = NULL;
    session   = ( ST_weixin_file_session *)flow->app_session;
    precord_t *weixin_pyq_record = ya_create_record("weixin_pyq");
    ya_fvalue_t *new_value = NULL;
    gpointer _value=NULL;
    const char *key_value=NULL;
    int  key_value_len=0;

    new_value = ya_fvalue_new_stringn(YA_FT_STRING, "http", strlen("http"));
    dpi_precord_fvalue_put_by_name(weixin_pyq_record, "data_origin", new_value);

    new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
    dpi_precord_fvalue_put_by_name(weixin_pyq_record, "Http-Method", new_value);

    new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
    dpi_precord_fvalue_put_by_name(weixin_pyq_record, "Http-Uri", new_value);

    char url_buff[2048] = {0};
    _value = NULL;
    struct header_value *value = NULL;
    _value = g_hash_table_lookup(line_info->table, "host");
    if (_value != NULL) {
        value = (struct header_value *)_value;
        memcpy(url_buff, (const char *)value->ptr, value->len);
        int left_len = 2048 - value->len;
        if (left_len > (int)line_info->PROTOCOL_VAL_LEN(uri)) {
            memcpy(&url_buff[value->len], (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)url_buff, strlen(url_buff));
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "fileurl", new_value);
        }
    }

    _find_hash_write_record_delete(line_info, "user-agent","clientostype", weixin_pyq_record);

    if (referer_value && referer_value->ptr) {
        new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)referer_value->ptr, referer_value->len);
        dpi_precord_fvalue_put_by_name(weixin_pyq_record, "refererurl", new_value);
    }

    /* 提取referer中的 version,uin,nettype,signal,scene */

    if (referer_value) {
        key_value = NULL;
        key_value = dissect_wx_url_key((const char *)referer_value->ptr, referer_value->len, "version=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "referer_version", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key((const char *)referer_value->ptr, referer_value->len, "uin=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "uin", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key((const char *)referer_value->ptr, referer_value->len, "nettype=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "nettype", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key((const char *)referer_value->ptr, referer_value->len, "signal=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "signal", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key((const char *)referer_value->ptr, referer_value->len, "scene=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "scene", new_value);
        }
    }

    /* 提取fileurl中的 tp,token,idx,length,width*/
    if (line_info->PROTOCOL_VAL_LEN(uri)) {
        key_value = NULL;
        key_value = dissect_wx_url_key(
            (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "tp=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "tp", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key(
            (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "tolen=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "token", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key(
            (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "idx=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "idx", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key(
            (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "length=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "length", new_value);
        }

        key_value = NULL;
        key_value = dissect_wx_url_key(
            (const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "width=", &key_value_len);
        if (key_value) {
            new_value = ya_fvalue_new_stringn(YA_FT_STRING, key_value, key_value_len);
            dpi_precord_fvalue_put_by_name(weixin_pyq_record, "width", new_value);
        }
    }

    write_weixin_pyq_log_by_recorder(flow,direction,weixin_pyq_record);

    return 0;
}

int write_http_weixin_pyq(struct flow_info *flow, int direction,  struct http_request_info *line_info)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_PYQ] == 0)
    {
      return PKT_DROP;
    }
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;
    uint8_t  uri_flag=0;  /* 1-uri识别到mmsns */
    if(line_info->PROTOCOL_VAL_LEN(uri)>6 &&
       memmem((const char *)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "/mmsns", 6)){
        uri_flag=1;
    }else{
        uri_flag=0;
    }

    /* 获取referer指针 */
    gpointer _value=NULL;
    struct header_value *referer_value=NULL;
    _value = g_hash_table_lookup(line_info->table, "referer");
    if(_value!=NULL){
        referer_value = (struct header_value *)_value;
        /* uri中可能没有mmsns但是对应referer有 ，识别为pyq*/
        if(memmem(referer_value->ptr, referer_value->len,"/mmsns",6)){
            uri_flag=1;
        }

        if(0==uri_flag){
            return PKT_DROP;
        }
    }

    if(0==uri_flag){
        return PKT_DROP;
    }

    put_value_http_weixin_pyq_record(flow, direction,line_info,referer_value);
    return 0;//使用protorecord输出tbl

    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_DROP;
    }

    const char *key_value=NULL;
    int  key_value_len=0;
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);
    for(i=0;i<EM_WX_MAX;i++)
    {
        switch(i)
        {
            case EM_WX_HTTP_METHOD:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[EM_HTTP_METHOD].type,line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
                break;
            case EM_WX_HTTP_URI:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, http_field_array[EM_HTTP_URI].type,line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri));
                break;
            case EM_WX_FILEURL:
            {
                char url_buff[2048]={0};
                _value=NULL;
                 struct header_value *value=NULL;
                _value = g_hash_table_lookup(line_info->table, "host");
                if(_value!=NULL){
                    value = (struct header_value *)_value;
                    memcpy(url_buff,(const char *)value->ptr,value->len);
                    int left_len=2048-value->len;
                    if(left_len>(int)line_info->PROTOCOL_VAL_LEN(uri)){
                        memcpy(&url_buff[value->len],(const char *)line_info->PROTOCOL_VAL_PTR(uri),line_info->PROTOCOL_VAL_LEN(uri));
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)url_buff, strlen(url_buff));
                    }else{
                        write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    }
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }

                break;
            }
            case EM_WX_CLIENTOSTYPE:
                _find_hash_write_log_delete(line_info, "user-agent", log_ptr, &idx);
                break;
            case EM_WX_REFERER_URL:
                if(referer_value && referer_value->ptr){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)referer_value->ptr, referer_value->len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;

            /* 提取referer中的 version,uin,nettype,signal,scene */
            case EM_WX_REFERER_VERSION:
                if(referer_value ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)referer_value->ptr, referer_value->len, "version=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_PYQ_UIN:
                if(referer_value ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)referer_value->ptr, referer_value->len, "uin=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_NETTYPE:
                if(referer_value ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)referer_value->ptr, referer_value->len, "nettype=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_SIGNAL:
                if(referer_value ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)referer_value->ptr, referer_value->len, "signal=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_SCENE:
                if(referer_value ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)referer_value->ptr, referer_value->len, "scene=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;

            /* 提取fileurl中的 tp,token,idx,length,width*/
            case EM_WX_TP:
                if(line_info->PROTOCOL_VAL_LEN(uri)){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "tp=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_TOKEN:
                if(line_info->PROTOCOL_VAL_LEN(uri) ){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "tolen=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_IDX:
                if(line_info->PROTOCOL_VAL_LEN(uri)){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "idx=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_LENGTH:
                if(line_info->PROTOCOL_VAL_LEN(uri)){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "length=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_WIDTH:
                if(line_info->PROTOCOL_VAL_LEN(uri)){
                    key_value=NULL;
                    key_value=dissect_wx_url_key((const char*)line_info->PROTOCOL_VAL_PTR(uri), line_info->PROTOCOL_VAL_LEN(uri), "width=", &key_value_len);
                    if(key_value){
                        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, key_value, key_value_len);
                        break;
                    }
                }
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;

            case EM_WX_VALUE_ORIGIN:
                write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, "http", strlen("http"));
                break;
            default:
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
        }
    }

    log_ptr->type     = TBL_LOG_WEIXIN_PYQ;
    log_ptr->len      = idx;
    log_ptr->tid      = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return PKT_OK;
}

typedef struct SearchData {
  char query[256];
  int isMatch;
} SearchData;

static void search_key(gpointer key, gpointer value, gpointer user_data) {
  SearchData *search_data = user_data;
  int query_len = strlen(search_data->query);
  int key_len = strlen(key);
  if (key_len <= query_len) {
    if (strcmp(search_data->query + query_len - key_len, key) == 0) {
        search_data->isMatch = 1;
        return;
    }
  }
}

static
void dissect_wxpay(struct flow_info *flow, int direction, struct http_request_info *line_info)
{
  if (g_config.protocol_switch[PROTOCOL_WXPAY] == 0) {
    return ;
  }

  struct header_value *value = NULL;
  char idenit [] = "/mmtls";
  int find = 0;
  int len = 0;
  WxpayHeader   w_header;
  memset(&w_header, 0, sizeof(WxpayHeader));
  struct http_session *session = (struct http_session *)flow->app_session;

  // printf("url = %s\n", line_info->PROTOCOL_VAL_PTR(uri));
  find = dpi_strstr(line_info->PROTOCOL_VAL_PTR(uri),
                    line_info->PROTOCOL_VAL_LEN(uri),
                    idenit, strlen(idenit));

  if (find < 0)
    return;

  // printf("host = %s\n", session->host);

  // value = (struct header_value*)g_hash_table_lookup(line_info->table, "host");
  // snprintf(w_header.host, sizeof(w_header.host), "%s", value);
  // if (value == NULL)
  //   return;
  // if (value->ptr == NULL || value->len == 0)
  //   return;
  // printf("host host = %s\n", value->ptr);
  SearchData data ;
  memset(&data, 0, sizeof(SearchData));
  data.isMatch = 0;
  strncpy(data.query,session->host,sizeof(data.query));
  g_hash_table_foreach(g_config.wxpay_hash,(GHFunc)search_key,(gpointer)&data);
  // gpointer f_value = g_hash_table_lookup(g_config.wxpay_hash, (gconstpointer)session->host);
  // if (f_value == NULL)
  //   return;
  if(data.isMatch == 0)
    return;

  len = line_info->PROTOCOL_VAL_LEN(uri) > 256 ? 256 : line_info->PROTOCOL_VAL_LEN(uri);
  snprintf(w_header.url, len, "%s", line_info->PROTOCOL_VAL_PTR(uri));
  snprintf(w_header.host, sizeof(w_header.host), "%s", session->host);


  dissect_wxpay_from_http(flow, direction, &w_header);
}


// http 子业务模块
int http_sub(struct flow_info *flow, int direction, struct http_request_info *line_info, HttpSubAttr * attr)
{
    send_group_head(flow, direction, line_info);   //只做查找，不做删除操作 add_By chunli
    send_QQ_File(flow, direction, line_info);      //只做查找，不做删除操作 add_By chunli
    send_position(flow, direction, line_info);     //只做查找，不做删除操作 add_By chunli
    write_http_wx_tbl(flow, direction, line_info); //注意写日志会 删除操作部分信息 add_By chunli
    write_http_qq_tbl(flow, direction, line_info); //注意写日志会 删除操作部分信息 add_By chunli
    write_http_wx_msg(flow, direction, line_info); //注意写日志会 删除操作部分信息 add_By chunli
    wx_msg_red_packet(flow, direction, line_info); //红包行为记录  chenzq
    if (attr->is_request)
      dissect_wxpay(flow, direction, line_info);

    // http_douyin(flow, direction, line_info);

    if (attr->send_content) {
       send_group_head_pic_data(flow, direction, line_info);
    }

    return 0;
}

void  init_wx_http_sub_handle(void)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP] != 1) {
        return;
    }

    if (g_config.protocol_switch[PROTOCOL_WEIXIN_GROUP_HEAD] == 1){
        init_session_group_handle(g_config.wx_group_ip,   g_config.wx_group_port);
		init_wx_gh_thread();
    }

    if (g_config.protocol_switch[PROTOCOL_WEIXIN_POSITION] == 1){
        init_session_position_handle(g_config.wx_pos_ip,  g_config.wx_pos_port);
    }

    if (g_config.protocol_switch[PROTOCOL_QQ_FILE] == 1){
        init_session_QQ_file_handle(g_config.QQ_File_ip,  g_config.QQ_File_port);
    }

}
