#pragma once

#include <string>
#include <map>
#include <unordered_map>
#include <vector>
#include <set>


class TblFieldValue
{
public:
    TblFieldValue(const std::string &);

    ~TblFieldValue();

public:
    virtual void           setFieldAndProto(const std::string &, const std::string &);      // 指定字段名与协议名

    virtual std::string    getMatchFormat(const std::string &);                             // 指定协议类型，从配置项读取对应的字段值类型

    virtual bool           format_check(const std::string &, const std::string &);          // 格式检测

    virtual bool           FormatCheck(const std::string &, const std::string &);           // 格式检测(使用协议名进行格式检测)

    virtual std::string    getRequestFieldValue(std::string &, std::string &, int);         // 获取tbl中指定的字段值
 
    virtual void           setFieldTable(std::vector<std::pair<std::string, int>> &);       // 为自定义协议提供，以便获取额外字段的值

    virtual void           destoryItem(); 

public:
    static void            init();

    static void            destory();

    static void            RegistSpecialField();                                            // 注册特定字段表

    static TblFieldValue * getField(const std::string &);                                   // 根据字段名获取对应的解析指针

private:
    static std::map<std::string, TblFieldValue *> TblFieldManager;

protected:
    /* 以下属性为初始化化时赋值 */
    std::string field_name_;                                                                // 字段名

    std::map<std::string, std::set<std::string>> base_format;                               // 字段格式(如果以set中值开头，则以key的类型解释tbl value)  e.g. "string" : ["3G", "4G"]

    std::map<std::string, std::string>           proto_format_map;                          // 协议名与格式的映射
};


