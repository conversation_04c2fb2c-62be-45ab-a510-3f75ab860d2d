/****************************************************************************************
 * 文 件 名 : dpi_wxid.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/10/27
 修改: chenzq          2022/10/27
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#ifndef DPI_WXID_H
#define DPI_WXID_H

#define  WXID_FLAG_0      0x0000      // 2 字节 
#define  WXID_FLAG_1      0x001000010000  // 6 字节

#define  WXID_PORT_0      443
#define  WXID_PORT_1      80
#define  WXID_PORT_2      8080

enum wxid_index_em{
    EM_WXID_DATA,
    EM_WXID_MAX
};


typedef struct 
{
  char data[256];
}WXIDInfo;

#endif
