/****************************************************************************************
 * 文 件 名 : dpi_wx_group_head.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 
 * 版    本 : 
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: guoy         2019/08/23
 修改: guoy         2019/08/23
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2019 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <unistd.h>

#include <glib.h>
#include <pthread.h>

#include "dpi_detect.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_http.h"

extern struct global_config g_config;

static GHashTable* wx_gh_hash = NULL;
static pthread_rwlock_t  wx_gh_rwlock = PTHREAD_RWLOCK_INITIALIZER; 

/* hash 表初始化 */
static void wx_gh_hash_init(void)
{
    pthread_rwlock_wrlock(&wx_gh_rwlock);     //写锁
    if(!wx_gh_hash) {
        wx_gh_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
        if (!wx_gh_hash) {
            DPI_LOG(DPI_LOG_ERROR, "error on create wx group head hash");
            exit(-1);
        }
    }
    pthread_rwlock_unlock(&wx_gh_rwlock);     //解锁
}

/* hash 销毁 */
static void wx_gh_hash_destory(void)
{
    pthread_rwlock_wrlock(&wx_gh_rwlock);     //写锁
    if (wx_gh_hash) {
        g_hash_table_destroy(wx_gh_hash);
        wx_gh_hash = NULL;
    }
    pthread_rwlock_unlock(&wx_gh_rwlock);     //解锁
}

/* 超时处理 */
static void* wx_gh_timeout_thread(void* arg)
{
    #define MAX_TIME_OUT_NODE 1024

    wx_gh_hash_init();

    printf("start group head timeout thread, wx_gh_hash=%p\n", wx_gh_hash);

    char* hashKey[MAX_TIME_OUT_NODE]; // 用来存储超时节点的key
    while(1)  {
        sleep(g_config.wx_session_timeloop);

        int keyTop = 0;
        int hash_count= 0;

        char* key = NULL;
        char* value = NULL;

        pthread_rwlock_rdlock(&wx_gh_rwlock); 
        GHashTableIter iter;
        g_hash_table_iter_init(&iter, wx_gh_hash); //使用迭代器遍历哈希表
        hash_count = g_hash_table_size(wx_gh_hash);
        while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&value)) {
            // 超时判断
            if(value && key) {
                uint64_t now = time(NULL);
                if(now - atoi(value) > g_config.wx_group_head_timeout && keyTop < MAX_TIME_OUT_NODE) {
                    hashKey[keyTop++] = key; // 记录待删除的key
                }
            }
        }
        pthread_rwlock_unlock(&wx_gh_rwlock);     //解锁.锁.锁.锁.锁.锁.锁.

        if(1 == g_config.debug_group_head) {
            // 打印 hash 信息
            char buffer[32];
            time_t timer;
            time(&timer);
            struct tm* tm_info = localtime(&timer);
            strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
            printf("WXGroupHeadHash:[%s] delete/total=[%d/%d]\n", buffer, keyTop, hash_count);
        }

        // 开始删除超时的节点
        pthread_rwlock_wrlock(&wx_gh_rwlock);    //写锁
        while(keyTop--) {
            int ret = g_hash_table_remove(wx_gh_hash, hashKey[keyTop]);
        }
        pthread_rwlock_unlock(&wx_gh_rwlock);    //解锁
    }

    wx_gh_hash_destory();

    return NULL;
}


void init_wx_gh_thread(void) {
    pthread_t th;
    int ret = pthread_create(&th, NULL, wx_gh_timeout_thread, NULL);
    if(ret != 0) {
        DPI_LOG(DPI_LOG_ERROR, "error on create wx group head thread");
        exit(-1);
    }
}

/* hash 添加节点 */
void wx_gh_hash_insert(char *key, void* value);
void wx_gh_hash_insert(char *key, void* value)
{
    if(!wx_gh_hash || !key || !value) {
        return;
    }

    pthread_rwlock_wrlock(&wx_gh_rwlock);     //写锁
    g_hash_table_insert(wx_gh_hash, key, value);
    pthread_rwlock_unlock(&wx_gh_rwlock);     //解锁
}

/* hash 查找节点 */
int wx_gh_hash_has_key(char *key);
int wx_gh_hash_has_key(char *key)
{
    if(!key) {
        return 0;
    }

    pthread_rwlock_rdlock(&wx_gh_rwlock);     //读锁
    int ret = g_hash_table_contains(wx_gh_hash, key);
    pthread_rwlock_unlock(&wx_gh_rwlock);     //解锁
    return ret;
}
