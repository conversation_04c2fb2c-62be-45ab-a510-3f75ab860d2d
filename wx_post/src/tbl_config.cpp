#include <set>
#include <unordered_map>
#include <iostream>
#include <getopt.h>

#include "tbl_config.h"
#include "tbl_logger.h"
#include "version.h"


TblConfig &CFG = TblConfig::getInstance();
volatile int TblConfig::workState_ = 1;

int TblConfig::setConfigPath(std::string &file_path)
{
    configPath_ = file_path;
    return 0;
}

int TblConfig::setFieldPath(std::string &file_path)
{
    fieldPath_ = file_path;
    return 0;
}

int TblConfig::loadConfig()
{
    FILE *fp = fopen(configPath_.c_str(), "r");
    if(fp == nullptr)
    {
        LOG_DEF ->error("open config file error!");
        exit(-1);
    }
    char buf[0xffff];

    fread(buf, sizeof(buf), 1 ,fp);
    fclose(fp);

    try
    {
        json_conf = json::parse(std::string(buf)); 
    }
    catch(const std::exception& e)
    {
        std::cerr << e.what() << '\n';
        exit(-1);
    }
    
    return 0;
}

/**********************************************
* 函 数 名 : loadConstant
* 功    能 : 将config常用参数解
* 输入参数 : 无
* 返 回 值 : 字段文件的内容
***********************************************/
int TblConfig::loadConstant()
{
    for(const auto &it: getKeyArray({"work_conf"}))
    {
        protoRecord_.emplace(it);

        std::string && field_name = CFG.getCurrentConfigValue({"work_conf", it, "field_name"}, std::string(""));        // 获取字段名
    
        if(!field_name.empty())
        {
            for(auto dir:CFG.getKeyArray({"work_conf", it, field_name}))                                                // 获取输出路径
            {
                allOutputDir_.emplace(dir);
            }
        }

        std::string && default_dir = CFG.getCurrentConfigValue({"output_conf", "default_dir"}, std::string());          // 获取默认输出路径
        if(!default_dir.empty())
        {
            allOutputDir_.emplace(default_dir);
        }

        std::string && invalid_dir = CFG.getCurrentConfigValue({"output_conf", "invalid_dir"}, std::string());          // 获取非正常数据输出路径
        if(!invalid_dir.empty())
        {
            allOutputDir_.emplace(invalid_dir);
        }
    }
    
    return 0;
}

int TblConfig::init()
{
    setWorkMode(1);
    loadConfig();
    loadConstant();

    LOG_DEF->debug("proto record size {}\n", CFG.protoRecord_.size());
    return 0;
}

/**********************************************
* 函 数 名 : format_check
* 功    能 : 检测json格式正确,json格式不正确则直接退出
* 输入参数 : 原始字符串;
* 返 回 值 : true/false
***********************************************/
bool
TblConfig::format_check(const std::string& str_json)
{   
    try
    {
        json_conf = json::parse(str_json);
    }
    catch(const std::exception& e)
    {
        std::cerr << e.what() << '\n';
        exit(-1);
    }
    
    return true;
}

std::vector<json> TblConfig::getCurrentArray(std::initializer_list<std::string> args)
{
    std::vector<json> v1;
    json v = json_conf;

    for(const auto &it:args)
    {
        if(v.find(it) != v.end())
        {
            v = v[it];
        }
    }
    if(v.is_array())
    {
        for(auto &it: v)
        {
            v1.push_back(it);
        }
    }
    return v1;
}

std::vector<std::string> TblConfig::getKeyArray(std::initializer_list<std::string> args)
{
    std::vector<std::string> v1;
    json v = json_conf;

    for(const auto &it:args)
    {
        if(v.find(it)!=v.end())
        {
            v = v[it];
        }
    }
    if(v.is_object())
    {
        for(auto &it: v.items())
        {
            v1.push_back(it.key());
        }
    }
    return v1;
}

void TblConfig::setWorkMode(int work_state)
{
    workState_ = work_state;
}

int TblConfig::getWorkMode()
{
    return TblConfig::workState_;
}

int TblConfig::ParseCmdLineOpts(int argc, char *argv[])
{
    const char * HELP_INFO = R"([-htvl]
    -l : log to terminal
    -h : show this help info
    -v : show version
    -t : thread num 
)";

    static struct option long_options[] = {
        {"help",    no_argument,       NULL,  'h'},
        {"log",     no_argument,       NULL,  'l'},
        {"version", no_argument,       NULL,  'v'},
        {"thread",  required_argument, NULL,  't'},
        {0,         0,                 0,  0 }
    };
    int opt  = 0;
    //const char *appName                       = "APP_NAME";
	int i;

    const char* optstring = "hlvt:";
	char core_str[256];

    // 解析命令行选项与参数
	while ((opt = getopt_long(argc, argv, optstring, long_options, NULL)) != EOF) {
		switch (opt) {
            case 't':
                i = 0;
                strncpy(core_str, optarg, sizeof(core_str));
                i = strtol(core_str, NULL, 10);
                if(!(i > 0))
                {
                    printf("Invalid arguments!\n");
                    return -1;
                }
                threadNum_ = i;
                break;

            case 'l':
                bLogToTerminal_ = true;
                //return -1;
                break;

            case 'h':
                printf("%s", HELP_INFO);
                return -1;

            case 'v':
                printf("%s %s\n", argv[0], PROJECT_VERSION_DATE_STR);
                return -1;

            case '?':
            case ':':
                printf("invalid option : %s\n", optarg);
                return -1;

            default:
                printf("%u,%s\n",opt, HELP_INFO);
                return -1;
		}
	}
   
    if (optind < argc) {
        printf("non-option ARGV-elements: ");
        while (optind < argc)
            printf("%s ", argv[optind++]);
        printf("\n");
        return -1;
    }

    return 0;
}


bool TblConfig::getLoggerOut()
{
    return !bLogToTerminal_;
}

uint16_t TblConfig::getWorkThreadNum()
{
    return threadNum_;
}