/****************************************************************************************
 * 文 件 名 : dpi_wx_msg.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: zhengquan  		2020/01/06
编码: zhengquan			2020/01/06
修改: chenzq            2020/12/16
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#ifndef _DPI_WX_MSG_H_
#define _DPI_WX_MSG_H_

#include "wxcs_def.h"
#include "dpi_log.h"
#include "dpi_common.h"

//wx语音发送识别长度
#define WX_VOICE_SEND_FLAG_LEN          185

#define WX_TXT_MSG_FLAG_LEN             149
//wx语音接收识别长度
#define WX_VOICE_RECV_FLAG_LEN          2500
//去重时间间隔
#define WX_TIME_INTERVAL                5
//微信消息用户表老化时间
#define WX_MSG_USER_OLD                 600

enum _wx_action_index_em {
    EM_SEND_RED_PACKET  = 1, 
    EM_SEND_VOICE_MSG   = 2,
    EM_RECV_VOICE_MSG   = 3,
    EM_SEND_TXT_MSG     = 4,
    EM_ACTION_MAX
};

typedef struct user_head_t
{
	uint64_t IMSI;
	uint64_t MSISDN;
	uint64_t IMEI;
	uint32_t UserIp;
	uint16_t PLMNID;
	uint16_t TAC;
}UserHead;

//每个时间格内的每一个元素
typedef struct lattice_elem_t
{
    uint64_t time_stamp;//毫秒
    uint32_t seq;//tcp请求码
    uint16_t length;
    uint8_t c2s;
}LatEle;

//协议解析结构
typedef struct wx_action_info_t
{
    uint8_t c2s;
    uint8_t action_flag;
    uint16_t length;
    uint32_t time_stamp;
    uint32_t hash_key;
    uint32_t content_length;
    char  uri_buff[2048];
    ST_trailer trailer;
}WxActionInfo;

//数据处理结构
typedef struct wx_action_data_t
{
    UserHead *head;
    uint8_t  action_flag;
    //uint16_t content_length;
    uint32_t time;
    pthread_rwlock_t wxtm_rwlock;
}WxActionData;

//数据处理结构
typedef struct WxMsgData
{
    uint8_t  action_flag;
    //uint16_t content_length;
    uint32_t time;
}WxMsgData;

//数据处理结构
typedef struct wx_red_packet_data_t
{
    // WxActionData data;
    uint32_t time;
    uint16_t content_length;
    uint8_t  action_flag;
    char     uri_buff[1024];
    // pthread_rwlock_t wxtm_rwlock;
}WxRPData;

//数据处理结构
typedef struct wx_recv_voice_data_t
{
    UserHead head;
    uint32_t time;
    uint8_t  action_flag;
}WxRecvVoiceData;

enum _wxtm_index_em {
    EM_WXTM_START_TIME,
	EM_WXTM_ACTION,
	EM_WXTM_MAX
};

typedef struct dpi_hash_t {
	void *hash;
	pthread_rwlock_t rwlock;
}DpiHash;

//wx 发红包
struct dpi_hash_t g_wx_voice_hash;
struct dpi_hash_t g_wx_rp_hash;

extern struct rte_mempool *tbl_log_mempool;

void wx_action_red_packet(struct flow_info *flow, int direction, uint32_t key, WxActionInfo *info);

#endif
