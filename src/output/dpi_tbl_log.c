/****************************************************************************************
 * 文 件 名 : dpi_tbl_log.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <rte_ring.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_proto_ids.h"  // add by liugh
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_protorecord.h"
#include "dpi_precord_writer_IF.h"
#include "dpi_utils.h"
#include "wxcs_def.h"
#include "dpi_trailer.h"

#define TBL_RING_MAX_NUM 4
#define TBL_RING_SIZE (4096 * 16)
#define TBL_MAX_BURST 512
#define TBL_FILE_TIMEOUT 30


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
struct rte_mempool *tbl_log_content_mempool_256k;

struct        tbl_log_file tbl_log_array[TBL_LOG_MAX];
static struct rte_ring    *tbl_ring[TBL_RING_MAX_NUM];
extern uint64_t            log_total[TBL_LOG_MAX][TRAFFIC_NUM];
int tbl_log_queue_num = 2;
const char *empty_data = "\"\"|";

void register_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func )
{
	tbl_log_array[type].type=type;
	tbl_log_array[type].protoname=name;
	tbl_log_array[type].init_func=func;
}

dpi_field_table dpi_common_field[] = {
    DPI_FIELD_D(EM_COMMON_TAGTYPE,               EM_F_TYPE_STRING,              "TAGTYPE"),
    DPI_FIELD_D(EM_COMMON_TEID,                  EM_F_TYPE_STRING,              "TEID"),
    DPI_FIELD_D(EM_COMMON_MSISDN,                EM_F_TYPE_STRING,              "MSISDN"),
    DPI_FIELD_D(EM_COMMON_IMSI,                  EM_F_TYPE_STRING,              "IMSI"),
    DPI_FIELD_D(EM_COMMON_IMEI,                  EM_F_TYPE_STRING,              "IMEI"),
    DPI_FIELD_D(EM_COMMON_TAC,                   EM_F_TYPE_STRING,              "TAC"),
    DPI_FIELD_D(EM_COMMON_OPERATOR,              EM_F_TYPE_STRING,              "OPERATOR"),
    DPI_FIELD_D(EM_COMMON_DEVNAME,               EM_F_TYPE_STRING,              "DEVNAME"),
    DPI_FIELD_D(EM_COMMON_AREA,                  EM_F_TYPE_STRING,              "AREA"),
    DPI_FIELD_D(EM_COMMON_HW_BFLAGS,             EM_F_TYPE_STRING,              "HW_BFALGS"),
    DPI_FIELD_D(EM_COMMON_HW_APN,                EM_F_TYPE_STRING,              "HW_APN"),
    DPI_FIELD_D(EM_COMMON_HW_NCODE,              EM_F_TYPE_STRING,              "HW_NCODE"),
    DPI_FIELD_D(EM_COMMON_HW_ECGI,               EM_F_TYPE_STRING,              "HW_ECGI"),
    DPI_FIELD_D(EM_COMMON_HW_LAC,                EM_F_TYPE_STRING,              "HW_LAC"),
    DPI_FIELD_D(EM_COMMON_HW_SAC,                EM_F_TYPE_STRING,              "HW_SAC"),
    DPI_FIELD_D(EM_COMMON_HW_CI,                 EM_F_TYPE_STRING,              "HW_CI"),
    DPI_FIELD_D(EM_COMMON_RT_PLMN_ID,            EM_F_TYPE_STRING,              "RT_PLMN_ID"),
    DPI_FIELD_D(EM_COMMON_RT_ULI,                EM_F_TYPE_STRING,              "RT_ULI"),
    DPI_FIELD_D(EM_COMMON_RT_BS,                 EM_F_TYPE_STRING,              "RT_BS"),
    DPI_FIELD_D(EM_COMMON_RT_DNS,                EM_F_TYPE_STRING,              "RT_DNS"),
    // DPI_FIELD_D(EM_COMMON_FIX_ACCOUNT,           EM_F_TYPE_STRING,              "FIX_ACCOUNT"),
    // DPI_FIELD_D(EM_COMMON_DIRECTION,             EM_F_TYPE_STRING,              "DIRECTION"),
    // DPI_FIELD_D(EM_COMMON_LABEL,                 EM_F_TYPE_STRING,              "LABEL"),
    // DPI_FIELD_D(EM_COMMON_RULEID,                EM_F_TYPE_STRING,              "RULEID"),

    DPI_FIELD_D(EM_COMMON_DEVNO,				 EM_F_TYPE_EMPTY,				 "DevNo"),
    DPI_FIELD_D(EM_COMMON_LINENO,				 EM_F_TYPE_EMPTY,				 "LineNo"),
    DPI_FIELD_D(EM_COMMON_LINKLAYERTYPE,	     EM_F_TYPE_STRING,				 "LinkLayerType"),
    DPI_FIELD_D(EM_COMMON_ISIPV6,				 EM_F_TYPE_EMPTY,				 "isIPv6"),
    DPI_FIELD_D(EM_COMMON_ISMPLS,				 EM_F_TYPE_EMPTY,				 "isMPLS"),
    DPI_FIELD_D(EM_COMMON_NLABEL,				 EM_F_TYPE_EMPTY,				 "nLabel"),
    DPI_FIELD_D(EM_COMMON_INNERLABEL,			 EM_F_TYPE_EMPTY,				 "innerLabel"),
    DPI_FIELD_D(EM_COMMON_OTHERLABEL,			 EM_F_TYPE_EMPTY,				 "otherLabel"),
    DPI_FIELD_D(EM_COMMON_RESV1,				 EM_F_TYPE_EMPTY,				 "resv1"),
    DPI_FIELD_D(EM_COMMON_RESV2,				 EM_F_TYPE_EMPTY,				 "resv2"),
    DPI_FIELD_D(EM_COMMON_RESV3,				 EM_F_TYPE_EMPTY,				 "resv3"),
    DPI_FIELD_D(EM_COMMON_RESV4,				 EM_F_TYPE_EMPTY,				 "resv4"),
    DPI_FIELD_D(EM_COMMON_RESV5,				 EM_F_TYPE_EMPTY,				 "resv5"),
    DPI_FIELD_D(EM_COMMON_RESV6,				 EM_F_TYPE_EMPTY,				 "resv6"),
    DPI_FIELD_D(EM_COMMON_RESV7,				 EM_F_TYPE_EMPTY,				 "resv7"),
    DPI_FIELD_D(EM_COMMON_RESV8,				 EM_F_TYPE_EMPTY,				 "resv8"),
    DPI_FIELD_D(EM_COMMON_CAPDATE,				 EM_F_TYPE_STRING,				 "CapDate"),
    DPI_FIELD_D(EM_COMMON_SRCIP,				 EM_F_TYPE_STRING,				 "SrcIp"),
    DPI_FIELD_D(EM_COMMON_SRCCOUNTRY,			 EM_F_TYPE_EMPTY,				 "SrcCountry"),
    DPI_FIELD_D(EM_COMMON_SRCAREA,				 EM_F_TYPE_EMPTY,				 "SrcArea"),
    DPI_FIELD_D(EM_COMMON_SRCCITY,				 EM_F_TYPE_EMPTY,				 "SrcCity"),
    DPI_FIELD_D(EM_COMMON_SRCCARRIER,			 EM_F_TYPE_EMPTY,				 "SrcCarrier"),
    DPI_FIELD_D(EM_COMMON_DSTIP,				 EM_F_TYPE_STRING,				 "DstIp"),
    DPI_FIELD_D(EM_COMMON_DSTCOUNTRY,			 EM_F_TYPE_EMPTY,				 "DstCountry"),
    DPI_FIELD_D(EM_COMMON_DSTAREA,				 EM_F_TYPE_EMPTY,				 "DstArea"),
    DPI_FIELD_D(EM_COMMON_DSTCITY,				 EM_F_TYPE_EMPTY,				 "DstCity"),
    DPI_FIELD_D(EM_COMMON_DSTCARRIER,			 EM_F_TYPE_EMPTY,				 "DstCarrier"),
    DPI_FIELD_D(EM_COMMON_SRCPORT,				 EM_F_TYPE_UINT16,				 "SrcPort"),
    DPI_FIELD_D(EM_COMMON_DSTPORT,				 EM_F_TYPE_UINT16,				 "DstPort"),
    DPI_FIELD_D(EM_COMMON_C2S,				     EM_F_TYPE_STRING,				 "C2S"),
    DPI_FIELD_D(EM_COMMON_PROTO,				 EM_F_TYPE_UINT8,				 "Proto"),
    DPI_FIELD_D(EM_COMMON_TTL,				     EM_F_TYPE_UINT8,				 "TTL"),
	DPI_FIELD_D(EM_COMMON_IP_FLAG,				 EM_F_TYPE_STRING,				 "IPFlag"),
};


int detect_file_type(uint32_t header, char *buff)
{
	if(buff==NULL){return 0;}

	uint32_t header_3bytes=(header & 0xffffff00) >> 8;
	uint32_t header_2bytes=(header & 0xffff0000) >> 16;
	switch(header_2bytes){
		case 0xffd8:
			strncpy(buff,"JPG",3);
			return 1;
	}

	switch(header_3bytes)
	{
		case 0x255044:
			strncpy(buff,"PDF",3);
			break;
		case 0xd0cf11:
			strncpy(buff,"PPT",3);
			break;
		case 0x424d3e:
			strncpy(buff,"BMP",3);
			break;
		case 0x89504e:
			strncpy(buff,"PNG",3);
			break;
		case 0x526172:
			strncpy(buff,"RAR",3);
			break;
		case 0x504b03:
			strncpy(buff,"ZIP",3);
			break;
		case 0xfffb50:
			strncpy(buff,"MP3",3);
			break;
		default:
			break;
	}

	return 1;
}


int write_n_empty_reconds(char *log, int *idx, int max_len, int n)
{
	int i;
	int empty_len = strlen(empty_data);
	for (i = 0; i < n; i++) {
		if (*idx + empty_len < max_len) {
			memcpy(log + *idx, empty_data, empty_len);
			*idx += empty_len;
		}
	}

	return 0;
}



int write_one_str_reconds(char *log, int *idx, int max_len, const char *data, unsigned int data_len)
{
	unsigned int i;
	int index = *idx;

	if (index < max_len)
    {
		log[index++] = '"';
    }

	if (data_len > g_config.per_fields_len)
    {
		data_len = g_config.per_fields_len;
    }

	for (i = 0; i < data_len; i++)
    {
		if (index >= max_len)
			break;

        if(data == NULL)
            break;

        if(data[i]==0x00)
            break;

		if (data[i] == '|')
        {
			log[index++] = '_';
        }
		else
        if(data[i] == '\n' || data[i] == '\r')
        {
			log[index++] = ' ';
        }
		else
        {
			log[index++] = data[i];
        }
	}

	if (index + 2 < max_len)
    {
		log[index++] = '"';
		log[index++] = '|';
	}

	*idx = index;
	return 0;
}

int write_one_num_reconds(char *log, int *idx, int max_len, uint32_t data)
{
	int ret = snprintf(log + *idx, max_len - *idx, "\"%u\"|", data);
	*idx += ret;
	return 0;
}

/* add by liugh */
int write_uint64_reconds(char *log, int *idx, int max_len, uint64_t data)
{
    int ret = snprintf(log + *idx, max_len - *idx, "\"%lu\"|", data);
    *idx += ret;
    return 0;
}

/* add by liugh
 * 为什么第四个参数是uint8_t 类型而不是char类型：
 * 因为char类型在输出二进制时会在前面补充FF
 */
int write_multi_num_reconds(char *log, int *idx, int max_len, const uint8_t *data,uint32_t data_len)
{
    int i,ret;
    if(data==NULL || (int)data_len>max_len){
        write_n_empty_reconds(log, idx, max_len, 1);
		return 0;
    }
    ret = snprintf(log + *idx, max_len - *idx, "\"");
    *idx += ret;
    for(i=0;i<(int)data_len;i++){
        ret = snprintf(log + *idx, max_len - *idx, "%02x", data[i]);
        *idx += ret;
    }
    ret=snprintf(log + *idx, max_len - *idx, "\"|");
    *idx += ret;
    return 0;
}


int write_coupler_log(char *log, int *idx, int max_len, uint8_t data_type,const uint8_t *data,uint64_t int_data)
{

	switch(data_type){
		case EM_F_TYPE_EMPTY:
			write_n_empty_reconds(log, idx, max_len, (int)int_data);
			break;
		case EM_F_TYPE_UINT8:
			write_one_num_reconds(log, idx, max_len, (uint8_t)int_data);
			break;
		case EM_F_TYPE_UINT16:
			write_one_num_reconds(log, idx, max_len, (uint16_t)int_data);
			break;
		case EM_F_TYPE_UINT32:
			write_one_num_reconds(log, idx, max_len, (uint32_t)int_data);
			break;
		case EM_F_TYPE_UINT64:
			write_uint64_reconds(log, idx, max_len, (uint64_t)int_data);
			break;
		case EM_F_TYPE_STRING:
			write_one_str_reconds(log, idx, max_len, (const char *)data, (uint32_t)int_data);
			break;
		case EM_F_TYPE_HEX:
			write_multi_num_reconds(log, idx, max_len, data,( uint32_t)int_data);
			break;
		default:
		    break;
	}

	return 0;
}


/*
*rt标签的解析
*/
int write_rtl_reconds(char *log, int *idx, int max_len, struct flow_info *flow)
{
	int trailer_nums = 0;
    // 空白字段要写多少个
	if (flow->has_trailer == 0)
    {
		write_n_empty_reconds(log, idx, max_len, 20);
		return 0;
	}

	int i;
	uint64_t res    = 0;
	char _str[1024] = {0};
	char ULI[512]   = {0};
	const char *p   = NULL;

	ST_trailer trailer;
	memset(&trailer, 0, sizeof(trailer));
	dpi_TrailerParser(&trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
	dpi_TrailerGetMAC(&trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
	dpi_TrailerGetHWZZMAC(&trailer, (const char *)flow->ethhdr);
	dpi_TrailerSetDev(&trailer, g_config.devname);        // 解析板号
	dpi_TrailerSetOpt(&trailer, g_config.operator_name);  // 运营商
	dpi_TrailerSetArea(&trailer, g_config.devArea);       // 地域名

	switch(trailer.trailerType) {
		case TRAILER_ZXSK:
			trailer_nums = dpi_trailer_write_zxsk(log, max_len, &trailer);
			*idx += trailer_nums;
			return 0;
		default:
			break;
	}

	dpi_TrailerECI(&trailer, ULI, sizeof(ULI));           // ECI 转换


	snprintf(_str, sizeof(_str), "%u", trailer.trailerType);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

    snprintf(_str, sizeof(_str), "0x%08X", ntohl(trailer.TEID)); // 大端显示
    p = (0==trailer.TEID?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

	if (trailer.MSISDN != 0) {
		snprintf(_str, sizeof(_str), "%lu", trailer.MSISDN);
		write_one_str_reconds(log, idx, max_len, _str, strlen(_str));
	} else {
		write_one_str_reconds(log, idx, max_len, trailer.fixed_account, strlen(trailer.fixed_account));
	}

	snprintf(_str, sizeof(_str), "%lu", trailer.IMSI);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	snprintf(_str, sizeof(_str), "%lu", trailer.IMEI);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.TAC)); // 大端显示
	p = (0==trailer.TAC?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

	snprintf(_str, sizeof(_str), "%s", trailer.Operator);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	snprintf(_str, sizeof(_str), "%s", trailer.DevName);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	snprintf(_str, sizeof(_str), "%s", trailer.Area);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

    snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.BFLAG);
    p = (0==trailer.BFLAG?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

	snprintf(_str, sizeof(_str), "%s", trailer.APN);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

    snprintf(_str, sizeof(_str), "0x%02X", (unsigned char)trailer.NCODE);
    p = (0==trailer.NCODE?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

    snprintf(_str, sizeof(_str), "0x%08x", ntohl(trailer.ECGI));// 大端显示
    p = (0==trailer.ECGI?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.LAC)); // 大端显示
    p = (0==trailer.LAC?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.SAC)); // 大端显示
    p = (0==trailer.SAC?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.CI)); // 大端显示
    p = (0==trailer.CI?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

  if(g_config.trailertype == TRAILER_HWZZ){
  snprintf(_str, sizeof(_str), "%04x", ntohs(trailer.PLMN_ID));  //大端显示
    p = (0 == trailer.PLMN_ID ? "" : _str);
  }else {
    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.PLMN_ID));  //大端显示
    p = (0 == trailer.PLMN_ID ? "" : _str);
  }
  write_one_str_reconds(log, idx, max_len, p, strlen(p));

    snprintf(_str, sizeof(_str), "%s", ULI);
    p = (0==trailer.ULI?"":_str);
	write_one_str_reconds(log, idx, max_len, p, strlen(p));

	if (trailer.rat != 0 ) {
    snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.rat);
    p = (0==trailer.rat?"":_str);
	} else {
    snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.BS);
    p = (0==trailer.BS?"":_str);
	}
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	snprintf(_str, sizeof(_str), "%s", trailer.DomainName);
	write_one_str_reconds(log, idx, max_len, _str, strlen(_str));

	// write_one_str_reconds(log, idx, max_len, trailer.fixed_account, strlen(trailer.fixed_account));
	// write_one_num_reconds(log, idx, max_len, trailer.direction);
	// write_one_str_reconds(log, idx, max_len, trailer.label, strlen(trailer.label));
	// write_one_str_reconds(log, idx, max_len, trailer.ruleID, strlen(trailer.ruleID));
	return 0;
}

void write_tbl_log_common(struct flow_info *flow, int direction, char *log_content, int *idx, int log_len_max)
{
	char __str[64] = {0};
	int i;

	write_rtl_reconds(log_content, idx, log_len_max, flow);

	for(i=EM_COMMON_DEVNO;i<EM_COMMON_MAX;i++){
		switch(dpi_common_field[i].index){
		case EM_COMMON_LINKLAYERTYPE:
			write_one_str_reconds(log_content, idx, log_len_max, g_config.devname, strlen(g_config.devname));
			break;
		case EM_COMMON_ISIPV6:
			if (flow->ip_version == 4)
				write_one_num_reconds(log_content, idx, log_len_max, 0);
			else
				write_one_num_reconds(log_content, idx, log_len_max, 1);
			break;
		case EM_COMMON_CAPDATE:
			timet_to_datetime(flow->pkt_rx_timestamp_first/1000000, __str, sizeof(__str));
			write_one_str_reconds(log_content, idx, log_len_max, __str, strlen(__str));
			break;
		case EM_COMMON_SRCIP:
			if (direction == FLOW_DIR_SRC2DST){
				if (flow->ip_version == 4)
					get_ip4string(__str, sizeof(__str), flow->tuple.inner.ip_src.ip4);
				else
					get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_src.ip6);
			}else{
				if (flow->ip_version == 4)
					get_ip4string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src.ip4);
				else
					get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src.ip6);
			}

			write_one_str_reconds(log_content, idx, log_len_max, __str, strlen(__str));
			break;
		case EM_COMMON_DSTIP:
			if (direction == FLOW_DIR_SRC2DST){
				if (flow->ip_version == 4)
					get_ip4string(__str, sizeof(__str), flow->tuple.inner.ip_dst.ip4);
				else
					get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst.ip6);
			}else{
				if (flow->ip_version == 4)
					get_ip4string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst.ip4);
				else
					get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst.ip6);
			}
			write_one_str_reconds(log_content, idx, log_len_max, __str, strlen(__str));
			break;
		case EM_COMMON_SRCPORT:
			if (direction == FLOW_DIR_SRC2DST) {
				write_one_num_reconds(log_content, idx, log_len_max, ntohs(flow->tuple.inner.port_src));
			} else {
				write_one_num_reconds(log_content, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_src));
			}
			break;
		case EM_COMMON_DSTPORT:
			if (direction == FLOW_DIR_SRC2DST) {
				write_one_num_reconds(log_content, idx, log_len_max, ntohs(flow->tuple.inner.port_dst));
			} else {
				write_one_num_reconds(log_content, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_dst));
			}
			break;
		case EM_COMMON_C2S:
			if (direction == flow->direction)
				write_one_str_reconds(log_content, idx, log_len_max, "C2S", strlen("C2S"));
			else
				write_one_str_reconds(log_content, idx, log_len_max, "S2C", strlen("S2C"));
			break;
		case EM_COMMON_PROTO:
			write_one_num_reconds(log_content, idx, log_len_max, flow->tuple.inner.proto);
			break;
		case EM_COMMON_TTL:
			write_one_num_reconds(log_content, idx, log_len_max, flow->ttl);
			break;
		case EM_COMMON_IP_FLAG:
			write_one_str_reconds(log_content, idx, log_len_max, (char*)flow->ip_flag, strlen((char*)flow->ip_flag));
			break;
        case EM_COMMON_RESV1:
			write_n_empty_reconds(log_content, idx, log_len_max, 1);
            break;
		default:
			write_n_empty_reconds(log_content, idx, log_len_max, 1);
			break;
		}
	}
}

int write_proto_field_tab(dpi_field_table *proto_array,int max_len,const char *proto_name)
{
	if(proto_name==NULL || proto_array==NULL){return 0;}

	int i;
	char file_path[TBL_PATH_LENGTH]={0};
	snprintf(file_path,TBL_PATH_LENGTH,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);

    /* 路径是否可读 */
    struct stat File1Stat;
    if (0 != stat(g_config.dpi_field_dir, &File1Stat))
    {
        mkdirs(g_config.dpi_field_dir);        /* 创建目录 */
    }

	FILE *fp=fopen(file_path,"w+");
	if(fp){
		/* common field */
		for(i=0;i<(int)(sizeof(dpi_common_field)/sizeof(dpi_common_field[0]));i++){
			fwrite(dpi_common_field[i].field_name,strlen(dpi_common_field[i].field_name),1,fp);
			fwrite("\n",1,1,fp);
		}

		for(i=0;i< max_len;i++){
			fwrite(proto_array[i].field_name,strlen(proto_array[i].field_name),1,fp);
			fwrite("\n",1,1,fp);
		}

		fclose(fp);
	}
	return 1;
}


int write_proto_field_without_common(dpi_field_table *proto_array,
                                     int max_len,
                                     const char *proto_name)
{
	if(proto_name==NULL || proto_array==NULL){return 0;}

	int i;
	char file_path[TBL_PATH_LENGTH]={0};
	snprintf(file_path,TBL_PATH_LENGTH,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);

    /* 路径是否可读 */
    struct stat File1Stat;
    if (0 != stat(g_config.dpi_field_dir, &File1Stat))
    {
        mkdirs(g_config.dpi_field_dir);        /* 创建目录 */
    }

	FILE *fp=fopen(file_path,"w+");
	if(fp){
		// /* common field */
		// for(i=0;i<(int)(sizeof(dpi_common_field)/sizeof(dpi_common_field[0]));i++){
		// 	fwrite(dpi_common_field[i].field_name,strlen(dpi_common_field[i].field_name),1,fp);
		// 	fwrite("\n",1,1,fp);
		// }

		for(i=0;i< max_len;i++){
			fwrite(proto_array[i].field_name,strlen(proto_array[i].field_name),1,fp);
			fwrite("\n",1,1,fp);
		}

		fclose(fp);
	}

	return 1;
}


int get_now_datetime(char *time_str, int len)
{
	time_t now = g_config.g_now_time;
	struct tm tm_tmp;
	localtime_r(&now, &tm_tmp);

	snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
						tm_tmp.tm_year + 1900,
						tm_tmp.tm_mon + 1,
						tm_tmp.tm_mday,
						tm_tmp.tm_hour,
						tm_tmp.tm_min,
						tm_tmp.tm_sec);
	return 0;
}

// static const char *time_to_datetime(time_t t)
// {
// 	static char time_str[64];
// 	struct tm tm_tmp;
// 	localtime_r(&t, &tm_tmp);

// 	snprintf(time_str, sizeof(time_str), "%04d%02d%02d%02d%02d%02d",
// 						tm_tmp.tm_year + 1900,
// 						tm_tmp.tm_mon + 1,
// 						tm_tmp.tm_mday,
// 						tm_tmp.tm_hour,
// 						tm_tmp.tm_min,
// 						tm_tmp.tm_sec);
// 	return time_str;
// }

int get_ip4string(char *__str, int len, uint32_t ip)
{
	uint8_t *tmp = (uint8_t *)&ip;
	snprintf(__str, len, "%u.%u.%u.%u", tmp[0], tmp[1], tmp[2], tmp[3]);
	return 0;
}


int get_ip6string(char *__str, int len, const uint8_t *ip)
{
	struct in6_addr addr;
	memcpy(&addr, ip, 16);
	inet_ntop(AF_INET6, &addr, __str, len);

	return 0;
}

/* add by liugh */
int get_iparray_to_string(char *__str, int len,const uint8_t* ip)
{
    if(ip==NULL || ip+1==NULL || ip+2==NULL || ip+3==NULL){return 0;}
    snprintf(__str, len, "%u.%u.%u.%u", ip[0], ip[1], ip[2], ip[3]);

    return 0;
}


static int
tbl_file_close(int type, int tid)
{
	char   filename[128];
	char   filename_rename[128];

	if (tbl_log_array[type].tbl[tid].fp_tbl)
    {
		snprintf(filename,        sizeof(filename),        "%s.tbl.writing", tbl_log_array[type].tbl[tid].filename);
		snprintf(filename_rename, sizeof(filename_rename), "%s.tbl",         tbl_log_array[type].tbl[tid].filename);
		fclose(tbl_log_array[type].tbl[tid].fp_tbl);
		rename(filename, filename_rename);
        tbl_log_array[type].tbl[tid].fp_tbl  = NULL;
		tbl_log_array[type].tbl[tid].log_num = 0;
	}
    return 0;
}

static int
tbl_file_open(int type, int tid)
{
	char   filename[128];
	char   filename_rename[128];
	char   dirname[128];
    struct timeval tv;

	if (tbl_log_array[type].tbl[tid].fp_tbl == NULL)
    {
		snprintf(dirname, sizeof(dirname), "%s/%s", g_config.tbl_out_dir, tbl_log_array[type].protoname);
		if (access(dirname, F_OK))
        {
			mkdir(dirname, 0666);
        }

        gettimeofday(&tv, NULL);
		snprintf(tbl_log_array[type].tbl[tid].filename, sizeof(tbl_log_array[type].tbl[tid].filename), "%s/%s/%s_%06ld_%s_%03d",
				g_config.tbl_out_dir,
                tbl_log_array[type].protoname,
                time_to_datetime(tv.tv_sec),
                tv.tv_usec,
                tbl_log_array[type].protoname,
                tid);

		snprintf(filename, sizeof(filename), "%s.tbl.writing", tbl_log_array[type].tbl[tid].filename);

		tbl_log_array[type].tbl[tid].fp_tbl = fopen(filename, "w");
		if (tbl_log_array[type].tbl[tid].fp_tbl == NULL)
        {
			DPI_LOG(DPI_LOG_ERROR, "erro while fopen tbl log file");
            return -1;
        }

        // 记录文件的创建时间
		tbl_log_array[type].tbl[tid].ts = time(NULL);
	}
    return 0;
}

static void
tbl_file_timeout(void)
{
    for (int i = 0; i < TBL_LOG_MAX; i++)
    {
        for(int j = 0; j < (int)g_config.dissector_thread_num; j++)
        {
            if (tbl_log_array[i].tbl[j].fp_tbl && tbl_log_array[i].tbl[j].log_num > g_config.log_max_num)
            {
                tbl_file_close(i, j);
            }
            else
            if (tbl_log_array[i].tbl[j].fp_tbl && (time(NULL) - tbl_log_array[i].tbl[j].ts) > g_config.write_tbl_maxtime)
            {
                tbl_file_close(i, j);
            }
        }
    }
}


static
void http_write_v51(struct tbl_log *log)
{
	int ret = 0;
	int i = 0;
	if (tbl_log_array[log->type].tbl[log->tid].fp_tbl) {
		fwrite(log->log_content, log->len, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
		uint32_t len = (NULL == log->content_ptr ? 0 : strlen((const char *)log->content_ptr));

		if (log->content_ptr && len > 0 && (0 != memcmp(log->content_ptr, "ERR_", 4) && 0 != memcmp(log->content_ptr, "HTTP_BODY_FORMAT", 15)))
		{
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite(log->content_ptr, len, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
		}
		else
		if (log->content_ptr && len > 0 && (0 == memcmp(log->content_ptr, "ERR_", 4) || 0 == memcmp(log->content_ptr, "HTTP_BODY_FORMAT", 15)))
		{
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite(log->content_ptr, len, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
		}
		else
		{
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
		}

		for (i = 0; i < KV53_KV99_LEN; i++)
		{
			fwrite("|", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
		}

		fwrite("\n", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);

		log_total[TBL_LOG_HTTP][0]++;
		// tbl_log_array[log->log_type].log_num[log->thread_id]++;
	}

	if (log->content_len > 0 && log->content_ptr)
	{
		rte_mempool_put(tbl_log_content_mempool_256k, (void *)log->content_ptr);
	}
}

static void
write_file(struct tbl_log *log)
{
	int i;
	int ret;

    if(log->type>=TBL_LOG_MAX || log->type<0)
    {
        return;
    }

    if(log->tid>(int)g_config.dissector_thread_num || log->tid<0 ){
        return;
    }

	ret = tbl_file_open(log->type, log->tid);
    if(ret < 0)
    {
       return;
    }



	switch (log->type)
    {
		case TBL_LOG_HTTP:
		case TBL_LOG_HTTP_WXPH:
			http_write_v51(log);
			break;
		default:
		{
			if (tbl_log_array[log->type].tbl[log->tid].fp_tbl)
            {
				fwrite(log->log_content, log->len, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
				fwrite("\n", 1, 1, tbl_log_array[log->type].tbl[log->tid].fp_tbl);
				log_total[log->type][0]++;
				tbl_log_array[log->type].tbl[log->tid].log_num++;
			}
		}
        break;
	}
    fflush(tbl_log_array[log->type].tbl[log->tid].fp_tbl);
	return;
}




/*
*解析线程写日志的调用接口
*/
int tbl_log_enqueue(struct    tbl_log *log)
{
	int enqueue_num;

	enqueue_num = rte_ring_mp_enqueue_burst(tbl_ring[0], (void * const*)&log, 1, NULL);
	if (enqueue_num < 1)
    {
		DPI_LOG(DPI_LOG_WARNING, "enqueue packet ring error\n");
		return -1;
	}

	return 1;
}

// /*
// *解析线程写日志的调用接口
// */
// int write_tbl_log(struct    tbl_log *log)
// {
//     int enqueue_num;

//     enqueue_num = rte_ring_mp_enqueue_burst(tbl_ring[log->thread_id % g_config.log_thread_num], (void * const*)&log, 1, NULL);
//     if (enqueue_num < 1) {
// //    if ((enqueue_num & RTE_RING_SZ_MASK) != 1) {
//         // rte_atomic64_inc(&tbl_fail_pkts);
//         // rte_atomic64_add(&tbl_fail_bytes, log->log_len);
//         DPI_LOG(DPI_LOG_WARNING, "enqueue packet ring error, tbl ring %d", log->log_type);
//         return -1;
//     }

//     return 1;
// }

/*
*日志线程的处理函数
*/
void *write_tbl_log_to_file_func(void * arg)
{
	UNUSED(arg);
	int i, j;
	int dequeue_num;
	int dequeue_num_total;
	long ring_id = (long)arg;
	struct tbl_log *tbl;
	struct tbl_log *tbl_burst[TBL_MAX_BURST];

	int core_affinity = g_config.log_core_id[ring_id];

	cpu_set_t cpuset;
	CPU_ZERO(&cpuset);
	CPU_SET(core_affinity, &cpuset);

	if(pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0)
		DPI_LOG(DPI_LOG_ERROR, "Error while binding log thread to core %d", core_affinity);
	DPI_LOG(DPI_LOG_DEBUG, "Running log thread on core %d", core_affinity);

	while (1)
    {
		// dequeue_num_total = 0;
		// for (i = 0; i < tbl_log_queue_num; i++)
    //     {
			dequeue_num = rte_ring_sc_dequeue_burst(tbl_ring[ring_id], (void **)tbl_burst, TBL_MAX_BURST, NULL);
			if (dequeue_num > 0)
            {
				for (j = 0; j < dequeue_num; j++)
                {
					tbl = tbl_burst[j];
					write_file(tbl);
					rte_mempool_put(tbl_log_mempool, (void *)tbl);
				}
			}
			// dequeue_num_total += dequeue_num;
		// }

        // 超时关闭文件
        tbl_file_timeout();
        usleep(1000);
	}
}


static int init_tbl_log_mempool(void)
{
	if (tbl_log_mempool == NULL) {
		tbl_log_mempool = rte_mempool_create("tbl_log_mp", g_config.tbl_ring_size * 2,
											sizeof(struct tbl_log), RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
											NULL, NULL,
											NULL, NULL,
											SOCKET_ID_ANY,
											0);
											//MEMPOOL_F_NO_CACHE_ALIGN);

		if (tbl_log_mempool == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot init tbl_log_mempool");
			exit(-1);
		} else
			DPI_LOG(DPI_LOG_DEBUG, "Allocated tbl_log_mempool");
	}

	/*有内容的日志分成两个内存池，大小分别是4k（多）和256k（少），写内容时按内容的大小分配*/
    if (tbl_log_content_mempool_256k == NULL) {
        tbl_log_content_mempool_256k = rte_mempool_create("tbl_log_content_mp_256k", g_config.tbl_log_content_256k_num,
                                            g_config.tbl_log_content_256k, RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
                                            NULL, NULL,
                                            NULL, NULL,
                                            SOCKET_ID_ANY,
                                            0);
                                            //MEMPOOL_F_NO_CACHE_ALIGN);

        if (tbl_log_content_mempool_256k == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot init tbl_log_content_mempool_256k");
            exit(-1);
        } else
            DPI_LOG(DPI_LOG_DEBUG, "Allocated tbl_log_content_mempool_256k");
    }

	return 0;
}

int init_tbl_log(void)
{
	int i = 0;
	// if (tbl_log_queue_num > TBL_RING_MAX_NUM)
	// 	tbl_log_queue_num = TBL_RING_MAX_NUM;

	// for (i = 0; i < .; i++) {
		char ring_name[64] = {0};
		snprintf(ring_name, sizeof(ring_name), "tbl_ring_%d", i);
		tbl_ring[i] = rte_ring_create(ring_name, TBL_RING_SIZE, SOCKET_ID_ANY, RING_F_SC_DEQ);
		if (tbl_ring[i] == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "error while create tbl ring\n");
			return -1;
		}
		if (rte_ring_lookup(ring_name) != tbl_ring[i]) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name\n");
			return -1;
		}
	// }

	if (access(g_config.tbl_out_dir, F_OK))
		mkdir(g_config.tbl_out_dir, 0666);

	init_tbl_log_mempool();

	return 0;
}


int get_filename(char *path_name, char *name)
{
    if(path_name==NULL || name==NULL || strlen(path_name)<=0 ){
        return 0;
    }
    int i=0,k=0;
    for(i=strlen(path_name);i>=0;i--){
        if(path_name[i]!='/'){
            k++;
        }
        else
            break;
    }
    strcpy(name,path_name+(strlen(path_name)-k)+1);
    return 1;
}
