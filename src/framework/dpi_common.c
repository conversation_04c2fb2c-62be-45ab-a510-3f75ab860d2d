/****************************************************************************************
 * 文 件 名 : dpi_common.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			    2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <unistd.h>
#include <fcntl.h>
#include <paths.h>
#include <utmpx.h>

#include <sys/stat.h>
#include <sys/types.h>
#include <arpa/inet.h>
#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <math.h>
#include "dpi_pint.h"
#include "dpi_common.h"
#include "dpi_log.h"

int find_packet_line_end(const uint8_t *data, uint16_t len)
{
	uint16_t i;

	for (i = 0; i < len - 1; i++) {
		if (get_uint16_t(data, i) == ntohs(0x0d0a))
			return i;
	}

	return -1;
}

int match_prefix_str(const u_int8_t *payload, size_t payload_len, const char *str, size_t str_len)
{
	int rc = str_len <= payload_len ? memcmp(payload, str, str_len) == 0 : 0;

	return rc;
}

/*
 * Get the length of the next token in a line, and the beginning of the
 * next token after that (if any).
 * Return 0 if there is no next token.
 */
uint32_t get_token_len(const uint8_t *linep, const uint8_t *lineend, const uint8_t **next_token)
{
    const uint8_t *tokenp;
    uint32_t token_len;

    tokenp = linep;

    /*
     * Search for a blank, a CR or an LF, or the end of the buffer.
     */
    while (linep < lineend && *linep != ' ' && *linep != '\r' && *linep != '\n')
        linep++;
    token_len = (int) (linep - tokenp);

    /*
     * Skip trailing blanks.
     */
    while (linep < lineend && *linep == ' ')
        linep++;

    *next_token = linep;

    return token_len;
}

int find_blank_space(const uint8_t *start, int max_len)
{
	int index;

	for (index = 0; index < max_len; index++) {
		if (start[index] == ' ')
			return index;
	}

	return -1;
}

int find_special_char(const uint8_t *start, int max_len, char c)
{
	int index;

	for (index = 0; index < max_len; index++) {
		if (start[index] == c)
			return index;
	}

	return -1;
}

int _find_empty_line(const uint8_t *payload, uint32_t payload_len)
{
	uint32_t i;
	if (payload_len < 4)
		return -1;
	for (i = 0; i <= payload_len - 4; i++) {
		if (memcmp(payload + i, "\r\n\r\n", 4) == 0)
			return (i+4);
	}

	return -1;
}

int _find_three_zero_position(const uint8_t *payload, uint32_t payload_len)
{
	uint32_t i;
	if (payload_len < 4)
		return -1;
	for (i = 0; i <= payload_len -4; i++) {
		if (payload[i]==0x00 && payload[i+1]==0x00 && payload[i+2]==0x00)
			return (i);
	}

	return -1;
}



/*containes null char*/
int find_str_end_len(const uint8_t *start, uint32_t max_len)
{
	uint32_t i;

	for (i = 0; i < max_len; i++) {
		if (start[i] == 0)
			return i + 1;
	}
	return -1;
}

/* Convert all ASCII letters to lower case, in place. */
char *strdown_inplace(char *str)
{
	char *s;

	for (s = str; *s; s++)
		/* What 'g_ascii_tolower (gchar c)' does, this should be slightly more efficient */
		*s = isupper(*s) ? *s - 'A' + 'a' : *s;

	return (str);
}

/* Tries to match val against each element in the value_string array vs.
   Returns the associated string ptr, and sets "*idx" to the index in
   that table, on a match, and returns NULL, and sets "*idx" to -1,
   on failure. */
const char *val_to_string(const int val, const struct int_to_string *vs)
{
	int i = 0;

	if(vs) {
		while (vs[i].strptr) {
			if (vs[i].value == val) {
				return(vs[i].strptr);
			}
			i++;
		}
	}

	return NULL;
}

int timet_to_datetime(time_t t, char *time_str, int len)
{
	struct tm tm_tmp;
	localtime_r(&t, &tm_tmp);

	snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
						tm_tmp.tm_year + 1900,
						tm_tmp.tm_mon + 1,
						tm_tmp.tm_mday,
						tm_tmp.tm_hour,
						tm_tmp.tm_min,
						tm_tmp.tm_sec);
	return 0;
}

char * get_owner_path(void)
{
	int i;
	static char path[256];
	int cnt = readlink("/proc/self/exe", path, sizeof(path));
	if (cnt < 0 || (unsigned int)cnt > sizeof(path))
		return NULL;
	for (i = cnt; i >= 0; i--) {
		if (path[i] == '/') {
			path[i + 1] = 0;
			break;
		}
	}
	return path;
}

unsigned long long malloc_num;
unsigned long long free_num;

void *dpi_malloc(size_t size)
{
	malloc_num++;

	return malloc(size);
}

void dpi_free(void *ptr)
{
	free_num++;
	free(ptr);
}

void*
memdup(const void *src, size_t l)
{
    if(NULL == src || l==0)
    {
        return NULL;
    }

    void *dst = malloc(l+1);
    if(dst)
    {
        memcpy(dst, src, l);
        *((char*)(dst)+l) = 0; // with null end of terminal
        return dst;
    }
    return NULL;
}

const uint8_t
hashmap[] =
{
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //  !"#$%&'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ()*+,-./
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, // 01234567
    0x08, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 89:;<=>?
    0x00, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x00, // @ABCDEFG
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // HIJKLMNO
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // PQRSTUVW
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // XYZ[\]^_
    0x00, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x00, // `abcdefg
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // hijklmno
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // pqrstuvw
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // xyz{|}~.
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // ........
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  // ........
};


int hextobin(const char *i, size_t l, char *p, size_t s)
{
   size_t   pos;
   uint8_t  idx0;
   uint8_t  idx1;

   if(l % 2 != 0)
   {
       return 0;
   }

   if(l / 2 > s)
   {
       return 0;
   }

   for(pos = 0; pos < l; pos += 2)
   {
      idx0 = (uint8_t)i[pos+0];
      idx1 = (uint8_t)i[pos+1];
      if((pos/2)<DPI_COMMON_BUFF_LEN){
        p[pos/2] = (uint8_t)(hashmap[idx0] << 4) | hashmap[idx1];
      }
   }
   return pos/2;
}

int isHEX(const char* p, int len)
{
    if(NULL == p || len <= 0)
    {
        return -1;
    }

    const char hex_mask[] = {
     // 0 1 2 3 4 5 6 7 8 9
        0,0,0,0,0,0,0,0,0,0, /* 0 */
        0,0,0,0,0,0,0,0,0,0, /* 1 */
        0,0,0,0,0,0,0,0,0,0, /* 2 */
        0,0,0,0,0,0,0,0,0,0, /* 3 */
        0,0,0,0,0,0,0,0,1,1, /* 4 */
        1,1,1,1,1,1,1,1,0,0, /* 5 */
        0,0,0,0,0,1,1,1,1,1, /* 6 */
        1,0,0,0,0,0,0,0,0,0, /* 7 */
        0,0,0,0,0,0,0,0,0,0, /* 8 */
        0,0,0,0,0,0,0,1,1,1, /* 9 */
        1,1,1,0,0,0,0,0,0,0, /* 10 */
        0,0,0,0,0,0,0,0,0,0, /* 11 */
        0,0,0,0,0,0,0,0,0,0, /* 12 */
        0,0,0,0,0,0,0,0,
    };

    int i = 0;
    for(i = 0; i < len; i++)
    {
        unsigned char v = p[i];
        if(v > 127)
        {
            return -1;
        }

        if(1 != hex_mask[v])
        {
            return -1;
        }
    }
    return 1;
}


char **str_split(char *a_str, const char a_delim)
{
    char **result = 0;
    size_t count = 0;
    char *tmp = a_str;
    char *last_comma = 0;
    char delim[2];
    delim[0] = a_delim;
    delim[1] = 0;

    while (*tmp) {
        if (a_delim == *tmp) {
            count++;
            last_comma = tmp;
        }
        tmp++;
    }

    count += last_comma < (a_str + strlen(a_str) - 1);
    count ++;
    result = malloc(sizeof(char *) * count);
    if (result) {
        size_t idx = 0;
        char *token = strtok(a_str, delim);
        while (token) {
            *(result + idx++) = strdup(token);
            token = strtok(0, delim);
        }
        *(result + idx ) = 0;
    }

    return result;
}






/*****************************************************************
*Function    :isUTF8
*Description :检测数据是不是UTF8编码
*Input       :数据的长度与地址
*Output      :none
*Return      :返回UTF8数据的长度， 0代表不是UTF8编码数据
*Others      :none
*****************************************************************/
int isUTF8(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;

    if(NULL == pData || len <=0)
    {
        return 0;
    }

    while(loop > 0 )
    {
        if('\r'== *p|| '\n'== *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=2 &&  (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0)))
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        else if(loop>=4 &&  (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) )
        {
            p = p + 4;
            loop = loop - 4;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束, 返回数据的有效长度 */
}

/*****************************************************************
*Function    :isGBK
*Description :探测数据域是不是2字节的GBK数据
*Input       :数据的地址与长度
*Output      :none
*Return      :返回GBK数据的长度， -1代表不全是GBK数据
*Others      :none
*****************************************************************/
int isGBK(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }

        return 0;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}


const char *
dpi_now(time_t *t, char *time_str, int buff_len)
{
    struct tm tm_tmp;
    localtime_r(t, &tm_tmp);

    if(buff_len < 64 || NULL == time_str)
    {
        return NULL;
    }

    snprintf(time_str, buff_len, "%04d%02d%02d%02d%02d%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
    return time_str;
}


int
is_zero(const unsigned char *p, size_t len)
{
    for(size_t i=0; i<len; i++)
    {
        if(p[i])
        {
            return 0;
        }
    }
    return 1;
}

/* 二进制 转 16进制 字符 */
 static char hex2char[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
int bintohex(const char *i, size_t l, char *o, size_t s)
{
    size_t  pos = 0;
    size_t  walk = 0;
    for(pos = 0; pos < l && walk < s-1; pos++)
    {
        unsigned char H = i[pos] >> 4;
        unsigned char L = i[pos] & 0x0F;
        o[walk++] = hex2char[H];
        o[walk++] = hex2char[L];
    }
    return walk;
}


/*****************************************************************
*Function    :mkdirs
*Description :递归创建目录
*Input       :想要创建的目录
*Output      :
*Return      :void
*Others      :none
*****************************************************************/
void mkdirs(const char *dir)
{
    char tmp[2048];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp),"%s",dir);
    len = strlen(tmp);
    if(tmp[len - 1] == '/')
        tmp[len - 1] = 0;
    for(p = tmp + 1; *p; p++)
        if(*p == '/') {
            *p = 0;
            mkdir(tmp, S_IRWXU);
            *p = '/';
        }
    mkdir(tmp, S_IRWXU);
}


int dpi_get_uint8(struct dpi_pkt_st *pkt, uint32_t offset, uint8_t *val)
{
	if (offset + 1 > pkt->payload_len)
		return -1;

	*val = *(pkt->payload + offset);
	return 0;
}

int dpi_get_be16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val)
{
	if (offset + 2 > pkt->payload_len)
		return -1;

	*val = pntoh16(pkt->payload + offset);
	return 0;
}


int dpi_get_be24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
  if (offset + 3 > pkt->payload_len)
  return -1;

	*val = pntoh24(pkt->payload + offset);
	return 0;
}

int dpi_get_be32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
	if (offset + 4 > pkt->payload_len)
		return -1;

	*val = pntoh32(pkt->payload + offset);
	return 0;
}
int dpi_get_be48(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val)
{
	if (offset + 6 > pkt->payload_len)
		return -1;

	*val = pntoh48(pkt->payload + offset);
	return 0;
}


int dpi_get_be64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val)
{
	if (offset + 8 > pkt->payload_len)
		return -1;

	*val = pntoh64(pkt->payload + offset);
	return 0;
}


int dpi_get_le16(struct dpi_pkt_st *pkt, uint32_t offset, uint16_t *val)
{
	if (offset + 2 > pkt->payload_len)
		return -1;

	*val = pletoh16(pkt->payload + offset);
	return 0;
}

int dpi_get_le24(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
	if (offset + 3 > pkt->payload_len)
		return -1;

	*val = pletoh24(pkt->payload + offset);
	return 0;
}

int dpi_get_le32(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t *val)
{
	if (offset + 4 > pkt->payload_len)
		return -1;

	*val = pletoh32(pkt->payload + offset);
	return 0;
}


int dpi_get_le64(struct dpi_pkt_st *pkt, uint32_t offset, uint64_t *val)
{
	if (offset + 8 > pkt->payload_len)
		return -1;

	*val = pletoh64(pkt->payload + offset);
	return 0;
}

int dpi_get_string_endwith_null(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
	if (offset + len > pkt->payload_len)
		return -1;
	snprintf(val, max_len, "%s", (const char *)pkt->payload + offset);
	return 0;
}

int dpi_get_string_ascii(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
	uint32_t copy_len = len >= max_len ? max_len - 1 : len;
	if (offset + len > pkt->payload_len)
		return -1;

	memcpy(val, pkt->payload + offset, copy_len);
	val[copy_len] = 0;
	return 0;
}

int dpi_get_hex_string(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t len, char *val, uint32_t max_len)
{
	uint32_t i;
	if (offset + len > pkt->payload_len)
		return -1;

	val[0] = '0';
	val[1] = 'x';
	for (i = 0; i < len && i < (max_len - 3) / 2; i++) {
		sprintf(val + i * 2 + 2, "%02x", pkt->payload[offset + i]);
	}
	return 0;
}

int dpi_get_guid(struct dpi_pkt_st *pkt, uint32_t offset, char *val, uint32_t max_len)
{
	int idx;
	uint32_t i;
	if (offset + 16 > pkt->payload_len)
		return -1;

	if (max_len < sizeof("00112233-0011-0011-0011-001122334455"))
		return -1;

	for (i = 0; i < 4; i++) {
		sprintf(val + i * 2, "%02x", pkt->payload[offset + 4 - i - 1]);
	}

	val[8] = '-';
	for (i = 0; i < 2; i++) {
		sprintf(val + 9 + 2 * i, "%02x", pkt->payload[offset + 6 - i - 1]);
	}

	val[13] = '-';
	for (i = 0; i < 2; i++) {
		sprintf(val + 14 + i * 2, "%02x", pkt->payload[offset + 8 - i - 1]);
	}


	val[18] = '-';
	for (i = 0; i < 2; i++) {
		sprintf(val + 19 + i * 2, "%02x", pkt->payload[offset + 8 + i ]);
	}

	val[23] = '-';
	for (i = 0; i < 6; i++) {
		sprintf(val + 24 + i * 2, "%02x", pkt->payload[offset + 10 + i]);
	}

	return 0;
}

int dpi_strneql(struct dpi_pkt_st *pkt, uint32_t offset, const char *str, const size_t size)
{
	if (offset + size > pkt->payload_len)
		return -1;
	int cmp = strncmp((const char *)pkt->payload + offset, str, size);

	return cmp == 0 ? 0 : 1;
}

int dpi_strstr(const uint8_t *search_str, uint32_t ss_len, const char* t_str, uint8_t t_len)
{
    uint32_t i = 0 , j = 0;

    int str_index[256];  // ascii表映射

    if (search_str == NULL || t_str == NULL)
    {
        return -1;
    }

    for (i = 0; i < 256; ++i)
    {
        str_index[i] = t_len + 1;
    }

    for (i = 0; i < t_len; i ++)
    {
        str_index[(uint8_t)t_str[i]] = t_len - i;
    }

    i = 0 , j = 0;

    while (!(i + t_len > ss_len))
    {
        if (search_str[i + j] != t_str[j])
        {
            if( str_index[search_str[i + t_len]] == 0)
            {
                DPI_LOG(DPI_LOG_ERROR, "stack memory has been changed");
                return -1;
            }
            i += str_index[search_str[i + t_len]];
            j = 0;
        }
        else
        {
            j ++;
        }
        if (j == t_len)
        {
            return i;
        }
    }

    return -1;
}

void
wall(const char *format, ...)
{
    struct utmpx *utmpptr;
    va_list       args;
    char          tty    [1024];
    char          strbuff[1024];
    int           fd;
    int           len;

    if(NULL == format)
    {
        return;
    }

    va_start(args, format);
    vsnprintf(strbuff, sizeof(strbuff), format, args);
    va_end(args);
    len = strlen(strbuff);

    setutxent();
    while((utmpptr = getutxent()))
    {
        if (!utmpptr->ut_user[0])
        {
            continue;
        }

        if (utmpptr->ut_type != USER_PROCESS)
        {
            continue;
        }

        if (!*utmpptr->ut_line || *utmpptr->ut_line == ':')
        {
            continue;
        }

        snprintf(tty, sizeof(tty), "%s%s", _PATH_DEV, utmpptr->ut_line);
        if ((fd = open(tty, O_WRONLY|O_NONBLOCK, 0)) < 0)
        {
            return ;
        }

        write(fd, strbuff, len);
        close(fd);
    }
}

const char *
strncasestr(const char *s,const char *find, size_t slen)
{
    char c, sc;
    size_t len;

    if ((c = *find++) != '\0') {
        len = strlen(find);
        do {
            do {
                if (slen-- < 1 || (sc = *s++) == '\0')
                    return (NULL);
            } while (tolower(sc) != tolower(c));
            if (len > slen)
                return (NULL);
        } while (strncasecmp(s, find, len) != 0);
        s--;
    }
    return ((const char *)s);
}


const uint8_t* memstr(const uint8_t* a,const char* b,int len){
    if( a == NULL || b == NULL || len < 1)
        return NULL;
    int i,j,n = strlen(b);
    if(len < n)
        return NULL;
    for(i=0; i <= len - n; i++){
        for(j=i; j < n+i; j++)
            if(a[j] != b[j-i])
                break;
        if(j == n+i)
            return a+i;
    }
    return NULL;
}



const char *time_to_datetime(time_t t)
{
    static char time_str[64];
    struct tm tm_tmp;
    //t += 28800;  //东八区
    //gmtime_r(&t, &tm_tmp);

    int count=0;
    struct timeval tv;

    localtime_r(&t, &tm_tmp);
    /*
    snprintf(time_str, sizeof(time_str), "%04d%02d%02d%02d%02d%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
                        */
    strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &tm_tmp);

    return time_str;
}


const
char *filetype(const char*p, int l)
{
    struct
    {
        const char *prefix;
        int            len;
        const char *detail;
    } file_hdr[] = {
        {"\x19\xF1\x03\x00", 4, "Tencent_mmtls"},
        {"\xAB\xCD\x98\x76", 4, "Tencent_Encrypted"},
        {"\x30\x31\x30\x33\x25", 5, "Tencent_Encrypted"},
        {"voice_content=", 14, "搜狗语音输入法"},
        {"\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", 8,   "PNG"},
        {"\x47\x49\x46\x38\x37\x61", 6,   "gif"},
        {"\x47\x49\x46\x38\x39\x61", 6,   "gif"},
        {"\xFF\xD8\xFF\xE0\x00\x10\x4A\x46\x49\x46\x00\x01", 12,   "jpeg"},
        {"\xFF\xD8\xFF\xEE", 4,   "JPG"},
        {"\xFF\xD8\xFF\xE1", 4,   "JPG"},
        {"\x00\x00\x01\xBA", 4,   "mpeg"},
        {"\x52\x49\x46\x46", 4,   "webp"},
        {"\x25\x50\x44\x46\x2d", 5,   "pdf"},
        {"\x44\x48\x41\x56", 4, "dahua_IPC"},
        {"\x47\x40", 2, "MPEG_TS"},
        {"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1", 8, "Microsoft_Office"},
        {"\x50\x4B\x03\x04\x14\x00\x06\x00", 8, "Microsoft_Office 2016"},
        {"\x52\x61\x72\x21\x1A\x07\x00", 7,   "rar"},
        {"\x52\x61\x72\x21\x1A\x07\x01\x00", 8,   "rar"},
        {"\x50\x4B\x03\x04", 4,   "zip"},
        {"\x50\x4B\x05\x06", 4,   "zip"},
        {"\x50\x4B\x07\x08", 4,   "zip"},
        {"\x75\x73\x74\x61\x72\x00\x30\x30", 8,   "tar"},
        {"\x75\x73\x74\x61\x72\x20\x20\x00", 8,   "tar"},
        {"\x37\x7A\xBC\xAF\x27\x1C", 6,   "7Z"},
        {"\x1F\x9D", 2,   "tar.z"},
        {"\x1F\xA0", 2,   "tar.z"},
        {"\x42\x5A\x68", 3,   "bz2"},
        {"\xed\xab\xee\xdb", 4,   "rpm"},
        {"\x66\x74\x79\x70\x33\x67", 6,   "3gp"},
        {"\x5A\x4D", 2,   "exe"},
        {"\x30\x26\xB2\x75\x8E\x66\xCF\x11", 8,   "wma"},
        {"\xA6\xD9\x00\xAA\x00\x62\xCE\x6C", 8,   "wmv"},
        {"\x52\x49\x46\x46", 4,   "avi"},
        {"\xFF\xFB", 2,   "mp3"},
        {"\xFF\xF3", 2,   "mp3"},
        {"\xFF\xF2", 2,   "mp3"},
        {"\x49\x44\x33", 3,   "mp3"},
        {"\x43\x44\x30\x30\x31", 5,   "ios"},
        {"\x66\x4C\x61\x43", 4,   "flac"},
        {"\x1A\x45\xDF\xA3", 4,   "mkv"},
        {NULL, 0,   NULL},
    };

    for(int i = 0; 0 != file_hdr[i].len; i++)
    {
        if(l >= file_hdr[i].len && 0 == memcmp(p, file_hdr[i].prefix, file_hdr[i].len))
        {
            return file_hdr[i].detail;
        }
    }
    return NULL;
}


#define BASE64_PAD '='
#define BASE64DE_FIRST '+'
#define BASE64DE_LAST 'z'

/* BASE 64 encode table */
static const char base64en[] = {
	'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
	'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
	'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
	'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
	'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
	'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
	'w', 'x', 'y', 'z', '0', '1', '2', '3',
	'4', '5', '6', '7', '8', '9', '+', '/',
};

/* ASCII order for BASE 64 decode, 255 in unused character */
static const unsigned char base64de[] = {
	/* nul, soh, stx, etx, eot, enq, ack, bel, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/*  bs,  ht,  nl,  vt,  np,  cr,  so,  si, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* dle, dc1, dc2, dc3, dc4, nak, syn, etb, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* can,  em, sub, esc,  fs,  gs,  rs,  us, */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/*  sp, '!', '"', '#', '$', '%', '&', ''', */
	   255, 255, 255, 255, 255, 255, 255, 255,

	/* '(', ')', '*', '+', ',', '-', '.', '/', */
	   255, 255, 255,  62, 255, 255, 255,  63,

	/* '0', '1', '2', '3', '4', '5', '6', '7', */
	    52,  53,  54,  55,  56,  57,  58,  59,

	/* '8', '9', ':', ';', '<', '=', '>', '?', */
	    60,  61, 255, 255, 255, 255, 255, 255,

	/* '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', */
	   255,   0,   1,  2,   3,   4,   5,    6,

	/* 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', */
	     7,   8,   9,  10,  11,  12,  13,  14,

	/* 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', */
	    15,  16,  17,  18,  19,  20,  21,  22,

	/* 'X', 'Y', 'Z', '[', '\', ']', '^', '_', */
	    23,  24,  25, 255, 255, 255, 255, 255,

	/* '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', */
	   255,  26,  27,  28,  29,  30,  31,  32,

	/* 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', */
	    33,  34,  35,  36,  37,  38,  39,  40,

	/* 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', */
	    41,  42,  43,  44,  45,  46,  47,  48,

	/* 'x', 'y', 'z', '{', '|', '}', '~', del, */
	    49,  50,  51, 255, 255, 255, 255, 255
};

unsigned int
base64_encode(const unsigned char *in, unsigned int inlen, char *out)
{
	int s;
	unsigned int i;
	unsigned int j;
	unsigned char c;
	unsigned char l;

	s = 0;
	l = 0;
	for (i = j = 0; i < inlen; i++) {
		c = in[i];

		switch (s) {
		case 0:
			s = 1;
			out[j++] = base64en[(c >> 2) & 0x3F];
			break;
		case 1:
			s = 2;
			out[j++] = base64en[((l & 0x3) << 4) | ((c >> 4) & 0xF)];
			break;
		case 2:
			s = 0;
			out[j++] = base64en[((l & 0xF) << 2) | ((c >> 6) & 0x3)];
			out[j++] = base64en[c & 0x3F];
			break;
		}
		l = c;
	}

	switch (s) {
	case 1:
		out[j++] = base64en[(l & 0x3) << 4];
		out[j++] = BASE64_PAD;
		out[j++] = BASE64_PAD;
		break;
	case 2:
		out[j++] = base64en[(l & 0xF) << 2];
		out[j++] = BASE64_PAD;
		break;
	}

	out[j] = 0;

	return j;
}

unsigned int
base64_decode(const char *in, unsigned int inlen, unsigned char *out)
{
	unsigned int i;
	unsigned int j;
	unsigned char c;

	if (inlen & 0x3) {
		return 0;
	}

	for (i = j = 0; i < inlen; i++) {
		if (in[i] == BASE64_PAD) {
			break;
		}
		if (in[i] < BASE64DE_FIRST || in[i] > BASE64DE_LAST) {
			return 0;
		}

		c = base64de[(unsigned char)in[i]];
		if (c == 255) {
			return 0;
		}

		switch (i & 0x3) {
		case 0:
			out[j] = (c << 2) & 0xFF;
			break;
		case 1:
			out[j++] |= (c >> 4) & 0x3;
			out[j] = (c & 0xF) << 4;
			break;
		case 2:
			out[j++] |= (c >> 2) & 0xF;
			out[j] = (c & 0x3) << 6;
			break;
		case 3:
			out[j++] |= c;
			break;
		}
	}

	return j;
}


uint64_t hex_to_decimal(const char * hex, unsigned int hex_len)
{
    unsigned int i = 0;
    int val;
    uint64_t dec = 0;
    unsigned int len = hex_len - 1;
    if (hex_len == 0  || hex == NULL) return 0;

    for (i = 0; i < hex_len; ++i) {
        /* Find the decimal representation of hex[i] */
        if(hex[i]>='0' && hex[i]<='9') {
            val = hex[i] - 48;
        } else if(hex[i]>='a' && hex[i]<='f') {
            val = hex[i] - 97 + 10;
        } else if(hex[i]>='A' && hex[i]<='F') {
            val = hex[i] - 65 + 10;
        }

        dec += val * pow(16, len);
        len--;
    }

    return dec;
}


int dpi_strstr_kmp(char* haystack, int h_len, char* needle) {
    int n = h_len;
    // int n = strlen(haystack), m = strlen(needle);
    int m = strlen(needle);
    if (m == 0) {
        return 0;
    }
    int pi[m];
    pi[0] = 0;
    for (int i = 1, j = 0; i < m; i++) {
        while (j > 0 && needle[i] != needle[j]) {
            j = pi[j - 1];
        }
        if (needle[i] == needle[j]) {
            j++;
        }
        pi[i] = j;
    }
    for (int i = 0, j = 0; i < n; i++) {
        while (j > 0 && haystack[i] != needle[j]) {
            j = pi[j - 1];
        }
        if (haystack[i] == needle[j]) {
            j++;
        }
        if (j == m) {
            return i - m + 1;
        }
    }
    return -1;
}


int dpi_get_utf8_arr(const uint8_t * data, uint32_t data_len,
                     DpiUtf8Info *out, uint32_t utf8_min_len)
{
  uint32_t index = 0;
  uint32_t offset = 0;

  if (data == NULL || data_len == 0)
    return -1;

  const uint8_t *p = data;
  const uint8_t *str = NULL;
  char tmp_str[1024] = { 0 };
  uint32_t len = 0;

  // fwrite(split, strlen(split), 1, fp);
  while (index < data_len) {

    if(isprint(*p) > 0) {
      p++;
      if (str == NULL)
        str = p;
      len++;
      index++;

    } else if((0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) ) {
      p = p + 3;
      if (str == NULL)
        str = p;
      len += 3;
      index += 3;

    } else if((0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0))
            && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) ) {
      p = p + 4;
      if (str == NULL)
        str = p;
      len += 4;
      index += 4;
    } else {
        if (str != NULL) {
          // memcpy(tmp_str, data + offset, len);
          // // snprintf(tmp_str, sizeof(tmp_str), "%s", p);
          // char s_len[64] = { 0 };
          // snprintf(s_len, sizeof(s_len), "len = %d, index = %d, offset = %d\n", len, index, offset);
          // fwrite(s_len, strlen(s_len), 1, fp);
          // fwrite(tmp_str, strlen(tmp_str), 1, fp);
          // fwrite("\n", 1, 1, fp);
          // fflush(fp);
          if (len > utf8_min_len) {
            out->len[out->num] = len;
            out->offset[out->num] = offset;
            out->num++;
          }

          offset = offset + len ;
          len = 0;
          str = NULL;
          // memset(tmp_str, 0, sizeof(tmp_str));
        }
        p++;
        index++;
        offset++;
    }
  }

  return 0;
}
int is_file_exist(const char *filepath)
{
    if(filepath==NULL){
        return 0;
    }
    if((access(filepath,F_OK))!=-1){
        return 1;
    }

    return 0;
}
void dpi_utils_traverse_dir(const char * path, dir_callback cb)
{
  DIR           *dir;
  struct dirent *entry;
  struct stat    statbuf;

  // 打开目录
  if ((dir = opendir(path)) == NULL) {
    perror("opendir");
    return;
  }

  // 读取目录中的每个项
  while ((entry = readdir(dir)) != NULL) {
    char filepath[1024];
    snprintf(filepath, sizeof(filepath), "%s/%s", path, entry->d_name);

    // 获取文件信息
    if (lstat(filepath, &statbuf) == -1) {
      perror("lstat");
      continue;
    }

    // 如果是目录，则递归遍历该目录
    if (S_ISDIR(statbuf.st_mode)) {
      // 忽略 . 和 .. 目录
      if (strcmp(entry->d_name, ".") != 0 && strcmp(entry->d_name, "..") != 0) {
        dpi_utils_traverse_dir(filepath, cb);
      }
    } else {
      cb(filepath);
    }
  }

  closedir(dir);
}