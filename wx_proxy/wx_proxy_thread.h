/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_THREAD_H
#define WX_PROXY_THREAD_H

#define  CLIENT_QUEUE_SIZE 2048
enum SOCKET_PAIR_TYPE
{
    SOCKETPAIR_MASTER = 0,
    SOCKETPAIR_WORKER,
    SOCKETPAIR_MAX,
};

struct thread_info;
struct new_client;

struct new_client
{
    int                    client_fd;
    int                    salen;
    struct sockaddr       *sa;
    struct evconnlistener *listener;
    void                 (*accept)(struct evconnlistener *listener, int client_fd, struct sockaddr *sa, int salen,  struct thread_info* pts);
} __attribute__((packed)) ;

struct thread_info
{
    struct context       *ctx;

    struct event_base    *base;
    GPtrArray            *session_manager;

    pthread_t             thread;
    int                   index;

    struct event         *thread_timer;
    struct timeval        thread_tv;

    size_t                message;
    int                   ref;

    /**** queue ****/
    int                   pair[SOCKETPAIR_MAX];
    int                 (*wakeup)(struct thread_info *pts);
    struct new_client     queue[CLIENT_QUEUE_SIZE];
    size_t                queuq_to_read;
    size_t                queuq_to_write;
    int                   queuq_size;
};

#ifdef __cplusplus
extern "C" {
#endif

int
proxy_thread_init(int num, struct context *ctx);

void*
proxy_thread_entry(void*  thread_info);

void
proxy_thread_loopexit(struct context *ctx);

void
proxy_thread_wait(struct context *ctx);

struct thread_info*
proxy_thread_get_least_conn(struct context *ctx);


struct new_client*
accept_push(struct thread_info* thread);

struct new_client*
accept_pop(struct thread_info* thread);

#ifdef __cplusplus
}
#endif

#endif


