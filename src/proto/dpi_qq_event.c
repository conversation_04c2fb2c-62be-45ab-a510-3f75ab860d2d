/****************************************************************************************
 * 文 件 名 : dpi_qq_event.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: yangn  	    2020/07/08
编码: yangn			2020/07/08
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <ctype.h>
#include <regex.h>
#include <string.h>
#include <sys/time.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"

#include "dpi_detect.h"
#include "wxcs_def.h"
#include "dpi_tbl_log.h"
#include "dpi_dissector.h"


#define DATA_WITH_QQ_NUM_LEN 100            /*包含QQ号码的数据包长度范围 */

extern struct rte_mempool *tbl_log_mempool;
extern struct global_config g_config;
static wxc_handle  g_wxc_handle = NULL;

static int write_qq_event(struct flow_info *flow, int direction, ST_QQEventAlive *info);





/*同一条flow中发送qqevent消息时的信息 */
/*由于qqevent流量较大，同一条流中QQ几乎都相同，每次给wxcs发送相同的消息占用了较大带宽，消耗性能 */
/*所以只在第一次 */
typedef struct st_qq_event_flow_node
{
    int sendFlg;                 // 同一条流中qqevent是否给wxcs发过消息 1，已发送过消息，0：没有发送过消息
    uint32_t  lastSendTime;     // 上次发送消息时间

} ST_QQEventFlowNode;


static int BinToHex(const unsigned char *inPut, unsigned int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    char * pOrigin = OutBuffer;
    unsigned int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
        OutBuffer+=2;
    }
    return OutBuffer - pOrigin;
}

static void print_session(ST_QQEventAlive *p)
{
    if(0 == g_config.debug_qq_event)
    {
        return;
    }

    if(NULL == p)
    {
        return;
    }

    dpi_TrailerDump(&p->trailer);

    printf("client_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->client_ip)+0),
            *((uint8_t*)(&p->client_ip)+1),
            *((uint8_t*)(&p->client_ip)+2),
            *((uint8_t*)(&p->client_ip)+3));
    printf("server_ip         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->server_ip)+0),
            *((uint8_t*)(&p->server_ip)+1),
            *((uint8_t*)(&p->server_ip)+2),
            *((uint8_t*)(&p->server_ip)+3));
    printf("client_port       :%u\n",  p->client_port);
    printf("server_port       :%u\n",  p->server_port);
    printf("QQNum             :%zu\n", p->QQNum);
    printf("\n");
}


static int get_ip_port_V4(struct flow_info *flow, int *client_ip, int *server_ip, short *client_port, short *server_port, int *C2S)
{
    if (NULL == flow || NULL == client_ip || NULL == server_ip || NULL == client_port || NULL == server_port || NULL == C2S)
    {
        return -1;
    }

    int res = 0;
    if (8080 ==  ntohs(flow->pkt.dst_port)
        || 443 ==  ntohs(flow->pkt.dst_port)
        || 80 ==  ntohs(flow->pkt.dst_port)
        || 14000 ==  ntohs(flow->pkt.dst_port))
    {
        *client_ip   = flow->pkt.src_ip.ipv4;
        *server_ip   = flow->pkt.dst_ip.ipv4;
        *client_port = ntohs(flow->pkt.src_port);
        *server_port = ntohs(flow->pkt.dst_port);
        *C2S = 1;
        res = 0;
    }
    else if (8080 ==  ntohs(flow->pkt.src_port)
        || 443 ==  ntohs(flow->pkt.src_port)
        || 80 ==  ntohs(flow->pkt.src_port)
        || 14000 ==  ntohs(flow->pkt.src_port))
    {
        *server_ip   = flow->pkt.src_ip.ipv4;
        *client_ip   = flow->pkt.dst_ip.ipv4;
        *server_port = ntohs(flow->pkt.src_port);
        *client_port = ntohs(flow->pkt.dst_port);
        *C2S = 0;
        res = 0;
    }
    else
    {
       res = -1;
    }
    
    return res;
}

/*****************************************************************
*Function    :GetMaxLenNumStr
*Description :获取一片数据中的最长数字字符串
*pStrInput,lenInput :数据的地址与长度
*pStrOut,pLenOut:最长数字字符创地址和长度
*Return :找到的数字字符串在原始字符串中的位置
*****************************************************************/
static int GetMaxLenNumStr(const char *pStrInput, unsigned int lenInput,
            char *pStrOut, int *pLenOut)
{
    if (NULL == pStrInput
    || lenInput <= 0
    || NULL == pStrOut
    || NULL == pLenOut
    || *pLenOut <= 0)
    {
        return -1;
    }
    const char *pMaxLeft = NULL;
    int MaxNumCounter = 0;
    const char *pLeft = NULL;
    const char *p = pStrInput;
    int Len = lenInput;
    int isNum = 0;
    int lNumCounter = 0;
    unsigned int copyLen = 0;

    /*匹配二进制数据中最长的数字字符串(支持ASCII) */
    while(Len > 0)
    {
        /* 0x00  不算可见字符  */
        // if( *p >= 48 &&  *p <= 57 ) // ASCII 0~9 对应的值为48~57，用isdigit是多一层防御判断其实用一种条件就可以
        if(isdigit(p[0])) // ASCII 0~9 对应的值为48~57，用isdigit是多一层防御判断其实用一种条件就可以
        {
            if(0 == isNum)/* 状态跳变: 只有从不可见字符转为可见字符状态 */
            {
                isNum = 1;/* 改变状态机                     */
                pLeft = p;       /* 记录可见字符地址               */
            }

            p++;
            Len--;
            lNumCounter++;
            if(lNumCounter > MaxNumCounter) /* 当前匹配的串是不是最长的数字字符串 */
            {
                MaxNumCounter = lNumCounter;/* 更新最长串值                  */
                pMaxLeft = pLeft;                       /* 更新最长串的首地址            */
            }
            continue;
        }
	
        /* 遇到非数字串 */
        pLeft = NULL;
        isNum = 0;
        lNumCounter = 0;
        p++;
        Len--;
    }

    copyLen = *pLenOut < MaxNumCounter ?  *pLenOut : MaxNumCounter;
    *pLenOut = copyLen ;
    strncpy(pStrOut, pMaxLeft, copyLen);
    if (NULL == pMaxLeft)
    {
       return -1; 
    }
    else
    {
        return pMaxLeft - pStrInput;
    }
        
}


int init_dissector_qq_event_thread(void)
{
    if (g_config.protocol_switch[PROTOCOL_QQ_EVENT] == 0)
    {
        return 0;
    }
    wxc_init(&g_wxc_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    if(NULL == g_wxc_handle)
    {
        return -1;
    }
    return 0;
}

void fini_dissector_qq_event_thread(void)
{

    if(NULL != g_wxc_handle)
    {
        wxc_fini(g_wxc_handle);
        g_wxc_handle = NULL;
    }
    return ;
}



/********************************************
 * 函 数 名 : qq_event_is_find_qq_prefix
 * 功    能 : 查找数据包中是否含有QQ号码开始的前缀
 * 参    数 : payload数据包开始指针， payload_len：数据长度
 * 返 回 值 : 0 未找到， 1：找到类型一， 2找到类型二
 ********************************************/
static int qq_event_is_find_qq_prefix(const uint8_t *payload, const uint32_t payload_len)
{
    if (NULL == payload || payload_len < 20)
    {
        return 0;
    }

    int walk = 0;
    int res = 0;
    unsigned int prefix = 0;
    const char * p = NULL;
    const char *find = NULL;
    struct
    {
        const char *prefix;
        int            len;
    } magic[] =
    {
        {"\x00\x00\x00\x0b", 4},
        {"\x00\x00\x00\x0c", 4},
        {"\x00\x00\x00\x0d", 4},
        {"\x00\x00\x00\x0a", 4},
        {"\x00\x00\x00\x08", 4},
        {"\x00\x00\x00\x09", 4},
        {"\x00\x00\x00\x01", 4},
        {"\x08\x01\x12\x0a", 4},
        {NULL,       0},
    };

    /*qq活动报文中，在QQ号码前面有 0x0000000b或0x0000000d或者0x0000000c或者 0x0801120a 的前缀 */
    /*QQ活动事件中，大体有两种数据类型， 这里只对第一类 0x00 0x00 开始的数据做长度校验，只有这一类符合这个规则 */
    /*0x28 0x00 开始的数据不符合这种规则*/
    /*将长度校验放开，很多数据不符合这个规则 */
#if 0
    if (0x00 == payload[0] && 0x00 == payload[1])
    {
        prefix = ntohl(*(const unsigned int *)payload);
        if (prefix != payload_len)
        {
            return 0;
        }
    }
#endif
    p      = (const char*)payload;
    walk   = (payload_len < DATA_WITH_QQ_NUM_LEN)?payload_len:DATA_WITH_QQ_NUM_LEN;
    for(int i = 0; 0 != magic[i].len; i++)
    {
        find = memmem(p, walk, magic[i].prefix,  magic[i].len);
        if(find)
        {
            if (payload[0] == 0x28 && payload[1] == 0x00
                && payload[2] == 0x00)
            {
                res = 2;
            }
            else
            {
                res = 1;
            } 
            break;
        }
        else
        {
            continue;
        }
    }

    return res;
}


/*计算哈希KEY */
static int get_hashkey(struct flow_info *flow, int client_ip, int server_ip, char *qq_num, int qq_num_len, char *Hash_Key, int size)
{
    if (NULL == flow || NULL == qq_num || qq_num_len <= 0 || NULL == Hash_Key || size <= 0)
    {
        return -1;
    }

    int ret = 0;    
    if(1 == flow->has_trailer)
    {
        ST_trailer tr = {0};
        dpi_TrailerParser(&tr, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
        ret += BinToHex((const unsigned char*)&tr.IMSI, sizeof(tr.IMSI), Hash_Key+ret, size-ret);
    }
    else
    {
        ret += BinToHex((const unsigned char*)&client_ip, sizeof(client_ip), Hash_Key+ret, size-ret);
        ret += BinToHex((const unsigned char*)&server_ip, sizeof(server_ip), Hash_Key+ret, size-ret); 

    }
    BinToHex((const unsigned char*)qq_num, qq_num_len, Hash_Key+ret, size-ret);

    return 0;
}


/********************************************
 * 函 数 名 : getQQNumInTcpPayload
 * 功    能 : 在TCP payload部分查找QQ号码
 * 参    数 : payload数据包开始指针， payload_len：数据长度
 * 返 回 值 : -1 未找到， 0：找到
 ********************************************/
static int getQQNumInTcpPayload(const uint8_t *payload, const uint32_t payload_len,
            char *QQNum, unsigned int QQNumSize)
{
    if (NULL == payload || payload_len <= 0
        || NULL == QQNum || QQNumSize <= 0)
    {
        return -1;
    }
    int res = 0;
    int findQQNumPrefixFlg = 0;
    char szQQNum[48] = {0};     /*定义时长度长一点，防止后面拷贝时溢出*/
    int QQLen = 0;
    char szPreStr[4] = {0};
    int minLen = 0;

    findQQNumPrefixFlg = qq_event_is_find_qq_prefix(payload, payload_len);
    if (findQQNumPrefixFlg <= 0)
    {
        return -1;
    }

    /*获取最大长度数字字符串 */
    QQLen = sizeof(szQQNum);
    /*根据规律， QQ号码一般不会从tcp payload的第一个字节开始，所以不符合这种规则的就丢弃 */
    res = GetMaxLenNumStr((const char *)payload, (unsigned int)payload_len, szQQNum, &QQLen);
    if (res <= 0 || payload_len < (uint32_t )res)
    {
        return -1;
    }
    szQQNum[sizeof(szQQNum)-1] = '\0';
    /*qqevent消息中，QQ号码前一个字节和QQ号码长度相关，*/
    /*第一种规则--0x00 0x00 0xxx 0xxx 类型6到10位QQ的前一个字节分别是0x0a到0x0e, QQ号码前一个字节大小=QQ号码长度+4*/
    /*第二种规则--0x28 0x00 0x00 0x00 QQ号码前一个字节大小=QQ号码长度*/
    /*获取QQ号码前一个字节 */
    strncpy(szPreStr, (const char *)payload + res - 1, 1); 

    /*QQ号码前一个字节数值=QQ长度+4 */
    if (findQQNumPrefixFlg == 1)
    {
        if (QQLen < 5 || QQLen > 10 || (int)szPreStr[0] > 14 || (int)szPreStr[0] < 9)
        {
            return -1;
        }
        QQLen = (int)szPreStr[0] - 4;
    }
    else if (findQQNumPrefixFlg == 2)
    {
        if (QQLen < 5 || QQLen > 10 || (int)szPreStr[0] > 10 || (int)szPreStr[0] < 5)
        {
            return -1;
        }
        QQLen = (int)szPreStr[0];
    }
    else
    {
        return -1;
    }
    /*判断数字字符串第一个是否是字符'0'， QQ号码第一个不是0 */
    if (szQQNum[0] == '0')
    {
        return -1;
    }


    /*根据QQ号码前一个字符表示的QQ号码长度来截取QQ号码 */
    if ((unsigned int)QQLen > sizeof(szQQNum) || QQLen <= 0)
    {
        return -1;
    }
    szQQNum[QQLen] = '\0';
    memset((char *)&szQQNum[QQLen], 0, sizeof(szQQNum) - QQLen);

    minLen = QQNumSize < sizeof(szQQNum)? QQNumSize : sizeof(szQQNum);
    memcpy(QQNum, szQQNum, minLen);
    return 0;

}

static int dissect_qq_event(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    if (NULL == flow || NULL == payload || payload_len <= 0)
    {
        return -1;
    }

    /* 配置项加载 */
    if (g_config.protocol_switch[PROTOCOL_QQ_EVENT] == 0)
    {
        return -1;
    }

    if (flow->real_protocol_id != PROTOCOL_QQ_EVENT)
    {
        return -1;
    }

    if (NULL == g_wxc_handle)
    {
        return -1;
    }
    
    ST_QQEventFlowNode *pst_QQEventFlowNode = NULL;

    if(NULL == flow->app_session)
    {
        /*新建flow节点信息 */
        /*新的流app_session为空， 在此处malloc的内存，外部其他地方flow_session_free会释放，不需要自己释放 */
        pst_QQEventFlowNode = (ST_QQEventFlowNode*)malloc(sizeof(ST_QQEventFlowNode));
        if (NULL == pst_QQEventFlowNode)
        {
            DPI_LOG(DPI_LOG_ERROR, "error on malooc ST_QQEventFlowNode");
            exit(-1);
        }
        memset(pst_QQEventFlowNode, 0, sizeof(ST_QQEventFlowNode));
        flow->app_session = pst_QQEventFlowNode;
        flow->flow_EOF = NULL;
    }
    else
    {
        /*已创建了flow节点信息 ， 判断是否已同一条流中是否已发送过qqevent消息, 已发送过就不再解析不再重复发送*/
        pst_QQEventFlowNode = (ST_QQEventFlowNode*)flow->app_session;
        if (NULL == pst_QQEventFlowNode || (pst_QQEventFlowNode->sendFlg == 1 ))
        {
            return -1;
        }
    }

    int   client_ip            = 0;
    int   server_ip            = 0;
    short client_port          = 0;
    short server_port          = 0;
    int   C2S                  = 0;
    uint32_t data_len = payload_len < 256 ? payload_len : 256;
    int findQQNumPrefixFlg = 0;
    int res = 0;
    char szQQNum[48] = {0};     /*定义时长度长一点，防止后面拷贝时溢出*/
    ST_QQEventAlive stQQEvent;
    memset(&stQQEvent, 0, sizeof(stQQEvent));

    res = getQQNumInTcpPayload(payload, data_len, (char*)szQQNum, sizeof(szQQNum));
    if (res != 0)
    {
        return -1;
    }

    res = get_ip_port_V4(flow, &client_ip, &server_ip, &client_port, &server_port, &C2S);
    if (res == -1)
    {
        return -1;
    }

    ST_QQEventAlive* value = &stQQEvent;

    //1 设置建联 相关 参数
    if (1 == flow->has_trailer)
    {
        dpi_TrailerParser(&value->trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
        dpi_TrailerGetMAC(&value->trailer, (const char *)flow->ethhdr, g_config.RT_model);                       // 解析戎腾MAC
        dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);
    }
    dpi_TrailerSetDev(&value->trailer, g_config.devname);       // 解析板号
    dpi_TrailerSetOpt(&value->trailer, g_config.operator_name); // 运营商
    dpi_TrailerSetArea(&value->trailer, g_config.devArea);      // 地域名
    dpi_TrailerUpdateTS(&value->trailer);

    value->ip_version = 4;
    value->client_ip.ipv4 = client_ip;
    value->server_ip.ipv4 = server_ip;
    value->client_port    = client_port;
    value->server_port    = server_port;
    value->PersonLastActiveTime = g_config.g_now_time;
    value->PersonFirstActiveTime = g_config.g_now_time;

    if(1 == C2S)
    {
        ++value->PersonC2STransPackets;
        value->PersonC2STransBytes += payload_len;
    }
    else
    {
        ++value->PersonS2CTransPackets;
        value->PersonS2CTransBytes += payload_len;
    }

    sscanf(szQQNum, "%zu", &(value->QQNum));
    wxc_sendMsg(g_wxc_handle, (const unsigned char*)value, sizeof(ST_QQEventAlive), WXCS_QQ_EVENT);
    pst_QQEventFlowNode->sendFlg = 1;
    pst_QQEventFlowNode->lastSendTime = g_config.g_now_time;
    print_session(value);

    write_qq_event(flow, direction, value);
    return 0;
}

static void identify_qq_event(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (NULL == flow || NULL == payload || payload_len < 20)
    {
        return;
    }

    if (g_config.protocol_switch[PROTOCOL_QQ_EVENT] == 0)
    {
        return;
    }

    /* 判断报文的目标端口  目前QQEvent的端口有443, 8080, 80, 14000*/
    uint16_t port_src = ntohs(flow->tuple.inner.port_src);
    uint16_t port_dst = ntohs(flow->tuple.inner.port_dst);

    if(443 == port_src
         || 443 == port_dst
         || 8080 == port_src
         || 8080 == port_dst
         || 80 == port_src
         || 80 == port_dst
         || 14000 == port_src
         || 14000 == port_dst)
    {
        /* 判断报文的前缀 */
        /*QQ活动事件特点一 ， 前两个字节为0， 第三和第四个字节为TCP payload长度(此处暂不做判断) */
        /* 第五，六，七个字节为空，第八个字节为0x0b或0x0c或0x0d , 第九个字节为0x01或0x02 */
        if( 0x00   == payload[0]
           && 0x00   == payload[1]
           && 0x00   == payload[4]
           && 0x00   == payload[5]
           && 0x00   == payload[6]
           && (0x0b == payload[7] || 0x0c == payload[7] || 0x0d == payload[7]
                || 0x0a == payload[7] || 0x08 == payload[7] || 0x09 == payload[7] || 0x01 == payload[7])
           &&  (0x01  == payload[8] || 0x02 == payload[8]))
        {
            //当前这路流, 是微信话单.
            flow->real_protocol_id = PROTOCOL_QQ_EVENT;
        }

        /*QQ活动事件特点一 ，前三个字节为0x28 0x00 0x00 第六、七字节为0x00 0x00 */
        /* 第十二，十三，十四，十五个字节为 0x08 0x01 0x12 0x0a, 也是QQ号码开始前固定四个字节数值*/
        if( 0x28 == payload[0]
           && 0x00 == payload[1]
           && 0x00 == payload[2]
           && (0x00 == payload[3] || 0x01 == payload[3])
           && 0x00   == payload[5]
           && 0x00   == payload[6]
           && 0x08   == payload[11]
           && 0x01   == payload[12]
           && 0x12   == payload[13]
           && 0x0a   == payload[14])
        {
            flow->real_protocol_id = PROTOCOL_QQ_EVENT;
        }
    }
    return;
}

static int write_qq_event(struct flow_info *flow, int direction, ST_QQEventAlive *info)
{
    int                 idx      = 0,i;
    struct rte_mempool *pool     = NULL;
    struct tbl_log     *log_ptr  = NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        return PKT_OK;
    }

    // 公共部分
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    // QQ Event 部分
    write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->PersonC2STransPackets);
    write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->PersonC2STransBytes);
    write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->PersonS2CTransPackets);
    write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->PersonS2CTransBytes);
    write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->QQNum);

    // 保留 部分
    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);

    log_ptr->type        = TBL_LOG_QQ_EVENT;
    log_ptr->len         = idx;
    log_ptr->tid         = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 0;
}

enum EM_QQ_EVENT
{
    EM_QQ_EVENT_C2S_PKT,
    EM_QQ_EVENT_C2S_BYTE,
    EM_QQ_EVENT_S2C_PKT,
    EM_QQ_EVENT_S2C_BYTE,
    EM_QQ_EVENT_QQNUMBER,
    EM_QQ_EVENT_RES,
    EM_QQ_EVENT_MAX,
};

static dpi_field_table  qq_event_field[] = {
    DPI_FIELD_D(EM_QQ_EVENT_C2S_PKT,                  EM_F_TYPE_STRING,               "C2S_Pkt"),
    DPI_FIELD_D(EM_QQ_EVENT_C2S_BYTE,                 EM_F_TYPE_STRING,               "C2S_Byte"),
    DPI_FIELD_D(EM_QQ_EVENT_S2C_PKT,                  EM_F_TYPE_STRING,               "S2C_Pkt"),
    DPI_FIELD_D(EM_QQ_EVENT_S2C_BYTE,                 EM_F_TYPE_STRING,               "S2C_Byte"),
    DPI_FIELD_D(EM_QQ_EVENT_QQNUMBER,                 EM_F_TYPE_STRING,               "QQ_NUMBER"),
    DPI_FIELD_D(EM_QQ_EVENT_RES,                      EM_F_TYPE_STRING,               "reserve"),
};

void init_qq_event_dissector(void)
{
    write_proto_field_tab(qq_event_field, EM_QQ_EVENT_MAX, "qq_event");

    tcp_detection_array[PROTOCOL_QQ_EVENT].proto         = PROTOCOL_QQ_EVENT;
    tcp_detection_array[PROTOCOL_QQ_EVENT].identify_func = identify_qq_event;
    tcp_detection_array[PROTOCOL_QQ_EVENT].dissect_func  = dissect_qq_event;
    return;
}
