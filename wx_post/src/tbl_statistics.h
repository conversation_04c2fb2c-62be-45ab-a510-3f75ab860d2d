#pragma once

#include <initializer_list>
#include <iostream>
#include <cstdio>
#include <thread>
#include <map>
#include <vector>
#include <string>
#include <cstring>
#include <atomic>

class TblStatistics
{
public:
    static TblStatistics &getInstance()
    {
        static TblStatistics tblStatistics_;
        return tblStatistics_;
    }
    void init();

    void setLogPath(const std::string&);

    void writeLog();

    void addScan(const std::string&);
    void addRead(const std::string&);
    void addWrite(const std::string&, const std::string&);

    void startWork();
    void finishWork();


private:
    TblStatistics()  = default;
    ~TblStatistics() = default;

    int get_string_statistics();

    void work();

private:
    FILE *log_file;
    
    uint32_t log_loop_time = 1;

    std::string log_path;

    std::thread log_thread;

    std::vector<std::string> col;
    std::vector<std::string> raw;

    std::string string_statistics_result;

private:
    static std::map<std::string ,std::map<std::string, std::atomic<uint64_t>>> statistics_result;
};

extern TblStatistics& LOG_ST;