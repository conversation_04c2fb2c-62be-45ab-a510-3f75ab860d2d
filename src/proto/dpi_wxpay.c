/****************************************************************************************
 * 文 件 名 : dpi_wxpay.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/11/04
 修改: chenzq          2022/11/04
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
*****************************************************************************************/
#include "dpi_wxpay.h"

// #include <thread.h>
#include <unistd.h>

#include <rte_mempool.h>
#include <glib-2.0/glib.h>
#include <stdbool.h>
#include <stdlib.h>

#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_tbl_log.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

// extern void  wxpay_manager_init();


pthread_rwlock_t            pay_rwlock       = PTHREAD_RWLOCK_INITIALIZER;
GHashTable                 *g_pay_hash  = NULL;

static dpi_field_table  wxpay_field_array[] = {
  DPI_FIELD_D(EM_COMMON_TAGTYPE,               EM_F_TYPE_STRING,              "TAGTYPE"),
  DPI_FIELD_D(EM_COMMON_TEID,                  EM_F_TYPE_STRING,              "TEID"),
  DPI_FIELD_D(EM_COMMON_MSISDN,                EM_F_TYPE_STRING,              "MSISDN"),
  DPI_FIELD_D(EM_COMMON_IMSI,                  EM_F_TYPE_STRING,              "IMSI"),
  DPI_FIELD_D(EM_COMMON_IMEI,                  EM_F_TYPE_STRING,              "IMEI"),
  DPI_FIELD_D(EM_COMMON_TAC,                   EM_F_TYPE_STRING,              "TAC"),
  DPI_FIELD_D(EM_COMMON_OPERATOR,              EM_F_TYPE_STRING,              "OPERATOR"),
  DPI_FIELD_D(EM_COMMON_DEVNAME,               EM_F_TYPE_STRING,              "DEVNAME"),
  DPI_FIELD_D(EM_COMMON_AREA,                  EM_F_TYPE_STRING,              "AREA"),
  DPI_FIELD_D(EM_COMMON_HW_BFLAGS,             EM_F_TYPE_STRING,              "HW_BFALGS"),
  DPI_FIELD_D(EM_COMMON_HW_APN,                EM_F_TYPE_STRING,              "HW_APN"),
  DPI_FIELD_D(EM_COMMON_HW_NCODE,              EM_F_TYPE_STRING,              "HW_NCODE"),
  DPI_FIELD_D(EM_COMMON_HW_ECGI,               EM_F_TYPE_STRING,              "HW_ECGI"),
  DPI_FIELD_D(EM_COMMON_HW_LAC,                EM_F_TYPE_STRING,              "HW_LAC"),
  DPI_FIELD_D(EM_COMMON_HW_SAC,                EM_F_TYPE_STRING,              "HW_SAC"),
  DPI_FIELD_D(EM_COMMON_HW_CI,                 EM_F_TYPE_STRING,              "HW_CI"),
  DPI_FIELD_D(EM_COMMON_RT_PLMN_ID,            EM_F_TYPE_STRING,              "RT_PLMN_ID"),
  DPI_FIELD_D(EM_COMMON_RT_ULI,                EM_F_TYPE_STRING,              "RT_ULI"),
  DPI_FIELD_D(EM_COMMON_RT_BS,                 EM_F_TYPE_STRING,              "RT_BS"),
  DPI_FIELD_D(EM_COMMON_RT_DNS,                EM_F_TYPE_STRING,              "RT_DNS"),
  DPI_FIELD_D(EM_WXPAY_CAPDATE,                EM_F_TYPE_STRING,              "CapDate"),
  DPI_FIELD_D(EM_WXPAY_SRCIP,                   EM_F_TYPE_STRING,              "SrcIp"),
  DPI_FIELD_D(EM_WXPAY_DSTIP,                   EM_F_TYPE_STRING,              "DstIp"),
  DPI_FIELD_D(EM_WXPAY_SRCPORT,                EM_F_TYPE_UINT16,              "SrcPort"),
  DPI_FIELD_D(EM_WXPAY_DSTPORT,                EM_F_TYPE_UINT16,              "DstPort"),


  DPI_FIELD_D(EM_WXPAY_TYPE,                    EM_F_TYPE_UINT8,                 "type"),
  DPI_FIELD_D(EM_WXPAY_RESV0,                   EM_F_TYPE_NULL,                 "resv0"),
  DPI_FIELD_D(EM_WXPAY_RESV1,                   EM_F_TYPE_NULL,                 "resv1"),
  DPI_FIELD_D(EM_WXPAY_RESV2,                   EM_F_TYPE_NULL,                 "resv2"),
  DPI_FIELD_D(EM_WXPAY_RESV3,                   EM_F_TYPE_NULL,                 "resv3"),
  DPI_FIELD_D(EM_WXPAY_RESV4,                   EM_F_TYPE_NULL,                 "resv4"),
};


static int write_wxpay_log(WxpayInfo *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    char __str[256] = { 0 };
    char _str[256] = { 0 };
    char ULI[512]   = {0};
    const char *p   = NULL;
    // struct http_session *session = (struct http_session *)flow->app_session;
    ST_trailer *trailer = &info->trailer;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    dpi_TrailerECI(trailer, ULI, sizeof(ULI));           // ECI 转换

    // write_tbl_log_common(flow, direction, log_ptr->log_content, &&idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_WXPAY_MAX; i++)
    {
      switch(wxpay_field_array[i].index) {
      case EM_WXPAY_TEID:
        snprintf(_str, sizeof(_str), "%u", trailer->trailerType);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case EM_WXPAY_TAGTYPE:
        snprintf(_str, sizeof(_str), "0x%08X", ntohl(trailer->TEID)); // 大端显示
        p = (0==trailer->TEID?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case   EM_WXPAY_MSISDN:
        snprintf(_str, sizeof(_str), "%lu", trailer->MSISDN);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case   EM_WXPAY_IMSI:
        snprintf(_str, sizeof(_str), "%lu", trailer->IMSI);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case  EM_WXPAY_IMEI:
            snprintf(_str, sizeof(_str), "%lu", trailer->IMEI);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case EM_WXPAY_TAC:
        snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->TAC)); // 大端显示
        p = (0==trailer->TAC?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case  EM_WXPAY_OPERATOR:
        snprintf(_str, sizeof(_str), "%s", trailer->Operator);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case   EM_WXPAY_DEVNAME:
        snprintf(_str, sizeof(_str), "%s", trailer->DevName);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case   EM_WXPAY_AREA:
        snprintf(_str, sizeof(_str), "%s", trailer->Area);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case EM_WXPAY_HW_BFLAGS:
        snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer->BFLAG);
        p = (0==trailer->BFLAG?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case   EM_WXPAY_HW_APN:
        snprintf(_str, sizeof(_str), "%s", trailer->APN);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case EM_WXPAY_HW_NCODE:
        snprintf(_str, sizeof(_str), "0x%02X", (unsigned char)trailer->NCODE);
        p = (0==trailer->NCODE?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_HW_ECGI:
        snprintf(_str, sizeof(_str), "0x%08x", ntohl(trailer->ECGI));// 大端显示
        p = (0==trailer->ECGI?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_HW_LAC:
        snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->LAC)); // 大端显示
        p = (0==trailer->LAC?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_HW_SAC:
        snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->SAC)); // 大端显示
        p = (0==trailer->SAC?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_HW_CI:
        snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->CI)); // 大端显示
        p = (0==trailer->CI?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_RT_PLMN_ID:
        snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->PLMN_ID));//大端显示
        p = (0==trailer->PLMN_ID?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_RT_ULI:
        snprintf(_str, sizeof(_str), "%s", ULI);
        p = (0==trailer->ULI?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
        break;
      case EM_WXPAY_RT_BS:
        snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer->BS);
        p = (0==trailer->BS?"":_str);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case  EM_WXPAY_RT_DNS:
        snprintf(_str, sizeof(_str), "%s", trailer->DomainName);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
        break;
      case EM_WXPAY_CAPDATE:
        // get_now_datetime(__str, sizeof(__str));
        timet_to_datetime(info->timestamp, __str, sizeof(__str));
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
        break;
      case EM_WXPAY_SRCIP:
        if (info->sonwden.ip_ver == 4)
          get_ip4string(__str, sizeof(__str), info->sonwden.client_ip.ipv4);
        else
          get_ip6string(__str, sizeof(__str), (const uint8_t *)info->sonwden.client_ip.ipv6);

        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
        break;
      case EM_WXPAY_DSTIP:
        if (info->sonwden.ip_ver == 4)
          get_ip4string(__str, sizeof(__str), info->sonwden.server_ip.ipv4);
        else
          get_ip6string(__str, sizeof(__str), (const uint8_t *)info->sonwden.server_ip.ipv6);
        write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
        break;
      case EM_WXPAY_SRCPORT:
        write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->sonwden.client_port);
        break;
      case EM_WXPAY_DSTPORT:
        write_one_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->sonwden.server_port);
        break;
      case EM_WXPAY_TYPE:
          write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, "WXPay", strlen("WXPay"));
          break;
      case EM_WXPAY_RESV0:
          write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->host, strlen(info->host));
          break;
      case EM_WXPAY_RESV1:
          write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->url, strlen(info->url));
          break;
      default:
          write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
          break;
      }
    }


    log_ptr->type        = TBL_LOG_WXPAY;
    log_ptr->len         = idx;
    log_ptr->tid          = info->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

GFunc slist_callback(gpointer data, gpointer userdata)
{
  if (data == NULL)
    return NULL;
  WxpayInfo *info = (WxpayInfo *)data;

  write_wxpay_log(info);

  return NULL;
}

static gboolean
pay_hash_callback_remove(gpointer key, gpointer value, gpointer user_data)
{
  time_t now;
  WxpayInfo * val = (WxpayInfo *)value;

  if (val == NULL)
    return FALSE;

  time(&now);
  if (now - val->timestamp > 5) {
    write_wxpay_log(val);
    return TRUE;
  }

  return FALSE;
}

void *thread_wxpay(void *arg)
{
  // printf("being wx pay thread!!\n");
  time_t now;
  int size = 0;
  GHashTableIter iter;
  char *key;
  WxpayInfo *value;

  if (g_pay_hash == NULL) {
    g_pay_hash = g_hash_table_new_full(g_str_hash, g_str_equal, NULL, free);
  }

  while (1) {
    sleep(5);
    GSList *slist = NULL;

    time(&now);
    size = g_hash_table_size(g_pay_hash);
    if (size <= 0 ) {
      continue;
    }

    pthread_rwlock_rdlock(&pay_rwlock);     //读锁
    g_hash_table_foreach_remove(g_pay_hash, pay_hash_callback_remove, NULL);

    // g_hash_table_iter_init(&iter, (GHashTable *)g_pay_hash);//使用迭代器遍历哈希表
    // while (g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer *)&value)) {
    //   // printf("now = %d, timestamp = %d\n", now, value->timestamp);
    //   if (now - value->timestamp > 5) {
    //     // printf("push_back!!!");
    //     slist = g_slist_append(slist, (gpointer)value);
    //     g_hash_table_iter_remove(&iter);
    //   }
    // }

    pthread_rwlock_unlock(&pay_rwlock);     //解锁

    // if (g_slist_length(slist) <= 0)
    //   continue;

    // // printf("foreach!!!");
    // g_slist_foreach(slist, slist_callback, NULL);
    // g_slist_free_full(slist, g_free);
    // g_slist_free(slist);
  }
}


// int dissect_wxpay_from_http(struct flow_info *flow, int direction,
//                             struct http_request_info *line_info)
// {

// }



// static
// int dissect_wxpay(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
//     const uint32_t payload_len, uint8_t flag)
// {
//   if(g_config.protocol_switch[TBL_LOG_WXPAY] == 0) {
//       return;
//   }

//   // int i = 0;
//   // DpiUtf8Info utf8_info;
//   WxpayInfo    info;
//   // char tmp_str[64] = { 0 };
//   // memset(&utf8_info, 0, sizeof(DpiUtf8Info));
//   // memset(&info, 0, sizeof(WxpayInfo));

//   // char find_str[] = "wxpay";
//   // int  find_flag = 0;


//   // dpi_get_utf8_arr(payload, payload_len, &utf8_info, 8);

//   // for (i = 0; i < utf8_info.num; ++i) {
//   //   snprintf(tmp_str, utf8_info.len[i], "%s", payload + utf8_info.offset[i]);
//   //   find_flag = dpi_strstr(tmp_str, strlen(tmp_str), find_str, strlen(find_str));
//   //   if (find_flag < 0)
//   //     continue;
//   //   snprintf(info.data + strlen(info.data), sizeof(info.data), "%s;", tmp_str);
//   // }

//   // if (strlen(info.data) == 0 )
//   //   return 0;

//   // int len = strlen(info.data);
//   // if (info.data[len - 1] == ';') {
//   //   info.data[len - 1] = '\0';
//   // }

//   write_wxpay_log(flow, direction, &info);

//   // char tmp_str[256] = { 0 };
//   // for (i = 0; i < utf8_info.num; ++i) {
//   //   snprintf(tmp_str, utf8_info.len[i], "%s", payload + utf8_info.offset[i]);
//   //   printf("index = %d, offset = %d, len = %d\n", i, utf8_info.offset[i], utf8_info.len[i]);
//   //   printf("content = %s\n", tmp_str);
//   // }

//   // dissect_dy_data(flow, direction, payload, payload_len);

//   return 0;
// }


int dissect_wxpay_from_http(struct flow_info *flow, int direction,
                            WxpayHeader *http_header)
{
  WxpayInfo * value = NULL;
  // memset(&info, 0, sizeof(WxpayInfo));
  char key[64] = { 0 };
  char ip[64] = { 0 };
  time_t  now;
  int C2S = 0;

  struct sonwden sonwden;

  memset(&sonwden, 0, sizeof(sonwden));


  get_ip_port(&flow->pkt, &sonwden, &C2S);

  if (sonwden.ip_ver == 4) {
    get_ip4string(ip, sizeof(ip), sonwden.client_ip.ipv4);
  } else {
    get_ip6string(ip, sizeof(ip), (const uint8_t *)sonwden.client_ip.ipv6);
  }

  value = (WxpayInfo *)wxpay_manager_find((void *)ip);
  if (value == NULL) {
    value = malloc(sizeof(WxpayInfo));
    memset(value, 0, sizeof(WxpayInfo));

    get_ip_port(&flow->pkt, &value->sonwden, &C2S);
    // 只有双向 d5 的报文，在解析阶段不会进入 trailer 解析
    dpi_TrailerParser(&value->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
    dpi_TrailerGetMAC(&value->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);

    dpi_TrailerSetDev(&value->trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&value->trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&value->trailer,g_config.devArea);

    value->timestamp = time(&now);
    value->thread_id = flow->thread_id;
    value->direction = direction;
    snprintf(value->host, sizeof(value->host), "%s", http_header->host);
    snprintf(value->url, sizeof(value->url), "%s", http_header->url);
    wxpay_manager_add((void *)ip, (void *)value);
  }

  return 0;
}

static
void identify_wxpay(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{

  if(g_config.protocol_switch[TBL_LOG_WXPAY] == 0) {
      return;
  }

  // struct dpi_pkt_st pkt;
  // uint16_t  flag_0 = 0;
  // uint64_t  flag_1 = 0;


  // pkt.payload = payload;
  // pkt.payload_len = payload_len;



  // dpi_get_be16(&pkt, 0, &flag_0);

  // if (flag_0 != WXPAY_FLAG_0)
  //   return;

  // dpi_get_be48(&pkt, 4, &flag_1);
  // if (flag_1 == WXPAY_FLAG_1) {
  //   flow->real_protocol_id = TBL_LOG_WXPAY;
  // }

  return;
}



void init_wxpay_dissector(void)
{
  // fp = fopen(path, "w+");
  //   // int       i = 0;
  //   // int       j = 0;
  //   // int       count = 0;
  //   // char      tmp_buff[MAX_FIELD_LEN]={0};
  //   // char      *tmp=NULL;

  write_proto_field_without_common(wxpay_field_array, EM_WXPAY_MAX, "wxpay");

  //   // init_wx_relation_dissector();

  // port_add_proto_head(IPPROTO_TCP, WXPAY_PORT_0, TBL_LOG_WXPAY);
  // port_add_proto_head(IPPROTO_TCP, WXPAY_PORT_1, TBL_LOG_WXPAY);
  // port_add_proto_head(IPPROTO_TCP, WXPAY_PORT_2, TBL_LOG_WXPAY);

  // tcp_detection_array[TBL_LOG_WXPAY].proto         = TBL_LOG_WXPAY;
  // tcp_detection_array[TBL_LOG_WXPAY].identify_func = identify_wxpay;
  // tcp_detection_array[TBL_LOG_WXPAY].dissect_func  = dissect_wxpay;

  //   // DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN].excluded_protocol_bitmask, PROTOCOL_WEIXIN);


    return;
}


// typedef int (*RmFunc)(void *key, void *data, void *user_data);
// typedef void (*FreeFunc)(void *key, void *data, void *user_data);

int wxpay_remove_func(void *key, void *data, void *user_data)
{
  time_t now;
  WxpayInfo * val = (WxpayInfo *)data;

  if (val == NULL)
    return FALSE;

  time(&now);
  if (now - val->timestamp > 5) {
    write_wxpay_log(val);
    return TRUE;
  }

  return FALSE;
}

void init_wxpay()
{
  if(g_config.protocol_switch[PROTOCOL_WXPAY] == 0) {
      return;
  }

  pthread_t th;
  // pthread_create(&th, NULL, thread_wxpay, NULL);

  wxpay_manager_init(wxpay_remove_func, free);
}


static __attribute((constructor)) void     before_init_wxpay(void){
    register_tbl_array(TBL_LOG_WXPAY, 0, "wxpay", init_wxpay_dissector);
}