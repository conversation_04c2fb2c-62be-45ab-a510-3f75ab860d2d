/****************************************************************************************
 * 文 件 名 : dpi_main.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <inttypes.h>
#include <sys/types.h>
#include <string.h>
#include <errno.h>
#include <getopt.h>
#include <unistd.h>
#include <sys/time.h>
#include <signal.h>

#include <rte_errno.h>
#include <rte_eal.h>
#include <rte_lcore.h>
#include <rte_ether.h>
#include <rte_ethdev.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <rte_ip.h>
#include <rte_tcp.h>
#include <rte_udp.h>
#include <rte_net.h>

#include "dpi_detect.h"
#include "dpi_protorecord.h"
#include "dpi_typedefs.h"
#include "dpi_tbl_log.h"
#include "dpi_tbl_record_log.h"
#include "dpi_proto_ids.h"
#include "dpi_dissector.h"
#include "dpi_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "iniparser/dictionary.h"
#include "iniparser/iniparser.h"
#include "dpi_match_action.h"
#include "dpi_wxpay.h"
#include "dpi_wx_voice_peers.h"

unsigned int nb_txq;
#define RTE_TEST_RX_DESC_DEFAULT 128
#define RTE_TEST_TX_DESC_DEFAULT 512
#define NB_SOCKETS                      (2)
#define MAX_RX_QUEUE_PER_LCORE 32
#define MAX_PKT_BURST 32 //512
#define NB_MBUF (8192 * 16 * 8)


static uint16_t nb_rxd = RTE_TEST_RX_DESC_DEFAULT;
static uint16_t nb_txd = RTE_TEST_TX_DESC_DEFAULT;
static int numa_on = 1; /**< NUMA is enabled by default. */
static struct rte_mempool *pktmbuf_pool[NB_SOCKETS];
struct rte_port *ports;	       /**< For all probed ethernet ports. */
__thread ya_allocator_t *th_alloc;

static struct rte_ring *packet_ring[RTE_MAX_LCORE];

uint8_t init_wx_relation(void);
void init_dissector_zoom_sesion_thread(void);


static uint32_t rss_flow_type[] = {
	//RTE_ETH_FLOW_IPV4,
	//RTE_ETH_FLOW_FRAG_IPV4,
	RTE_ETH_FLOW_NONFRAG_IPV4_TCP,
	RTE_ETH_FLOW_NONFRAG_IPV4_UDP,
	RTE_ETH_FLOW_NONFRAG_IPV4_SCTP,
	//RTE_ETH_FLOW_NONFRAG_IPV4_OTHER,
	//RTE_ETH_FLOW_IPV6,
	//RTE_ETH_FLOW_FRAG_IPV6,
	RTE_ETH_FLOW_NONFRAG_IPV6_TCP,
	RTE_ETH_FLOW_NONFRAG_IPV6_UDP,
	RTE_ETH_FLOW_NONFRAG_IPV6_SCTP,
	//RTE_ETH_FLOW_NONFRAG_IPV6_OTHER,
};

// symmetric rss key
static uint8_t symmetric_rss_key[] = {
	0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
	0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
	0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
	0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
	0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
};

static struct rte_eth_conf port_conf = {
	.rxmode = {
		.mq_mode = ETH_MQ_RX_RSS,
		.max_rx_pkt_len = ETHER_MAX_LEN,
		.split_hdr_size = 0,
	},
	.rx_adv_conf = {
		.rss_conf = {
			.rss_key = symmetric_rss_key,
			.rss_key_len = RTE_DIM(symmetric_rss_key),
			.rss_hf = ETH_RSS_IP | ETH_RSS_TCP | ETH_RSS_UDP | ETH_RSS_SCTP,
		},
	},
	.txmode = {
		.mq_mode = ETH_MQ_TX_NONE,
	},
};

static struct rte_hash_parameters hash_params = {
	//.entries = MAX_HASH_NODE_PER_THREAD,
	.key_len = sizeof(struct flow_key), /* 13 */
	.hash_func = rte_jhash,
	.hash_func_init_val = 0,
	// .socket_id = 0,
};

struct lcore_rx_queue {
	uint8_t port_id;
	uint8_t queue_id;
} __rte_cache_aligned;

struct lcore_conf {
	uint8_t nb_rxq;
	struct lcore_rx_queue rxq_list[MAX_RX_QUEUE_PER_LCORE];
} __rte_cache_aligned;

struct lcore_conf lcore_conf[RTE_MAX_LCORE];

struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];  //每个解析线程的数据
struct rte_mempool *flow_mempool;   //解析线程的会话内存池
struct rte_mempool *tcp_reassemble_mempool;
struct rte_mempool *tbl_log_mempool;
struct rte_mempool *tbl_log_content_mempool_256k;
struct rte_mempool *tbl_log_content_mempool_4k;

/*record tbl 线程池*/
struct rte_mempool *tbl_log_record_mempool;
struct rte_mempool *tbl_log_content_record_mempool_256k;
struct rte_mempool *tbl_log_content_record_mempool_4k;

struct global_config g_config = {
    .max_conversation_hash_node = (1 << 20),
    .max_hash_node_per_thread   = (1 << 22),
    .max_flow_num               = (1 << 24),
    .tcp_reassemble_mempool_num = (1 << 20),
    .tbl_ring_size              = (4096 * 16),
    .tbl_log_content_256k       = (1024 * 256),
};

extern const char *protocol_name_array[PROTOCOL_MAX];
extern void init_thread_timer(void);
extern int socket_main(void);

static void print_usage(const char *prgname)
{
	printf("%s [EAL options] --"
		" -r rx_queue_num"
		" -t tx_queue_num"
        " -v",
		prgname);
}


// 程序正在启动, 无需做到 O(1)
static int http_host_uri_list_init(const char*type, struct http_host_uri* host_uri, int size, const char *file)
{
    FILE       *f      =   NULL;
    const char *http   =   "http://";
    const char *head   =   NULL;
    const char *tail   =   NULL;
    const char *str    =   NULL;
	const char *p = NULL;
    int         index  =   0;
    char        buff[1024];

    if(0 != access(file, R_OK))
    {
        return -1;
    }

    printf("\n%s ->  %s\n", type, file);
    f = fopen(file, "r");
    if(NULL == f)
    {
        return -1;
    }

    while(0 == feof(f))
    {
        memset(buff, 0, sizeof(buff));
        fgets(buff, sizeof(buff), f);
		if (buff[0] == '\0' || buff[0] == '#') {
			continue;
		}
		printf("buffer= %s\n", buff);
		// if buff has suffix "\n", delete it
		if (buff[strlen(buff) - 1] == '\n') {
			buff[strlen(buff) - 1] = '\0';
		}

		p = strstr(buff, http);
		if (p == NULL) {
			continue;
		}

		// buff delete http
		// head = p + strlen(http) - buff;
		head = p + strlen(http);
		tail = strstr(head, "/");
		if (tail == NULL) {
			memcpy(host_uri[index].host, head, strlen(head));
		    snprintf(host_uri[index].uri, sizeof(host_uri[index].uri), "/");
			printf("host=%-30s  uri=%-20s\n", host_uri[index].host,
					host_uri[index].uri);
			continue;
		}

		memcpy(host_uri[index].host, head, tail - head);
		memcpy(host_uri[index].uri, tail, strlen(tail));

		printf("host=%-30s  uri=%-20s\n", host_uri[index].host,
				host_uri[index].uri);

		index++;
    }

    fclose(f);
    return 0;
}

GHFunc print_wxpay(gpointer key, gpointer value, gpointer data)
{
  printf("key = %s, value = %s\n", (char *)key, (char *)value);
	return NULL;
}

// 程序正在启动, 无需做到 O(1)
static int http_host_wxpay_init(const char*type, GHashTable **hash, const char *file)
{
    FILE       *f      =   NULL;
    const char *http   =   "http://";
    const char *head   =   NULL;
    const char *tail   =   NULL;
    const char *str    =   NULL;
    int         index  =   0;
    char        buff[1024];

    if(0 != access(file, R_OK))
    {
        return -1;
    }

    printf("\n%s ->  %s\n", type, file);
    f = fopen(file, "r");
    if(NULL == f)
    {
        return -1;
    }

    *hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);

    while(0 == feof(f))
    {
        memset(buff, 0, sizeof(buff));
        fgets(buff, sizeof(buff), f);
        if(buff[0]=='\0')
            break;
        if (buff[strlen(buff) - 1] == '\n') {
          buff[strlen(buff) - 1] = '\0';
        }
        g_hash_table_insert(*hash, g_strdup(buff), g_strdup(buff));
    }
    fclose(f);

    return 0;
}

static int parse_pkt_filter(int mask, uint8_t *port, int size)
{
    uint32_t i;

    for(i = 0; i < sizeof(mask)*8; i++)
    {
        if(mask &  (1 << i ))
        {
            port[i] = 1;
        }
    }
    return 0;
}

static void parser_init_config(const char *filename)
{
	int i;
	dictionary *ini = NULL;
	char protocol_switch_name[64];
	const char *str;

	ini = iniparser_load(filename);
	if (ini == NULL)
	{
		DPI_LOG(DPI_LOG_ERROR, "cannot parse file: %s", filename);
		exit(-1);
	}

	g_config.nb_rxq = iniparser_getint(ini, ":RXQ_NUM", 4);
//	g_config.dissector_thread_num = iniparser_getint(ini, ":DISSECTOR_THREAD_NUM", 16);

	g_config.mbuf_size = iniparser_getint(ini, ":MBUF_SIZE", RTE_MBUF_DEFAULT_DATAROOM);
	g_config.max_pkt_len = iniparser_getint(ini, ":MAX_PKT_LEN", ETHER_MAX_LEN);
	if (g_config.max_pkt_len < ETHER_MAX_LEN) {
		fprintf(stderr, "Invalid max-pkt-len=%d - should be >= %d\n", g_config.max_pkt_len, ETHER_MAX_LEN);
	} else {
		port_conf.rxmode.max_rx_pkt_len = g_config.max_pkt_len;
	}

	g_config.nb_mbuf = iniparser_getint(ini, ":NB_MBUF", NB_MBUF);

	g_config.mempool_cache_size = iniparser_getint(ini, ":MEMPOOL_CACHE_SIZE", 256);
	g_config.packet_ring_size = iniparser_getint(ini, ":PACKET_RING_SIZE", 1024 * 16 * 16);

	/* detect config */
    g_config.max_hash_node_per_thread   = iniparser_getint(ini, ":MAX_HASH_NODE_PER_THREAD"   , g_config.max_hash_node_per_thread);
    g_config.max_flow_num               = iniparser_getint(ini, ":MAX_FLOW_NUM"               , g_config.max_flow_num);
    g_config.tcp_reassemble_mempool_num = iniparser_getint(ini, ":TCP_REASSEMBLE_MEMPOOL_NUM" , g_config.tcp_reassemble_mempool_num);

    /* conversation config*/
    g_config.max_conversation_hash_node = iniparser_getint(ini, ":MAX_CONVERSATION_HASH_NODE", g_config.max_conversation_hash_node);

    /* log config */
    g_config.tbl_ring_size        = iniparser_getint(ini, ":TBL_RING_SIZE"        , g_config.tbl_ring_size);
    g_config.tbl_log_content_256k = iniparser_getint(ini, ":TBL_LOG_CONTENT_256K" , g_config.tbl_log_content_256k);
    g_config.tbl_log_content_256k_num = iniparser_getint(ini, ":TBL_LOG_CONTENT_256K_NUM" , 8192);



	g_config.write_tbl_maxtime = iniparser_getint(ini, ":WRITE_TBL_MAXTIME", 60);

	g_config.tcp_flow_timeout = iniparser_getint(ini, ":TCP_FLOW_TIMEOUT", 30);
	g_config.udp_flow_timeout = iniparser_getint(ini, ":UDP_FLOW_TIMEOUT", 30);
	g_config.sctp_flow_timeout = iniparser_getint(ini, ":SCTP_FLOW_TIMEOUT", 30);
	g_config.tcp_identify_pkt_num = iniparser_getint(ini, ":TCP_IDENTIFY_PKT_NUM", 10);
	g_config.udp_identify_pkt_num = iniparser_getint(ini, ":UDP_IDENTIFY_PKT_NUM", 8);
	g_config.sctp_identify_pkt_num = iniparser_getint(ini, ":SCTP_IDENTIFY_PKT_NUM", 10);
    g_config.per_fields_len        = iniparser_getint(ini, ":PER_FIELDS_LEN", 5000);

	g_config.tcp_resseamble_max_num = iniparser_getint(ini, ":TCP_RESSEAMBLE_MAX_NUM", 32);
	g_config.log_max_num = iniparser_getint(ini, ":LOG_MAX_NUM", 10000);
	g_config.idle_scan_period = iniparser_getint(ini, ":IDLE_SCAN_PERION", 10000);

	// 默认将每个网卡的rss设置为RSS_USE_CUSTOM
	for (unsigned i = 0; i < RTE_MAX_ETHPORTS; i++) {
		g_config.rss_use_type[i] = RSS_USE_CUSTOM;
	}
		/* 是否强制使用ip模式队列负载均衡，0-自动检测机制，1-强制使用源目ip模式*/
    g_config.ring_rss_mode   = iniparser_getint(ini, ":RING_RSS_MODE", 0);

	str = iniparser_getstring(ini, ":LOG_OUTPUT_LEVEL", "ERROR");
	if (strcmp(str, "DEBUG") == 0)
		g_config.log_output_level = DPI_LOG_DEBUG;
	else if (strcmp(str, "WARNING") == 0)
		g_config.log_output_level = DPI_LOG_WARNING;
	else
		g_config.log_output_level = DPI_LOG_ERROR;

	str = iniparser_getstring(ini, ":TBL_OUT_DIR", "/tmp/tbls/");
	snprintf(g_config.tbl_out_dir, sizeof(g_config.tbl_out_dir), "%s", str);


	str = iniparser_getstring(ini, ":TBL_FIELD_TABLE_DIR", "/tmp");
	snprintf(g_config.dpi_field_dir, sizeof(g_config.dpi_field_dir), "%s", str);

    /* 微信文件传输相关配置 */
		// --------------------------- weixin  begin-------------------------------------------
    g_config.wxf_filter             = iniparser_getint(ini, ":TBL_WXF_FILTER_SWITCH", 1);
    g_config.wxf_filter_repeat      = iniparser_getint(ini, ":TBL_WXF_FILTER_REPLEAT", 1);
    g_config.wxf_check_http_times   = iniparser_getint(ini, ":WXF_CHECK_HTTP_TIMES", 50);
    g_config.wxf_fileid_part1_byte = iniparser_getint(ini, ":WXF_FILEID_PART1_BYTE", 10);
    g_config.wxf_fileid_part2_byte = iniparser_getint(ini, ":WXF_FILEID_PART2_BYTE", 64);
	g_config.wxf_time_precise_flag = iniparser_getint(ini, ":WXF_TIME_PRECISE", 0);
	str = iniparser_getstring(ini, ":WXF_PYQ_DATA_DIR", "OFF");
	snprintf(g_config.wxf_pyq_data_dir, sizeof(g_config.wxf_pyq_data_dir), "%s", str);
    str = iniparser_getstring(ini, ":WXF_FILTER_FIELDS_CONFIG","filemd5,fileid,url");
    snprintf(g_config.wxf_filter_config, 512, "%s", str);
  g_config.wx_filedata_bytes = iniparser_getint(ini, ":WXF_FILEDATA_BYTES", 16);
	// --------------------------- weixin  end-------------------------------------------
	// gquic 配置
	g_config.gquic_wxf_filter        		= iniparser_getint(ini, ":TBL_GQUIC_WXF_FILTER_SWITCH", 1);
	str = iniparser_getstring(ini, ":GQUIC_WXF_FILTER_FIELDS_CONFIG","totalsize");
	snprintf(g_config.gquic_wxf_filter_str, 512, "%s", str);
	str = iniparser_getstring(ini, ":DEVNAME", "D00");
	snprintf(g_config.devname, sizeof(g_config.devname), "%s", str);

	str = iniparser_getstring(ini, ":DEVAREA", "");
	snprintf(g_config.devArea, sizeof(g_config.devArea), "%s", str);

	str = iniparser_getstring(ini, ":OPERATOR", NULL);
    if(NULL == str || memcmp(str, "AUTO", 4))
    {
       snprintf(g_config.operator_name, sizeof(g_config.operator_name), "%s", str);
    }
    else
    {
        memset(g_config.operator_name, 0, sizeof(g_config.operator_name));
    }

	str = iniparser_getstring(ini, ":RT_MODEL", NULL);
    if(NULL != str)
    {
       snprintf(g_config.RT_model, sizeof(g_config.RT_model), "%s", str);
    }
    else
    {
        memset(g_config.RT_model, 0, sizeof(g_config.RT_model));
    }

	str = iniparser_getstring(ini, ":LOG_OUTPUT_PATH", "./log.txt");
	snprintf(g_config.log_output_path, sizeof(g_config.log_output_path), "%s", str);

	g_config.fp_log = fopen(g_config.log_output_path, "a+");

	for (i = 0; i < PROTOCOL_MAX; i++) {
		snprintf(protocol_switch_name, sizeof(protocol_switch_name), ":PROTOCOL_SWITCH_%s", protocol_name_array[i]);
		g_config.protocol_switch[i] = iniparser_getint(ini, protocol_switch_name, 1);
		if (g_config.protocol_switch[i] != 0)
			g_config.protocol_switch[i] = 1;
	}

	g_config.conversation_switch = iniparser_getint(ini, ":CONVERSATION_SWITCH", 1);
	if (g_config.conversation_switch != 0)
		g_config.conversation_switch = 1;

	g_config.protocol_switch_wxinfo_mini = iniparser_getint(ini, ":PROTOCOL_SWITCH_WEIXIN_INFO_MINI", 0);

	g_config.trailertype = iniparser_getint(ini, ":TRAILER_TYPE", 0);
	g_config.wx_group_head_timeout = iniparser_getint(ini, ":WEXIN_GROUP_HEAD_TIMEOUT", 10);

    /* ADD_S By chunli */
    // 微信话单 服务器配置项
    str = iniparser_getstring(ini, ":WEXIN_VOICE_SESSION_SERVER_IP",                          "127.0.0.1");
    snprintf(g_config.wx_voice_ip, sizeof(g_config.wx_voice_ip),      "%s", str  );
    g_config.wx_voice_port        = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_SERVER_PORT", 1024);

    // QQ话单 服务器配置项
    str = iniparser_getstring(ini, ":QQ_VOIP_SESSION_SERVER_IP",                              g_config.wx_voice_ip);
    snprintf(g_config.qq_voip_ip,  sizeof(g_config.qq_voip_ip),       "%s", str  );
    g_config.qq_voip_port         = iniparser_getint(ini, ":QQ_VOIP_SESSION_SERVER_PORT",     g_config.wx_voice_port);

    // 微信群图像 服务器配置项
    str = iniparser_getstring(ini, ":WEXIN_GROUP_SESSION_SERVER_IP",                          g_config.wx_voice_ip);
    snprintf(g_config.wx_group_ip, sizeof(g_config.wx_group_ip),      "%s", str  );
    g_config.wx_group_port        = iniparser_getint(ini, ":WEXIN_GROUP_SESSION_SERVER_PORT", g_config.wx_voice_port);

    // 微信位置共享 服务器配置项
    str = iniparser_getstring(ini, ":WEXIN_POSITION_SHARE_SERVER_IP",                         g_config.wx_voice_ip);
    snprintf(g_config.wx_pos_ip,   sizeof(g_config.wx_pos_ip),        "%s", str  );
    g_config.wx_pos_port          = iniparser_getint(ini, ":WEXIN_POSITION_SHARE_SERVER_PORT",g_config.wx_voice_port);

    //QQ文件传输 服务器配置项
    str = iniparser_getstring(ini, ":QQ_FILE_SERVER_IP",                                      g_config.wx_voice_ip );
    snprintf(g_config.QQ_File_ip,sizeof(g_config.QQ_File_ip), "%s", str);
    g_config.QQ_File_port         = iniparser_getint(ini, ":QQ_FILE_SERVER_PORT",             g_config.wx_voice_port);

	// QQ话单 服务器配置项
    str = iniparser_getstring(ini, ":SKYPE_SESSION_SERVER_IP",                                g_config.wx_voice_ip);
    snprintf(g_config.skype_ip, sizeof(g_config.skype_ip),       "%s", str  );
    g_config.skype_port          = iniparser_getint(ini, ":SKYPE_SESSION_SERVER_PORT",        g_config.wx_voice_port);

	// QQ话单 服务器配置项
    str = iniparser_getstring(ini, ":WEIXIN_RELATION_IP",                                	  g_config.wx_voice_ip);
    snprintf(g_config.wx_rela_ip, sizeof(g_config.wx_rela_ip),	"%s", str  );
    g_config.wx_rela_port         = iniparser_getint(ini, ":WEIXIN_RELATION_PORT",        	  g_config.wx_voice_port);


    // 微信话单 业务配置项
    g_config.wx_session_timeloop  = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_TIMELOOP",       5);//微信轮询间隔时间
    g_config.wx_session_timeout   = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_TIMEOUT",       15);//微信person超时时间
    g_config.wx_session_drop      = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_DROP",           0);//微信的sessionID检查
    g_config.wx_session_pkt_size  = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_PKT_SIZE",      50);//微信通话未接通前，UDP数据域的大小为50, d5为51
    g_config.wx_session_flag_pkt  = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_FLAGS_PKT",      3);//微信通话未接通前，须有几包D5才允许进入话单解析?
    g_config.wx_session_seq_jitter= iniparser_getint(ini, ":WEXIN_VOICE_SESSION_SEQ_JITTER",  1024);//微信session 中的sequence, 允许抖动范围
    g_config.wx_session_ring_size = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_RING_SIZE",     60);//微信 1对1 响铃结束包大小
	  g_config.wx_session_filter    = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_FILTER",         0);//微信 1对1 响铃结束包大小
		g_config.wxa_recognition      = iniparser_getint(ini, ":WXA_RECOGNITION", 2);
    str = iniparser_getstring(ini,                        ":WEXIN_VOICE_SESSION_PHONE_WATCH","null");//微信话单手机号监视列表
    snprintf(g_config.wx_phone_watch, sizeof(g_config.wx_phone_watch), "%s", str);
    i                             = iniparser_getint(ini, ":WEXIN_VOICE_SESSION_PKT_FILTER",      0);//微信话单报文(9711类型), 收包时直接过滤
    parse_pkt_filter(i, g_config.drop_filter, sizeof(g_config.drop_filter)/sizeof(g_config.drop_filter[0]));

    g_config.protocol_tbl_http    = iniparser_getint(ini, ":PROTOCOL_TBL_HTTP",                  0);//HTTP 输出TBL文件
    g_config.http_rsm             = iniparser_getint(ini, ":HTTP_RSM_SWITCH",                  0);//HTTP 启动重组
    g_config.tbl_out_quote = iniparser_getint(ini, ":TBL_OUT_QUOTE",                  0);//HTTP 输出TBL文件
	g_config.qq_session_seq_jitter= iniparser_getint(ini, ":QQ_VOIP_SESSION_SEQ_JITTER",        10);//qq session 中的sequence, 允许抖动范围


	g_config.wx_red_packet_precise = iniparser_getint(ini, ":WEXIN_MSG_RP_PRIECISE",     0);
	g_config.wx_red_packet_switch = iniparser_getint(ini, ":WEXIN_MSG_RP_SWITCH",     0);


	/* wx info 是否保留不完整字符串 */
	g_config.wx_info_incomplete    		= iniparser_getint(ini, ":WX_INFO_INCOMPLETE_FLAG",   0);
	g_config.wx_info_export_illegal  	= iniparser_getint(ini, ":WX_INFO_EXPORT_ILLEGAL",   0);


	// ---------------------------- begin wxmisc
	g_config.wx_misc_payload_save    	= iniparser_getint(ini, ":DEBUG_WXMISC_PAYLOAD_SAVE",   0);

	//----------------------------- end wxmisc


    // 调试参数
    g_config.debug_weixin_voice   = iniparser_getint(ini, ":DEBUG_WEIXIN_VOIP",           0);// 调试项参数
    g_config.debug_zoom_conference= iniparser_getint(ini, ":DEBUG_ZOOM_CONFERENCE",       0);// 调试项参数
    g_config.debug_qq_voip        = iniparser_getint(ini, ":DEBUG_QQ_VOIP",               0);// 调试项参数
    g_config.debug_qq_file        = iniparser_getint(ini, ":DEBUG_QQ_FILE",               0);// 调试项参数
    g_config.debug_group_head     = iniparser_getint(ini, ":DEBUG_WEIXIN_GROUP_HEAD",     0);// 调试项参数
    g_config.debug_position_share = iniparser_getint(ini, ":DEBUG_WEIXIN_POSITION_SHARE", 0);// 调试项参数
    g_config.debug_skype_media    = iniparser_getint(ini, ":DEBUG_SKYPE_MEDIA_CHAT",      0);// 调试项参数
	g_config.debug_qq_event       = iniparser_getint(ini, ":DEBUG_QQ_EVENT",              0);// 调试项参数


	//--- http config, http rms,  HTTP_TCP 设置
    g_config.http.tcp_out_of_order= iniparser_getint(ini, ":HTTP_TCP_OUT_OF_ORDER_NUM", 20); // HTTP_TCP 乱序个数容忍值, 默认容忍抖动间隔20个
    g_config.http.tcp_padding_len = iniparser_getint(ini, ":HTTP_TCP_MISS_PADDING_LEN", 0);  // HTTP_TCP 缺包小于N时,打PADDING字符, 默认0关闭
    g_config.http.error_pcap_dump = iniparser_getint(ini, ":HTTP_TCP_ERROR_PCAP_DUMP", 0);   // HTTP_TCP 无法解析时输出 PCAP,默认关闭
    str = iniparser_getstring(ini, ":HTTP_TCP_MISS_PADDING_STR", " ");                       // HTTP_TCP PADDING 字符, 默认空格.
    memcpy(g_config.http.tcp_padding_str, str, strlen(str));
	g_config.http.http_strip_cache_size    =iniparser_getint(ini, ":HTTP_STRIP_CACHE_SIZE", 512*1024);
    g_config.http.http_switch_store_file   =iniparser_getint(ini, ":HTTP_SWITCH_STORE_FILE", 0);
    g_config.http.http_switch_response_file=iniparser_getint(ini, ":HTTP_SWITCH_RESPONSE_FILE", 1);
    g_config.http.http_file_size        =iniparser_getint(ini, ":HTTP_STORE_FILE_SIZE", 100*1024);
    if(g_config.http.http_file_size>g_config.http.http_strip_cache_size){
        g_config.http.http_file_size=g_config.http.http_strip_cache_size;
    }
	//--- http config, http rms,  HTTP_TCP 设置

    str = iniparser_getstring(ini, ":HTTP_HOST_URI_QQ", "http_host_uri_qq_list.txt");
    http_host_uri_list_init("HTTP_QQ", &g_config.http_qq[0], sizeof(g_config.http_qq)/sizeof(g_config.http_qq[0]), str);

    str = iniparser_getstring(ini, ":HTTP_HOST_URI_WX", "http_host_uri_wx_list.txt");
    http_host_uri_list_init("HTTP_WX", &g_config.http_wx[0], sizeof(g_config.http_wx)/sizeof(g_config.http_wx[0]), str);
	g_config.http_wx_host = iniparser_getint(ini, ":HTTP_HOST_URI_WX_HOST", 0);   // 是否进行 host 匹配

    str = iniparser_getstring(ini, ":HTTP_HOST_URI_WX_MSG", "http_host_uri_wx_msg_list.txt");
    http_host_uri_list_init("HTTP_WX_MSG", &g_config.http_wx_msg[0], sizeof(g_config.http_wx_msg)/sizeof(g_config.http_wx_msg[0]), str);
    /* ADD_E By chunli */

  str = iniparser_getstring(ini, ":HTTP_HOST_WXPAY", "http_host_wxpay.txt");
  http_host_wxpay_init("HTTP_WXPAY", &g_config.wxpay_hash, str);
  // printf("hash_table size = %d\n", g_hash_table_size(g_config.wxpay_hash));
  //   g_hash_table_foreach(g_config.wxpay_hash, print_wxpay, NULL);


  strncpy(g_config.ip_interface, iniparser_getstring(ini, ":IP_INTERFACE", "ens192"), sizeof(g_config.ip_interface));
  strncpy(g_config.tbl_out_format, iniparser_getstring(ini, ":TBL_OUT_FORMAT", "tbl"), sizeof(g_config.tbl_out_format));
  g_config.tcp_rsm_report_err=iniparser_getint(ini, ":TCP_RSM_REPORT_ERR", 0);

  str = iniparser_getstring(ini, ":LUA_ADAPT_DIR", "./lua_adapt/");
  strncpy(g_config.lua_adapt_dir, str , strlen(str));
  str = iniparser_getstring(ini, ":LUA_ADAPT_SCRIPT_TYPE", ".yalua");
  strncpy(g_config.lua_adapt_script_type, str , strlen(str));
  g_config.stop_time=iniparser_getint(ini, ":DPDK_STOP_RECEIVE_TIME", 100*1024);
  g_config.dpdk_stop_receive=iniparser_getint(ini, ":DPDK_STOP_RECEIVE", 0);

	iniparser_freedict(ini);
}

/* Parse the argument given in the command line of the application */
static int
parser_args(int argc, char **argv, char *prgname)
{
	int opt;
	int i;
	char **argvopt;
	const char *optstring = "r:p:l:v";
	char core_str[256];
	char *tmp;

	argvopt = argv;

	while ((opt = getopt(argc, argvopt, optstring)) != EOF) {
		switch (opt) {
		case 'r':
			i = 0;
			strncpy(core_str, optarg, sizeof(core_str));
			tmp = strtok(core_str, ",");
			while (tmp) {
				g_config.pkt_rcv_core_id[i] = atoi(tmp);
				g_config.pkt_rcv_queue_id[atoi(tmp)] = i;
				i++;
				tmp = strtok(NULL, ",");
			}
			g_config.nb_rxq = i;
			break;

		case 'p':
			i = 0;
			strncpy(core_str, optarg, sizeof(core_str));
			tmp = strtok(core_str, ",");
			while (tmp) {
				g_config.pkt_process_core_id[i++] = atoi(tmp);
				tmp = strtok(NULL, ",");
			}
			g_config.dissector_thread_num = i;

			if (g_config.dissector_thread_num > MAX_FLOW_THREAD_NUM) {
				fprintf(stderr, "the max dissector thread num is 32\n");
				exit(-1);
			}
			break;

		case 'l':
			i = 0;
			strncpy(core_str, optarg, sizeof(core_str));
			// g_config.log_core_id = atoi(core_str);
			tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max write tbl thread num is 4");
                    exit(-1);
                }
                g_config.log_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.log_thread_num = i;
			break;

        case 'v':
            printf("%s %s\n", prgname, PROJECT_VERSION_STR);
            return -1;

		default:
			print_usage(prgname);
			return -1;
		}
	}

	return 0;
}


extern rte_atomic64_t drop_pkts;
extern rte_atomic64_t drop_bytes;
extern rte_atomic64_t receive_pkts;
extern rte_atomic64_t receive_bytes;
extern rte_atomic64_t dpi_fail_pkts;
extern rte_atomic64_t test_pkts1;
extern rte_atomic64_t test_pkts2;
extern rte_atomic64_t test_pkts3;
extern rte_atomic64_t test_pkts4;

/*收包线程处理函数*/
static int packet_receive_thread_func(__attribute__((unused)) void *ptr_data)
{
	int i;
	int ret;
	int nb_ports;
	int cnt_recv_frames;
	int check;
	unsigned lcore_id;

	uint8_t portid;
	uint8_t queueid;
	uint8_t proto;

	uint16_t packet_len;
	uint16_t ip_offset;
	uint16_t type;
	uint16_t ip_len;

    uint8_t drop_filter[16];
	const uint8_t *packet;
	struct rte_mbuf  *mb;
	struct rte_mbuf *pkts_burst[MAX_PKT_BURST];

	const struct dpi_ethhdr *ethhdr;
	const struct dpi_iphdr *iph;
	const struct dpi_ipv6hdr *iph6;

	lcore_id = rte_lcore_id();
	nb_ports = rte_eth_dev_count_avail();
    memcpy(drop_filter, g_config.drop_filter, sizeof(g_config.drop_filter));
	unsigned int ring_id = 0;
    unsigned int rc      = 0;
	while (1) {
    int run_time = g_config.g_now_time - g_config.g_start_time;

    if(run_time > g_config.stop_time &&g_config.dpdk_stop_receive == 1){
       printf("STOP RECEIVE PACKET!!!!!!!!!\n");
       return 0;
    }
		for (i = 0; i < (int)g_config.nb_rxq; i++) {
			if (lcore_id == g_config.pkt_rcv_core_id[i])
				break;
		}
		if (i >= (int)g_config.nb_rxq)
			break;

		for (portid = 0; portid < nb_ports; portid++) {
				if(g_config.data_source == 3 && portid != g_config.pcap_port_id)
						continue;

			queueid = g_config.pkt_rcv_queue_id[lcore_id];

			cnt_recv_frames = rte_eth_rx_burst(portid, queueid, pkts_burst, MAX_PKT_BURST);
			if (cnt_recv_frames > 0) {
				for (i = 0; i < cnt_recv_frames; i++) {
					mb = pkts_burst[i];
					// rte_atomic64_inc(&receive_pkts);
					// rte_atomic64_add(&receive_bytes, mb->data_len);
					// rte_pktmbuf_free(mb);
					// continue;

          //dpdk17.x版本无法获取网卡硬件时间，升级到18.x版本后请改为rte_eth_conf.rte_eth_rxmode.offloads=PKT_RX_TIMESTAMP
				if (mb->timestamp == 0) {
          mb->timestamp = g_config.g_now_time_usec;
				}
					packet = (const uint8_t *)mb->buf_addr + mb->data_off;
					packet_len = mb->data_len;

					// enable pkt fliter of udp 97 ?
					if(drop_filter[portid] && drop_udp_97(packet, packet_len))
					{
							rte_pktmbuf_free(mb);
							continue;
					}

					rte_atomic64_inc(&receive_pkts);
					rte_atomic64_add(&receive_bytes, mb->data_len);


					ethhdr = (const struct dpi_ethhdr *)&packet[0];
					ip_offset = sizeof(struct dpi_ethhdr);
					check = ntohs(ethhdr->h_proto);

					if (check >= 0x0600)
						type = check;
					else {
						DPI_LOG(DPI_LOG_DEBUG, "unknown eth packet type");
						rte_atomic64_inc(&drop_pkts);
						rte_atomic64_add(&drop_bytes, mb->data_len);
						rte_pktmbuf_free(mb);
						continue;
					}

					switch(type) {
						case VLAN:
							type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
							ip_offset += 4;
							// double tagging for 802.1Q
							if(type == 0x8100) {
								type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
								ip_offset += 4;
							}
							break;
						case MPLS_UNI:
						case MPLS_MULTI:
							type = ETH_P_IP;
							ip_offset += 4;
							break;
						case PPPoE:
							type = ETH_P_IP;
							ip_offset += 8;
							break;
						default:
							break;
					}

					if (type != ETH_P_IP && type != ETH_P_IPV6) {
						DPI_LOG(DPI_LOG_DEBUG, "unknown eth packet type");
						rte_atomic64_inc(&drop_pkts);
						rte_atomic64_add(&drop_bytes, mb->data_len);
						rte_pktmbuf_free(mb);
						continue;
					}

					iph = (const struct dpi_iphdr *) &packet[ip_offset];

					if (iph->version == 4) {
						ip_len = ((uint16_t)iph->ihl * 4);
						proto = iph->protocol;
						iph6 = NULL;
					} else if (iph->version == 6) {
						iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
						proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
						ip_len = sizeof(struct dpi_ipv6hdr);
						iph = NULL;
						if (proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
							const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
							proto = options[0];
							ip_len += 8 * (options[1] + 1);
						}
					} else {
						DPI_LOG(DPI_LOG_DEBUG, "unknown ip type");
						rte_atomic64_inc(&drop_pkts);
						rte_atomic64_add(&drop_bytes, mb->data_len);
						rte_pktmbuf_free(mb);
						continue;
					}
          uint16_t sport = 0;
          uint16_t dport = 0;
					/*gtp协议按照内层ip地址分发*/
					if (proto == IPPROTO_UDP) {
						const struct dpi_udphdr *udp = (const struct dpi_udphdr *)&packet[ip_offset + ip_len];
            sport =	 ntohs(udp->source);
            dport = ntohs(udp->dest);

						if (((sport == GTP_U_V1_PORT) || (dport == GTP_U_V1_PORT))
								&& (ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8 + 20 <= packet_len)) {

							u_int offset = ip_offset + ip_len + sizeof(struct dpi_udphdr);
							uint8_t flags = packet[offset];
							uint8_t message_type = packet[offset + 1];

							if ((((flags & 0xE0) >> 5) == 1 /* GTPv1 */)
									&& (message_type == 0xFF /* T-PDU */)) {

								ip_offset = ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8; /* GTPv1 header len */
								if (flags & 0x04) ip_offset += 1; /* next_ext_header is present */
								if (flags & 0x02) ip_offset += 4; /* sequence_number is present (it also includes next_ext_header and pdu_number) */
								if (flags & 0x01) ip_offset += 1; /* pdu_number is present */

								iph = (const struct dpi_iphdr *) &packet[ip_offset];

								if (iph->version == 6) {
									iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
									iph = NULL;
								} else if (iph->version != 4) {
									rte_atomic64_inc(&drop_pkts);
									rte_atomic64_add(&drop_bytes, mb->data_len);
									rte_pktmbuf_free(mb);
									continue;
								}
							}
						}
					}else if (proto == IPPROTO_TCP) {
            const struct dpi_tcphdr *tcp = (const struct dpi_tcphdr *)&packet[ip_offset + ip_len];
            sport =	 ntohs(tcp->source);
            dport = ntohs(tcp->dest);
          }

					rte_atomic64_inc(&test_pkts1);

					if (iph) {
						// if (g_config.rss_use_type[portid] == RSS_USE_CUSTOM) {
              // 使用大端口做取模，防止入队不均匀
              if(0 != sport && 0 != dport){
                if(sport > dport){
                    ring_id = (ntohl(iph->saddr) + sport) % g_config.dissector_thread_num;
                }else {
                    ring_id = (ntohl(iph->daddr) + dport) % g_config.dissector_thread_num;
                }
              }else {
                ring_id = (ntohl(iph->saddr) + ntohl(iph->daddr)) % g_config.dissector_thread_num;
              }
						// else {
						// 	ring_id = mb->hash.rss % g_config.dissector_thread_num;
						// }
						ret = rte_ring_mp_enqueue_burst(packet_ring[ring_id], (void * const*)&mb, 1, NULL);
						if (ret < 1) {
						//if ((ret & RTE_RING_SZ_MASK) != 1) {
              rte_atomic64_inc(&dpi_fail_pkts);
							DPI_LOG(DPI_LOG_WARNING, "ipv4 enqueue packet ring error, ring_id=%d, ret=%d", ring_id, ret);
							rte_pktmbuf_free(mb);
						}

						rte_atomic64_inc(&test_pkts2);
					} else if (iph6) {
						// if (g_config.rss_use_type[portid] == RSS_USE_CUSTOM) {
              if(0 != sport && 0 != dport){
                if(sport > dport){
                    ring_id = (*((const uint64_t *)iph6->ip6_src)
                              + *((const uint64_t *)iph6->ip6_src + 1)
                              + sport) % g_config.dissector_thread_num;
                }else {
                    ring_id = (*((const uint64_t *)iph6->ip6_dst)
                              + *((const uint64_t *)iph6->ip6_dst + 1)+ dport) % g_config.dissector_thread_num;
                }
              }else {
							ring_id = (*((const uint64_t *)iph6->ip6_src)
								+ *((const uint64_t *)iph6->ip6_src + 1)
								+ *((const uint64_t *)iph6->ip6_dst)
								+ *((const uint64_t *)iph6->ip6_dst + 1)) % g_config.dissector_thread_num;
              }
						// }
						// else {
						// 	ring_id = mb->hash.rss % g_config.dissector_thread_num;
						// }
						ret = rte_ring_mp_enqueue_burst(packet_ring[ring_id], (void * const*)&mb, 1, NULL);
						if (ret < 1) {
              rte_atomic64_inc(&dpi_fail_pkts);
							DPI_LOG(DPI_LOG_WARNING, "ipv6 enqueue packet ring error, ring_id=%d, ret=%d", ring_id, ret);
							rte_pktmbuf_free(mb);
						}
						rte_atomic64_inc(&test_pkts3);
					} else {
						rte_atomic64_inc(&drop_pkts);
						rte_atomic64_add(&drop_bytes, mb->data_len);
						rte_pktmbuf_free(mb);
					}
				}
			}
		}
	}

	return 0;
}

/*解析线程的处理函数*/
static void *packet_process_thread_func(void *_thread_id)
{
	int i;
//	int pkt_status = PKT_OK;
	int cnt_recv_frames;
	long thread_id = (long) _thread_id;
	struct rte_mbuf *pkts_burst[MAX_PKT_BURST * 2];
	struct rte_mbuf  *mb;

	/*线程绑定核心*/
	int core_affinity = g_config.pkt_process_core_id[thread_id];
	if (core_affinity >= 0) {
		cpu_set_t cpuset;
		CPU_ZERO(&cpuset);
		CPU_SET(core_affinity, &cpuset);

		if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0)
			DPI_LOG(DPI_LOG_WARNING, "Error while binding thread %ld to core %d", thread_id, core_affinity);
	}
	DPI_LOG(DPI_LOG_DEBUG, "Running thread %ld. on core %d", thread_id, core_affinity);
  th_alloc = ya_allocator_create_pool(YA_ALLOC_FLAG_NONE);

	while (1) {
		if (g_config.exit) {
			usleep(1);
			continue;
		}

		cnt_recv_frames = rte_ring_sc_dequeue_burst(packet_ring[thread_id], (void **)pkts_burst, MAX_PKT_BURST * 2, NULL);
		if (cnt_recv_frames > 0) {
			for (i = 0; i < cnt_recv_frames; i++) {
				rte_atomic64_inc(&test_pkts4);
				mb = pkts_burst[i];
        flow_thread_info[thread_id].timestamp = mb->timestamp;
				workflow_process_packet2(&flow_thread_info[thread_id], mb, g_config.g_now_time_usec,
						(const u_char *)mb->buf_addr + mb->data_off, mb->data_len);
				mb->timestamp = 0;
				rte_pktmbuf_free(mb);
			}
		} else {
			/*当队列为空时，销毁超时会话*/
			flow_thread_info[thread_id].last_time = g_config.g_now_time_usec;
			do_idle_flow_free(thread_id, g_config.g_now_time_usec, 0);
			usleep(1);
		}
	}
  ya_allocator_destroy(th_alloc);

	return NULL;
}

static int init_mem(unsigned nb_mbuf)
{
	int socketid;
	unsigned lcore_id;
	char s[64];

	for (lcore_id = 0; lcore_id < RTE_MAX_LCORE; lcore_id++) {
		if (rte_lcore_is_enabled(lcore_id) == 0)
			continue;

		if (numa_on)
			socketid = rte_lcore_to_socket_id(lcore_id);
		else
			socketid = 0;

		if (socketid >= NB_SOCKETS) {
			DPI_LOG(DPI_LOG_ERROR, "Socket %d of lcore %u is out of range %d",
					socketid, lcore_id, NB_SOCKETS);
			exit(-1);
		}

		if (pktmbuf_pool[socketid] == NULL) {
			snprintf(s, sizeof(s), "mbuf_pool_%d", socketid);
			pktmbuf_pool[socketid] =
				rte_pktmbuf_pool_create(s, nb_mbuf,
					g_config.mempool_cache_size, 0,
					g_config.mbuf_size + RTE_PKTMBUF_HEADROOM, socketid);
//					RTE_MBUF_DEFAULT_BUF_SIZE, socketid);
			if (pktmbuf_pool[socketid] == NULL) {
				DPI_LOG(DPI_LOG_ERROR, "Cannot init mbuf pool on socket %d", socketid);
				exit(-1);
			} else
				DPI_LOG(DPI_LOG_DEBUG, "Allocated mbuf pool on socket %d", socketid);
		}
	}
	return 0;
}

static void init_flow_obj(struct rte_mempool *mp, __attribute__((unused)) void *arg,
	    void *obj, unsigned i)
{
	UNUSED(i);
	memset(obj, 0, mp->elt_size);
}

#if 0
static int init_flow_mempool(int thread_id, unsigned nb_flow)
{
	char s[64];

	if (thread_id < MAX_FLOW_THREAD_NUM) {
		if (flow_mempool[thread_id] == NULL) {
			snprintf(s, sizeof(s), "flow_mempool_%d", thread_id);
			flow_mempool[thread_id] = rte_mempool_create(s, nb_flow,
												sizeof(struct flow_info), 0, 0,
												NULL, NULL,
												init_flow_obj, NULL,
												SOCKET_ID_ANY,
												MEMPOOL_F_NO_CACHE_ALIGN | MEMPOOL_F_SP_PUT | MEMPOOL_F_SC_GET);


			if (flow_mempool[thread_id] == NULL) {
				DPI_LOG(DPI_LOG_ERROR, "Cannot init flow mem pool on thread %d", thread_id);
				exit(-1);
			}
			else
				DPI_LOG(DPI_LOG_DEBUG, "Allocated flow mem pool on thread %d", thread_id);
		}
	}
	return 0;
}
#endif

static int init_flow_mempool(unsigned nb_flow)
{
	if (flow_mempool == NULL) {
		flow_mempool= rte_mempool_create("flow_mempool", nb_flow,
					sizeof(struct flow_info), RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
					NULL, NULL,
					init_flow_obj, NULL,
					SOCKET_ID_ANY,
					0);
	//				MEMPOOL_F_NO_CACHE_ALIGN);

		if (flow_mempool == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot init flow mem pool");
			exit(-1);
		}
		else
			DPI_LOG(DPI_LOG_DEBUG, "Allocated flow mem pool");
	}
	return 0;
}

static int init_tcp_reassemble_mempool(void)
{
	if (tcp_reassemble_mempool == NULL) {
		tcp_reassemble_mempool = rte_mempool_create("tcp_reassemble_mempool", g_config.tcp_reassemble_mempool_num,
											sizeof(struct tcp_reassemble), RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
											NULL, NULL,
											NULL, NULL,
											SOCKET_ID_ANY,
											0);
											//MEMPOOL_F_NO_CACHE_ALIGN);


		if (tcp_reassemble_mempool == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot init tcp_reassemble_mempool");
			exit(-1);
		}
		else
			DPI_LOG(DPI_LOG_DEBUG, "Allocated tcp_reassemble_mempool");
	}

	return 0;
}

static int init_flow_module(int thread_num) {
	int thread_id;
	char hash_name[64] = {0};
	int i;

	/* setup config */
    hash_params.entries = g_config.max_hash_node_per_thread;

	for(thread_id = 0; thread_id < thread_num; thread_id++) {
		memset(&flow_thread_info[thread_id], 0, sizeof(flow_thread_info[thread_id]));

		flow_thread_info[thread_id].thread_id = thread_id;
		snprintf(hash_name, sizeof(hash_name), "hash_%d", thread_id);
		hash_params.name = hash_name;
		flow_thread_info[thread_id].hash = rte_hash_create(&hash_params);
		if (flow_thread_info[thread_id].hash == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "create hash failed");
			exit(-1);
		}

		for (i = 0; i < TIMEOUT_MAX; i++) {
			INIT_LIST_HEAD(&flow_thread_info[thread_id].timeout_head[i]);
		}
	}

	return 0;
}



static void init_dissector_module(void)
{
	int i;
	for(i=0;i<TBL_LOG_MAX;i++){
		if(tbl_log_array[i].init_func!=NULL){
			tbl_log_array[i].init_func();
		}
	}
}

static void init_dissector_thread(void)
{
	int status;
	unsigned int i;

	//init_flow_mempool(MAX_FLOW_NUM);
	init_flow_mempool(g_config.max_flow_num);

	for (i = 0; i < g_config.dissector_thread_num; i++) {
		char packet_ring_name[64] = {0};
		snprintf(packet_ring_name, sizeof(packet_ring_name), "packet_ring_%d", i);
		packet_ring[i] = rte_ring_create(packet_ring_name, g_config.packet_ring_size, SOCKET_ID_ANY, RING_F_SC_DEQ);
		if (packet_ring[i] == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "error while create packet ring");
			exit(-1);
		}
		if (rte_ring_lookup(packet_ring_name) != packet_ring[i]) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name");
			exit(-1);
		}
	}

	for(i = 0; i < g_config.dissector_thread_num; i++) {
		long arg = i;
		status = pthread_create(&flow_thread_info[i].pthread, NULL, packet_process_thread_func, (void *)arg);
		if(status != 0) {
			DPI_LOG(DPI_LOG_ERROR, "error on create %u thread", i);
			exit(-1);
		}
	}
}

static void init_log_thread(void)
{
	int status;
	pthread_t th;
	long i;
	// for(i=0; i < g_config.log_thread_num; i++){
  status = pthread_create(&th, NULL, write_tbl_log_to_file_func, 0);
  if(status != 0) {
      // DPI_SYS_LOG(DPI_LOG_ERROR, "error on create log thread");
      exit(-1);
  }
    // }
}

static void init_record_log_thread(void)
{
	int status;
	pthread_t th;
	long i;
	for(i=0; i < g_config.log_thread_num; i++){
        status = pthread_create(&th, NULL, write_tbl_log_to_file_func_, (void*)i);
        if(status != 0) {
            // DPI_SYS_LOG(DPI_LOG_ERROR, "error on create log thread");
            exit(-1);
        }
    }
}

// 微信群图像 依赖 HTTP, 他没有自己的初始化线程, handle 的初始化工作, 需要提前创建
void init_wx_gh_thread(void);


static char* flowtype_to_str(uint16_t ftype)
{
	uint16_t i;
	static struct {
		char str[16];
		uint16_t ftype;
	} ftype_table[] = {
		{"ipv4", RTE_ETH_FLOW_IPV4},
		{"ipv4-frag", RTE_ETH_FLOW_FRAG_IPV4},
		{"ipv4-tcp", RTE_ETH_FLOW_NONFRAG_IPV4_TCP},
		{"ipv4-udp", RTE_ETH_FLOW_NONFRAG_IPV4_UDP},
		{"ipv4-sctp", RTE_ETH_FLOW_NONFRAG_IPV4_SCTP},
		{"ipv4-other", RTE_ETH_FLOW_NONFRAG_IPV4_OTHER},
		{"ipv6", RTE_ETH_FLOW_IPV6},
		{"ipv6-frag", RTE_ETH_FLOW_FRAG_IPV6},
		{"ipv6-tcp", RTE_ETH_FLOW_NONFRAG_IPV6_TCP},
		{"ipv6-udp", RTE_ETH_FLOW_NONFRAG_IPV6_UDP},
		{"ipv6-sctp", RTE_ETH_FLOW_NONFRAG_IPV6_SCTP},
		{"ipv6-other", RTE_ETH_FLOW_NONFRAG_IPV6_OTHER},
		{"l2_payload", RTE_ETH_FLOW_L2_PAYLOAD},
		{"port", RTE_ETH_FLOW_PORT},
		{"vxlan", RTE_ETH_FLOW_VXLAN},
		{"geneve", RTE_ETH_FLOW_GENEVE},
		{"nvgre", RTE_ETH_FLOW_NVGRE},
	};

	for (i = 0; i < RTE_DIM(ftype_table); i++) {
		if (ftype_table[i].ftype == ftype)
			return ftype_table[i].str;
	}

	return NULL;
}

static int dpdk_main(int argc, char **argv)
{
    int argc_app;
    char **argv_app;
    uint8_t portid;
    uint8_t socketid;
    int ret;
    unsigned int i;
    unsigned int nb_ports;
    unsigned int lcore_id;
    uint32_t nb_lcores;
    struct rte_eth_txconf *txconf;
    struct rte_eth_dev_info dev_info;
    uint32_t flow_type, idx, offset;
    struct rte_eth_hash_filter_info info;
    const char *str;
    const char *status;
    const char *dev_name = NULL;

    /* skip the dpdk args */
    for (argc_app = 1; argc_app < argc && strcmp(argv[argc - argc_app], "--") != 0; argc_app++);
    argv_app = argv + argc - argc_app;

    /* parse app args, argv_app may begin with "--" */
    ret = parser_args(argc_app, argv_app, argv[0]);
    if (ret < 0) return -1;

	ret = rte_eal_init(argc, argv);
	if (ret < 0) {
        DPI_LOG(DPI_LOG_ERROR, "Invalid EAL parameters:%s", rte_strerror(rte_errno));
		exit(-1);
	}
	argc -= ret;
	argv += ret;

		int sid=0;
    for (sid = 0; sid < RTE_MAX_LCORE; sid++) {
        if (rte_lcore_is_enabled(sid) == 0)
            continue;

        if (numa_on)
            g_config.socketid = rte_lcore_to_socket_id(sid);
        else
            g_config.socketid = 0;

        if (g_config.socketid >= NB_SOCKETS) {
            DPI_LOG(DPI_LOG_ERROR, "CONFIG Socket %d of lcore %u is out of range %d",
                    g_config.socketid, sid, NB_SOCKETS);
            exit(-1);
        }
    }

	nb_ports = rte_eth_dev_count_avail();
	nb_lcores = rte_lcore_count();
	g_config.nb_rxq = g_config.nb_rxq > nb_lcores ? nb_lcores : g_config.nb_rxq;
	nb_txq = nb_txq > nb_lcores ? nb_lcores : nb_txq;

  init_mem(g_config.nb_mbuf - 1);
  init_thread_timer();
  init_flow_module(g_config.dissector_thread_num);
  init_tcp_reassemble_mempool();
  ya_record_init();
  init_dissector_module();
  /*这里初始化了两个log线程，后续替代旧的log*/
  init_tbl_log();
  init_tbl_record_log();
  init_dissector_thread();
  init_log_thread();
  init_record_log_thread();
  init_conversation();
  init_dissector_wx_sesion_thread();
  init_dissector_qq_sesion_thread();

  init_wx_http_sub_handle();

  init_weixin();

  init_dissector_skype_sesion_thread();
  init_dissector_zoom_sesion_thread();
  init_dissector_qq_event_thread();

  init_wx_tm_data();
  init_wx_relation();
  init_wx_peers();
  init_wxpay();
        /* initialize all ports */
	for (portid = 0; portid < nb_ports; portid++) {
		// 根据网卡驱动名确定RSS使用方式, 目前支持驱动：[ixgbe i40e]
		for (unsigned i = 0; i < RTE_MAX_ETHPORTS; i++) {
			if (rte_eth_devices[i].state != RTE_ETH_DEV_ATTACHED || rte_eth_devices[i].data->port_id != portid)
				continue;

			if(1==g_config.ring_rss_mode){
      	g_config.rss_use_type[portid] = RSS_USE_CUSTOM;
					if(   !strcmp("pcap_dir", rte_eth_devices[i].device->driver->name)
							|| !strcmp("pcap_recur", rte_eth_devices[i].device->driver->name))
					{
							g_config.pcap_port_id = portid;
							g_config.data_source  = 3;
					}
			}else{
					if (!strcmp("net_ixgbe", rte_eth_devices[i].device->driver->name)) {
							g_config.rss_use_type[portid] = RSS_USE_CONF;
					}
					else if (!strcmp("net_i40e", rte_eth_devices[i].device->driver->name)) {
							g_config.rss_use_type[portid] = RSS_USE_FILTER_CTRL;
					}
					else {
							g_config.rss_use_type[portid] = RSS_USE_CUSTOM;
							if(   !strcmp("pcap_dir", rte_eth_devices[i].device->driver->name)
									|| !strcmp("pcap_recur", rte_eth_devices[i].device->driver->name))
							{
									g_config.pcap_port_id = portid;
									g_config.data_source  = 3;
							}
					}
			}
			// dev_name = rte_eth_devices[i].device->driver->name;
      // printf("dev_name = %s\n", dev_name);
			// if (!strcmp("net_ixgbe", rte_eth_devices[i].device->driver->name)) {
			// 	g_config.rss_use_type[portid] = RSS_USE_CONF;
			// }
			// else if (!strcmp("net_i40e", rte_eth_devices[i].device->driver->name)) {
			// 	g_config.rss_use_type[portid] = RSS_USE_FILTER_CTRL;
			// }
			// else {
			// 	g_config.rss_use_type[portid] = RSS_USE_CUSTOM;
			// }

      // if (strcmp("pcap_dir", dev_name) == 0 || strcmp("pcap_recur", dev_name) == 0) {
      //   g_config.pcap_port_id = portid;
      // }
		}
  }

  for (portid = 0; portid < nb_ports; portid++) {
    if(g_config.data_source == 3 && portid != g_config.pcap_port_id)
				continue;

		rte_eth_dev_info_get(portid, &dev_info);
		port_conf.rx_adv_conf.rss_conf.rss_hf&=dev_info.flow_type_rss_offloads;
		ret = rte_eth_dev_configure(portid, g_config.nb_rxq, (uint16_t)nb_txq, &port_conf);
		if (ret < 0) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot configure device: err=%d, port=%d", ret, portid);
			exit(-1);
		}
		ret = rte_eth_dev_adjust_nb_rx_tx_desc(portid, &nb_rxd, &nb_txd);
		if (ret < 0) {
			DPI_LOG(DPI_LOG_ERROR, "Cannot adjust number of descriptors: err=%d, port=%d", ret, portid);
			exit(-1);
		}
		/* init one TX queue per couple (lcore,port) */
		for (i = 0; i < nb_txq; i++) {
			// socketid = rte_eth_dev_socket_id(portid);
			rte_eth_dev_info_get(portid, &dev_info);
			txconf = &dev_info.default_txconf;

			ret = rte_eth_tx_queue_setup(portid, i, nb_txd, g_config.socketid, txconf);
			if (ret < 0) {
				DPI_LOG(DPI_LOG_ERROR, "rte_eth_tx_queue_setup: err=%d, port=%d", ret, portid);
				exit(-1);
			}
		}

		for (i = 0; i < g_config.nb_rxq; i++) {
            // socketid = rte_lcore_to_socket_id(rte_lcore_id());
			ret = rte_eth_rx_queue_setup(portid, i, nb_rxd,
					g_config.socketid, NULL, pktmbuf_pool[g_config.socketid]);
			if (ret < 0) {
				DPI_LOG(DPI_LOG_ERROR, "rte_eth_rx_queue_setup: err=%d, port=%d", ret, portid);
				exit(-1);
			}
		}

		if (g_config.rss_use_type[portid] == RSS_USE_FILTER_CTRL) {
			// 检测是否支持hash filter
			if (rte_eth_dev_filter_supported(portid, RTE_ETH_FILTER_HASH) < 0) {
				DPI_LOG(DPI_LOG_ERROR, "RTE_ETH_FILTER_HASH not supported on port %d\n", portid);
				exit(-1);
			}
			// 读
			memset(&info, 0, sizeof(info));
			info.info_type = RTE_ETH_HASH_FILTER_GLOBAL_CONFIG;
			ret = rte_eth_dev_filter_ctrl(portid, RTE_ETH_FILTER_HASH,
										  RTE_ETH_FILTER_GET, &info);
			if (ret < 0) {
				DPI_LOG(DPI_LOG_ERROR, "Cannot get hash global configurations by port %d\n", portid);
				exit(-1);
			}
			// 修改-写
			info.info_type = RTE_ETH_HASH_FILTER_GLOBAL_CONFIG;
			info.info.global_conf.hash_func = RTE_ETH_HASH_FUNCTION_TOEPLITZ;
			for (i = 0; i < RTE_DIM(rss_flow_type); i++) {
				flow_type = rss_flow_type[i];
				idx = flow_type / (CHAR_BIT * sizeof(uint32_t));
				offset = flow_type % (CHAR_BIT * sizeof(uint32_t));
				info.info.global_conf.valid_bit_mask[idx] |= (1UL << offset);
				info.info.global_conf.sym_hash_enable_mask[idx] |= (1UL << offset);
			}
			ret = rte_eth_dev_filter_ctrl(portid, RTE_ETH_FILTER_HASH, RTE_ETH_FILTER_SET, &info);
			if (ret < 0) {
				DPI_LOG(DPI_LOG_ERROR, "Cannot set global hash configurations by port %d\n", portid);
				exit(-1);
			}
			else {
				DPI_LOG(DPI_LOG_DEBUG, "Global hash configurations have been set succcessfully by port %d\n", portid);
			}
			// 查看结果
			// for (i = 0; i < RTE_ETH_FLOW_MAX; i++) {
			// 	idx = i / UINT32_BIT;
			// 	offset = i % UINT32_BIT;
			// 	if (!(info.info.global_conf.valid_bit_mask[idx] & (1UL << offset)))
			// 		continue;
			// 	str = flowtype_to_str(i);
			// 	if (!str)
			// 		continue;
			// 	status = (info.info.global_conf.sym_hash_enable_mask[idx] & (1UL << offset)) ? "enabled" : "disabled";
			// 	DPI_LOG(DPI_LOG_DEBUG, "Symmetric hash is %s globally for flow type %s by port %d\n", status, str, portid);
			// }
			// 开启对称rss
			info.info_type = RTE_ETH_HASH_FILTER_SYM_HASH_ENA_PER_PORT;
			info.info.global_conf.hash_func = RTE_ETH_HASH_FUNCTION_TOEPLITZ;
			info.info.enable = 1;
			ret = rte_eth_dev_filter_ctrl(portid, RTE_ETH_FILTER_HASH, RTE_ETH_FILTER_SET, &info);
			if (ret < 0) {
				DPI_LOG(DPI_LOG_ERROR, "Cannot get symmetric hash enable per port on port %u\n", portid);
				exit(-1);
			}
			else {
				status = info.info.enable ? "enabled" : "disabled";
				DPI_LOG(DPI_LOG_DEBUG, "Symmetric hash is %s on port %u\n", status, portid);
			}
		}
	}

	/* start ports */
	for (portid = 0; portid < nb_ports; portid++) {
    if(g_config.data_source == 3  && portid != g_config.pcap_port_id)
				continue;
		int r = rte_eth_dev_set_mtu(portid, g_config.max_pkt_len);
		if (r == 0) {
			DPI_LOG(DPI_LOG_DEBUG, "Port %i: MTU set to %i", portid, g_config.max_pkt_len);
		} else if (r == -ENOTSUP) {
			DPI_LOG(DPI_LOG_ERROR, "Port %i: Set MTU not supported\n", portid);
			fprintf(stderr, "Port %i: Set MTU not supported\n", portid);
			exit(-1);
		} else	{
			DPI_LOG(DPI_LOG_ERROR, "Port %i: Error setting MTU\n", portid);
			fprintf(stderr, "Port %i: Error setting MTU\n", portid);
			exit(-1);
		}

		ret = rte_eth_dev_start(portid);
		if (ret < 0) {
			DPI_LOG(DPI_LOG_ERROR, "rte_eth_dev_start: err=%d, port=%d", ret, portid);
			exit(-1);
		}
		rte_eth_promiscuous_enable(portid);
	}

	/* launch per-lcore init on every lcore */
	rte_eal_mp_remote_launch(packet_receive_thread_func, NULL, SKIP_MASTER);

	socket_main();

	RTE_LCORE_FOREACH_SLAVE(lcore_id) {
		if (rte_eal_wait_lcore(lcore_id) < 0) {
			ret = -1;
			break;
		}
	}

	for (portid = 0; portid < nb_ports; portid++) {
		rte_eth_dev_stop(portid);
		rte_eth_dev_close(portid);
		DPI_LOG(DPI_LOG_DEBUG, " Done");
	}
	DPI_LOG(DPI_LOG_DEBUG, "Bye...");

	return ret;
}

static void do_quit_job(void)
{
	/*其他需要释放的资源也可以放在这里 */
	/*释放QQ活动事件的资源 */
	// fini_dissector_qq_event_thread();

	/*ADD_S by yangna 2020-08-20 */
    /*这里应该释放各种资源，回收掉各种线程后优雅退出 ，由于项目业务，线程复杂，最好由业务的相关人员来释放各自的资源和线程*/
    /*这里exit是因为ctrl+c 无法结束进程，只好粗暴退出 */
	// fini_http_dissector();
    exit(0);
    /*ADD_E by yangna 2020-08-20 */
}

static void stop(int signo)
{
  g_config.stop_write_tbl = 1;
  dpi_distory_schemaDB();
	switch (signo)
	{
		case SIGINT:
		case SIGTERM:
		case SIGQUIT:
			do_quit_job();
			break;
		default:
			do_quit_job();
			break;
	}
}
#define   FILE_CONFIG    "config.ini"
int main(int argc, char **argv)
{
	g_config.g_now_time = time(NULL);
	g_config.g_start_time = g_config.g_now_time;

/*
	char path_cfg[256];
	char *path = get_owner_path();
	if (path == NULL)
		return -1;


    snprintf(path_cfg, sizeof(path_cfg), "%s/"FILE_CONFIG, path);
    if(0 == access(FILE_CONFIG, R_OK))
    {
        parser_init_config(FILE_CONFIG);
    }
    else
    if(0 == access(path_cfg, R_OK))
    {
        parser_init_config(path_cfg);
*/
	signal(SIGPIPE, SIG_IGN);
	signal(SIGINT, stop);
	signal(SIGTERM, stop);
	signal(SIGQUIT, stop);

	dpdk_main(argc, argv);

	return 0;
}


static __attribute((constructor)) void     before_main_init(void){
    char path_cfg[256];
    char *path = get_owner_path();
    if (path == NULL){
        exit(-1);
    }
    snprintf(path_cfg, sizeof(path_cfg), "%s/config.ini", path);
    parser_init_config(path_cfg);

    if(strlen(g_config.dpi_field_dir)>0){
        mkdirs(g_config.dpi_field_dir);
    }

	// port proto init
    register_tbl_array(TBL_LOG_HTTP, 				0, 	"http_n", 			init_http_dissector);
    register_tbl_array(TBL_LOG_QQ_VOIP, 			0, 	"QQVoipChat", 		init_qq_voip_chat_dissector);
    register_tbl_array(TBL_LOG_WEIXIN_MEDIA_CHAT,	0, 	"WeixinMediaChat", 	init_WeixinMediaChat_dissector);
    register_tbl_array(TBL_LOG_ZOOM_CONFERENCE, 	0, 	"ZOOM_Conference", 	init_ZOOM_Conference_dissector);
    register_tbl_array(TBL_LOG_WEIXIN, 				0, 	"weixin2", 			init_weixin_dissector);
	register_tbl_array(TBL_LOG_WX_LOCSHARING, 		0, 	"wx_locsharing", 	init_wx_location_sharing_dissector);
	register_tbl_array(TBL_LOG_GQUIC_WEIXIN, 			0, 	"gquic_weixin", 		init_gquic_dissector);

	// not port proto init
	register_tbl_array(TBL_LOG_SKYPE_MEDIA_CHAT, 	0, 	"SKypeMediaChat",	init_skype_media_dissector);
	register_tbl_array(TBL_LOG_WEIXIN_INFO, 		0, 	"wx_info", 			init_weixin_info_dissector);
	register_tbl_array(TBL_LOG_WX_PEERS, 			0, 	"wx_peers", 		init_weixin_peers_dissector);
	register_tbl_array(TBL_LOG_WEIXIN_RELA, 		0, 	"wx_rela", 			init_weixin_rela_dissector);
    register_tbl_array(TBL_LOG_QQ_EVENT, 			0, 	"qq_event", 		init_qq_event_dissector);
	register_tbl_array(TBL_LOG_WEIXIN_MISC, 		0, 	"wx_misc", 			init_weixin_misc_dissector);

	// register without init
	register_tbl_array(TBL_LOG_HTTP_QQACC, 					0, "http_qqacc", 		NULL);
    register_tbl_array(TBL_LOG_HTTP_WXPH, 				0, "http_wxph", 		NULL);
    register_tbl_array(TBL_LOG_HTTP_WX_MSG, 			0, "http_wx_msg", 		NULL);
    register_tbl_array(TBL_LOG_WEIXIN_PYQ, 				0, "weixin_pyq2", 		NULL);

	register_tbl_array(TBL_LOG_RELATION, 						0, "relation", 			NULL);
	// register_tbl_array(TBL_LOG_GQUIC_WEIXIN_F, 			0, "gquic_wx", 		NULL);
	// register_tbl_array(TBL_LOG_GQUIC_WEIXIN_PYQ, 		0, "gquic_wx_pyq", 	NULL);

}
