

#include "tbl_worker.h"
#include "tbl_utils.h"
#include "tbl_logger.h"
#include "tbl_manager.h"

#include <iostream>
#include <string>

TblWorker::TblWorker(std::string &proto_name):proto_name_(proto_name), writer_(proto_name)
{
    field_dir_ = CFG.getCurrentConfigValue({"field_conf", "field_path"}, std::string("/root/program/field/"));
}

TblWorker::~TblWorker()
{
    tbl_data_queue_.clear();
    outputDirs_.clear();
}

bool TblWorker::tbl_check()
{
    uint32_t tmp =  strCountChar(tbl_data_queue_.front(), '|');
    LOG_DEF->debug("tbl_data_queue_ size {} , field size {}", tmp, size_);
    
    return (tmp == size_ || tmp + 1 == size_) ? true : false;
}

void TblWorker::init()
{
    load_field();
    
    //获取需要使用的字段名
    use_field_ = CFG.getCurrentConfigValue({"work_conf", proto_name_, "field_name"}, std::string());
    //如果是field.subfield……格式，则仅使用field查表
    check_field_ = use_field_.find('.') != std::string::npos? use_field_.substr(0, use_field_.find_first_of('.')) : use_field_;

    p_FieldValue = TblFieldValue::getField(check_field_);
    if(nullptr != p_FieldValue)
    {
        p_FieldValue->setFieldTable(protoFields_);

        p_FieldValue->setFieldAndProto(use_field_, proto_name_);

        LOG_DEF->debug ( "use_field:{}, proto_name:{}", use_field_, proto_name_);
    }

    // (这里不以指定字段开头的字段表中的字段值将会被删除)
    for(auto it = protoFields_.begin(); it != protoFields_.end(); )
    {
        if(!strBeginwith(it->first, check_field_))
        {
            LOG_DEF->debug("{} will erase", __FUNCTION__);
            it = protoFields_.erase(it);
        }
        else
        {
            ++it;
        }
    }
    
    //获取协议对应的目录和字段值范围
    for(auto & it : CFG.getKeyArray({"work_conf", proto_name_, use_field_}))
    {
        for(auto & iter : CFG.getCurrentArray({"work_conf", proto_name_, use_field_, it}))
        {
            if(iter.is_string())
            {
                configRecoder_[it].emplace(std::string(iter));
                outputRecoder_[std::string(iter)].emplace(it);
            }
            else if(iter.is_number())
            {
                configRecoder_[it].emplace(std::to_string(int(iter)));
                outputRecoder_[std::to_string(int(iter))].emplace(it);
            }
        }
    }
    
    work_mode_ =  CFG.getCurrentConfigValue({"work_conf", proto_name_ , "work_mode"}, std::string("match"));

    init_workmode(work_mode_);
}

int TblWorker::init_workmode(std::string &work_mode)
{
    if(work_mode == "range")
    {
        work_func = std::bind(&TblWorker::range_work1, this, std::placeholders::_1);
    }
    else if(work_mode == "match")
    {
        work_func = std::bind(&TblWorker::match_work2, this, std::placeholders::_1);
    }
    else if(work_mode == "contains")
    {
        work_func = std::bind(&TblWorker::contains_work, this, std::placeholders::_1);
    }
    else if(work_mode == "!match")
    {
        work_func = std::bind(&TblWorker::not_match_work, this, std::placeholders::_1);
    }

    return 0;
}

int TblWorker::init_fieldcheck()
{
    return 0;
}


void TblWorker::work()
{
    if(tbl_data_queue_.empty())
    {
        LOG_DEF->warn("there is no tbl to use.");
        return;
    }
    if(!outputDirs_.empty())
    {
        outputDirs_.clear();
    }

    if(!tbl_check())
    {
        LOG_TBL->warn("{} data format doesn't match the field table!", proto_name_);
        tbl_data_queue_.erase(tbl_data_queue_.begin());
        return;
    }

    for(auto & tbl : tbl_data_queue_)
    {
        work_func(tbl);  
    }
    tbl_data_queue_.clear();
}

/*************************
 * name : load_field
 * function : 导入字段表文件
 *
 *
 *************************/
int TblWorker::load_field()
{
    char * field_name = nullptr;
    int32_t index     = 0;
    size_t field_len  = 0;
    FILE *fp = fopen((field_dir_ + '/' + proto_name_ + "_f.txt").c_str(), "r");

    if(fp == nullptr)
    {
        LOG_DEF->error("{} field txt is not exist!", proto_name_);
        return 0;
    }
    while(getline(&field_name, &field_len, fp) != -1)
    {   
        std::string tmp_str(field_name, field_len);

        if(tmp_str.find_first_of('\n') != std::string::npos)
        {
            tmp_str.erase(tmp_str.find_first_of('\n'));
        }
        protoFields_.emplace_back(tmp_str, index);
        ++index;
    }
    size_ = protoFields_.size();
    return 0;
}

void TblWorker::setTblData(std::vector<std::string>& src_tbl_data)
{
    while (!tbl_data_queue_.empty())
    {
        usleep(1000);
    }
    tbl_data_queue_ = src_tbl_data;
}

int TblWorker::match_work2(std::string &data)
{
    bool valid = true;
    if(!outputDirs_.empty())
    {
        outputDirs_.clear();
    }

    for(const auto & iter:configRecoder_)
    {
        outputDirs_.insert(iter.first);
    }
    for(auto &it: protoFields_)
    {
        std::string tbl_value;
        //真正应该重写的函数！  !-----------------------------------------------------------------------------------------!
        //std::string && rst = getStrNthChar(data, '|', it.second);

        p_FieldValue = TblFieldValue::getField(check_field_);

        tbl_value = getStrNthChar(data, '|', it.second);

        if(p_FieldValue)
        {
            // 对所有要检测的字段值合法性检测
            if(valid)
            {
                valid = p_FieldValue->FormatCheck(proto_name_, tbl_value);
            }

            if(valid)
            {
                tbl_value = p_FieldValue->getRequestFieldValue(proto_name_, data, it.second);
            }
        }
        
        
        LOG_DEF->info("field {} , val {}", check_field_, tbl_value);
        for(auto iter = outputDirs_.begin(); iter != outputDirs_.end(); )
        {
            if(configRecoder_[*iter].find(tbl_value) !=  configRecoder_[*iter].end())
            {
                //TblWriterManager::getCurrentWriter(proto_name_)->writeToFile(*iter, data);
                writer_.writeToFile(*iter, data);
                iter = outputDirs_.erase(iter);
                // outputDirs_.erase(iter++);
            }
            else
            {
                ++iter;
            }
        }   
    }

    if(outputDirs_.size() == configRecoder_.size()) // 一条都没输出
    {
        LOG_DEF->warn("There is a tbl not {} {} used!", proto_name_, work_mode_);
        if(valid)
        {
            //TblWriterManager::getCurrentWriter(proto_name_)->writeToFile(TblWriter::getDefaultDir(), data);
            writer_.writeToFile(TblWriter::getDefaultDir(), data);
        }
        else
        {
            //TblWriterManager::getCurrentWriter(proto_name_)->writeToFile(TblWriter::getInvalidDir(), data);
            writer_.writeToFile(TblWriter::getInvalidDir(), data);
        }
    }

    return 0;
}

int TblWorker::range_work1(std::string &data)
{
    bool valid = true;

    outputDirs_.clear();

    for(auto &iter : configRecoder_)
    {
        outputDirs_.emplace(iter.first);
    }

    for(auto &it : protoFields_)
    {
        std::string tbl_value;
        //真正应该重写的函数！  !-----------------------------------------------------------------------------------------!
        //std::string && rst = getStrNthChar(data, '|', it.second);

        p_FieldValue = TblFieldValue::getField(check_field_);

        tbl_value = getStrNthChar(data, '|', it.second);
        
        if(p_FieldValue)
        {
            // 对所有要检测的字段值合法性检测
            if(valid)
            {
                valid = p_FieldValue->FormatCheck(proto_name_, tbl_value);
            }

            if(valid)
            {
                tbl_value = p_FieldValue->getRequestFieldValue(proto_name_, data, it.second);
            }
        }

        for(auto iter = outputDirs_.begin(); iter != outputDirs_.end(); )
        {
            if( atol(tbl_value.c_str()) >= atoi(configRecoder_[*iter].begin()->c_str()) &&  atol(tbl_value.c_str()) <= atoi(configRecoder_[*iter].rbegin()->c_str()) )
            {
                LOG_DEF->debug("{} will erase", __FUNCTION__);
                writer_.writeToFile(*iter, data);
                iter = outputDirs_.erase(iter);
            }
            else
            {
                ++iter;
            }
            
        }   
    }

    if(outputDirs_.size() == configRecoder_.size()) // 一条都没输出
    {
        LOG_DEF->warn("There is a tbl not {} {} used!", proto_name_, work_mode_);
        if(valid)
        {
            writer_.writeToFile(TblWriter::getDefaultDir(), data);
        }
        else
        {
            writer_.writeToFile(TblWriter::getInvalidDir(), data);
        }
    }
    return 0;
}

int TblWorker::contains_work(std::string &data)
{
    bool valid = true;
    if(!outputDirs_.empty())
    {
        outputDirs_.clear();
    }

    for(const auto & iter:configRecoder_)
    {
        outputDirs_.insert(iter.first);
    }
    for(auto &it: protoFields_)
    {
        std::string tbl_value;

        p_FieldValue = TblFieldValue::getField(check_field_);

        tbl_value = getStrNthChar(data, '|', it.second);
        
        if(p_FieldValue)
        {
            // 对所有要检测的字段值合法性检测
            if(valid)
            {
                valid = p_FieldValue->FormatCheck(proto_name_, tbl_value);
            }

            if(valid)
            {
                tbl_value = p_FieldValue->getRequestFieldValue(proto_name_, data, it.second);
            }
        }
        
        LOG_DEF->info("field {} , val {}", check_field_, tbl_value);

        for(auto &item : outputRecoder_)
        {
            if(item.first.find(tbl_value) != std::string::npos)
            {
                for(auto &dir :item.second)
                {
                    if(outputDirs_.find(dir)!=outputDirs_.end())
                    {
                        writer_.writeToFile(dir, data);
                        outputDirs_.erase(dir);
                    }
                }
            } 
        } 
    }

    if(outputDirs_.size() == configRecoder_.size()) // 一条都没输出
    {
        LOG_DEF->warn("There is a tbl not {} {} used!", proto_name_, work_mode_);
        if(valid)
        {
            writer_.writeToFile(TblWriter::getDefaultDir(), data);
        }
        else
        {
            writer_.writeToFile(TblWriter::getInvalidDir(), data);
        }
    }
    return 0;
}

int TblWorker::not_match_work(std::string &data)
{
    bool valid = true;
    if(!outputDirs_.empty())
    {
        outputDirs_.clear();
    }

    for(const auto & iter:configRecoder_)
    {
        outputDirs_.insert(iter.first);
    }
    for(auto &it: protoFields_)
    {
        std::string tbl_value;
        //真正应该重写的函数！  !-----------------------------------------------------------------------------------------!
        //std::string && rst = getStrNthChar(data, '|', it.second);

        p_FieldValue = TblFieldValue::getField(check_field_);

        tbl_value = getStrNthChar(data, '|', it.second);
        
        if(p_FieldValue)
        {
            // 对所有要检测的字段值合法性检测
            if(valid)
            {
                valid = p_FieldValue->FormatCheck(proto_name_, tbl_value);
            }

            if(valid)
            {
                tbl_value = p_FieldValue->getRequestFieldValue(proto_name_, data, it.second);
            }
        }
        
        LOG_DEF->info("field {} , val {}", check_field_, tbl_value);
        for(auto iter = outputDirs_.begin(); iter != outputDirs_.end(); )
        {
            if(configRecoder_[*iter].find(tbl_value) ==  configRecoder_[*iter].end())
            {
                writer_.writeToFile(*iter, data);
                iter = outputDirs_.erase(iter);
                // outputDirs_.erase(iter++);
            }
            else
            {
                ++iter;
            }
        }   
    }

    if(outputDirs_.size() == configRecoder_.size()) // 一条都没输出
    {
        LOG_DEF->warn("There is a tbl not {} {} used!", proto_name_, work_mode_);
        if(valid)
        {
            writer_.writeToFile(TblWriter::getDefaultDir(), data);
        }
        else
        {
            writer_.writeToFile(TblWriter::getInvalidDir(), data);
        }
    }

    return 0;
}
