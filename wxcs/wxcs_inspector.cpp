/****************************************************************************************
 * 文 件 名 : wxcs_inspector.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : inspector
 * 功    能 : http 接口对外提供程序内部状态查询功能
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-09-06
* 编    码 : root      '2019-09-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_inspector.h"
#include "wxcs_utils.h"


#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <map>
#include <sys/time.h>
#include <time.h>

#include <cstdarg>

#include <muduo/net/EventLoop.h>
#include <muduo/net/EventLoopThread.h>

using namespace muduo;
using namespace muduo::net;
using namespace std::placeholders;

WxcsInspector::WxcsInspector(EventLoop* loop, const InetAddress& httpAddr, WxcsServer *pServer)
    : ins_(loop, httpAddr, "wxcs_inspect")
    , pServer_(pServer)
{
    RegisterCommands(&ins_);
}

WxcsInspector::~WxcsInspector()
{

}

void WxcsInspector::RegisterCommands(Inspector *ins)
{
    ins->add("wxcs", "queryQQByMsisdn", std::bind(&WxcsInspector::queryQQByMsisdn, this, _1, _2), "get QQ by MSISDN ");
    ins->add("wxcs", "queryQQListByMsisdn", std::bind(&WxcsInspector::queryQQListByMsisdn, this, _1, _2), "get all QQ by MSISDN ");
    ins->add("wxcs", "queryAllMsisdnQQ", std::bind(&WxcsInspector::queryAllMsisdnQQ, this, _1, _2), "get all Msisdn and QQ  ");

}

int stringPrintf(string* out, const char* fmt, ...)
{
    char buf[256];
    va_list args;
    va_start(args, fmt);
    int ret = vsnprintf(buf, sizeof buf, fmt, args);
    va_end(args);
    out->append(buf);
    return ret;

}

long getLong(const string& procStatus, const char* key)
{
    long result = 0;
    size_t pos = procStatus.find(key);
    if (pos != string::npos)
    {
        result = ::atol(procStatus.c_str() + pos + strlen(key));

    }

    return result;
}

/*通过手机号码查询权重最大的QQ */
muduo::string WxcsInspector::queryQQByMsisdn(HttpRequest::Method , const Inspector::ArgList& args)
{
    muduo::string strRes;
    if (args.size() != 1)
    {
        stringPrintf(&strRes, "error format!!! the right fomat is curl localhost:2048/wxcs/queryQQByMsisdn/msisdnNum\n");
        return strRes;
    }

    /*传入的是手机号码 */
    if (args[0].length() > 20 || args[0].length() < 5)
    {
        stringPrintf(&strRes, "error format!!! Msisdn length error!!!\n");
        return strRes;
    }

    const char *strMsisdn = args[0].c_str();
    size_t msisdnNum = 0;
    size_t QQNum = 0;
    unsigned int weight = 0;
    unsigned int lastTime = 0;
    sscanf(strMsisdn, "%lu", &msisdnNum);
    int ret = pServer_->qqEventMap_.getQQByMsisdnWithWeight(msisdnNum, QQNum, weight, lastTime);
    if (ret != 0)
    {
        stringPrintf(&strRes, "not found QQ by :%s\n", strMsisdn);
    }
    else
    {
        stringPrintf(&strRes, "input:%s, QQNum:%zu\n", strMsisdn, QQNum);
    }

    return strRes;
}


/*通过手机号码获取对应的所有QQ */
muduo::string WxcsInspector::queryQQListByMsisdn(HttpRequest::Method , const Inspector::ArgList& args)
{
    muduo::string strRes;
    if (args.size() != 1)
    {
        stringPrintf(&strRes, "error format!!! the right fomat is curl localhost:2048/wxcs/queryQQListByMsisdn/msisdnNum\n");
        return strRes;
    }

    /*传入的是手机号码 手机号码检测不需要太精确，由用户控制 */
    if (args[0].length() > 20 || args[0].length() < 5)
    {
        stringPrintf(&strRes, "error format!!! Msisdn length error!!!\n");
        return strRes;
    }
    const char *strMsisdn =  args[0].c_str();
    size_t msisdnNum = 0;

    size_t QQNum = 0;
    unsigned int weight = 0;
    unsigned int lastTime = 0;
    size_t  IMEI = 0;
    size_t  IMSI = 0;
    std::map<size_t, QQEventInfo> mapQQEventValue;
    std::map<size_t, QQEventInfo>::iterator it;
    sscanf(strMsisdn, "%zu", &msisdnNum);

    int ret = pServer_->qqEventMap_.getQQListByMsisdn(msisdnNum, mapQQEventValue);
    if (ret != 0)
    {
        stringPrintf(&strRes, "not found QQ by :%s\n", strMsisdn);
    }
    else
    {
        for (it = mapQQEventValue.begin(); it != mapQQEventValue.end(); it++)
        {
            QQNum = 0;
            weight = 0;
            lastTime = 0;

            QQNum = it->first;
            weight = it->second.weight;
            lastTime = it->second.lastTime;
            IMEI =  it->second.IMEI;
            IMSI =  it->second.IMSI;
            stringPrintf(&strRes, "input:%s, QQNum:%zu, IMSI:%zu, IMEI:%zu, weight=%u,lastTime=%u \n",
            strMsisdn, QQNum, IMSI, IMEI, weight, lastTime);
        }

    }

    return strRes;
}

/*查询所有的手机号码和QQ号码 */
muduo::string WxcsInspector::queryAllMsisdnQQ(HttpRequest::Method , const Inspector::ArgList& args _U_)
{
    muduo::string strRes;
    std::map<size_t, std::map<size_t, QQEventInfo>> *mapQQEvent;

    int ret = pServer_->qqEventMap_.getAllMsisdnQQ(&mapQQEvent);
    if (ret != 0 || mapQQEvent->size() <= 0)
    {
        stringPrintf(&strRes, "Msisdn and qq empty!!! \n");
    }
    else
    {
        /*获取当前时间作为输出文件名 */
        struct timeval tv;          //获取1970-1-1 到现在的时间结果保存到tv中
        gettimeofday(&tv, NULL);
        uint64_t sec = tv.tv_sec;
        struct tm cur_tm;           //保存转换后的时间结果
        localtime_r((time_t*)&sec, &cur_tm);
        char cur_time[20] = {0};
        snprintf(cur_time, sizeof(cur_time),  "%d_%02d_%02d_%02d_%02d_%02d", cur_tm.tm_year+1900,
        cur_tm.tm_mon+1, cur_tm.tm_mday, cur_tm.tm_hour, cur_tm.tm_min, cur_tm.tm_sec);
        char outFileName[128] = {0};
        snprintf(outFileName, sizeof(outFileName), "%s%s%s", "/tmp/tbls/msisdnQQ/msisdn_", cur_time, ".txt");
        FILE *fp = NULL;
        if (access("/tmp/tbls/msisdnQQ \n", F_OK) !=0 )
        {
            makeDir("/tmp/tbls/msisdnQQ");
        }

        fp = fopen(outFileName, "w");
        if (fp == NULL)
        {
            stringPrintf(&strRes, "打开文件 %s 异常\n", outFileName);
        }
        else
        {
            std::map<size_t, std::map<size_t, QQEventInfo>>::iterator it1;
            std::map<size_t, QQEventInfo>::iterator it2;
            for (it1 = mapQQEvent->begin(); it1 != mapQQEvent->end(); it1++)
            {
                for (it2 = it1->second.begin(); it2 != it1->second.end(); it2++)
                {
                    fprintf(fp, "Msisdn:%zu\tQQ:%zu\tIMSI:%zu\tIMEI:%zu\tweight:%u\tlastTime:%u\n",
                    it1->first, it2->first, it2->second.IMSI, it2->second.IMEI,
                    it2->second.weight, it2->second.lastTime);
                }
                fprintf(fp, "\n");
            }

            stringPrintf(&strRes, "文件输出路径 %s\n", outFileName);
        }
        if (NULL != fp)
        {
            fclose(fp);
        }
    }

    return strRes;

}