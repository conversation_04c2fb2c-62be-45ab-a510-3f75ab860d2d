#pragma once
/****************************************************************************************
* 文 件 名 : tbl_utils.h
* 项目名称 : 
* 模 块 名 :
* 功    能 :
* 操作系统 : LINUX
* 修改记录 : 1次
* 版    本 : Rev 0.1.0
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2018-05-12
* 编    码 : zhengsw      '2018-05-12
* 修    改 : zhangsx      '2018-11-13
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/


#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <arpa/inet.h>
#include <errno.h>
#include <dirent.h>
#include <endian.h>

#include <memory>
#include <ctime>
#include <string>
#include <typeinfo>
#include <deque>
#include <vector>

#include <functional>

#include "tbl_logger.h"

/* macros */
#define dimen_of(x) (sizeof(x) / sizeof((x)[0]))

// check_nor_exit
#define CHECK_NOR_EXIT(cond, retCode, ...) if ((cond)) \
    {                                                  \
        fprintf(stderr, __VA_ARGS__);                  \
        return (retCode);                              \
    }

#define EXIT_IF_ERROR(cond, retCode, ...) if ((cond)) \
    {                                                 \
        fprintf(stderr, __VA_ARGS__);                 \
        perror(" ");                                  \
        return (retCode);                             \
    }

#define MAX(a, b)                   (a) > (b) ? (a):(b)

/* finaly do ... */
#define defer(fun) std::shared_ptr<void> _(nullptr, [&](void *){fun})

// easy tools
// unique_ptr 自动管理 malloc/new 出来的内存
typedef std::unique_ptr<char, void(*)(void *)> alloced_uptr;
/****************************************************************************************
* 函 数 名 : be2le
* 功    能 : 大端序转换为小端序数值
* 输入参数 : num: 大端序数值
* 输出参数 : 无
* 返 回 值 : 小端序数值
***************************************************************************************/
template <class T>
inline T be2le(const T &num)
{
    T         ret = 0;
    uint8_t * src = (uint8_t *)&num;
    uint8_t*  dst = (uint8_t *)&ret;

    for (uint i = 0; i < sizeof(T); i++)
    {
        dst[sizeof(T) - i - 1] = src[i];
    }

    return ret;
}
/****************************************************************************************
* 函 数 名 : getCanonicalPath
* 功    能 : 获取指定文件或目录的绝对路径
* 输入参数 : 文件路径
* 输出参数 : 无
* 返 回 值 : 文件的绝对路径(不带'/')
***************************************************************************************/
/*static std::string getCanonicalPath(const char* path)
{
    std::string canonical_path = "";
    std::deque <std::string> filename_list;

    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = { 0 };
    char *pFilename;

    //相对路径
    if ('/' != path[0])
    {   //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");

        strcat(currentPath, path);
        if (path[pathLen - 1] != '/')
        {
            strcat(currentPath, "/");
        }
    }
    else
    {   //绝对路径
        strcpy(currentPath, path);
        if (path[pathLen - 1] != '/')
        {
            strcat(currentPath, "/");
        }
    }

    pFilename = strtok(currentPath, "/");

    filename_list.clear();

    filename_list.push_back(std::string(pFilename));

    while ((pFilename = strtok(NULL, "/")) != NULL)
    {
        if (strcmp(".", pFilename) == 0)
        {
            continue;
        }

        if (strcmp("..", pFilename) == 0)
        {
            filename_list.pop_back();
            continue;
        }

        filename_list.push_back(std::string(pFilename));
        //printf("Path %d:%s\n",i, pFilename);
    }

    for (const auto & itr : filename_list)
    {
        //printf("%s\n", pFilename);
        canonical_path.append("/" + itr);
    }

    filename_list.clear();

    if (access(canonical_path.c_str(), 0) != 0)
    {
        return "";
    }

    return canonical_path;
}
*/
/****************************************************************************************
* 函 数 名 : hundredThousandthSecond
* 功    能 : 10 万分之一秒
* 输出参数 : 无
* 返 回 值 : YV_SUCCSS: 成功; YV_FAIL: 失败
***************************************************************************************/
inline uint hundredThousandthSecond()
{
    struct timespec ts;

    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_nsec / 10000;
}

static inline
int makeDir(const char* path)
{
    int lSts = 0;
    int beginCmpPath;
    int endCmpPath;
    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = { 0 };

    //相对路径
    if ('/' != path[0])
    {
        //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");
        beginCmpPath = strlen(currentPath);
        strcat(currentPath, path);
        if (path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        endCmpPath = strlen(currentPath);
    }
    else
    {
        //绝对路径
        int pathLen = strlen(path);
        strcpy(currentPath, path);
        if (path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        beginCmpPath = 1;
        endCmpPath = strlen(currentPath);
    }
    //创建各级目录
    for (int i = beginCmpPath; i < endCmpPath; i++)
    {
        if ('/' == currentPath[i])
        {
            currentPath[i] = '\0';
            if (access(currentPath, 0) != 0)
            {
                lSts = mkdir(currentPath, 0755);
                EXIT_IF_ERROR(lSts < 0 && errno != EEXIST, -1, "mkdir %s error", currentPath);
            }
            currentPath[i] = '/';
        }
    }
    return 0;
}

inline
int ensureDirExist(const char *dirPath)
{
    int lSts = 0;

    if (access(dirPath, R_OK) != 0)
    {
        lSts = makeDir(dirPath);
        EXIT_IF_ERROR(lSts < 0, lSts, dirPath);
    }

    return 0;
}

static inline char
low_nibble_of_octet_to_hex(uint8_t oct)
{
	static const char hex_digits[16] =
	{ '0', '1', '2', '3', '4', '5', '6', '7',
	  '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

	return hex_digits[oct & 0xF];
}

static inline bool strEndwith(const std::string &str, const std::string &end);
/****************************************************************************************
* 函 数 名 : forDirEntry
* 功    能 : 扫描指定路径下的文件
* 输入参数 : 指定路径，指定的后缀(若为空则无用)，传的函数，检测层数
* 输出参数 : 无
* 返 回 值 : 若指定后缀为空，则检测所有文件，否则只检测指定后缀的文件
***************************************************************************************/

inline
int forDirEntry(const char *dir_name, const char *proto_name, const char *end, std::function <int(const char *, const char *)> func, int depth = 1)
{
    if (NULL == dir_name)
    {
        LOG_DEF->debug("{}  is NULL!");
        return -EINVAL;
    }

    if (NULL == proto_name)
    {
        LOG_DEF->debug("{}  is proto_name!");
        return -EINVAL;
    }

    /* check dir type */
    struct stat s;
    lstat(dir_name, &s);
    CHECK_NOR_EXIT(!S_ISDIR(s.st_mode), -EINVAL, "%s not a dir.\n", dir_name);

    /* open dir */
    dirent *pDirEntry = NULL;
    DIR    *pDir = NULL;

    pDir = opendir(dir_name);
    EXIT_IF_ERROR(NULL == pDir, -1, "opendir %s", dir_name);

    char fileEntryPathBuf[PATH_MAX] = { 0 };

    /* loop all  */
    while ((pDirEntry = readdir(pDir)) != NULL)
    {
        if (strcmp(pDirEntry->d_name, ".") == 0
            || strcmp(pDirEntry->d_name, "..") == 0)
        {   // 忽略它俩
            continue;
        }

        if(NULL != end && !strEndwith(pDirEntry->d_name, end))
        {   // 忽略非指定后缀的文件
            continue;
        }

        /* 生成 fileEntry path */
        sprintf(fileEntryPathBuf, "%s/%s", dir_name, pDirEntry->d_name);

        if(func(fileEntryPathBuf, proto_name) < 0)
        {
            return -1;
        }

        // recurse sub dir
        struct stat st;
        lstat(fileEntryPathBuf, &st);

        if (depth > 1
            && S_ISDIR(st.st_mode))
        {
            forDirEntry(fileEntryPathBuf, proto_name ,end, func, depth - 1);
        }
    }

    if (pDir)
    {
        closedir(pDir);
    }
    return 0;
}




static inline std::string ip_addr_to_str(uint32_t ip)
{
    return inet_ntoa(*(struct in_addr*)(&ip));
}

static inline std::string bytes_to_hexstring(const uint8_t *in, int len)
{
    std::string strHex("0x");
    strHex.resize(2 * len + 2);     // 2 for "0x", 1 for '\0'

    for (int i = 0; i < len; i++)
    {
        strHex[2 * i + 2]     = low_nibble_of_octet_to_hex(in[i] >> 4);
        strHex[2 * i + 1 + 2] = low_nibble_of_octet_to_hex(in[i]);
    }

    return strHex;
}

template <typename T>
static inline std::string bytes_to_hexstring(const T &data)
{
    return bytes_to_hexstring((uint8_t *)&data, sizeof data);
}

inline
std::string unixTime2Str(time_t timeValue)
{
    char strTimeBuf[30] = { 0 };
    struct tm *tm = localtime((time_t *)&timeValue);

    strftime(strTimeBuf, sizeof strTimeBuf, "%Y-%m-%d %H:%M:%S", tm);
    return strTimeBuf;
}

inline
std::string nanosecond2ms(int time)
{
    char strMsTimeBuf[10] = { 0 };

    sprintf(strMsTimeBuf, ".%d ms", int(time / (1000000.0)));
    return strMsTimeBuf;
}

/* return dir string with final backslash */
static inline
std::string getAppDir()
{
    std::string strAppPath;
    if (!strAppPath.empty())
    {
        return strAppPath;
    }

    char pathBuff[PATH_MAX];
    uint cnt = readlink("/proc/self/exe", pathBuff, sizeof(pathBuff));
    if (cnt >= sizeof(pathBuff))
    {
        return "";
    }

    // terminate the path
    pathBuff[cnt] = '\0';

    strAppPath = pathBuff;
    strAppPath = strAppPath.substr(0, strAppPath.find_last_of('/') + 1);
    return strAppPath;
}

static inline
bool strBeginwith(const std::string &str, const std::string &start)
{
    return str.find(start) == 0;
}

static inline
bool strEndwith(const std::string &str, const std::string &end)
{
    // add by zhangsx 2018.11.13
    if (str.size() < end.size())
    {
        return false;
    }
    if(end.empty())
    {
        return true;
    }
    // add end
    return str.compare(str.size() - end.size(), end.size(), end) == 0;      //这里参数为负数时函数会崩溃
}

static inline
void strReplace(std::string &str, const std::string &from, const std::string &to)
{
    std::string::size_type fIndex = str.find(from);

    if (fIndex == std::string::npos)
    {
        return;
    }

    str.replace(fIndex, from.length(), to);
}

static inline
void strSplitByStr(std::string &str, const std::string &sep, std::vector<std::string> &v)
{
    if (str.empty())
    {
        return;
    }
    std::string::size_type tmp_pos1, tmp_pos2;

    tmp_pos1 = 0;

    tmp_pos2 = str.find(sep);
    
    while (tmp_pos2 != std::string::npos)
    {
        v.emplace_back(str.substr(tmp_pos1, tmp_pos2 - tmp_pos1));

        tmp_pos1 = tmp_pos2 + sep.size();
        
        tmp_pos2 = str.find(sep, tmp_pos1);
    }

    if (tmp_pos1 != str.length())
    {
        v.emplace_back(str.substr(tmp_pos1));
    }
}

static inline
uint32_t strCountChar(const std::string &str, const char sep)
{
    if(str.empty())
    {
        return -1;
    }
    uint32_t count = 0;

    for(const auto & it : str)
    {
        if(it == sep)
        {
            ++count;
        }
    }

    return count;
}
static std::string getCleanStr(std::string str);
/****************************************************************************************
* 函 数 名 : getStrNthChar
* 功    能 : 获取指定字符串第N个到第N+1个字符之间的子串
* 输入参数 : 原始字符串，指定字符，从第几个指定字符开始
* 输出参数 : 无
* 返 回 值 : 存在则返回子串，若不存在，则返回空字符串
***************************************************************************************/
static inline 
std::string getStrNthChar(const std::string &str, const char c, const size_t index)
{
    uint32_t idx    = 0;
    uint32_t strLen = 0;
    uint32_t i = 0;

    for(size_t j = 0; j < str.size(); ++j)
    {
        if(str[j] == c)
        {
            ++i;
        }

        if(i == index && idx == 0)
        {
            idx = j + 1;
            continue;
        }
        if(i == index+1)
        {
            break;
        }
        if(i == index && i < index +1)
        {
            ++strLen;
        }
        
    }
    return idx&&strLen ? getCleanStr(str.substr(idx, strLen)): "";
}

/****************************************************************************************
* 函 数 名 : getCleanStr
* 功    能 : 若字符串开头结尾带引号，则去掉字符串前后的双引号
* 输入参数 : 原始字符串，指定字符，从第几个指定字符开始
* 输出参数 : 无
* 返 回 值 : 双引号存在则去掉，不存在则返回原始数据
***************************************************************************************/

static inline 
std::string getCleanStr(std::string str)
{
    if(str.empty())
    {
        return str;
    }
    if(str.front() == '"')
    { 
        str.erase(str.begin());
    }
    if(str.back() == '"')
    { 
        str.pop_back();
    }
    return str;
}