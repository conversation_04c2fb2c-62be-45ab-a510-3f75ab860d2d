/****************************************************************************************
 * 文 件 名 : wxcs_log.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_LOG_H_
#define _WXCS_LOG_H_

#include <memory>
#include <string>

#include <spdlog/spdlog.h>

namespace spdlog
{
    class logger;
}

class WxcsLogger
{
public:
    static void createLogger(const std::string &strLogDir, bool bLogToFile);

    static std::shared_ptr<spdlog::logger> defaultLogger();

    static std::shared_ptr<spdlog::logger> interestingLogger();

};

#define LOG_DEF   WxcsLogger::defaultLogger()
#define LOG_INTST WxcsLogger::interestingLogger()

#endif /* _WXCS_LOG_H_ */
