cmake_minimum_required(VERSION 3.10)

set(WXCS_NAME_BASE yaWxcs)
set(WXCS_NAME ${WXCS_NAME_BASE}_${PROGRAM_VERSION})

# 是否生成 compile_command.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# generate project version to version.h
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR})

include(version.git.cmake)


set(CMAKE_BUILD_TYPE Debug)
#set(CMAKE_VERBOSE_MAKEFILE on)
set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/../run)
set(LIBRARY_OUTPUT_PATH    ${PROJECT_SOURCE_DIR}/../lib)
set(CMAKE_CXX_COMPILER    g++)
set(CMAKE_CXX_FLAGS       "${CMAKE_CXX_FLAGS} -std=c++11")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -DDebug")

if (NOT "$ENV{BUILD_DEBUG}" STREQUAL "")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -DDebug" )
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fstack-protector-all")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fsanitize=address")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fno-stack-protector")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fno-omit-frame-pointer")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -static-libasan")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fsanitize=address -fno-omit-frame-pointer -static-libasan")  #GDB:b __asan_report_error
endif()

message(${WXCS_NAME})
include_directories(${PROJECT_SOURCE_DIR}/include
                    ${PROJECT_SOURCE_DIR}/../include
                    ${PROJECT_SOURCE_DIR}/../src)

link_directories(${PROJECT_SOURCE_DIR}/lib
                 ${PROJECT_SOURCE_DIR}/../lib)
option(ENABLE_WXA_WEB "complie project as wxa web" OFF)
if (ENABLE_WXA_WEB)
    add_compile_definitions(DPI_WXA_WEB)
endif()

find_package(spdlog)
# ### wxcs server ###
add_executable(${WXCS_NAME}
               wxcs_config.cpp
               wxcs_person.cpp
               wxcs_session.cpp
               wxcs_tbl_writer.cpp
               wxcs_logger.cpp
               wxcs_server.cpp
               wxcs_dpi_node.cpp
               wxcs_inspector.cpp
               wxcs_content_writer.cpp
               wxcs_voice.cpp
               wxcs_voice_peers.cpp
               wxcs_loc_sharing.cpp
               wxcs.cpp
)

target_link_libraries(${WXCS_NAME}
                      iniparser
                      muduo_inspect
                      muduo_http
                      muduo_net
                      muduo_base
                      pthread
                      crypto
                      spdlog::spdlog
)

# # 为可执行文件生成软链接
add_custom_command(TARGET ${WXCS_NAME} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E create_symlink $<TARGET_FILE:${WXCS_NAME}> ${CMAKE_SOURCE_DIR}/run/${WXCS_NAME_BASE}
)

########## INSTALL
set(WXCS_INSTALL_ROOTDIR ${WXCS_NAME_BASE} CACHE PATH "Installation ROOT DIR")
set(WXCS_INSTALL_BINDIR ${WXCS_NAME_BASE} CACHE PATH "Installation directory for executables")
# 报告安装位置
message(STATUS "Project ${WXCS_NAME_BASE} will be installed to ${CMAKE_INSTALL_PREFIX}")
foreach(p LIB BIN INCLUDE CMAKE ROOT)
  file(TO_NATIVE_PATH ${CMAKE_INSTALL_PREFIX}/${INSTALL_${p}DIR} _path)
  message(STATUS "Installing ${p} components to ${_path}")
  unset(_path)
endforeach()

install(
TARGETS
    ${WXCS_NAME}
DESTINATION
    ${WXCS_INSTALL_BINDIR}
)
install(
FILES
    ${CMAKE_SOURCE_DIR}/wxcs/etc/wxcs.conf
DESTINATION
    ${WXCS_INSTALL_ROOTDIR}
)

# add symlink
#execute_process(COMMAND    ${CMAKE_COMMAND} -E create_symlink ${WXCS_NAME} yaWxcs)

# libwxc.a
# add_library(wxc STATIC
                # libwxc.cpp)
