
cmake_minimum_required(VERSION 2.8)

project(wx_post)

# generate project version to version.h
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR})
include(ProjectVersion)
PROJECT_VERSION(0.16.0 ${CMAKE_SOURCE_DIR} ./include/version.h.in ./include/version.h)

# generate execute process name 
string(TIMESTAMP DATE "%y%m%d") # %Y->2019 %y->19

set(EXECUTE_EXTRA_NAME v${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}.${PROJECT_VERSION_RELEASE}.${PROJECT_VERSION_BUILD}_${DATE})

set(CMAKE_BUILD_TYPE Debug)

#file(GLOB SOURCE_CODE_PATH ${PROJECT_SOURCE_DIR}/src/*.cpp)

set(SOURCE_CODE_PATH ${PROJECT_SOURCE_DIR}/src/)

set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/run)

set(CMAKE_CXX_COMPILER    g++)
set(CMAKE_CXX_FLAGS       "${CMAKE_CXX_FLAGS} -std=c++11 -Wall")

set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -DDebug" )
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fstack-protector-all")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fsanitize=address")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fno-stack-protector")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fno-omit-frame-pointer")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -static-libasan")

include_directories(${PROJECT_SOURCE_DIR}/include)

link_directories(${PROJECT_SOURCE_DIR}/lib)

#add_executable(yaTblPost ${SOURCE_CODE_PATH})

add_executable(yaTblPost 
                ${SOURCE_CODE_PATH}tbl_value.cpp
                ${SOURCE_CODE_PATH}tbl_config.cpp
                ${SOURCE_CODE_PATH}tbl_logger.cpp
                ${SOURCE_CODE_PATH}tbl_reader.cpp
                ${SOURCE_CODE_PATH}tbl_worker.cpp
                ${SOURCE_CODE_PATH}tbl_writer.cpp
                ${SOURCE_CODE_PATH}tbl_scanner.cpp
                ${SOURCE_CODE_PATH}tbl_value_PersonULI.cpp
                ${SOURCE_CODE_PATH}tbl_worker_manager.cpp
                ${SOURCE_CODE_PATH}tbl_statistics.cpp
                ${SOURCE_CODE_PATH}tbl.cpp)

target_link_libraries(yaTblPost pthread)
#target_include_directories(yaTblPost PRIVATE ${SOURCE_CODE_PATH})
add_custom_command(TARGET yaTblPost POST_BUILD COMMAND ln -fsv ${PROJECT_SOURCE_DIR}/etc/config.json  ${EXECUTABLE_OUTPUT_PATH}/config.json)

add_custom_command(TARGET yaTblPost POST_BUILD COMMAND mv -v ${EXECUTABLE_OUTPUT_PATH}/yaTblPost ${EXECUTABLE_OUTPUT_PATH}/yaTblPost_${EXECUTE_EXTRA_NAME})