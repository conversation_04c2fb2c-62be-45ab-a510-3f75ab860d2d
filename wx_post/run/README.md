#<center>使用说明</center>

###指令说明：

>-t 指定分类线程数目（参数必需）

```bash
./yaTblPost -t 4  #指定4个分类线程
```
```bash
./yaTblPost -t thread
```
>-l 将日志打印到屏幕上（否则会写入日志文件）

```bash
./yaTblPost -l 
```
```bash
./yaTblPost --log 
```
>-v 查看程序版本版本

```bash
./yaTblPost -v 
```
```bash
./yaTblPost --version 
```

>-h 查看程序帮助信息

```bash
./yaTblPost -h 
```
```bash
./yaTblPost --help 
```
**以上参数中， -t和-l不冲突，其它都相互冲突！**


###配置说明：

>**以下所有代码部分注释均仅作说明用，实际部署时不得添加！**

#####读取配置
```json
    "read_conf":{
        "input_path":"/tmp/tbls/",         # 指定扫描tbl的来源路径
        "delete_tbl":false,                # 读取完tbl后是否删除
        "file_suffix":".tbl",              # 指定读取的文件名后缀
        "update_suffix":".finish"          # 读取完成后添加的后缀
    }

```

#####输出配置
```json
    "output_conf":{
        "file_suffix":".tbl",              # 输出完成后的文件后缀名（暂时无用）
        "file_writing":".writing",         # 输出中的的文件后缀名  （暂时无用）
        "default_dir":"out/default/",      # 默认文件输出路径                  
        "invalid_dir":"out/invalid/"       # 格式或内容有误文件输出路径                   
    }

```

#####日志配置
```json
    "log_conf":{
        "log_dir":"log",                   # 默认日志文件输出目录
        "tbl_log_dir":"logs",              # tbl 日志输出路径
        "log_statistics":".",              # 统计结果输出路径
        "log_level":"error",               # 日志等级（程序默认日志和tbl日志共用）
        "log_max_size":104857600,          # 单个日志文件大小
        "log_to_file":true                 # 是否输出日志到文件
    },

```

#####字段表配置
```json
    "field_conf": {                                 
        "field_path":"/home/<USER>/field",      ·# 字段表路径
        "field_extra":{                             # 特殊字段定义
            "Person_RT_ULI":{                       # 字段名
                "string":["3G", "4G", "UN"],        #     字段格式：对应字符串开头（对程序而言，tbl中内容均为字符串）
                "hex"   :["0x"]                     #     字段格式可添加, 数组中可添加任意数目的
            }                                       # 字段名可添加
        }                                           # Person_RT_ULI 和 RT_ULI 目前为特殊字段，仅有这两个字段支持子字段
    },

```
#####工作模式配置
```json
"work_conf":{
        "wxa":{                                         #   协议名称
            "work_mode":"match",                        #   分类模式(支持匹配(match)，范围(range)，包含(contains))
            "field_name":"Person_RT_ULI.eNodeBid",      #   字段名（字段名.子字段）
            "field_type":"string",                      #   字段值类型（最好与字段表配置中字段格式相符）
            "file_limit":8000,                          #   单个文件输出数目上限
            "file_timeout":60,                          #   文件超时上限
            "Person_RT_ULI.eNodeBid":{                  #   使用指定的字段值进行分类
                "out/hu":["382355"],                    #       目录：[值]
                "out/su":["384114"],                    #       匹配模式支持多个值
                "out/nj":["384657"],                    #       范围模式只支持两个值（小在前，大在后）
                "out/nt":["380846"],                    #
                "out/sz":["377728"],                    #       
                "out/cz":["733034"],                    #
                "out/xz":["727075"]                     #       目录可以此格式任意添加
            }                                           #       
        },                                              #   协议可以参照此格式添加，其中field_type不是必需项
        "weixin":{   
            "work_mode":"match",
            "field_name":"Person_RT_ULI.eNodeBid",
            "field_type":"hex",
            "file_limit":800,
            "file_timeout":60,
            "Person_RT_ULI.eNodeBid":{
                "out/hu":["726371"],
                "out/su":["416115"],
                "out/nj":["951194"],
                "out/nt":["380846"],
                "out/sz":["34547"],
                "out/cz":["50814"],
                "out/xz":["43420"]
            }
        }
    }

```
#####总体预览
```json

{
    "read_conf":{
        "input_path":"/tmp/tbls/",
        "delete_tbl":false,
        "file_suffix":".tbl",
        "update_suffix":".finish"
    },
    "output_conf":{
        "file_suffix":".tbl",
        "file_writing":".writing",
        "default_dir":"out/default/",                         
        "invalid_dir":"out/invalid/"                           
    },

    "log_conf":{
        "log_dir":"log",
        "tbl_log_dir":"logs",
        "log_statistics":".",
        "log_level":"error",
        "log_max_size":104857600,
        "log_to_file":true
    },
    
    "field_conf": {
        "field_path":"/home/<USER>/field",
        "field_extra":{
            "Person_RT_ULI":{
                "string":["3G", "4G", "UN"],
                "hex"   :["0x"]
            }
        }
    },

    "work_conf":{
        "wxa":{
            "work_mode":"match",
            "field_name":"Person_RT_ULI.eNodeBid",
            "field_type":"string",
            "file_limit":8000,
            "file_timeout":60,
            "Person_RT_ULI.eNodeBid":{
                "out/hu":["382355"],
                "out/su":["384114"],
                "out/nj":["384657"],
                "out/nt":["380846"],
                "out/sz":["377728"],
                "out/cz":["733034"],
                "out/xz":["727075"]
            }
        },
        "weixin":{   
            "work_mode":"match",
            "field_name":"Person_RT_ULI.eNodeBid",
            "field_type":"hex",
            "file_limit":800,
            "file_timeout":60,
            "Person_RT_ULI.eNodeBid":{
                "out/hu":["726371"],
                "out/su":["416115"],
                "out/nj":["951194"],
                "out/nt":["380846"],
                "out/sz":["34547"],
                "out/cz":["50814"],
                "out/xz":["43420"]
            }
        }
    }
}

```