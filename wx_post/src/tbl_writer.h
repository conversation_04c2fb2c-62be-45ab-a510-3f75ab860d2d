#pragma once
/****************************************************************************************
 * 文 件 名 : tbl_writer.h
 * 项目名称 : 
 * 模 块 名 : TblWriter
 * 功    能 : 每种协议对应一个writer,每个writer管理自己下辖多个目录
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <queue>
#include <string>
#include <vector>
#include <map>
#include <iostream>

#include <stdint.h>
#include <linux/limits.h>

class TblWriter
{
private:
    std::string protoName_;             // 每个输出器对应的协议名

    char        fileNameBuf_[PATH_MAX];
    char        fileNameBufWriting_[PATH_MAX];
    uint32_t    lThreadNO_ = 42;
    uint32_t    tblNumLimit_;                           // 每个输出器有独立的tbl数限制
    time_t      tblTimeLimit_;                          // 每个输出器有独立的tbl超时

    std::map< std::string, std::tuple< FILE *, uint32_t, time_t, std::string > > outputTblList_;        /*  输出状态记录    dir1: fp1, fp1Writen, fp1CreateTime */
                                                                                                        /*                 dir2: fp2, fp2Writen, fp2CreateTime */

private:
    constexpr static const char *TBL_WRITING_SUFFIX = ".writing";
    static std::string defaultDir_;
    static std::string invalidDir_;
    
private: 
    void generateFileName(const std::string & );       // 参数：路径名
    int  createAllTbl();
    int  createDirTbl(const std::string &);            // 按路径名创建tbl
public:
    static std::string getDefaultDir();
    static std::string getInvalidDir();

public:
    TblWriter(std::string &);
    ~TblWriter() {
        writeCurrentFileDone();
    }

    void  writeCurrentFileDone();
    void  writeCurrentFileDone(const std::string &);   // 参数：路径名
    int   writeToFile(const std::string &outputDir, std::string &strRecordLine);
};