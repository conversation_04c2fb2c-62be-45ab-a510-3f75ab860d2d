/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */
#include <sys/types.h>          /* See NOTES */
#include <sys/socket.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <event2/bufferevent_ssl.h>
#include <event2/listener.h>
#include <event2/buffer.h>

#include <glib.h>

#include <openssl/ssl.h>
#include <openssl/err.h>

#include "wx_proxy_thread.h"
#include "wx_proxy_log.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_common.h"
#include "wx_proxy_server.h"
#include "wx_proxy_process.h"
#include "wxcs_def.h"
#include "jhash.h"

#define MAX_OUTPUT (512*1024)

int do_enevt(int event, struct connection_t *connection)
{
    switch(event)
    {
        case BEV_EVENT_CONNECTED:
            log_write(INFO, "BEV_EVENT_CONNECTED:%p", connection);
            return 0;

        case BEV_EVENT_ERROR:
            log_write(ERROR, "BEV_EVENT_ERROR:%p", connection);
            return 1;

        case BEV_EVENT_EOF:
            log_write(ERROR, "BEV_EVENT_EOF:%p", connection);
            return 1;

        case BEV_EVENT_READING:
            log_write(ERROR, "BEV_EVENT_READING:%p", connection);
            return 1;
            break;

        case BEV_EVENT_WRITING:
            log_write(ERROR, "BEV_EVENT_WRITING:%p", connection);
            return 1;

        case BEV_EVENT_TIMEOUT:
            log_write(ERROR, "BEV_EVENT_TIMEOUT:%p", connection);
            return 1;

        case 0:
            return 0;

        default:
            log_write(ERROR, "%s on default:%d %p", __func__, event, connection);
            return 1;
    }
    return 0;
}



/*****************************************************************
*Function    : control_send_buffer
*Description : 控制拥塞
*Input       : src, dst
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void control_send_buffer(struct connection_t *src, struct connection_t *dst)
{
    //当缓冲区数据拥塞, 控制速率
    //写完后 恢复写端的 dst->writecb()方法
    if(evbuffer_get_length(bufferevent_get_output(dst->bev)) > MAX_OUTPUT)
    {
        bufferevent_disable(src->bev, EV_READ); //源端不再读
        bufferevent_setwatermark(dst->bev, EV_WRITE, MAX_OUTPUT/2, MAX_OUTPUT);//宿端缓存限制
        bufferevent_setcb(dst->bev, dst->readcb, dst->writecb, dst->eventcb, dst); //写完后在writecb通知我
    }
}


/*****************************************************************
*Function    : connection_finish
*Description : after write finish!
*Input       : connection, bev
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void connection_finish(struct bufferevent *bev, void *connection_)
{
    connection_free(connection_);
}

/////////////////////////////////////////////////////////////////////////////////////
////////////---------------------------------------------------//////////////////////
/////////////////////////////////////////////////////////////////////////////////////


/*****************************************************************
*Function    : master_readcb
*Description : reader callback used by client
*Input       : client bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
master_readcb(struct bufferevent *bev, void *connection_)
{
    struct mirror_t      *mirror = NULL;
    struct connection_t  *master = NULL;
    struct connection_t  *slave_1 = NULL;
    struct connection_t  *slave_2 = NULL;
    size_t                len = 0;

    master      = connection_;
    mirror      = master->mirror;
    slave_1     = mirror->slave_1;
    slave_2     = mirror->slave_2;

    len = evbuffer_get_length(bufferevent_get_input(bev));

    //发往 SLAVE_1 && 控制速率
    evbuffer_add_buffer_reference(bufferevent_get_output(slave_1->bev), bufferevent_get_input(master->bev));
    control_send_buffer(master, slave_1);

    //发往 SLAVE_2 && 控制速率
    evbuffer_add_buffer_reference(bufferevent_get_output(slave_2->bev), bufferevent_get_input(master->bev));
    control_send_buffer(master, slave_2);

    evbuffer_drain(bufferevent_get_input(bev), len);
}

/*****************************************************************
*Function    : master_readcb
*Description : reader callback used by client
*Input       : client bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
master_writecb(struct bufferevent *bev, void *connection_)
{
    printf("in master_writecb\n");
    abort();
}

/*****************************************************************
*Function    : master_eventcb
*Description : events callback used by slave
*Input       : slave bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
master_eventcb(struct bufferevent *bev, short what, void *connection_)
{
    int                      error = 0;
    int                      length_input = 0;
    int                      length_output = 0;
    struct mirror_t         *mirror = NULL;
    struct connection_t     *master = NULL;
    struct connection_t     *connection = NULL;
    struct connection_t     *slave_1 = NULL;
    struct connection_t     *slave_2 = NULL;

    connection      = connection_;
    mirror          = connection->mirror;
    master          = mirror->master;
    slave_1         = mirror->slave_1;
    slave_2         = mirror->slave_2;

    log_write(WARN, "event connection=%p", connection_);
    error |= do_enevt(what & BEV_EVENT_CONNECTED,connection);
    error |= do_enevt(what & BEV_EVENT_ERROR,    connection);
    error |= do_enevt(what & BEV_EVENT_EOF,      connection);
    error |= do_enevt(what & BEV_EVENT_READING,  connection);
    error |= do_enevt(what & BEV_EVENT_WRITING,  connection);
    error |= do_enevt(what & BEV_EVENT_TIMEOUT,  connection);

    if(error)
    {
        mirror_free(connection->mirror);

        //将master的数据全部读出-不再写
        length_input = evbuffer_get_length(bufferevent_get_input(master->bev));
        //log_write(DEBUG, "master length_input=%u", length_input);
        if(length_input)
        {
            master->readcb(master->bev, master);
        }
        else
        {
            connection_free(master);
        }

        //将slave1的数据全部输出-不再读
        length_output = evbuffer_get_length(bufferevent_get_output(slave_1->bev));
        //log_write(DEBUG, "slave_1 length_output=%u", length_output);
        if(length_output)
        {
            bufferevent_setcb(slave_1->bev, NULL, connection_finish, NULL, slave_1);
        }
        else
        {
            connection_free(slave_1);
        }

        //将slave2的数据全部输出-不再读
        length_output = evbuffer_get_length(bufferevent_get_output(slave_2->bev));
        //log_write(DEBUG, "slave_2 length_output=%u", length_output);
        if(length_output)
        {
            bufferevent_setcb(slave_2->bev, NULL, connection_finish, NULL, slave_2);
        }
        else
        {
            connection_free(slave_2);
        }
    }
}

/////////////////////////////////////////////////////////////////////////////////////
////////////---------------------------------------------------//////////////////////
/////////////////////////////////////////////////////////////////////////////////////



/*****************************************************************
*Function    : slave_readcb
*Description : reader callback used by slave
*Input       : slave bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
slave_readcb(struct bufferevent *bev, void *connection_t)
{
    struct mirror_t      *mirror = NULL;
    struct connection_t  *connection = NULL;

    connection = connection_t;
    mirror = connection->mirror;

    evbuffer_add_buffer(bufferevent_get_output(mirror->master->bev), bufferevent_get_input(bev));
}

/*****************************************************************
*Function    : slave_writecb
*Description : drained_writecb  callback used by slave
*Input       : slave bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
slave_writecb(struct bufferevent *bev, void *connection_)
{
    struct mirror_t      *mirror = NULL;
    struct connection_t  *connection = NULL;
    struct connection_t  *master = NULL;

    connection  = connection_;
    mirror      = connection->mirror;
    master      = mirror->master;

    //当写端空闲时, 恢复默认的写端
    bufferevent_setcb(bev, connection->readcb, NULL, connection->eventcb, connection);//reset calllback
    bufferevent_setwatermark(bev, EV_WRITE, 0, 0); //no limited
    bufferevent_enable(master->bev, EV_READ); //enable master read socket
}



/*****************************************************************
*Function    : slave_eventcb
*Description : events callback used by slave
*Input       : slave bufferevent, mirror session
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
slave_eventcb(struct bufferevent *bev, short what, void *connection_)
{
    int                      error = 0;
    int                      length_input = 0;
    int                      length_output = 0;
    struct mirror_t         *mirror = NULL;
    struct connection_t     *master = NULL;
    struct connection_t     *connection = NULL;
    struct connection_t     *slave_1 = NULL;
    struct connection_t     *slave_2 = NULL;

    connection      = connection_;
    mirror          = connection->mirror;
    master          = mirror->master;
    slave_1         = mirror->slave_1;
    slave_2         = mirror->slave_2;

    log_write(WARN, "event connection=%p", connection_);

    error |= do_enevt(what & BEV_EVENT_CONNECTED,connection);
    error |= do_enevt(what & BEV_EVENT_ERROR,    connection);
    error |= do_enevt(what & BEV_EVENT_EOF,      connection);
    error |= do_enevt(what & BEV_EVENT_READING,  connection);
    error |= do_enevt(what & BEV_EVENT_WRITING,  connection);
    error |= do_enevt(what & BEV_EVENT_TIMEOUT,  connection);

    if(error)
    {
        mirror_free(connection->mirror);

        //将slave_1的数据全部读出-不再写
        length_input = evbuffer_get_length(bufferevent_get_input(slave_1->bev));
        //log_write(DEBUG, "slave_1 length_input=%u", length_input);
        if(length_input)
        {
            slave_1->readcb(slave_1->bev, slave_1);
        }
        else
        {
            connection_free(slave_1);
        }

        //将slave_2的数据全部读出-不再写
        length_input = evbuffer_get_length(bufferevent_get_input(slave_2->bev));
        //log_write(DEBUG, "slave_2 length_input=%u", length_input);
        if(length_input)
        {
            slave_2->readcb(slave_2->bev, slave_2);
        }
        else
        {
            connection_free(slave_2);
        }

        //将master的数据全部输出-不再读
        length_output = evbuffer_get_length(bufferevent_get_output(master->bev));
        //log_write(DEBUG, "master length_output=%u", length_output);
        if(length_output)
        {
            bufferevent_setcb(master->bev, NULL, connection_finish, NULL, master);
        }
        else
        {
            connection_free(master);
        }
    }
}

