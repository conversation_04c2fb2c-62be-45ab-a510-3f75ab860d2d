/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_PROCESS_H
#define WX_PROXY_PROCESS_H

#ifdef __cplusplus
extern "C" {
#endif

void
master_readcb(struct bufferevent *bev, void *connection);

void
master_writecb(struct bufferevent *bev, void *connection);

void
master_eventcb(struct bufferevent *bev, short what, void *connection);

void
slave_readcb(struct bufferevent *bev, void *connection);

void
slave_writecb(struct bufferevent *bev, void *connection);

void
slave_eventcb(struct bufferevent *bev, short what, void *connection);

#ifdef __cplusplus
}
#endif

#endif

