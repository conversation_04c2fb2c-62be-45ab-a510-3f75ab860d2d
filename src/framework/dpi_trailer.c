/****************************************************************************************
 * 文 件 名 : dpi_trailer.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.4
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chunli      '2019-06-26
* 编    码 :
* 修    改 :

相关环境变量配置:
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "dpi_trailer.h"
#include <ctype.h>
#include <stdlib.h>

#include "stdio.h"
#include "string.h"
#include "glib.h"
#include "wxcs_def.h"
#include <arpa/inet.h>
#include "jhash.h"
#include "dpi_detect.h"
#include "dpi_common.h"
#include "dpi_log.h"
#include "dpi_pint.h"
#include "dpi_tbl_log.h"



extern struct global_config g_config;
static unsigned int err_msisdn_count = 0;   /*错误手机号码条数 */


struct mac_hdr
{
    uint8_t  h_dest  [6];
    uint8_t  h_source[6];
    uint16_t h_proto;
} __attribute__((packed));

typedef struct _value_string {
    uint32_t      value;
    const char*   strptr;
} value_string;

static const value_string
operatorType_HW[] = {
    { 0X01,    "CTCC"        },
    { 0X02,    "中国网通"    },
    { 0X03,    "CUCC"        },
    { 0X04,    "中国长城宽带"},
    { 0X05,    "中国铁通"    },
    { 0X06,    "CMCC"        },
    { 0X08,    "教育部门"    },
    { 0X09,    "中科院"      },
    { 0X0b,    "广电部门"    },
    { 0X63,    "其他"        },
    { 0x00,    NULL          },
};

static const value_string
operatorType_MCC[] = {
    { 0,    "CMCC"   },
    { 2,    "CMCC"   },
    { 4,    "CMCC"   },
    { 7,    "CMCC"   },
    { 1,    "CUCC"   },
    { 6,    "CUCC"   },
    { 3,    "CTCC"   },
    { 5,    "CTCC"   },
    { 0,     NULL    },
};

static struct int_to_string JL_RAT[] = {
    {0x01,            "UTRAN"},
    {0x02,            "GERAN"},
    {0x03,            "WLAN"},
    {0x04,            "GAN"},
    {0x05,            "HSPA Evolution"},
    {0x06,            "EUTRAN"},
    {0x07,            "Virtual"},
    {0x08,            "EUTRAN-NB-IoT"},
    {0x09,            "LTE-M"},
    {0x0a,            "NR"},
    {0x00,            NULL}
};

static const char*
value2String(size_t value, const value_string *KVList)
{
    if(NULL == KVList)
    {
        return "";
    }

    while(!(NULL == KVList->strptr && 0 == KVList->value))
    {
        if(value ==  KVList->value)
        {
            return KVList->strptr;
        }
        KVList++;
    }
    return "";
}


/* 7字节 转 long long */
static size_t
Get7Byte(const char*p)
{
    if(NULL == p)
    {
        return -1;
    }

    char buff[8];
    buff[0] = p[6];
    buff[1] = p[5];
    buff[2] = p[4];
    buff[3] = p[3];
    buff[4] = p[2];
    buff[5] = p[1];
    buff[6] = p[0];
    buff[7] = 0;
    return *(size_t*)&buff;
}
void get_eth_info(const uint8_t *payload, uint32_t payload_len, union eth_data *data, int type)
{
	switch(type){
		// case HW_SH_MAC:
		// case PH_DEFAULT:
		// 	data->nsp  = (payload[4] >> 5) & 0x03;
		// 	break;
		// case RT_6402:
		// case RT_9800:
		// 	data->nsp  = (payload[6] >> 6) & 0x03;
		// 	break;
		// case HW_YN:
		// 	data->base = payload[7];
		// 	break;
		// case HW_SH_VLAN:
		// 	if(payload_len > 16 && get_uint16_t(payload, 12) == 0x8100){
		// 		data->vlan_id = get_uint16_t(payload, 14) & 0x0fff;
		// 	}
		// 	break;
		// case HY_FS:
		// 	data->inter_id = get_uint16_ntohs(payload, 10);
		// 	break;
		// case LINE_470:
		// 	data->line_id = get_uint64_t(payload, 6);
		// 	break;
		// case LINE_YN_470:
    //         data->yunnan_470.nsp           = (payload[4] >> 5) & 0x03;
    //         data->yunnan_470.channel_num   = (payload[6] >> 1) & 0x3f;
    //         data->yunnan_470.line_num      = get_uint16_ntohs(payload, 7);
    //         data->yunnan_470.interface_num = payload[9] & 0x3f;
    //         data->yunnan_470.linktype      = payload[10];
    // break;
    case TRAILER_HWZZ:
            memcpy(&data->hwzz_eth, payload , sizeof(data->hwzz_eth));
            data->hwzz_eth.totLen= ntohs(data->hwzz_eth.totLen);
            data->hwzz_eth.trailerLen= ntohs(data->hwzz_eth.trailerLen);
    break;
		default:
			break;
	}
	return;
}

/*****************************************************************
*Function    :isUTF8
*Description :检测数据是不是UTF8编码
*Input       :数据的长度与地址
*Output      :none
*Return      :返回UTF8数据的长度， 0代表不是UTF8编码数据
*Others      :none
*****************************************************************/
static int
isUTF8_V2(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;

    if(NULL == pData || len <=0)
    {
        return 0;
    }

    while(loop > 0 )
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=2 &&  (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0)))
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        else if(loop>=4 &&  (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) )
        {
            p = p + 4;
            loop = loop - 4;
            continue;
        }
        return 0;/* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

void
dpi_TrailerDump(ST_trailer* trailer)
{
    switch(trailer->trailerType)
    {
        case TRAILER_RT:
        case TRAILER_FL:
            printf("类型              :%-25s", trailer->trailerType == TRAILER_RT  ? "戎腾" : "富乐");
            printf("Operator          :%s\n",  trailer->Operator);
            printf("DevName           :%-25s",  trailer->DevName);
            printf("TEID              :%u\n",  trailer->TEID);
            printf("OUTTER_SRC        :%u.%u.%u.%u\n",  *(((unsigned char*)&trailer->OUTTER_SRC)+0),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+1),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+2),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+3));
            printf("OUTTER_DST        :%u.%u.%u.%u\n",  *(((unsigned char*)&trailer->OUTTER_DST)+0),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+1),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+2),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+3));
            printf("MSISDN            :%-25zu", trailer->MSISDN);
            printf("IMEI              :%zu\n", trailer->IMEI);
            printf("IMSI              :%zu\n", trailer->IMSI);
            printf("TAC               :%-25u",  trailer->TAC);
            printf("PLMN_ID           :%u\n",  (unsigned short)trailer->PLMN_ID);
            printf("ULI               :%-25u",  trailer->ULI);
            printf("BS                :%u\n",  trailer->BS);
            printf("DomainName        :%s\n",  trailer->DomainName);
            break;

        case TRAILER_HW:
            printf("类型              :%-25s", "恒为");
            printf("Operator          :%s\n",  trailer->Operator);
            printf("DevName           :%s\n",  trailer->DevName);
            printf("TAGTYPE           :0X%02X\n",trailer->TAGTYPE);
            printf("BFLAG             :0X%02X\n",trailer->BFLAG);
            printf("NCODE             :0X%02X\n",trailer->NCODE);
            printf("TEID              :%u\n",  trailer->TEID);
            printf("OUTTER_SRC        :%u.%u.%u.%u\n",  *(((unsigned char*)&trailer->OUTTER_SRC)+0),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+1),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+2),
                                                        *(((unsigned char*)&trailer->OUTTER_SRC)+3));
            printf("OUTTER_DST        :%u.%u.%u.%u\n",  *(((unsigned char*)&trailer->OUTTER_DST)+0),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+1),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+2),
                                                        *(((unsigned char*)&trailer->OUTTER_DST)+3));
            printf("MSISDN            :%zu\n", trailer->MSISDN);
            printf("IMEI              :%zu\n", trailer->IMEI);
            printf("IMSI              :%zu\n", trailer->IMSI);
            printf("TAC               :%u\n",  trailer->TAC);
            printf("ACCOUNT           :%s\n",  trailer->ACCOUNT);
            printf("ESN               :%s\n",  trailer->ESN);
            printf("MEID              :%s\n",  trailer->MEID);
            printf("LAC               :%u\n",  trailer->LAC);
            printf("SAC               :%u\n",  trailer->SAC);
            printf("CI                :%u\n",  trailer->CI);
            printf("ECGI              :%u\n",  trailer->ECGI);
            printf("BSID              :%s\n",  trailer->BSID);
            printf("GRE_KEY           :%u\n",  trailer->GRE_KEY);
            printf("TAI               :%u\n",  trailer->TAI);
            printf("ECGI_MNC          :%u\n",  trailer->ECGI_MNC);
            printf("APN               :%s\n",  trailer->APN);
            break;

        case TRAILER_JSC:
            printf("类型              :%-25s", "江苏金陵");
            printf("Operator          :%s\n",  trailer->Operator);
            printf("DevName           :%s\n",  trailer->DevName);
            printf("MSISDN            :%zu\n", trailer->MSISDN);
            printf("IMEI              :%zu\n", trailer->IMEI);
            printf("IMSI              :%zu\n", trailer->IMSI);
            printf("MEID              :%s\n",  trailer->MEID);
            printf("ESN               :%s\n",  trailer->ESN);
            printf("APN               :%s\n",  trailer->APN);
            printf("PLMN_ID           :%u\n",  (unsigned short)trailer->PLMN_ID);
            printf("LAC               :%u\n",  trailer->LAC);
            printf("SAC               :%u\n",  trailer->SAC);
            printf("TAC               :%u\n",  trailer->SAC);
            printf("CI                :%u\n",  trailer->CI);
            printf("ULI               :%u\n",  trailer->ULI);
            printf("BSID              :%s\n",  trailer->BSID);
            break;

        case TRAILER_HZ:
            printf("类型              :江苏汇智\n");
            printf("Operator          :%s\n",  trailer->Operator);
            printf("DevName           :%s\n",  trailer->DevName);
            printf("MSISDN            :%zu\n", trailer->MSISDN);
            printf("IMEI              :%zu\n", trailer->IMEI);
            printf("IMSI              :%zu\n", trailer->IMSI);
            printf("NCODE             :0X%02X\n",trailer->NCODE);
            printf("PLMN_ID           :%u\n",  (unsigned short)trailer->PLMN_ID);
            printf("LAC               :%u\n",  trailer->LAC);
            printf("CI                :%u\n",  trailer->CI);
            printf("TAC               :%u\n",  trailer->TAC);
            printf("ULI               :%u\n",  trailer->ULI);
            printf("BS                :%u\n",  trailer->BS);
            printf("ECGI_MNC          :%u\n",  trailer->ECGI_MNC);
            break;
        case TRAILER_NO:
            printf("No trailer\n");
            break;

        default:
            printf("unKnown_trailer   :%d\n",  trailer->trailerType);
            break;
    }
}

int
dpi_TrailerUpdateTS(ST_trailer* trailer)
{
    trailer->TS = time(NULL);
    return trailer->TS;
}

int
dpi_TrailerGetHWZZMAC(ST_trailer* trailer, const char* mac)
{
    int             opt;

    if(NULL == trailer || NULL == mac)
    {
        return -1;
    }

    // only for HWZZ
    if(TRAILER_HWZZ != trailer->trailerType)
    {
        return -1;
    }

    if(0 != *(size_t*)(size_t)trailer->Operator) // already set ?
    {
        return -1;
    }

    const struct hwzz_eth_hdr* hwzz_eth = (const struct hwzz_eth_hdr*)mac;
    trailer->BFLAG  = hwzz_eth->dataSource.operator_;
    trailer->NCODE =  hwzz_eth->type;
    const char*p = value2String(hwzz_eth->dataSource.operator_, operatorType_HW);
    snprintf(trailer->Operator, sizeof(trailer->Operator), "%s", p);
    return 0;
}

int
dpi_TrailerGetMAC(ST_trailer* trailer, const char* mac,  const char* rt_name)
{
    const struct mac_hdr *p;
    int             opt;

    if(NULL == trailer || NULL == mac || NULL == rt_name)
    {
        return -1;
    }

    // only for RT
    if(TRAILER_RT != trailer->trailerType)
    {
        return -1;
    }

    if(0 != *(size_t*)(size_t)trailer->Operator) // already set ?
    {
        return -1;
    }

    p   = (const struct mac_hdr*)mac;
    opt = (p->h_source[0] & 0XC0) >> 6;
    if(0 == memcmp("RTL6402", rt_name, strlen("RTL6402")))
    {
        switch(opt)
        {
            case 0:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CTCC");
                break;
            case 1:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CMCC");
                break;
            case 2:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CUCC");
                break;
            case 3:
            default:
                memset(trailer->Operator, 0, sizeof(trailer->Operator));
                break;
        }
    }
    else
    if(0 == memcmp("RTL9800", rt_name, strlen("RTL9800")))
    {
        switch(opt)
        {
            case 1:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CTCC");
                break;
            case 2:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CMCC");
                break;
            case 3:
                snprintf(trailer->Operator, sizeof(trailer->Operator), "CUCC");
                break;
            case 0:
            default:
                memset(trailer->Operator, 0, sizeof(trailer->Operator));
                break;
        }
    }
    else
    {
        printf("unsupport RT device [%s]", rt_name);
        exit(1);
    }
    return 0;
}

int
dpi_TrailerSetArea(ST_trailer* trailer, const char* str)
{
    if(NULL == str)
    {
        return -1;
    }

    if(0 != *(size_t*)(size_t)trailer->Area) // already ?
    {
        return -1;
    }

    int len = strlen(str);
    int safelen = len < (int)sizeof(trailer->Area) -1 ? len : (int)sizeof(trailer->Area) -1 ;
    memcpy(trailer->Area, str, safelen);
    trailer->Area[safelen] = '\0';

    char buff[1024];
    snprintf(buff, sizeof(buff), "%s_%s",trailer->Area, trailer->DevName);
    trailer->DPI_Node_id = jhash(buff, strlen(buff), 31);
    return safelen;
}

int
dpi_TrailerSetDev(ST_trailer* trailer, const char* str)
{
    if(NULL == str)
    {
        return -1;
    }

    if(0 != *(size_t*)(size_t)trailer->DevName) // already ?
    {
        return -1;
    }

    int len = strlen(str);
    int safelen = len < (int)sizeof(trailer->DevName) -1 ? len : (int)sizeof(trailer->DevName) -1 ;
    memcpy(trailer->DevName, str, safelen);
    trailer->DevName[safelen] = '\0';
    return safelen;
}

int
dpi_TrailerSetOpt(ST_trailer* trailer, const char* str)
{
    if(NULL == str)
    {
        return -1;
    }

    if(0 != *(size_t*)(size_t)str)
    {
        int len = strlen(str);
        int safelen = len < (int)sizeof(trailer->Operator)-1 ? len : (int)sizeof(trailer->Operator) -1 ;
        memcpy(trailer->Operator, str, safelen);
        trailer->Operator[safelen] = '\0';
    }
    return 0;
}

int
dpi_TrailerECI(ST_trailer* trailer, char*buff, int size)
{
    uint32_t ULI      = ntohl(trailer->ULI);
    uint32_t eNodeBid = 0;
    uint32_t CellId   = 0;
    uint32_t LAC      = 0;
    uint32_t SAC      = 0;
    if(g_config.trailertype == TRAILER_HWZZ){
            snprintf(buff, size, "0x%08x", ULI);
            return 0;
    }

    if (trailer->trailerType == TRAILER_ZXSK) {
        if (trailer->nci != 0) {
            snprintf(buff, size, "5G:0x%lx", trailer->nci);
        } else {
            eNodeBid = ULI >> 12;
            CellId   = (ULI >> 4) & 0xff;
            snprintf(buff, size, "4G:0x%08x,eNodeBid:%u,CellId:%u", ULI, eNodeBid, CellId);
        }

        return 0;
    }

    if (trailer->rat  != 0) {
        eNodeBid = ULI >> 12;
        CellId   = (ULI >> 4) & 0xff;
        snprintf(buff, size, "%s:0x%08x,eNodeBid:%u,CellId:%u",val_to_string(trailer->rat, JL_RAT), ULI, eNodeBid, CellId);
    } else {
        switch (trailer->BS & 0x0F)
        {
        case 0xd:
            eNodeBid = ULI >> 12;
            CellId   = (ULI >> 4) & 0xff;
            snprintf(buff, size, "4G:0x%08x,eNodeBid:%u,CellId:%u", ULI, eNodeBid, CellId);
            return 0;

        case 0xc:
            LAC = ULI >> 16;
            SAC = ULI & 0xffff;
            snprintf(buff, size, "3G:0x%08x,LAC:%u,SAC:%u", ULI, LAC,SAC);
            return 0;

        default:
            snprintf(buff, size, "UN:0x%08x", ULI);
            return 0;
        }
    }

    return 0;
}


static int
dpi_ParseTrailer_RT(ST_trailer* trailer, const char* data, unsigned int len)
{
    /*戎腾标签最短长度56  */
    if (len < 56)
    {
        return -1;
    }

    int offset = 0;
    trailer->TEID          =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    /**** Skip resevre              ****/                             offset += sizeof(int);
    trailer->OUTTER_SRC    =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    trailer->OUTTER_DST    =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    trailer->MSISDN        =        Get7Byte(data + offset);          offset += 7;
    trailer->IMEI          =        Get7Byte(data + offset);          offset += 7;
    trailer->IMSI          =        Get7Byte(data + offset);          offset += 7;
    trailer->TAC           =        *(short*)(size_t)(data + offset); offset += sizeof(short);
    trailer->PLMN_ID       =        *(short*)(size_t)(data + offset); offset += sizeof(short);
    trailer->ULI           =        *(int*)  (size_t)(data + offset); offset += sizeof(int);
    trailer->BS            = 0x0f & *(char*) (size_t)(data + offset); offset += sizeof(char);

    if(len > 60 && len <= (60+26))
    {
        /**** Skip resevre       ****/                         offset += 10;
        /**** Skip domain length ****/                         offset += 1;
        uint32_t datalen = *(char*)(size_t)(data + offset);    offset += sizeof(char);
        int32_t  safelen = datalen < sizeof(trailer->DomainName)-1 ? datalen : sizeof(trailer->DomainName)-1;
        if(safelen <= 0)
        {
            return -1;
        }

        memcpy(trailer->DomainName, data + offset, safelen);
        trailer->DomainName[safelen] = '\0';

        if(safelen != isUTF8_V2(trailer->DomainName, safelen))
        {
            trailer->DomainName[0] = '\0';
        }
    }
    return 0;
}

static int
dpi_ParseTrailer_FL(ST_trailer* trailer, const char* data, unsigned int len)
{
    if(len < 56)
        return 1;

    int offset = 0;
    trailer->TEID          =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    /**** Skip resevre              ****/                             offset += sizeof(int);
    trailer->OUTTER_SRC    =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    trailer->OUTTER_DST    =        *(int*)(size_t)(data + offset);   offset += sizeof(int);
    trailer->MSISDN        =        Get7Byte(data + offset);          offset += 7;
    trailer->IMEI          =        Get7Byte(data + offset);          offset += 7;
    trailer->IMSI          =        Get7Byte(data + offset);          offset += 7;
    trailer->TAC           =        *(short*)(size_t)(data + offset); offset += sizeof(short);
    trailer->PLMN_ID       =        *(short*)(size_t)(data + offset); offset += sizeof(short);
    trailer->ULI           =        *(int*)  (size_t)(data + offset); offset += sizeof(int);
    trailer->BS            = 0x0f & *(char*) (size_t)(data + offset); offset += sizeof(char);

    int mnc;
    char str[8];
    sprintf(str, "%u",  ntohs(trailer->PLMN_ID));
    if(strlen(str) > 3)
       mnc = atoi(str + 3);
    else
       mnc = -1;

    switch(mnc){
        case 0: case 2: case 4: case 7: case 8: case 13:
            strcpy(trailer->Operator, "CMCC");
            break;
        case 1: case 6: case 9: case 10:
            strcpy(trailer->Operator, "CUCC");
            break;
        case 3: case 5: case 11: case 12:
            strcpy(trailer->Operator, "CTCC");
            break;
    }

    return 0;
}

static int
dpi_ParseTrailer_HW_Etag(ST_trailer* trailer, char Etag, const char*data, int datalen)
{
    switch(Etag)
    {
        case 0X01:  // IMSI  8 字节
            if(8 == datalen)
            {
                // be64toh;
                trailer->IMSI = be64toh(*(size_t*)(size_t)data);
            }
            break;

        case 0X02:  // Account 宽带账号  N字节
            if(datalen < (int)sizeof(trailer->ACCOUNT))
            {
                int safelen = datalen < (int)sizeof(trailer->ACCOUNT)-1 ? datalen : (int)sizeof(trailer->ACCOUNT)-1;
                memcpy(trailer->ACCOUNT, data, safelen);
                trailer->ACCOUNT[safelen]= '\0';
            }
            break;


        case 0X03:  // MSISDN 手机号     N字节
            if(8 == datalen)
            {
                trailer->MSISDN = be64toh(*(size_t*)(size_t)data);
            }
            break;

        case 0X04:  // IMEI              N字节
            {
                int index = 0;
                char imei[64];
                while(index < datalen && datalen < 32)
                {
                    imei[index+0]  = data[index+1];
                    imei[index+1]  = data[index+0];
                    index +=2;
                }
                imei[index] = '\0';
                trailer->IMEI = g_ascii_strtoull(imei, NULL, 0);
                break;
            }

        case 0X05:  // ESN               N字节
            {
                int len = strlen(data);
                int safelen = len < (int)sizeof(trailer->ESN)-1 ? len : (int)sizeof(trailer->ESN)-1 ;
                memcpy(trailer->ESN, data, safelen);
                trailer->ESN[safelen] = '\0';
                break;
            }

        case 0X06:  // MEID              N字节
            {
                int len = strlen(data);
                int safelen = len < (int)sizeof(trailer->MEID)-1 ? len : (int)sizeof(trailer->MEID)-1 ;
                memcpy(trailer->MEID, data, safelen);
                trailer->MEID[safelen] = '\0';
                break;
            }

        case 0X07:  // LAC               2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->LAC = *(short*)(size_t)data;
                }
                break;
            }

        case 0X08:  // SAC               2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->SAC = *(short*)(size_t)data;
                }
                break;
            }

        case 0X09:  // CI                2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->CI = *(short*)(size_t)data;
                }
                break;
            }

        case 0X10:  // TAC              2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->TAC = *(short*)(size_t)data;
                }
                break;
            }

        case 0X12:  // BSID             N字节
            {
                int len = strlen(data);
                int safelen = len < (int)sizeof(trailer->BSID)-1 ? len : (int)sizeof(trailer->BSID)-1 ;
                memcpy(trailer->BSID, data, safelen);
                trailer->BSID[safelen] = '\0';
                break;
            }

        case 0X13:  // GRE Key          4字节
            {
                if(4 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->GRE_KEY = *(int*)(size_t)data;
                }
                break;
            }

        case 0X14:  // TAI              2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->TAI = *(short*)(size_t)data;
                }
                break;
            }

        case 0X11:  // ECGI             4字节
            {
                if(4 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->ECGI = *(int*)(size_t)data;
                }
                break;
            }

        case 0X15:  // ECGI_MNC         2字节
            {
                if(2 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->ECGI_MNC = *(short*)(size_t)data;
                }
                break;
            }

        case 0X16:  // TEID             4 字节
            {
                if(4 == datalen)
                {
                    // 在TBL中最终输出为 HEX, 无需端序转换
                    trailer->TEID = *(int*)(size_t)data;
                }
                break;
            }

        case 0X17:  // GTP_SIP          4字节
            {
                if(4 == datalen)
                {
                    trailer->OUTTER_SRC = *(unsigned int*)(size_t)data;
                }
                break;
            }

        case 0X18:  // GTP_DIP          4字节
            {
                if(4 == datalen)
                {
                    trailer->OUTTER_DST = *(unsigned int*)(size_t)data;
                }
                break;
            }

        case 0x21:  // APN              N字节
            {
                int safelen = datalen < (int)sizeof(trailer->APN)-1 ? datalen : (int)sizeof(trailer->APN)-1 ;
                memcpy(trailer->APN, data, safelen);
                trailer->APN[safelen] = '\0';
                break;
            }

        case 0x29:  // end
            break;

        default:
            break;
    }

    return -1; // 出错了
}

static int
dpi_ParseTrailer_HW(ST_trailer* trailer, const char* data, unsigned int len)
{
    int  offset = 0;
    trailer->TAGTYPE = *(char*)(size_t)(data + offset);             offset += sizeof(char);
    /**** Nothing TODO    ****/                                     offset += sizeof(char);
    trailer->BFLAG   = *(char*)(size_t)(data + offset);             offset += sizeof(char);
    trailer->NCODE   = *(char*)(size_t)(data + offset);             offset += sizeof(char);

    while(offset < (int)len)
    {
        unsigned char Etag = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        unsigned char Elen = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        dpi_ParseTrailer_HW_Etag(trailer, Etag, data+offset, Elen); offset += Elen;
    }

    {// SET operator
        const char*p = value2String(trailer->BFLAG, operatorType_HW);
        int len = strlen(p);
        int safelen = len < (int)sizeof(trailer->Operator) -1 ? len : (int)sizeof(trailer->Operator) -1 ;
        memcpy(trailer->Operator, p, safelen);
        trailer->Operator[safelen] = '\0';
    }
    return 0;
}

// BCD(十进制大端)转十进制数值(存在F分割)
static size_t
JSC_BCD_DEC(const char*data, int datalen)
{
    unsigned long long num = 0;
    unsigned char a, b;
    for(int i = 0; i < datalen; i++)
    {
        a = 0X0F &  data[i];
        b = 0X0F & (data[i] >> 4);
        if(0x0F != a)
        {
            num = num * 10 + a;
        }
        if(0x0F != b)
        {
            num = num * 10 + b;
        }
    }
    return num;
}

// BCD(十六进制大端)转十六进制数值(无F分割)
static size_t
JSC_BCD_HEX(const char*data, int datalen)
{
    unsigned long long num = 0;
    unsigned char a, b;
    for(int i = 0; i < datalen; i++)
    {
        a = 0X0F &  data[i];
        b = 0X0F & (data[i] >> 4);
        num = num * 16 + a;
        num = num * 16 + b;
    }
    return num;
}

static unsigned short
JSC_PLMN(const char*data, int datalen)
{
    char     buff[64]  = {0};
    unsigned short mcc = 0;
    unsigned short mnc = 0;
    unsigned char  H;
    unsigned char  L;

    for(int i = 0; i < 2; i++)
    {
        H   = 0X0F &  data[i];
        L   = 0X0F & (data[i] >> 4);
        mcc*= 10;
        mcc+= ((0xF == H) ? 0 : H);
        mcc*= 10;
        mcc+= ((0xF == L) ? 0 : L);
    }
    mcc/=10;

    {
        L = 0X0F &  data[2];
        mnc = mnc * 10 + (0x0F != L)?L:0;
    }

    snprintf(buff, sizeof(buff), "%u%02u", mcc, mnc);
    return ntohs(atoi(buff));//返回网络字节序(16进制输出格式,统一规范)
}

static int BinToHex(const unsigned char *inPut, unsigned int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if(NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    char * pOrigin = OutBuffer;
    unsigned int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
        OutBuffer+=2;
    }
    return OutBuffer - pOrigin;
}

static int
dpi_ParseTrailer_JSC_tag(ST_trailer* trailer, uint16_t tag, const char*data, int datalen)
{
    switch(tag)
    {
        case 0:
          if (datalen < 64) {
            memcpy(trailer->fixed_account, data, datalen);
          }
          break;
        case 1:  // IMSI  8 字节
            if(8 == datalen)
            {
                trailer->IMSI = JSC_BCD_DEC(data, datalen);
            }
            break;

        case 2:  // MSISDN  8 字节
            if(8 == datalen)
            {
                trailer->MSISDN = JSC_BCD_DEC(data, datalen);
            }
            break;

        case 3:  // IMEI  8 字节
            if(8 == datalen)
            {
                trailer->IMEI = JSC_BCD_DEC(data, datalen);
            }
            break;

        case 4:  // MEID  7 字节
            if(7 == datalen)
            {
                trailer->IMEI = JSC_BCD_HEX(data, datalen);
            }
            break;

        case 5:  // ESN  4 字节
            if(4 == datalen)
            {
                int ESN = JSC_BCD_HEX(data, datalen);
                snprintf(trailer->ESN, sizeof(trailer->ESN), "0X%08X", ESN);
            }
            break;

        case 6:  // APN  N 字节
            if(datalen <= 16)
            {
                memcpy(trailer->APN, data, datalen);
            }
            break;

        case 7:  // CGI = MCC&MNC(3) + LAC(2) + CellID(2)
            if(7 == datalen)
            {
                trailer->PLMN_ID  = JSC_PLMN(data, 3);
                trailer->LAC      = *(const unsigned short*)(data+3);
                trailer->CI       = *(const unsigned short*)(data+3+2);
            }
            break;

        case 8:  // SAI = MCC&MNC(3) + LAC(2) + SAC(2)
            if(7 == datalen)
            {
                trailer->PLMN_ID  = JSC_PLMN(data, 3);
                trailer->LAC      = *(const unsigned short*)(data+3);
                trailer->SAC      = *(const unsigned short*)(data+3+2);
            }
            break;

        case 9:  // RAI = MCC&MNC(3) + LAC(2) + RAC(2)
            if(7 == datalen)
            {
                trailer->PLMN_ID  = JSC_PLMN(data, 3);
                trailer->LAC      = *(const unsigned short*)(data+3);
            }
            break;

        case 10:  // TAI = MCC&MNC(3) + TAC(2)
            if(5 == datalen)
            {
                trailer->PLMN_ID        = JSC_PLMN(data, 3);
                trailer->TAC            = *(const unsigned short*)(data+3);
            }
            break;

        case 11:  // ECGI = = MCC&MNC(3) + ECI(4)
            if(7 == datalen)
            {
                trailer->PLMN_ID        = JSC_PLMN(data, 3);
                trailer->ULI            = ntohl((ntohl(*(int*)(size_t)(data+3)))<<4);
                trailer->ECGI            = *(int*)(size_t)data;
            }
            break;

        case 12:  // BSID
            if(6 == datalen)
            {
                int BSID = JSC_BCD_HEX(data, datalen);
                snprintf(trailer->BSID, sizeof(trailer->BSID), "0X%012X", BSID);
            }
            break;
        case 14:
            trailer->direction = data[0];
            break;
        case 15:
            if (4 == datalen) {
                BinToHex((const unsigned char *)data, datalen, trailer->ruleID, sizeof(trailer->ruleID));
            }
            break;
        case 16:
            if (datalen < 128) {
                BinToHex((const unsigned char *)data, datalen, trailer->label, sizeof(trailer->label));
            }
            break;
        /*ADD_S by yangna 2020-09-23 */
        case 200:  // RAT 16进制，3G-3,4G-4,转换成戎腾标识方法
            if(1 == datalen)
            {
                if (3 == data[0])
                {
                    trailer->BS = 0x0f & 0xc;
                }
                else if (4 == data[0])
                {
                  trailer->BS = 0x0f & 0xd;
                }
            }
            break;
        case 201:  // SERVICE 运营商标识，移动-1，联通-2，电信-3，广电-4
            if(1 == datalen)
            {
                trailer->BFLAG = data[0];
                if (1 == data[0])
                {
                    memcpy(trailer->Operator, "CMCC", 5);
                }
                else if (2 == data[0])
                {
                    memcpy(trailer->Operator, "CUCC", 5);
                }
                else if (3 == data[0])
                {
                    memcpy(trailer->Operator, "CTCC", 5);
                }
                else if (4 == data[0])
                {
                    memcpy(trailer->Operator, "广电部门", 13);
                }
            }
            break;
        case 900:
            if(6 == datalen)
            {
                trailer->PLMN_ID        = JSC_PLMN(data, 3);
                trailer->TAC            = pntoh24(data + 3);
            }
            break;
        case 901:       // NCGI  MCC&MNC 3 byte + NCI 5 bytes
            if (8 == datalen) {
                trailer->PLMN_ID        = JSC_PLMN(data, 3);
                trailer->nci            = pntoh40(data + 3);
            }
            break;
        /*ADD_E by yangna 2020-09-23 */
        case 13:
            trailer->rat = data[0];
            break;
        default:
            break;
    }

    return 0;
}

static int
dpi_ParseTrailer_JSC(ST_trailer* trailer, const char* data, unsigned int len)
{
    int  offset      = 0;
    int  trailerLen  = 0;
    int  trailerFlag = 0;
    if(0xFEFF != *(unsigned short*)(size_t)(data + offset))
    {
        return 0;
    }
    offset += sizeof(short);

    trailerLen  = *(char*)(size_t)(data + offset); offset += sizeof(char);
    trailerFlag = *(char*)(size_t)(data + offset); offset += sizeof(char);
    if((unsigned int)trailerLen > len)
    {
        return 0;
    }
    /*trailerLen 长度不包括前三个字节(2个字节标签特征码+1个字节长度指示) */
    while(offset - 3  < trailerLen)
    {
        unsigned char tag = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        unsigned char len = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        dpi_ParseTrailer_JSC_tag(trailer, tag, data+offset, len);  offset += len;
    }

    return 0;
}


#define ZSXK_FLAG  "JDZK"
static int
dpi_ParseTrailer_ZXSK(ST_trailer* trailer, const char* data, unsigned int len)
{
    int  offset      = 0;
    int  trailerLen  = 0;
    int  trailerFlag = 0;
    if (strncmp(data, ZSXK_FLAG, strlen(ZSXK_FLAG)) != 0 &&
        strncmp(data + len - 4, ZSXK_FLAG, strlen(ZSXK_FLAG)) != 0) {
      return 0;
    }

    offset += strlen(ZSXK_FLAG);

    trailerLen = len - strlen(ZSXK_FLAG);

    if((unsigned int)trailerLen > len)
    {
        return 0;
    }
    /*trailerLen 长度不包括前三个字节(2个字节标签特征码+1个字节长度指示) */
    while(offset < trailerLen)
    {
        uint16_t tag = ntohs(*(const uint16_t *)(((const uint8_t *)data) + offset));
        offset+=2;
        uint16_t len = ntohs(*(const uint16_t *)(((const uint8_t *)data) + offset));
        offset+=2;
        dpi_ParseTrailer_JSC_tag(trailer, tag, data+offset, len);  offset += len;
    }
    return 0;
}

static size_t
HZ_BCD_DEC(const char*data, int datalen)
{
    unsigned long long num = 0;
    unsigned char a, b;
    for(int i = 0; i < datalen; i++)
    {
        a = 0X0F &  data[i];
        b = 0X0F & (data[i] >> 4);
        num = num * 100 + b * 10 + a;
    }
    return num;
}

static int
dpi_ParseTrailer_HZ_Etag(ST_trailer* trailer, char Etag, const char*data, int datalen)
{
    short tmpMcc;
    short tmpMnc;
    short tmpPlmnid;
    switch(Etag)
    {
        case 1:  // IMSI  8 字节
            {
                if(8 == datalen)
                {
                    trailer->IMSI = HZ_BCD_DEC(data, datalen);
                }
                break;
            }
        case 2:  // MSISDN 手机号     8字节
            {
                if(8 == datalen)
                {
                    trailer->MSISDN = HZ_BCD_DEC(data, datalen);
                }
                break;
            }
        case 3:   // IMEI            8字节
            {
                if(8 == datalen)
                {
                    trailer->IMEI = HZ_BCD_DEC(data, datalen);
                }
                break;
            }
        case 4:   // NCODE         1字节
            {
                if(1 == datalen)
                {
                    trailer->NCODE = *(char*)(size_t)data;
                    if (1 == trailer->NCODE)
                    {
                        trailer->BS = 0x0f & 0xc;
                    }
                    else if (2 == trailer->NCODE)
                    {
                        trailer->BS = 0x0f & 0xd;
                    }
                }
                break;
            }
        case 5:  // MCC               2字节
            {
                if(2 == datalen)
                {
                    tmpMcc = *(short*)(size_t)data;
                    tmpMcc = ntohs(tmpMcc) * 100;
                    trailer->PLMN_ID = tmpMcc;
                }
                break;
            }
        case 6:  // MNC  2字节 +    OPERATOR PLMNID
            {
                if(2 == datalen)
                {
                    tmpMnc = *(short*)(size_t)data;
                    tmpPlmnid = trailer->PLMN_ID + ntohs(tmpMnc);
                    trailer->PLMN_ID = ntohs(tmpPlmnid);  //返回网络字节序(16进制输出格式,统一规范)
                }
                switch(tmpMnc){
                        case 0: case 2: case 4: case 7: case 8: case 13:
                            strcpy(trailer->Operator, "CMCC");
                            break;
                        case 1: case 6: case 9: case 10:
                            strcpy(trailer->Operator, "CUCC");
                            break;
                        case 3: case 5: case 11: case 12:
                            strcpy(trailer->Operator, "CTCC");
                            break;
                    }
                break;
            }
        case 7:  // LAC               2字节
            {
                if(2 == datalen)
                {
                    trailer->LAC = *(short*)(size_t)data;
                    trailer->BS = 0xc;
                }
                break;
            }
        case 8:   // CI               2字节
            {
                if(2 == datalen)
                {
                    trailer->CI = *(short*)(size_t)data;
                    trailer->BS = 0xc;
                }
                break;
            }
        case 9: // ULI                4字节
            {
                if(4 == datalen)
                {
                    trailer->ULI = ntohl((ntohl(*(int*)(size_t)(data)))<<4);
                    trailer->BS = 0xd;
                }
                break;
            }
        case 10:  // TAC               2字节
            {
                if(2 == datalen)
                {
                    trailer->TAC = *(short*)(size_t)data;
                    trailer->BS = 0xd;
                }
                break;
            }
        default:
            break;
    }

    return -1; // 出错了
}

static int
dpi_ParseTrailer_HZ(ST_trailer* trailer, const char* data, unsigned int len)
{
    int  offset = 0;
    int  trailerLen  = 0;

    /**** Nothing TODO (magic code:0x9876) ****/                 offset += 2*sizeof(char);
    trailerLen  = *(char*)(size_t)(data + offset);               offset += 2*sizeof(char);
    if((unsigned int)trailerLen > len)
    {
        return 0;
    }

    while(offset < (int)len)
    {
        unsigned char Etag = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        unsigned char Elen = *(char*)(size_t)(data + offset);       offset += sizeof(char);
        dpi_ParseTrailer_HZ_Etag(trailer, Etag, data+offset, Elen); offset += Elen;
    }

    return 0;

}
static int dpi_ParseTrailer_HWZZ(ST_trailer *trailer, const char *data, unsigned int len) {
    if (len <= 1) {
        return 0;
    }
    int offset = 0;
    int trailerLen = len;
    int trailerFlag = 0;
    for (unsigned int i = 0; i < len - 1; i++) {
        if ((uint8_t)data[i] == 0xff && (uint8_t)data[i + 1] == 0xfe) {
            break;
        }
        offset++;
    }
    if (offset == (int)len - 1) {
        return 0;
    }
    offset += 2; //两字节头 FFFE


    /*trailerLen 长度不包括前三个字节(2个字节标签特征码+1个字节长度指示) */
    while (offset < trailerLen) {
        // uint8_t tag = ntohs(*(const uint16_t *)(((const uint8_t *)data) + offset));
        uint8_t tag = get_uint8_t(data, offset);
        offset += 1;
        // uint16_t len = ntohs(*(const uint16_t *)(((const uint8_t *)data) + offset));
        uint8_t len = get_uint8_t(data, offset);
        offset += 1;
        dpi_ParseTrailer_JSC_tag(trailer, tag, data + offset, len);
        offset += len;
    }
    return 0;
}

/*ADD_S by yangna 2020-09-27 */
/*检查配置文件中trailer标签类型和实际标签类型是否一致 */
static void checkTrailerTypeByMsisdn(int trailerType, uint64_t MSISDN)
{
    /*只在程序启动前10分钟内检测异常手机号码(不是86开头的)是否达到每秒钟10W个 */
    if (g_config.g_now_time - g_config.g_start_time <= 30 && trailerType
        && MSISDN)
    {
        /*判断msisdn是否以86开头 */
        char msisdn[46] = {0};
        snprintf(msisdn, sizeof(msisdn), "%zu", MSISDN);
        msisdn[sizeof(msisdn) - 1] = '\0';
        if (strncmp(msisdn, "86", 2))
        {
            ATOMIC_ADD_FETCH(&err_msisdn_count);
            DPI_LOG(DPI_LOG_DEBUG, "手机号码异常[msisdn=%zu]", MSISDN);
        }

        if (g_config.g_now_time - g_config.g_start_time != 0
            && err_msisdn_count/(g_config.g_now_time - g_config.g_start_time) >= 100000)
        {
            DPI_LOG(DPI_LOG_ERROR, "退出, 手机号解析错误[msisdn=%zu], 请检查配置文件标签类型是否正确!!!", MSISDN);
            exit(-1);
        }
    }

}
/*ADD_E by yangna 2020-09-27 */


int
dpi_TrailerParser(ST_trailer* trailer, const char* trailerData, unsigned int trailerLen, int trailerType)
{
    int Ret = 0;
    if(NULL == trailer || NULL == trailerData || trailerLen < 20)
    {
        trailer->trailerType = TRAILER_NO; // 没有 Trailer
        return -1;
    }
    trailer->trailerType = trailerType;
    switch(trailer->trailerType)
    {
        case TRAILER_RT:
            Ret = dpi_ParseTrailer_RT(trailer, trailerData, trailerLen);
            break;

        case TRAILER_FL:
            Ret = dpi_ParseTrailer_FL(trailer, trailerData, trailerLen);
            break;

        case TRAILER_HW:
            Ret = dpi_ParseTrailer_HW(trailer, trailerData, trailerLen);
            break;

        case TRAILER_JSC:
            Ret = dpi_ParseTrailer_JSC(trailer, trailerData, trailerLen);
            break;

        case TRAILER_HZ:
            Ret = dpi_ParseTrailer_HZ(trailer, trailerData, trailerLen);
            break;

        case TRAILER_ZXSK:
            Ret = dpi_ParseTrailer_ZXSK(trailer, trailerData, trailerLen);
            break;
    case TRAILER_HWZZ:
            Ret =  dpi_ParseTrailer_HWZZ(trailer, (const char *)trailerData, trailerLen);
            break;
        default:
            break;
    }

    checkTrailerTypeByMsisdn(trailer->trailerType, trailer->MSISDN);
    return Ret;
}


static
void dpi_uli_zxsk(char *uli, int uli_len, ST_trailer *trailer)
{
    if (trailer->nci != 0) {
        snprintf(uli, uli_len, "5G:%lx", trailer->nci);
    } else {
        uint32_t uli_num = ntohl(trailer->ULI);
        uint32_t eNodeBid  = uli_num >> 12;
        uint32_t cellId  = (uli_num >> 4) & 0xff;
        snprintf(uli, uli_len, "4G:0x%08x,eNodeBid:%u,CellId:%u", uli_num, eNodeBid, cellId);
    }
}

int dpi_trailer_write_zxsk(char *log, int max_len, void *ptrailer)
{
    int idx = 0;
    char _str[1024] = {0};
    const char *p = NULL;

    ST_trailer *trailer = (ST_trailer *)ptrailer;

    // 遍历所有字段，直到 EM_COMMON_RT_DNS
    for (int i = 0; i <= EM_COMMON_RT_DNS; ++i) {
        switch (i) {
        case EM_COMMON_TEID:
            snprintf(_str, sizeof(_str), "%u", trailer->trailerType);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_TAGTYPE:
            snprintf(_str, sizeof(_str), "0x%08X", ntohl(trailer->TEID));
            p = (trailer->TEID == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_MSISDN:
            if (trailer->MSISDN != 0) {
                snprintf(_str, sizeof(_str), "%lu", trailer->MSISDN);
                write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            } else {
                write_one_str_reconds(log, &idx, max_len, trailer->fixed_account, strlen(trailer->fixed_account));
            }
            break;

        case EM_COMMON_IMSI:
            snprintf(_str, sizeof(_str), "%lu", trailer->IMSI);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_IMEI:
            snprintf(_str, sizeof(_str), "%lu", trailer->IMEI);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_TAC:
            snprintf(_str, sizeof(_str), "0x%06x", trailer->TAC);
            p = (trailer->TAC == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_OPERATOR:
            snprintf(_str, sizeof(_str), "%s", trailer->Operator);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_DEVNAME:
            snprintf(_str, sizeof(_str), "%s", trailer->DevName);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_AREA:
            snprintf(_str, sizeof(_str), "%s", trailer->Area);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_HW_BFLAGS:
            snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer->BFLAG);
            p = (trailer->BFLAG == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_HW_APN:
            snprintf(_str, sizeof(_str), "%s", trailer->APN);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_HW_NCODE:
            snprintf(_str, sizeof(_str), "0x%02X", (unsigned char)trailer->NCODE);
            p = (trailer->NCODE == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_HW_ECGI:
            snprintf(_str, sizeof(_str), "0x%08x", ntohl(trailer->ECGI));
            p = (trailer->ECGI == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_HW_LAC:
            snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->LAC));
            p = (trailer->LAC == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_HW_SAC:
            snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->SAC));
            p = (trailer->SAC == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_HW_CI:
            snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->CI));
            p = (trailer->CI == 0 ? "" : _str);
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_RT_PLMN_ID:
            if (g_config.trailertype == TRAILER_HWZZ) {
                snprintf(_str, sizeof(_str), "%04x", ntohs(trailer->PLMN_ID));
                p = (trailer->PLMN_ID == 0 ? "" : _str);
            } else {
                snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer->PLMN_ID));
                p = (trailer->PLMN_ID == 0 ? "" : _str);
            }
            write_one_str_reconds(log, &idx, max_len, p, strlen(p));
            break;

        case EM_COMMON_RT_ULI:
            dpi_uli_zxsk(_str, sizeof(_str), trailer);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_RT_BS:
            if (trailer->rat != 0) {
                snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer->rat);
                p = (trailer->rat == 0 ? "" : _str);
            } else {
                snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer->BS);
                p = (trailer->BS == 0 ? "" : _str);
            }
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        case EM_COMMON_RT_DNS:
            snprintf(_str, sizeof(_str), "%s", trailer->DomainName);
            write_one_str_reconds(log, &idx, max_len, _str, strlen(_str));
            break;

        default:
            write_n_empty_reconds(log, &idx, max_len, 1);
            break;
        }
    }
    return idx;
}

