/****************************************************************************************
 * 文 件 名 : wxcs.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019-01-03
* 编    码 : zhengsw      '2019-01-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_server.h"
#include "wxcs_config.h"
#include "wxcs_inspector.h"

#include <signal.h>

#include <muduo/net/EventLoop.h>
#include <muduo/net/EventLoopThread.h>

using namespace muduo;
using namespace muduo::net;

#define SERVER_PORT_DEFAULT                 (1024)
#define TBL_DIR_DEFAULT                     "/tmp/tbls"
#define CONFIG_FILE                         "./wxcs.conf"

int main(int argc, char *argv[])
{
    int lSts = 0;

    // env
    CFG->prepareEnv();

    lSts = CFG->ParseConfig(CONFIG_FILE, argc, argv);
    if (lSts < 0                // parse 出错
        || CFG->OnHelpMode())   // help 模式
    {   // -h 选项被打开，help msg 已经由 ParseCmdLineOpts 输出
        return lSts;
    }

    // register signal handler
    signal(SIGINT, WxcsServer::handleSignal);
    /*ADD_S by yangna 2020-08-20 */
    signal(SIGQUIT, WxcsServer::handleSignal);
    signal(SIGTERM, WxcsServer::handleSignal);
    /*ADD_E by yangna 2020-08-20 */

    struct sigaction act;
    act.sa_handler = SIG_IGN;
    act.sa_flags   = 0;
    sigemptyset(&act.sa_mask);
    sigaction(SIGPIPE, &act, NULL);

    // setup logger
    bool bLogToFile = !CFG->logToTerminal() && CFG->GetValueOf<bool>("LOG_TO_FILE", false);
    WxcsLogger::createLogger("logs", bLogToFile);

    // 启动服务器
    EventLoop loop;
    WxcsServer server(&loop,
                      CFG->GetValueOf<int>("SERVER_PORT",       SERVER_PORT_DEFAULT),
                      CFG->GetValueOf<CSTR>("TBL_OUTPUT_DIR",   TBL_DIR_DEFAULT));
    
    // 启动TcpServer及检测超时定时器
    server.start();
    EventLoopThread t;
    WxcsInspector  inspector(t.startLoop(), InetAddress(2048), &server);

    // 主事件循环
    loop.loop();

    LOG_DEF->info("wxcs exiting!");
}
