-- 对 wxf 新增字段进行适配，从 from 到 to 进行适配转换
-- Copyright (c) 2023 YVIEW infra.
-- 更新说明:
-- V0.1: at 2023.05.06
--      1) 完成基本的由 from 到 to 的适配转换;
-- V0.2: by xumh at 2023.05.10
--      1）添加了由fileid解析出的多个字段
-- V0.3: by xumh at 2023.05.15
--      1）优化代码逻辑与可读性
-- V0.4：by xumh at 2023.05.26、
--      1）增加了对输入的容错梳理；优化了代码逻辑


local b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

function base64_encode(data)
	if data == "" then
        return ""
	end
	
    return ((data:gsub('.', function(x)
        local r, b ='', x:byte()
        for i = 8, 1, -1 do r = r .. (b % 2 ^ i - b % 2 ^ (i - 1) > 0 and '1' or '0') end
        return r;
    end)..'0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then return '' end
        local c = 0
        for i = 1, 6 do c = c + (x:sub(i, i) == '1' and 2 ^ (6 - i) or 0) end
        return b:sub(c + 1, c + 1)
    end)..({ '', '==', '=' })[#data % 3 + 1])
end


function hex_to_str(hex)                          	   -- 将形如"32303334"的字符串转换为"2034"
	local str = ""
	if hex == "00" then
        return str
    end
	if hex ~= nil and #hex ~= 0 then
		for i = 1, #hex, 2 do
			local hex_part   = hex:sub(i, i+1)         -- 每次取出两个字符
			local ascii_part = tonumber(hex_part, 16)  -- 转换为 16 进制数字
			local char_part  = string.char(ascii_part) -- 转换为对应的 ASCII 字符
			str = str..char_part                  	   -- 将转换后的字符拼接到新的字符串中
		end
	end
	return str
end

function hex_to_ip(hex)                                -- 小端序形式的"a0b1c2d3"的字符串转换为***************
	local ip = ""
	if hex == "00" then
        return ip
    end
	if hex ~= nil and #hex ~= 0 then
		local ip_parts = {}
		for i = 1, #hex, 2 do
			local hex_part     = hex:sub(i, i + 1)     -- 每次取出两个字符
			local dec_part = tonumber(hex_part, 16)    -- 转换为数字
			table.insert(ip_parts, 1, dec_part)        -- 每次在表的开头插入值
		end
		ip = table.concat(ip_parts, ".")
	end
	return ip
end

function decodeTLV(input)                         	   -- tlv格式解析
	local i = 1
	local output = {}
	while i <= #input do
		local t = tonumber(input:sub(i, i + 1), 16)
		i = i + 2
		local len_high = tonumber(input:sub(i, i+1), 16)
		local len_low  = nil
		local l = nil

		if len_high < 0x80 then                    	   -- 小于0x80，则是单字节的长度
			l = len_high
			i = i + 2
		elseif len_high >= 0x80 then               	   -- 大于等于0x80，则是双字节的长度
			len_low = tonumber(input:sub(i+2, i+3), 16)
			l = (len_high - 0x80) * 0x80 + (len_low - 0x80)
			i = i + 4
		end

		local v = ""
		if l > 0 then
			v = input:sub(i, i + l * 2 - 1)
			i = i + l * 2
		end

		table.insert(output, {t = t, l = l, v = v})
	end
	return output
end

function fileid_param(fileid)                          -- 解析fileid，提取字段
	if fileid == nil or #fileid == 0 then              -- 输入为空值时输出空值
		f_wxnum          = ""
		f_originfiletype = ""
		f_srvIP          = ""
		f_timestamp      = ""
		f_filekey        = ""
		return
	end

	local topLevelTLV    = decodeTLV(fileid)           -- tlv嵌套结构解析
	local nestedTLV1     = decodeTLV(topLevelTLV[1].v)
	local nestedTLV1_2   = decodeTLV(nestedTLV1[2].v)
	local nestedTLV1_2_1 = decodeTLV(nestedTLV1_2[1].v)

	local temp_wxnum          = nestedTLV1_2_1[2].v
	local temp_srvIP          = nestedTLV1_2_1[4].v
	local temp_timestamp      = nestedTLV1_2_1[5].v
	local temp_filekey        = nestedTLV1_2_1[6].v
	local temp_originfiletype = nestedTLV1_2_1[#nestedTLV1_2_1-1].v
	
	local hasNoValue = {[0] = true, [1] = true}
	if hasNoValue[nestedTLV1_2_1[2].l] then
		temp_wxnum = ""
	else
		while #temp_wxnum < 8 do                     -- 小于4字节长度的wxnum需要在最高位前添加"f"，直至长度为4字节
			temp_wxnum = "f" .. temp_wxnum
		end
		temp_wxnum  = tonumber(temp_wxnum, 16)
		temp_wxnum  = tostring(temp_wxnum)
	end
	
	if hasNoValue[nestedTLV1_2_1[5].l] then
		temp_timestamp = ""
	else
		temp_timestamp = tonumber(temp_timestamp, 16)
		temp_timestamp = os.date("%Y-%m-%d %H:%M:%S", temp_timestamp)
	end
	
	if nestedTLV1_2_1[7].l > 0 then
		f_originfiletype = "0x" .. temp_originfiletype .. ";" .. "0x0" .. nestedTLV1[3].t .. "0" .. nestedTLV1[3].l     --单字节长度的fileid中，包含4字节的originfiletype，并且和最后的0x0400或0x0405一同输出
	else
		f_originfiletype = ";" .. "0x0" .. nestedTLV1[3].t .. "0" .. nestedTLV1[3].l
	end
	
	f_wxnum          = base64_encode(temp_wxnum)
	f_srvIP          = hex_to_ip(temp_srvIP)
	f_timestamp      = temp_timestamp
    f_filekey        = hex_to_str(temp_filekey)
	f_originfiletype = temp_filetype
	
end



function adapt_weixin(to, from)
    precord.clone(to, from)
	to["fileid_1"] = ""
	to["fileid_2"] = ""
	to["fileid_3"] = ""
	to["fileid_4"] = ""
	to["resv_2"]   = ""
	to["resv_3"]   = ""
	to["resv_4"]   = ""
	to["resv_5"]   = ""
	to["resv_6"]   = ""
	
	fileid_param(from["fileid"]:as_string())
	to["f_wxnum"] = f_wxnum
	to["f_srvIP"] = f_srvIP
    to["f_timestamp"] = f_timestamp
    to["f_filekey"] = f_filekey
    to["f_originfiletype"] = f_originfiletype

end

-- 注册 adapt_weixin 协议适配器, 将 weixin 协议适配到 weixin 协议
yalua_register("weixin", "weixin", adapt_weixin)