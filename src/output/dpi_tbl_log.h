/****************************************************************************************
 * 文 件 名 : dpi_tbl_log.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TBL_LOG_H_
#define _DPI_TBL_LOG_H_

#include "dpi_detect.h"

#define TBL_RING_MAX_NUM            4
#define TBL_LOG_MAX_LEN             (2048 * 10)
#define TBL_LOG_CONTENT_4K          (1024 * 4)
#define TBL_LOG_CONTENT_4K_NUM      (4096 * 16)
#define TBL_LOG_CONTENT_256K        (1024 * 256)
#define TBL_LOG_CONTENT_256K_NUM    4096

#define TBL_FIELDS_MAX_LEN (2048 * 1)

#define KV53_KV99_LEN ((99 - 53 + 1) * (2))


#define TBL_PATH_LENGTH    256

extern const char *empty_data;

typedef struct ProtoRecord        precord_t;
struct tbl_log {
    int         thread_id;
    int         log_type;
    int         content_len;
	int         type;
	int         len;
    int         tid;
    uint8_t * content_ptr;
	char        log_content[TBL_LOG_MAX_LEN];
};
typedef int (*field_callback)(char *, int *, int , const char, unsigned int );

typedef struct _dpi_field_table_t {
	uint16_t        index;
	uint8_t         type;
	const char      *field_name;
}dpi_field_table;


typedef enum _data_type{
	EM_F_TYPE_EMPTY,
	EM_F_TYPE_UINT8,
	EM_F_TYPE_UINT16,
	EM_F_TYPE_UINT32,
	EM_F_TYPE_UINT64,
	EM_F_TYPE_STRING,
	EM_F_TYPE_HEX,
	EM_F_TYPE_NULL,
  EM_F_TYPE_BYTES,
	EM_F_TYPE_MAX
}data_type;


typedef enum _dpi_field_common_em{
    EM_COMMON_TEID,          /* 公共部分 */
    EM_COMMON_TAGTYPE,
    EM_COMMON_MSISDN,
    EM_COMMON_IMSI,
    EM_COMMON_IMEI,
    EM_COMMON_TAC,
    EM_COMMON_OPERATOR,
    EM_COMMON_DEVNAME,
    EM_COMMON_AREA,
    EM_COMMON_HW_BFLAGS,     /* 恒为独有 */
    EM_COMMON_HW_APN,
    EM_COMMON_HW_NCODE,
    EM_COMMON_HW_ECGI,
    EM_COMMON_HW_LAC,
    EM_COMMON_HW_SAC,
    EM_COMMON_HW_CI,
    EM_COMMON_RT_PLMN_ID,    /* 戎腾独有 */
    EM_COMMON_RT_ULI,
    EM_COMMON_RT_BS,
    EM_COMMON_RT_DNS,
    // EM_COMMON_FIX_ACCOUNT,
    // EM_COMMON_DIRECTION,
    // EM_COMMON_LABEL,
    // EM_COMMON_RULEID,

    EM_COMMON_DEVNO,
    EM_COMMON_LINENO,
    EM_COMMON_LINKLAYERTYPE,
    EM_COMMON_ISIPV6,
    EM_COMMON_ISMPLS,
    EM_COMMON_NLABEL,
    EM_COMMON_INNERLABEL,
    EM_COMMON_OTHERLABEL,
    EM_COMMON_RESV1,
    EM_COMMON_RESV2,
    EM_COMMON_RESV3,
    EM_COMMON_RESV4,
    EM_COMMON_RESV5,
    EM_COMMON_RESV6,
    EM_COMMON_RESV7,
    EM_COMMON_RESV8,
    EM_COMMON_CAPDATE,
    EM_COMMON_SRCIP,
    EM_COMMON_SRCCOUNTRY,
    EM_COMMON_SRCAREA,
    EM_COMMON_SRCCITY,
    EM_COMMON_SRCCARRIER,
    EM_COMMON_DSTIP,
    EM_COMMON_DSTCOUNTRY,
    EM_COMMON_DSTAREA,
    EM_COMMON_DSTCITY,
    EM_COMMON_DSTCARRIER,
    EM_COMMON_SRCPORT,
    EM_COMMON_DSTPORT,
    EM_COMMON_C2S,
    EM_COMMON_PROTO,
    EM_COMMON_TTL,
	EM_COMMON_IP_FLAG,
	EM_COMMON_MAX
}dpi_field_common_em;

#define DPI_FIELD_D(index, type, field_name)  {(index), (type), (field_name)}

extern dpi_field_table dpi_common_field[];
void register_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func );
int get_ip4string(char *__str, int len, uint32_t ip);
int get_ip6string(char *__str, int len, const uint8_t *ip);
int get_iparray_to_string(char *__str, int len,const uint8_t* ip);
int get_now_datetime(char *time_str, int len);
int detect_file_type(uint32_t header, char *buff); // add by liugh
int write_rtl_reconds(char *log, int *idx, int max_len, struct flow_info *flow);
int write_n_empty_reconds(char *log, int *idx, int max_len, int n);
int write_one_str_reconds(char *log, int *idx, int max_len, const char *data, unsigned int data_len);
int write_one_num_reconds(char *log, int *idx, int max_len, uint32_t data);
int write_uint64_reconds(char *log, int *idx, int max_len, uint64_t data);
int write_multi_num_reconds(char *log, int *idx, int max_len, const uint8_t *data,uint32_t data_len);
int write_coupler_log(char *log, int *idx, int max_len, uint8_t data_type,const uint8_t *data,uint64_t int_data);// add by liugh
void write_tbl_log_common(struct flow_info *flow, int direction, char *log_content, int *idx, int log_len_max);
int write_proto_field_tab(dpi_field_table *proto_array,int max_len,const char *proto_name);
int tbl_log_enqueue(struct tbl_log *log);
int write_tbl_log(struct tbl_log *log);
void *write_tbl_log_to_file_func(void * arg);
int init_tbl_log(void);

void mkdirs(const char *dir);

int get_filename(char *path_name, char *name);


int write_proto_field_without_common(dpi_field_table *proto_array,
                                     int max_len,
                                     const char *proto_name);

#endif
