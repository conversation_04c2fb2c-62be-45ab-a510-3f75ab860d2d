/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_CONF_H
#define WX_PROXY_CONF_H

struct conf
{
    unsigned short           listen;
    struct sockaddr          slave_1;
    struct sockaddr          slave_2;
    int                      log_level;
    int                      log_size;
    int                      log_num;
    char                     log_filepath[1024];
    int                      thread_num;
};


#ifdef __cplusplus
extern "C" {
#endif

struct conf*
proxy_conf_parse(const char *file, struct conf* conf);

void
proxy_conf_release(struct conf* conf);

void
proxy_conf_dump(struct conf* conf);

#ifdef __cplusplus
}
#endif

#endif

