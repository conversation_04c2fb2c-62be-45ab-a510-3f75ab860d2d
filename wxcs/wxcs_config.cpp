/****************************************************************************************
 * 文 件 名 : wxcs_config.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_config.h"
#include "wxcs_utils.h"
#include "version.h"

#include <iniparser/iniparser.h>

#include <string>

#include <stdlib.h>
#include <getopt.h>

#define APP_NAME "wxcs"
#include "wxcs_logger.h"

WxcsConfig::WxcsConfig()
    : ini_(NULL)
    , bHelpMode_(false)
    , bLogToTerminal_(false)
{
}

WxcsConfig::~WxcsConfig()
{
    if (NULL != ini_)
    {
        iniparser_freedict(ini_);
        ini_ = NULL;
    }
}

WxcsConfig *WxcsConfig::GetInstance()
{
    static WxcsConfig s_config;
    return &s_config;
}

int WxcsConfig::ParseConfigFile(const std::string &strConfigFilePath)
{
    // 如果是 "./" 打头的需要进行调整为从程序所在文件开始
    std::string configFilePath = strConfigFilePath;
    if (0 == configFilePath.compare(0, 2, "./"))
    {
        //configFilePath = getAppDir() + configFilePath;
        configFilePath = configFilePath;
    }

    const char    *ini_name  = configFilePath.c_str();

    ini_ = iniparser_load(ini_name);
    if (ini_ == NULL)
    {
        return -1 ;
    }

    const char *p = NULL;
    // 读取 MSISDN 到 MSISDN 名单
    p = CFG->GetValueOf<CSTR>("INTERESTING_MSISDN");
    if (NULL != p && strcmp(p, "") != 0)
    {
        do
        {
            printf("MSISDN:%zd\n", atol(p));
            vecInterestingMsisdn_.push_back(atol(p));
            while(*p && *p++ != ',');
        }
        while(*p);
    }

    // 读取 IMSI 到 IMSI 名单
    p = CFG->GetValueOf<CSTR>("INTERESTING_IMSI");
    if (NULL != p &&  strcmp(p, "") != 0)
    {
        do
        {
            printf("IMSI  :%zd\n", atol(p));
            vecInterestingImsi_.push_back(atol(p));
            while(*p && *p++ != ',');
        }
        while(*p);
    }

    // 读取 IMEI 到 IMEI 名单
    p = CFG->GetValueOf<CSTR>("INTERESTING_IMEI");
    if (NULL != p && strcmp(p, "") != 0)
    {
        do
        {
            printf("IMEI  :%zd\n", atol(p));
            vecInterestingImei_.push_back(atol(p));
            while(*p && *p++ != ',');
        }
        while(*p);
    }


    return 0;
}

int WxcsConfig:: ParseConfig(const char * configFile, int argc, char *argv[])
{
    int lSts = 0;

    // 解析配置文件
    lSts = ParseConfigFile(configFile);
    CHECK_NOR_EXIT(lSts < 0, 0, "parse config file %s error.\n", configFile);

    // 解析命令行选项与参数
    lSts = ParseCmdLineOpts(argc, argv);
    CHECK_NOR_EXIT(lSts < 0, lSts, "parse cmdline options error.\n");

    // 是否 help mode
    if (OnHelpMode())
    {
        return lSts;
    }

    return 0;
 }

int WxcsConfig:: ParseCmdLineOpts(int argc, char *argv[])
{
#   define OPT_STRING  ":htv"
#   define HELP_STRING " [-ht] \n"                                 \
                       " -t : log to terminal\n"                   \
                       " -h : show this help info\n"               \
                       " -v : show version."                       \

    static const char          optstring[]    = OPT_STRING;
    static const struct option long_options[] =
      {
          {"help",            no_argument, NULL, 'h'},
          {"log-to-terminal", no_argument, NULL, 't'},
          {"version",         no_argument, NULL, 'v'},
          {0, 0, 0, 0 }
      };

    int opt                                   = 0;
    const char *appName                       = argv[0];

    // 解析命令行选项与参数
    while ((opt = getopt_long(argc, argv, optstring, long_options, NULL)) != -1)
    {
        switch (opt)
        {
        case 'h':
        {
            bHelpMode_ = true;
            printf("%s %s\n", appName, HELP_STRING);
            return 0;
        }
        case 'v':
        {
            bHelpMode_ = true;
            printf("%s %s\n", appName, version);
            return 0;
        }
        case 't':
        {
            bLogToTerminal_ = true;
            break;
        }
        case '?':
            printf("invalid option : %s\n", optarg);
            break;
        case ':':
        default:
            break;
        }
    }

    // 调整后再没有选项，只有 oprand (文件名，路径之类)
    argc = argc - (optind -1);
    argv = &argv[optind - 1];

    return 0;
}

const std::string WxcsConfig::toGlobalConfigName(const std::string &strConfigName)
{
        return ":" + strConfigName;
}

template <>
uint32_t WxcsConfig::GetValueOf_inner(const std::string &strConfigName, uint32_t def)
{
    return iniparser_getint(ini_, strConfigName.c_str(), def);
}

template <>
int WxcsConfig::GetValueOf_inner(const std::string &strConfigName, int def)
{
    return iniparser_getint(ini_, strConfigName.c_str(), def);
}

template <>
long WxcsConfig::GetValueOf_inner(const std::string &strConfigName, long def)
{
    return iniparser_getlongint(ini_, strConfigName.c_str(), def);
}

template <>
CSTR WxcsConfig::GetValueOf_inner(const std::string &strConfigName, CSTR def)
{
    return iniparser_getstring(ini_, strConfigName.c_str(), def);
}

template <>
bool WxcsConfig::GetValueOf_inner(const std::string &strConfigName, bool b)
{
    return iniparser_getboolean(ini_, strConfigName.c_str(), b) == 1;
}

int WxcsConfig::prepareEnv()
{
    // chdir to the dir contains exe file.
    chdir(getAppDir().c_str());

    return 0;
}
