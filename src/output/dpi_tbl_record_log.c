
/****************************************************************************************
 * 文 件 名 : dpi_tbl_log.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_ring.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <yaFtypes/ftypes.h>

#include "dpi_proto_ids.h"  // add by liugh
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_protorecord.h"
#include "dpi_precord_writer_IF.h"
#include "dpi_utils.h"
#include "wxcs_def.h"
#include "dpi_tbl_record_log.h"

#define PSCHEMA_COMMON "pschema_common"
#define gettid() ((pid_t)syscall(SYS_gettid))

#define TBL_RING_SIZE (4096 * 16)
#define TBL_MAX_BURST 512
#define TBL_FILE_TIMEOUT 30
extern struct global_config g_config;
/*record tbl 线程池*/
extern struct rte_mempool *tbl_log_record_mempool;
extern struct rte_mempool *tbl_log_content_record_mempool_256k;
extern struct rte_mempool *tbl_log_content_record_mempool_4k;
extern rte_atomic64_t tbl_fail_pkts;

static struct rte_ring *tbl_ring[TBL_RING_MAX_NUM];
extern uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];


dpi_record_field_table dpi_common_field_[] = {
    DPI_FIELD_D(EM_COMMON_TAGTYPE, EM_F_TYPE_STRING, "TAGTYPE"),
    DPI_FIELD_D(EM_COMMON_TEID, EM_F_TYPE_STRING, "TEID"),
    DPI_FIELD_D(EM_COMMON_MSISDN, EM_F_TYPE_STRING, "MSISDN"),
    DPI_FIELD_D(EM_COMMON_IMSI, EM_F_TYPE_STRING, "IMSI"),
    DPI_FIELD_D(EM_COMMON_IMEI, EM_F_TYPE_STRING, "IMEI"),
    DPI_FIELD_D(EM_COMMON_TAC, EM_F_TYPE_STRING, "TAC"),
    DPI_FIELD_D(EM_COMMON_OPERATOR, EM_F_TYPE_STRING, "OPERATOR"),
    DPI_FIELD_D(EM_COMMON_DEVNAME, EM_F_TYPE_STRING, "DEVNAME"),
    DPI_FIELD_D(EM_COMMON_AREA, EM_F_TYPE_STRING, "AREA"),
    DPI_FIELD_D(EM_COMMON_HW_BFLAGS, EM_F_TYPE_STRING, "HW_BFALGS"),
    DPI_FIELD_D(EM_COMMON_HW_APN, EM_F_TYPE_STRING, "HW_APN"),
    DPI_FIELD_D(EM_COMMON_HW_NCODE, EM_F_TYPE_STRING, "HW_NCODE"),
    DPI_FIELD_D(EM_COMMON_HW_ECGI, EM_F_TYPE_STRING, "HW_ECGI"),
    DPI_FIELD_D(EM_COMMON_HW_LAC, EM_F_TYPE_STRING, "HW_LAC"),
    DPI_FIELD_D(EM_COMMON_HW_SAC, EM_F_TYPE_STRING, "HW_SAC"),
    DPI_FIELD_D(EM_COMMON_HW_CI, EM_F_TYPE_STRING, "HW_CI"),
    DPI_FIELD_D(EM_COMMON_RT_PLMN_ID, EM_F_TYPE_STRING, "RT_PLMN_ID"),
    DPI_FIELD_D(EM_COMMON_RT_ULI, EM_F_TYPE_STRING, "RT_ULI"),
    DPI_FIELD_D(EM_COMMON_RT_BS, EM_F_TYPE_STRING, "RT_BS"),
    DPI_FIELD_D(EM_COMMON_RT_DNS, EM_F_TYPE_STRING, "RT_DNS"),
    // DPI_FIELD_D(EM_COMMON_RT_DNS, EM_F_TYPE_STRING, "FIX_ACCOUNT"),
    // DPI_FIELD_D(EM_COMMON_RT_DNS, EM_F_TYPE_STRING, "DIRECTION"),
    // DPI_FIELD_D(EM_COMMON_RT_DNS, EM_F_TYPE_STRING, "LABEL"),
    // DPI_FIELD_D(EM_COMMON_RT_DNS, EM_F_TYPE_STRING, "RULEID"),
    DPI_FIELD_D(EM_COMMON_DEVNO, EM_F_TYPE_STRING, "DevNo"),
    DPI_FIELD_D(EM_COMMON_LINENO, EM_F_TYPE_EMPTY, "LineNo"),
    DPI_FIELD_D(EM_COMMON_LINKLAYERTYPE, EM_F_TYPE_STRING, "LinkLayerType"),
    DPI_FIELD_D(EM_COMMON_ISIPV6, EM_F_TYPE_UINT8, "isIPv6"),
    DPI_FIELD_D(EM_COMMON_ISMPLS, EM_F_TYPE_UINT8, "isMPLS"),
    DPI_FIELD_D(EM_COMMON_NLABEL, EM_F_TYPE_EMPTY, "nLabel"),
    DPI_FIELD_D(EM_COMMON_INNERLABEL, EM_F_TYPE_EMPTY, "innerLabel"),
    DPI_FIELD_D(EM_COMMON_OTHERLABEL, EM_F_TYPE_EMPTY, "otherLabel"),
    DPI_FIELD_D(EM_COMMON_RESV1, EM_F_TYPE_EMPTY, "resv1"),
    DPI_FIELD_D(EM_COMMON_RESV2, EM_F_TYPE_EMPTY, "resv2"),
    DPI_FIELD_D(EM_COMMON_RESV3, EM_F_TYPE_EMPTY, "resv3"),
    DPI_FIELD_D(EM_COMMON_RESV4, EM_F_TYPE_EMPTY, "resv4"),
    DPI_FIELD_D(EM_COMMON_RESV5, EM_F_TYPE_EMPTY, "resv5"),
    DPI_FIELD_D(EM_COMMON_RESV6, EM_F_TYPE_EMPTY, "resv6"),
    DPI_FIELD_D(EM_COMMON_RESV7, EM_F_TYPE_EMPTY, "resv7"),
    DPI_FIELD_D(EM_COMMON_RESV8, EM_F_TYPE_EMPTY, "resv8"),
    DPI_FIELD_D(EM_COMMON_CAPDATE, EM_F_TYPE_STRING, "CapDate"),
    DPI_FIELD_D(EM_COMMON_SRCIP, EM_F_TYPE_STRING, "SrcIp"),
    DPI_FIELD_D(EM_COMMON_SRCCOUNTRY, EM_F_TYPE_EMPTY, "SrcCountry"),
    DPI_FIELD_D(EM_COMMON_SRCAREA, EM_F_TYPE_EMPTY, "SrcArea"),
    DPI_FIELD_D(EM_COMMON_SRCCITY, EM_F_TYPE_EMPTY, "SrcCity"),
    DPI_FIELD_D(EM_COMMON_SRCCARRIER, EM_F_TYPE_EMPTY, "SrcCarrier"),
    DPI_FIELD_D(EM_COMMON_DSTIP, EM_F_TYPE_STRING, "DstIp"),
    DPI_FIELD_D(EM_COMMON_DSTCOUNTRY, EM_F_TYPE_EMPTY, "DstCountry"),
    DPI_FIELD_D(EM_COMMON_DSTAREA, EM_F_TYPE_EMPTY, "DstArea"),
    DPI_FIELD_D(EM_COMMON_DSTCITY, EM_F_TYPE_EMPTY, "DstCity"),
    DPI_FIELD_D(EM_COMMON_DSTCARRIER, EM_F_TYPE_EMPTY, "DstCarrier"),
    DPI_FIELD_D(EM_COMMON_SRCPORT, EM_F_TYPE_UINT16, "SrcPort"),
    DPI_FIELD_D(EM_COMMON_DSTPORT, EM_F_TYPE_UINT16, "DstPort"),
    DPI_FIELD_D(EM_COMMON_C2S, EM_F_TYPE_STRING, "C2S"),
    DPI_FIELD_D(EM_COMMON_PROTO, EM_F_TYPE_UINT8, "Proto"),
    DPI_FIELD_D(EM_COMMON_TTL, EM_F_TYPE_UINT8, "TTL"),
    DPI_FIELD_D(EM_COMMON_IP_FLAG, EM_F_TYPE_STRING, "IPFlag"),
};

int dpi_pschema_get_common_field(dpi_record_field_table *field_table_array[]) {
  *field_table_array = dpi_common_field_;
  return ARRAY_LEN(dpi_common_field_);
}

/*
*rt标签的解析
*/
static int write_rtl_reconds_by_record(struct flow_info *flow, precord_t *record) {
  int i;
  int idx = 0;
  uint64_t res = 0;
  char _str[1024] = {0};
  char ULI[512] = {0};
  const char *p = NULL;
  ya_fvalue_t *value = NULL;
  ST_trailer trailer;
  memset(&trailer, 0, sizeof(trailer));
  dpi_TrailerParser(&trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype);
  dpi_TrailerGetMAC(&trailer, (const char *)flow->ethhdr, g_config.RT_model);  // 解析戎腾MAC
  dpi_TrailerGetHWZZMAC(&trailer, (const char *)flow->ethhdr);
  dpi_TrailerSetDev(&trailer, g_config.devname);                               // 解析板号
  dpi_TrailerSetOpt(&trailer, g_config.operator_name);                         // 运营商
  dpi_TrailerSetArea(&trailer, g_config.devArea);                              // 地域名
  dpi_TrailerECI(&trailer, ULI, sizeof(ULI));                                  // ECI 转换

  snprintf(_str, sizeof(_str), "%u", trailer.trailerType);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%08X", ntohl(trailer.TEID));  // 大端显示
  p = (0 == trailer.TEID ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  	if (trailer.MSISDN != 0) {
		snprintf(_str, sizeof(_str), "%lu", trailer.MSISDN);
    value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
	} else {
    value = ya_fvalue_new_stringn(YA_FT_STRING, trailer.fixed_account, strlen(trailer.fixed_account));
	}
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%lu", trailer.IMSI);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%lu", trailer.IMEI);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  if (trailer.trailerType == TRAILER_ZXSK && trailer.nci != 0) {
    snprintf(_str, sizeof(_str), "0x%06x", trailer.TAC);  // 大端显示
  } else {
    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.TAC));  // 大端显示
  }
  p = (0 == trailer.TAC ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", trailer.Operator);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", trailer.DevName);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", trailer.Area);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.BFLAG);
  p = (0 == trailer.BFLAG ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", trailer.APN);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%02X", (unsigned char)trailer.NCODE);
  p = (0 == trailer.NCODE ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%08x", ntohl(trailer.ECGI));  // 大端显示
  p = (0 == trailer.ECGI ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.LAC));  // 大端显示
  p = (0 == trailer.LAC ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.SAC));  // 大端显示
  p = (0 == trailer.SAC ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.CI));  // 大端显示
  p = (0 == trailer.CI ? "" : _str);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  if(g_config.trailertype == TRAILER_HWZZ){
  snprintf(_str, sizeof(_str), "%04x", ntohs(trailer.PLMN_ID));  //大端显示
    p = (0 == trailer.PLMN_ID ? "" : _str);
  }else {
    snprintf(_str, sizeof(_str), "0x%04x", ntohs(trailer.PLMN_ID));  //大端显示
    p = (0 == trailer.PLMN_ID ? "" : _str);
  }
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)p, strlen(p));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", ULI);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  if (trailer.rat != 0 ) {
    snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.rat);
    p = (0==trailer.rat?"":_str);
	} else {
    snprintf(_str, sizeof(_str), "0x%02x", (unsigned char)trailer.BS);
    p = (0==trailer.BS?"":_str);
	}
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  snprintf(_str, sizeof(_str), "%s", trailer.DomainName);
  value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  // snprintf(_str, sizeof(_str), "%s", trailer.fixed_account);
  // value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  // dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  // snprintf(_str, sizeof(_str), "%d", trailer.direction);
  // value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  // dpi_precord_fvalue_put_by_index_increment(record, &idx, value);
  // snprintf(_str, sizeof(_str), "%s", trailer.label);
  // value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  // dpi_precord_fvalue_put_by_index_increment(record, &idx, value);
  // snprintf(_str, sizeof(_str), "%s", trailer.ruleID);
  // value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)_str, strlen(_str));
  // dpi_precord_fvalue_put_by_index_increment(record, &idx, value);

  return 0;
}

/*将flow中公共字段写入record*/
void write_tbl_log_common_by_record(struct flow_info *flow, int direction, precord_t *record) {
  char __str[64] = {0};
  int i;

  ya_fvalue_t *value = NULL;
  precord_layer_move_cursor(record, "common");
  write_rtl_reconds_by_record(flow, record);

  for (i = EM_COMMON_DEVNO; i < EM_COMMON_MAX; i++) {
    switch (dpi_common_field[i].index) {
      case EM_COMMON_LINKLAYERTYPE:
        value = ya_fvalue_new_stringn(YA_FT_STRING, g_config.devname, strlen(g_config.devname));
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_ISIPV6:
        if (flow->ip_version == 4) {
          value = ya_fvalue_new_uinteger(YA_FT_UINT8, 0);
          dpi_precord_fvalue_put_by_index(record, i, value);
        } else {
          value = ya_fvalue_new_uinteger(YA_FT_UINT8, 1);
          dpi_precord_fvalue_put_by_index(record, i, value);
        }
        break;
      case EM_COMMON_CAPDATE:
        timet_to_datetime(flow->pkt_rx_timestamp_first/1000000, __str, sizeof(__str));
        value = ya_fvalue_new_stringn(YA_FT_STRING, __str, strlen(__str));
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_SRCIP:
        if (direction == FLOW_DIR_SRC2DST) {
          if (flow->ip_version == 4)
            get_ip4string(__str, sizeof(__str), flow->tuple.inner.ip_src.ip4);
          else
            get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_src.ip6);
        } else {
          if (flow->ip_version == 4)
            get_ip4string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src.ip4);
          else
            get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src.ip6);
        }
        value = ya_fvalue_new_stringn(YA_FT_STRING, __str, strlen(__str));
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_DSTIP:
        if (direction == FLOW_DIR_SRC2DST) {
          if (flow->ip_version == 4)
            get_ip4string(__str, sizeof(__str), flow->tuple.inner.ip_dst.ip4);
          else
            get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst.ip6);
        } else {
          if (flow->ip_version == 4)
            get_ip4string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst.ip4);
          else
            get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst.ip6);
        }
        value = ya_fvalue_new_stringn(YA_FT_STRING, __str, strlen(__str));
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_SRCPORT:
        if (direction == FLOW_DIR_SRC2DST) {
          value = ya_fvalue_new_uinteger(YA_FT_UINT32, ntohs(flow->tuple.inner.port_src));
          dpi_precord_fvalue_put_by_index(record, i, value);
        } else {
          value = ya_fvalue_new_uinteger(YA_FT_UINT32, ntohs(flow->tuple_reverse.inner.port_src));
          dpi_precord_fvalue_put_by_index(record, i, value);
        }
        break;
      case EM_COMMON_DSTPORT:
        if (direction == FLOW_DIR_SRC2DST) {
          value = ya_fvalue_new_uinteger(YA_FT_UINT32, ntohs(flow->tuple.inner.port_dst));
          dpi_precord_fvalue_put_by_index(record, i, value);
        } else {
          value = ya_fvalue_new_uinteger(YA_FT_UINT32, ntohs(flow->tuple_reverse.inner.port_dst));
          dpi_precord_fvalue_put_by_index(record, i, value);
        }
        break;
      case EM_COMMON_C2S:
        if (direction == flow->direction) {
          value = ya_fvalue_new_stringn(YA_FT_STRING, "C2S", strlen("C2S"));
          dpi_precord_fvalue_put_by_index(record, i, value);
        }

        else {
          value = ya_fvalue_new_stringn(YA_FT_STRING, "S2C", strlen("S2C"));
          dpi_precord_fvalue_put_by_index(record, i, value);
        }
        break;
      case EM_COMMON_PROTO:
        value = ya_fvalue_new_uinteger(YA_FT_UINT32, flow->tuple.inner.proto);
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_TTL:

        value = ya_fvalue_new_uinteger(YA_FT_UINT32, flow->ttl);
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      case EM_COMMON_IP_FLAG:
        value = ya_fvalue_new_stringn(YA_FT_STRING, (char *)flow->ip_flag, strlen((char *)flow->ip_flag));
        dpi_precord_fvalue_put_by_index(record, i, value);
        break;
      default:
        break;
    }
  }
}

/*
*解析线程写日志的调用接口
*/
int tbl_record_log_enqueue(struct tbl_log_record *log) {
  int enqueue_num;
  uint8_t ring_id = 0;
  if (g_config.dissector_thread_num > g_config.log_thread_num) {
    ring_id = log->thread_id % g_config.log_thread_num;
  } else {
    ring_id = log->thread_id;
  }
  enqueue_num = rte_ring_mp_enqueue_burst(tbl_ring[ring_id], (void *const *)&log, 1, NULL);
  if (enqueue_num < 1) {
    rte_atomic64_inc(&tbl_fail_pkts);
    DPI_LOG(DPI_LOG_ERROR, "enqueue packet record_ring error, log->thread_id %d tbl ring %d",log->thread_id , ring_id);
    return -1;
  }

  return 1;
}

/*
*日志线程的处理函数
*/
void *write_tbl_log_to_file_func_(void *arg) {
  long write_thread_index = (long)arg;
  struct tbl_log_record *tbl_burst[TBL_MAX_BURST];
  int core_affinity = g_config.log_core_id[write_thread_index];
  cpu_set_t cpuset;
  padapt_engine_t *adapt_engine       = NULL;
  int              ret                = 0;
  precord_t       *adapt_record       = NULL;

  CPU_ZERO(&cpuset);
  CPU_SET(core_affinity, &cpuset);

  if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0)
    DPI_LOG(DPI_LOG_WARNING, "Error while binding record log thread to core %d", core_affinity);

  DPI_LOG(DPI_LOG_DEBUG, "Running log record thread on core %d", core_affinity);

  /* 对 output writer 进行配置 */
  char *ip_addr = dpi_utils_get_ipv4_addr_of_if(g_config.ip_interface);
  dpi_output_set_writer_property(write_thread_index, "dev_ip", ip_addr);
  dpi_output_set_writer_property(write_thread_index, "dev_name", NULL);
  dpi_output_set_writer_property_number(write_thread_index, "thread_id", write_thread_index);

  dpi_output_set_writer_record_count_per_file(write_thread_index, g_config.log_max_num);

  time_t timer, now, time_cycle;
  time(&timer);
  adapt_engine = dpi_padapt_engine_create();

  // dpi_padapt_print_all_schema();

  while (1) {
    time(&now);
    time_cycle = now - timer;

    /* tbl_ring 还未初始化 */
    if (tbl_ring[write_thread_index] == NULL) {
      continue;
    }

    /* output 线程该退出了 */
    if (g_config.stop_write_tbl && rte_ring_count(tbl_ring[write_thread_index]) == 0) {
      dpi_output_on_process_going_to_terminate(write_thread_index);
      pthread_exit(0);
    }

    /* 将 precord 出队列进行消费 */
    int dequeue_num = rte_ring_sc_dequeue_burst(tbl_ring[write_thread_index], (void **)tbl_burst, TBL_MAX_BURST, NULL);

    /* 没有需要消费的 precord, 执行 idle check */
    if (dequeue_num == 0) {
      dpi_output_do_idle_check(write_thread_index, now);
      usleep(100);
      continue;
    }

    /* 消费 precord */
    for (int i = 0; i < dequeue_num; i++) {
      // printf_record(tbl_burst[i]->record);
      ret = dpi_padapt_record_adapt(adapt_engine, &adapt_record, tbl_burst[i]->record);
      if (ret < 0) {
          DPI_LOG(DPI_LOG_ERROR, "加载脚本出错, %s\n", precord_misc_get_last_error());
          printf("加载脚本出错, %s\n", precord_misc_get_last_error());
      }
      if (adapt_record == NULL) {
        dpi_output_write_one_record(write_thread_index, tbl_burst[i]->record);
      } else {
        // printf_record(adapt_record);
        dpi_output_write_one_record(write_thread_index, adapt_record);
        ya_destroy_record(adapt_record);
      }
      ya_destroy_record(tbl_burst[i]->record);
      rte_mempool_put(tbl_log_record_mempool, (void *)tbl_burst[i]);
    }
  }
}

/*初始化线程池*/
static int init_tbl_log_mempool(void) {
  if (tbl_log_record_mempool == NULL) {
    tbl_log_record_mempool = rte_mempool_create("tbl_log_record_mp", g_config.tbl_ring_size * 2, sizeof(struct tbl_log_record),
        RTE_MEMPOOL_CACHE_MAX_SIZE, 0, NULL, NULL, NULL, NULL, SOCKET_ID_ANY, 0);
    //MEMPOOL_F_NO_CACHE_ALIGN);

    if (tbl_log_record_mempool == NULL) {
      DPI_LOG(DPI_LOG_ERROR, "Cannot init tbl_log_record_mempool");
      exit(-1);
    } else
      DPI_LOG(DPI_LOG_DEBUG, "Allocated tbl_log_record_mempool");
  }
  return 0;
}

/*初始化record_log*/
int init_tbl_record_log(void) {
  int i;
  dpi_padapt_load_script(g_config.lua_adapt_dir);

  dpi_output_create_writer_keeper_for_n_thread(g_config.log_thread_num, g_config.tbl_out_dir, g_config.tbl_out_format);

  for (i = 0; i < g_config.log_thread_num; i++) {
    char ring_name[64] = {0};
    snprintf(ring_name, sizeof(ring_name), "tbl_record_ring_%d", i);
    tbl_ring[i] = rte_ring_create(ring_name, TBL_RING_SIZE, SOCKET_ID_ANY, RING_F_SC_DEQ);
    if (tbl_ring[i] == NULL) {
      DPI_LOG(DPI_LOG_ERROR, "error while create record tbl ring\n");
      return -1;
    }
    if (rte_ring_lookup(ring_name) != tbl_ring[i]) {
      DPI_LOG(DPI_LOG_ERROR, "Cannot lookup record ring from its name\n");
      return -1;
    }
  }

  if (access(g_config.tbl_out_dir, F_OK))
    mkdir(g_config.tbl_out_dir, 0666);

  init_tbl_log_mempool();
  dpi_pschema_dump_proto_schemas(g_config.dpi_field_dir);
  return 0;
}

/*注册协议字段表*/
int write_proto_record_field_tab(dpi_record_field_table *proto_array, int field_count, const char *proto_name) {
  if (proto_name == NULL || proto_array == NULL) {
    return 0;
  }

  dpi_pschema_register_proto_field(proto_name, proto_array, field_count, 1);

  return 1;
}