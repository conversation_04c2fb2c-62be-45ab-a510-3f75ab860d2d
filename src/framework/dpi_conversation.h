#ifndef _DPI_CONVERSATION_H_
#define _DPI_CONVERSATION_H_

#include <stdint.h>
#include <stddef.h>
#include <arpa/inet.h>

#define NO_ADDR_B 0x01
#define NO_PORT_B 0x02

#define CONVERSATION_TIMEOUT 30

struct conversation_tuple
{
	uint8_t proto;
	uint16_t port_src;
	uint16_t port_dst;
	union {
		uint32_t ip4;
		uint8_t ip6[16];
	} ip_src;
	union {
		uint32_t ip4;
		uint8_t ip6[16];
	} ip_dst;
};

struct conversation_value
{
	uint16_t protocol;
	uint32_t createtime;
	void *conv_session;  // add by liugh
};


/* conversation for ftp add by liugh*/
struct ftp_session
{
	int filesize;

	char username[64];
	char password[64];

	char filetype[10];
	char storpath[64];
	char filepath[128];
	
	uint16_t port_src;
	uint16_t port_dst;
};


/* conversation for tftp add by liugh*/
struct tftp_session
{
	char     opt_str[32];
	char     transmode[32];
	char     filename[128];
	char     filepath[256];
	char     filetype[10];
	uint16_t blocksize;
	int      timeout;
	int      filesize;
	uint64_t now_time_usec;

	uint8_t  convert;
	char     conv_c;         /* convert support data */
	int      last_block;
	uint8_t  lost;

	uint16_t port_src;
	uint16_t port_dst;
};





struct conversation_value * find_conversation(struct conversation_tuple *tuple, uint8_t options);
struct conversation_value *find_or_create_conversation(struct conversation_tuple *tuple, uint8_t options, uint16_t protocol, void *session);

void timeout_conversation_hash(void);
void init_conversation(void);

#endif
