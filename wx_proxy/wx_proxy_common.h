/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#ifndef WX_PROXY_COMMON_H
#define WX_PROXY_COMMON_H

#define new                  pthread_create
#define delete               pthread_detach
#define names                pthread_setname_np

#define ATOMIC_ADD_FETCH(a)  __sync_add_and_fetch(a, 1)
#define ATOMIC_SUB_FETCH(a)  __sync_sub_and_fetch(a, 1)
#define ATOMIC_ADD_NUM(a,n)    __sync_add_and_fetch(a, n)
#define ATOMIC_SUB_NUM(a,n)    __sync_sub_and_fetch(a, n)
#define likely(x)   __builtin_expect(!!(x), 1)
#define unlikely(x) __builtin_expect(!!(x), 0)

#ifdef __cplusplus
extern "C" {
#endif

const char *
format_sockaddr(const struct sockaddr *sa, char *out, size_t outlen);

void*
palloc(int size);

void
pfree(void*p);

int
get_counter();

void
pexit(int code);

void
wall(const char *format, ...);


#ifdef __cplusplus
}
#endif

#endif





