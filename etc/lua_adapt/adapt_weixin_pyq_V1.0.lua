-- 对 weixin_pyq协议进行适配，从 from 到 to 进行适配转换
-- Copyright (c) 2023 YVIEW infra.
-- 更新说明:
-- V0.1: by xumh at 2023.05.10
--      1) 完成基本的由 from 到 to 的适配转换;
--      2) 增加storeid和videocdnmsg字段的适配转换
-- V0.2：by xumh at 2023.05.15
--		1) 优化代码逻辑与可读性
-- V0.3：by xumh at 2023.05.23
--      1) 增加对异常videocdnmsg的处理
-- V0.4：by xumh at 2023.06.25
--      1) 修改了hex_to_str，hex_to_ip函数，当输入长度为奇数或"00"时不处理
-- V0.5：by xumh at 2024.02.02
--      1) 修改了hex_to_ip函数的table为空报错
-- V0.6: by wuby at 2024.02.04
--      1) 修复hex_to_str中对空的判断
-- V0.7: by xumh at 2024.02.20
--      1) 增加了对ascii_part的nil值判断
-- V0.8: by xumh at 2024.02.22
--      1) 增加了对modeflag的逻辑判断
-- V0.9: by xumh at 2024.02.23
--      1) 增加对storeid的长度检测
-- V1.0: by xumh at 2024.03.04
--      1) 增加对storeid的空值判断

local b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

function base64_encode(data)
    return ((data:gsub('.', function(x)
        local r, b ='', x:byte()
        for i = 8, 1, -1 do r = r .. (b % 2 ^ i - b % 2 ^ (i - 1) > 0 and '1' or '0') end
        return r;
    end)..'0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then return '' end
        local c = 0
        for i = 1, 6 do c = c + (x:sub(i, i) == '1' and 2 ^ (6 - i) or 0) end
        return b:sub(c + 1, c + 1)
    end)..({ '', '==', '=' })[#data % 3 + 1])
end

function uri_query_param(uri, param)
    param = param .. "="
    local param_start = string.find(uri, param)
    if param_start == nil then
       return nil
    end

    param_start = param_start + #param

    local param_end = string.find(uri, "&", param_start) or 0
    return string.sub(uri, param_start, param_end - 1)
end

function hex_to_str(hex)                          	   -- 将形如"32303334"的字符串转换为"2034"
	local str = ""
	if hex == "00" then
        return str
    end
	if hex ~= nil and #hex % 2 == 0 then
		for i = 1, #hex, 2 do
			local hex_part   = hex:sub(i, i+1)         -- 每次取出两个字符
			local ascii_part = tonumber(hex_part, 16)  -- 转换为 16 进制数字
			if acsii_part == nil then
				break
			end
			local char_part  = string.char(ascii_part) -- 转换为对应的 ASCII 字符
			str = str..char_part                  	   -- 将转换后的字符拼接到新的字符串中
		end
	end
	return str
end

function hex_to_ip(hex)                                -- 小端序形式的"a0b1c2d3"的字符串转换为***************
	local ip = ""
	local ip_flag = 1
	if hex == "00" then
        return ip
    end
	if hex ~= nil and #hex % 2 == 0 then
		local ip_parts = {}
		for i = 1, #hex, 2 do
			local hex_part     = hex:sub(i, i + 1)     -- 每次取出两个字符
			local dec_part = tonumber(hex_part, 16)    -- 转换为数字
			if dec_part == nil then
				ip_flag = 0
				break
			end
			table.insert(ip_parts, 1, dec_part)        -- 每次在表的开头插入值
		end
		if ip_flag == 1 then
			ip = table.concat(ip_parts, ".")
		end
	end
	return ip
end


function adjustStampLen(stamp)                      -- 调整微秒级stamp的长度
	stamp = tonumber(stamp, 16)
	stamp = tostring(stamp)
	local length = string.len(stamp)
  
	if length < 6 then
		local  zerosToAdd    = 6 - length
		local  adjustedStamp = string.rep("0", zerosToAdd) .. stamp
		return adjustedStamp
	elseif length == 6 then
		return stamp
	end
end

function storeid_param(storeid)                     -- 解析storeid字段

	if storeid == nil or #storeid == 0 then         --输入为空时输出空值

		s_timestamp = ""
		s_hy        = ""
		s_srvIP     = ""
		s_wxnum     = ""
		s_apptype   = ""
		s_filetype  = ""
		return
	end

	local modeflag = storeid:sub(1, 4)

	if modeflag ~= "3230" and #storeid >= 25 then                            --storeid格式一，非3230开头

		local stamp_1      = tonumber(storeid:sub(2,  9),  16)
		local stamp_2      = storeid:sub(10, 17)
		local date_stamp_1 = os.date("%Y-%m-%d %H:%M:%S.", stamp_1)
		local temp_hy      = ""
		local temp_srvIP   = ""
		local temp_wxnum   = tonumber(storeid:sub(18, 25),16)
		temp_wxnum       = tostring(temp_wxnum)
		
		if #storeid >= 49 then
			temp_hy    = storeid:sub(42, #storeid-17)    --从后往前取值
			temp_srvIP = storeid:sub(-16, -9)

		elseif #storeid < 49 and #storeid > 42 then
			temp_hy    = storeid:sub(42, #storeid-1)     --从后往前取值
		end

		stamp_2     = adjustStampLen(stamp_2)
		s_timestamp = date_stamp_1 .. stamp_2
		s_hy        = hex_to_str(temp_hy)
		s_srvIP     = hex_to_ip(temp_srvIP)
		s_wxnum     = base64_encode(temp_wxnum)
		s_apptype   = tonumber(storeid:sub(26, 33),16)
		s_filetype  = tonumber(storeid:sub(34, 41),16)

	elseif modeflag == "3230" and #storeid >= 46 then	                    --storeid格式二，3230开头

		storeid = hex_to_str(storeid)
		local stamp_1 = storeid:sub(1,  14)
		local stamp_2 = storeid:sub(15, 22)
		local temp_wxnum = tonumber(storeid:sub(23, 30), 16)
		temp_wxnum       = tostring(temp_wxnum)
		
		stamp_2    = adjustStampLen(stamp_2)
		s_wxnum    = base64_encode(temp_wxnum)
		s_apptype  = tonumber(storeid:sub(39, 46), 16)
		s_hy       = ""
		s_filetype = ""
		s_srvIP    = ""

		local Y = stamp_1:sub(1,  4)
		local m = stamp_1:sub(5,  6)
		local d = stamp_1:sub(7,  8)
		local H = stamp_1:sub(9,  10)
		local M = stamp_1:sub(11, 12)
		local S = stamp_1:sub(13, 14)
		s_timestamp = Y .. "-" .. m .. "-" ..d .. " " .. H .. ":" .. M .. ":" .. S .. "." .. stamp_2
	end
end

function videocdnmsg_param(videocdnmsg)             -- 解析videocdnmsg字段

	if videocdnmsg == nil or #videocdnmsg == 0 then             --输入为空时输出空值
		c_head_backsrccost = ""
		c_head_srvIP       = ""
		c_timestamp        = ""
		c_hy               = ""
		c_srvIP            = ""
		c_wxnum            = ""
		c_apptype          = ""
		c_filetype         = ""
		c_srvIP            = ""
		return
	end

	local parts = {}
	for part in videocdnmsg:gmatch("([^_]+)") do
		table.insert(parts, part)
	end

	c_head_backsrccost = parts[1]
	c_head_srvIP       = parts[2]
	local storeid      = parts[3]
	local modeflag = ""
	if storeid ~= nil then
		modeflag     = storeid:sub(1, 2)
	end


	if modeflag == "32" and #storeid >= 46 then	        	             --storeid格式二，3230开头

		storeid = hex_to_str(storeid)
		local stamp_1 = storeid:sub(1,  14)
		local stamp_2 = storeid:sub(15, 22)
		local temp_wxnum = tonumber(storeid:sub(23, 30), 16)
		temp_wxnum       = tostring(temp_wxnum)

		stamp_2    = adjustStampLen(stamp_2)
		c_wxnum    = base64_encode(temp_wxnum)
		c_apptype  = tonumber(storeid:sub(39, 46), 16)
		c_hy       = ""
		c_filetype = ""
		c_srvIP    = ""

		local Y = stamp_1:sub(1,  4)
		local m = stamp_1:sub(5,  6)
		local d = stamp_1:sub(7,  8)
		local H = stamp_1:sub(9,  10)
		local M = stamp_1:sub(11, 12)
		local S = stamp_1:sub(13, 14)
		c_timestamp = Y .. "-" .. m .. "-" ..d .. " " .. H .. ":" .. M .. ":" .. S .. "." .. stamp_2
	
	elseif modeflag == "xV" then
		c_timestamp = ""
		c_wxnum     = ""
		c_apptype   = ""
		c_hy        = ""
		c_filetype  = ""
		c_srvIP     = ""
	
	elseif storeid ~= nil then                                              --storeid格式一，非3230开头
		local stamp_1      = tonumber(storeid:sub(2,  9),  16)
		local stamp_2      = storeid:sub(10, 17)
		local date_stamp_1 = os.date("%Y-%m-%d %H:%M:%S.", stamp_1)
		local temp_hy      = ""
		local temp_srvIP   = ""
		local temp_wxnum   = tonumber(storeid:sub(18, 25),16)
		temp_wxnum       = tostring(temp_wxnum)

		if #storeid >= 49 then
			temp_hy    = storeid:sub(42, #storeid-17)    --从后往前取值
			temp_srvIP = storeid:sub(-16, -9)

		elseif #storeid < 49 and #storeid > 42 then
			temp_hy    = storeid:sub(42, #storeid-1)     --从后往前取值
		end

		stamp_2     = adjustStampLen(stamp_2)
		c_timestamp = date_stamp_1 .. stamp_2
		c_hy        = hex_to_str(temp_hy)
		c_srvIP     = hex_to_ip(temp_srvIP)
		c_wxnum     = base64_encode(temp_wxnum)
		c_apptype   = tonumber(storeid:sub(26, 33),16)
		c_filetype  = tonumber(storeid:sub(34, 41),16)
	else
		c_timestamp = ""
		c_hy        = ""
		c_srvIP     = ""
		c_wxnum     = ""
		c_apptype   = ""
	    c_filetype  = ""
	end
end




function adapt_weixin_pyq(to, from)
    precord.clone(to, from)
	to["fileid_1"] = ""
	to["fileid_2"] = ""
	to["fileid_3"] = ""
	to["fileid_4"] = ""
	to["resv_2"]   = ""
	to["resv_3"]   = ""
	to["resv_4"]   = ""
	to["resv_5"]   = ""
	to["resv_6"]   = ""
	
    to["storeid"] = uri_query_param(from["url"]:as_string(), "storeid")

    storeid_param(to["storeid"]:as_string())
    to["s_timestamp"] = s_timestamp
    to["s_wxnum"] = s_wxnum
    to["s_apptype"] = s_apptype
    to["s_filetype"] = s_filetype
    to["s_hy"] = s_hy
    to["s_srvIP"] = s_srvIP

    videocdnmsg_param(from["videocdnmsg"]:as_string())
    to["c_head_backsrccost"] = c_head_backsrccost
    to["c_head_srvIP"] = c_head_srvIP
    to["c_timestamp"] = c_timestamp
    to["c_wxnum"] = c_wxnum
    to["c_apptype"] = c_apptype 
    to["c_filetype"] = c_filetype
    to["c_hy"] = c_hy
    to["c_srvIP"] = c_srvIP

end


-- 注册 adapt_weixin_pyq 协议适配器, 将 weixin_pyq 协议适配到 weixin_pyq 协议
yalua_register("weixin_pyq", "weixin_pyq", adapt_weixin_pyq)
