# 启动命令,需要搭配 setup_dpdk_env.sh -b ethX
# 解析线程数量需要为输出线程的倍数关系
CMD_LINE = "./yaWxDpi -l 0-2 -- -r 1,2 -p 3,4,5,6,7,8 -l 9,10,11"
# 离线读包， speed 速率， pps 每 s 读取帧数。
#CMD_LINE = "./yaWxDpi --vdev="pcap_recur,dir=/root/pcaps/,speed=200M" -l 0-2 -- -r 1,2 -p 3,4,5,6,7,8 -l 9,10,11"
#CMD_LINE = "./yaWxDpi --vdev="pcap_recur,dir=/root/pcaps/,pps=20k" -l 0-2 -- -r 1,2 -p 3,4,5,6,7,8 -l 9,10,11"
# -p 表示解析线程 -l 表示日志线程 解析线程数量可以设置为日志线程数量的一倍或两倍
# 解析线程可根据计算版cpu核心进行配置，使用命令`lscpu`查看 解析线程不可跨NUMA核心

# 开发机专用配置: 网卡只有1个队列,小内存 #
#CMD_LINE = "./yaWxDpi -l 0-2 -- -r 1 -p 3 -l 6"
#CMD_LINE = "gdb --args ./yaWxDpi --vdev="pcap_recur,dir=/home/<USER>/pcaps/pc/qq/20250528/test,pps=20k" -l 0-2 -- -r 1 -p 3 -l 6"
#MEMPOOL_CACHE_SIZE         = 256
#PACKET_RING_SIZE           = 1024
#MAX_PKT_LEN                = 2000
#MAX_FLOW_NUM               = 1024
#MAX_CONVERSATION_HASH_NODE = 256
#TBL_RING_SIZE              = 1024
#TBL_LOG_CONTENT_256K       = 1024               # default value: 262144
#LUA_ADAPT_DIR        = ./lua_adapt/
#LUA_ADAPT_SCRIPT_TYPE  = .lua
#NB_MBUF = 1024                                                    # default value: 1048576  ,minimum value 512
#TCP_REASSEMBLE_MEMPOOL_NUM = 1024                                 # default value: 1048576  ,必须为大于 768 的 2 的幂 ,minimum value 512
# DPDK 相关参数
RXQ_NUM              = 4
DISSECTOR_THREAD_NUM = 16
MEMPOOL_CACHE_SIZE   = 256
PACKET_RING_SIZE     = 65536
MAX_PKT_LEN          = 2000

# lua脚本相关
LUA_ADAPT_DIR        = /root/program/yaWxDpi/lua_adapt/

# DPI 框架相关
TCP_FLOW_TIMEOUT       = 30          # TCP 流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
UDP_FLOW_TIMEOUT       = 30          # UDP 流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
SCTP_FLOW_TIMEOUT      = 30          # STCP流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
TCP_IDENTIFY_PKT_NUM   = 100         # 协议识别 TCP 包个数, 超过此值还未识别成功,就不再识别
UDP_IDENTIFY_PKT_NUM   = 100         # 协议识别 UDP 包个数, 超过此值还未识别成功,就不再识别
SCTP_IDENTIFY_PKT_NUM  = 10          # 协议识别 SCTP包个数, 超过此值还未识别成功,就不再识别
TCP_REASSEMBLE_MEMPOOL_NUM = 1048576 # default value: 1048576, 必须为大于 768 的 2 的幂 ,minimum value 512
TCP_RESSEAMBLE_MAX_NUM = 50000       # TCP 重组最多报文个数
LOG_MAX_NUM            = 10000       # TBL文件输出行数
IDLE_SCAN_PERION       = 10000       # usec
WRITE_TBL_MAXTIME      = 60          # tbl.writing 存在的最长时间，如果没有达到LOG_MAX_NUM，则过了这个时间，则重命名为tbl

TBL_FIELD_TABLE_DIR   = /root/program/field/                      #TBL字段表输出路径
LOG_OUTPUT_LEVEL      = ERROR                                     #日志文件输出级别
TBL_OUT_DIR           = /tmp/tbls                                 #TBL文件输出路径
DEVNAME               = Blade_204                                 #计算板板号 标识   : 格式[Blade_XXX]
DEVAREA               = "浙江杭州"                                #计算板所在地域标识: 格式[省名市名],双引号括起来
TBL_OUT_QUOTE         = 0                                         #tbl中是否输出引号
#报文标签 解析设置
TRAILER_TYPE          = 6                                         #标签类型 0:无标签 1:戎腾 2:恒为 3:金陵 4:赋乐 5:汇智 6：中新赛克/瑞安/网安标准 7:HWZZ
OPERATOR              = "AUTO"                                    #三大运营商标识:[CMCC,CUCC,CTCC,AUTO](AUTO时则自动探测)
RT_MODEL              = "RTL9800"                                 #戎腾设备型号  :[RTL6402,RTL9800]

#微信解析功能开关[1:开 / 0:关]
PROTOCOL_SWITCH_SKYPE_MEDIA_CHAT               = 0 # SKYPE话单 与抖音互斥
PROTOCOL_SWITCH_WEIXIN_MEDIA_CHAT              = 1 # 微信话单
PROTOCOL_SWITCH_WEIXIN_HTTP_POST              = 1 # 微信http cgi-bin
PROTOCOL_SWITCH_ZOOM_CONFERENCE                = 1 # ZOOM 会议
PROTOCOL_SWITCH_QQ_VOIP                        = 1 # QQ  话单
PROTOCOL_SWITCH_HTTP                           = 1 # HTTP 协议
PROTOCOL_SWITCH_WEIXIN_POSITION                = 1 # 微信分享位置             (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN_GROUP_HEAD              = 1 # 微信群图像               (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN_GROUP_HEAD_CONTENT      = 1 # 微信群图像内容还原       (依赖 HTTP协议)
PROTOCOL_SWITCH_QQ_FILE                        = 1 # QQ  文件传输             (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN                         = 1 # 微信文件协议
PROTOCOL_SWITCH_WEIXIN_PYQ                     = 1 # 朋友圈行为单独输出TBL文件(依赖 微信文件协议)
PROTOCOL_SWITCH_QQ_EVENT                       = 1 # QQ 活动事件
PROTOCOL_SWITCH_WEIXIN_MESSAGE                 = 1 # 微信消息
PROTOCOL_SWITCH_WEIXIN_RELA                    = 1 # weixin_id 与 uin 关联解析功能
PROTOCOL_SWITCH_WEIXIN_INFO                    = 1 # 微信个人信息
PROTOCOL_SWITCH_WEIXIN_INFO_MINI               = 1 # 微信附加个人信息
PROTOCOL_SWITCH_WX_PEERS                       = 1 # 微信个人通话，对端 IP
PROTOCOL_SWITCH_WX_LOCSHARING                  = 1 # 微信共享实时位置
PROTOCOL_SWITCH_WEIXIN_MISC                    = 1 # wx misc
PROTOCOL_SWITCH_GQUIC_WEIXIN                   = 1 #  gquic wx相关
PROTOCOL_SWITCH_DOUYIN                         = 0 # 抖音
PROTOCOL_SWITCH_RTP                            = 0 # rtp
PROTOCOL_SWITCH_WXPAY                          = 1 # wx 支付
PROTOCOL_TBL_HTTP                              = 0 # HTTP 协议 产生 TBL文件   (默认关闭HTTP产生TBL数据)

# HTTP 过滤输出设置
HTTP_HOST_URI_QQ        ="http_host_uri_qq_list.txt"     #QQ相关HTTP, 识别并输出
HTTP_HOST_URI_WX        ="http_host_uri_wx_list.txt"     #WX相关HTTP, 识别并输出
HTTP_HOST_URI_WX_HOST   = 0                              # 是否识别 host
HTTP_HOST_URI_WX_MSG    ="http_host_uri_wx_msg_list.txt" #WX 文字消息相关, 识别并输出
HTTP_HOST_WXPAY         = "http_host_wxpay.txt"          # wx 支付 host 相关

# 输出调试信息到屏幕[1:开 / 0:关]
DEBUG_WEIXIN_VOIP                 = 0
DEBUG_ZOOM_CONFERENCE             = 0
DEBUG_QQ_VOIP                     = 1
DEBUG_QQ_FILE                     = 0
DEBUG_WEIXIN_GROUP_HEAD           = 0
DEBUG_WEIXIN_POSITION_SHARE       = 0
DEBUG_QQ_EVENT                    = 0

##--- http config, http rsm config
HTTP_TCP_OUT_OF_ORDER_NUM           = 400           #HTTP TCP 报文乱序最大容忍值(建议20 ~ 800, 值越大,重组效果越好,越耗内存)
HTTP_TCP_MISS_PADDING_LEN           = 2000          #HTTP TCP 报文缺包长度小于N时,允许PADDING(0: 关闭)
HTTP_TCP_MISS_PADDING_STR           = "  "          #当允许 HTTP TCP LOSS PADDING  PADING自定义字符设定(默认空格)
HTTP_TCP_ERROR_PCAP_DUMP            = 0             #HTTP解析识别, 输出错包.(0:关闭 1:开启 需开启 ERROR_PCAP_SIZE功能)
HTTP_STATUS_WHITELIST               =  "*"          #HTTP下行Response Status白名单配置,默认"*"解析一切,如只接受200的响应则配置为"200",如只接受200和302的响应则配置为“200,302",逗号隔开

HTTP_DROP_NO_CONTENT_TYPE           = 0             #是否丢弃无content-type字段的下行http数据，1-丢弃，0-不丢弃
TBL_LOG_CONTENT_256K_NUM            = 1024          #解析HTTP.V51时256K内存池数量大小
HTTP_RSM_SWITCH                     = 1
##--- http config, http rsm config


#设置聚合服务器: QQ文件 QQ话单 微信位置 微信群图像 朋友圈视频 --> 默认使用[微信话单]的ip port
WEXIN_VOICE_SESSION_SERVER_IP   = 127.0.0.1                       # 微信话单 聚合程序服务 IP
WEXIN_VOICE_SESSION_SERVER_PORT = 1024                            # 微信话单 聚合程序服务 port
#QQ_VOIP_SESSION_SERVER_IP       = 127.0.0.1                       # QQ 聚合程序服务 IP
#QQ_VOIP_SESSION_SERVER_PORT     = 1024                            # QQ 聚合程序服务 Port
#WEXIN_GROUP_SESSION_SERVER_IP   = 127.0.0.1                       # 微信群图像  ip
#WEXIN_GROUP_SESSION_SERVER_PORT = 1024                            # 微信群图像  port
#WEXIN_POSITION_SHARE_SERVER_IP  = 127.0.0.1                       # 微信位置分享 ip
#WEXIN_POSITION_SHARE_SERVER_PORT= 1024                            # 微信位置分享 port
#QQ_FILE_SERVER_IP               = 127.0.0.1                       # QQ 文件传输 ip
#QQ_FILE_SERVER_PORT             = 1024                            # QQ 文件传输 port
#WEIXIN_MOMENTS_SERVER_IP        = 127.0.0.1                       # 微信朋友圈视频 ip
#WEIXIN_MOMENTS_SERVER_PORT      = 1024                            # 微信朋友圈视频 port

#微信话单解析的专有设置
WEXIN_VOICE_SESSION_TIMELOOP    = 5                               #微信话单 间隔发送所有会话到聚合端的时间 (单位秒)
WEXIN_VOICE_SESSION_TIMEOUT     = 10                              #微信话单 会话超时时间 (单位秒)
WEXIN_VOICE_SESSION_DROP        = 1                               #微信话单 SessionID 前4个字节为0的检查(1, 检查. 0 不检查)
WEXIN_VOICE_SESSION_PKT_SIZE    = 50                              #微信话单 UDP报文最小值(UDP报文小于此值, 不会进入解析)
WEXIN_VOICE_SESSION_RING_SIZE   = 65                              #微信话单 1对1呼叫等待 响铃包最大值
WEXIN_VOICE_SESSION_FILTER      = 0                               #微信话单 是否开启 16:1 过滤功能
WEXIN_VOICE_SESSION_PKT_FILTER  = 0x00                            #微信话单 设置端口掩码 开启WXA 97报文 16:1 的过滤功能
WEXIN_VOICE_SESSION_FLAGS_PKT   = 3                               #微信话单 UDP D5数据包最小值(D5报文个数少于此值, 不会进入解析)
WEXIN_VOICE_SESSION_SEQ_JITTER  = 4098                            #微信话单 Sequence num 检测, 抖动值超过此值,不会进入解析
WEXIN_VOICE_SESSION_PHONE_WATCH = "8612312341234,8612343214321"   #限290个手机号,英文逗号隔开,无空格(被监视的手机号, 调试信息直接打印到终端)
WXA_RECOGNITION                 = 2                               # 0 模糊识别 1 精确识别模式 2 严格模式
                                                                  # 模糊识别：只要包含 wxa 规则，并且合理的话单都会被输出。又一定的误识别率。
                                                                  # 精确识别：特殊流量或者网络异常状态下的 wxa 会被丢弃（单向流、d5包缺失等），话单数量会减少
                                                                  # 严格识别：严格遵守流量认知组调研规则，话单量更少，但是准确率最高。

#QQ 话单解析设置
QQ_VOIP_SESSION_SEQ_JITTER      = 5                               #qq群话单 Sequence num 检测，抖动值超过此值,不会进入解析

# 微信文件 pyq 解析的专有设置
TBL_WXF_FILTER_SWITCH = 1                                         # 微信文件传输,三种都不存在时,滤掉(!weixinnum && !wx_url && !fileid)
WXF_FILTER_FIELDS_CONFIG = "filemd5,fileid,url"                   # 配置检测上面过滤字段值，最多支持10个和TBL_WXF_FILTER_SWITCH配合使用
TBL_WXF_FILTER_REPLEAT= 0                                         # 开关，当有多个流传一份大文件时，可以只保留rangeend+1=totalsizetbl记录，默认开启，1-开启，0-关闭
WXF_CHECK_HTTP_TIMES =  200                                       # 最后组包时，在整个组包后数据块中查找kv对，不符kv对的跳过，
                                                                  # 并继续向后查三个0位标志，找到后则从这开始解析，最多支持WXF_CHECK_HTTP_TIMES次查找
WXF_FILEID_PART1_BYTE = 10                                        # 微信文件fileid拆分: 第1部分 字符长度(必须是偶数, 两个HEX字符为1个字节)
WXF_FILEID_PART2_BYTE = 64                                        # 微信文件fileid拆分: 第2部分 字符长度(必须是偶数, 两个HEX字符为1个字节)
WXF_PYQ_DATA_DIR      = "/tmp/tbls/weixin_pyq_data"               # 微信朋友圈视频文件存放路径  [PATH: 输出路径; OFF: 关闭还原文件输出]
WXF_TIME_PRECISE      = 0                                         # 微信文件 capdate_start capdate_last两个字段是否精确到ms，默认为0不精确
WXF_FILEDATA_BYTES     = 16                                       # 微信 tbl 保存 filedata 数据头长度，默认 16 字节,最大 256 字节


# 微信群头像解析
WEXIN_GROUP_HEAD_TIMEOUT        = 30                              #微信群头像上传(wxcs)超时时间，具有相同链接的图片上传一次后，超过该时间允许再次上传

# 微信红包解析
WEXIN_MSG_RP_PRIECISE           = 1								  # 识别方法，1 精确识别 0 模糊识别
WEXIN_MSG_RP_SWITCH             = 0								  # 红包行为识别开关 1 开 0 关

# 微信个人信息解析
WX_INFO_INCOMPLETE_FLAG         = 1                               # 是否输出不完整信息，1 代表是同时字符前会添加 “yvIncomplete” 前缀, 0代表不输出

## debug
WX_INFO_EXPORT_ILLEGAL          = 0                               # 将 wxid 和 weChatId 中含有非法字段的 payload 导出到文件

# 微信 misc
DEBUG_WXMISC_PAYLOAD_SAVE       = 0                               # wxmisc 负载保存, 目录默认 "/tmp/tbls/wx_misc_data"
