#ifndef _DPI_WX_INFO_H
#define _DPI_WX_INFO_H

#include <glib.h>
#include <stdint.h>

#include "dpi_proto_ids.h"
#include "dpi_string.h"

typedef enum _em_info_type{
    EM_TYPE_UNKNOWN,
    EM_TYPE_INFO,
    EM_TYPE_MISC,
    EM_TYPE_MAX
}InfoType;


#define true    1
#define false   0
#define error   (-1)

#define WX_INFO_IDENT_0     0xd6
#define WX_INFO_IDENT_1     0x2800


#define POSIBLE_ARR_LEN     8

// 遍历查找类型时候遍历的最大长度
#define FIND_BEGIN_MAX_LEN  100
#define FIND_MID_MAX_LEN    100

#define WX_INFO_MIN_LEN     100  


// 7B...7D 数据块最大元素个数
#define WX_INFO_MINI_BLOCK_MAX_LEN  8

extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];


typedef enum _em_weixin_info_type{
    EM_WX_INFOTYPE,
    EM_WX_INFO_NICK_NAME,
    EM_WX_INFO_NICK_NAME_SIMPLE,
    EM_WX_INFO_NICK_NAME_FULL,
    EM_WX_INFO_WXID,
    EX_EX_INFO_REMARK_NAME,
    EM_WX_INFO_WECHAT_ID,
    EX_WX_INFO_REMARK_NAME_SIMPLE,
    EM_WX_INFO_REMARK_NAME_FULL,
    EM_WX_INFO_HEAD_THUM_ATTR,
    EM_WX_INFO_HEAD_THUM_URL,
    EM_WX_INFO_HEAD_ATTR,
    EM_WX_INFO_HEAD_URL,
    EM_WX_INFO_COUNTRY,
    EM_WX_INFO_LOCATION_SIMPLE,
    EM_WX_INFO_PROVINCE_FULL,
    EM_WX_INFO_CITY_FULL,
    EM_WX_INFO_PERSON_SIGN,
    EM_WX_INFO_GH_ABOUT,
    EM_WX_INFO_PYQ_MSG_ID,
    EM_WX_INFO_PYQ_BG_IMG,
    EM_WX_INFO_RESV0,
    EM_WX_INFO_RESV1,
    EM_WX_INFO_RESV2,
    EM_WX_INFO_MAX
}EM_WX_INFO_TYPE;

/*********************************** WX info 消息类型 **********************************/
#define WX_INFO_WXID                0x0a    // wxid
#define WX_INFO_WXNUM               0X12    // 微信号
#define WX_INFO_18                  0x18    // unknown
#define WX_INFO_20                  0x20    // unknown
#define WX_INFO_NICKNAME_FULL       0x2a    // 昵称全拼
#define WX_INFO_32                  0x32    // 微信备注简拼或者国家地区简拼
#define WX_INFO_3A                  0x3a    // 微信备注
#define WX_INFO_42                  0x42    // 微信备注全拼或者县市全拼
#define WX_INFO_PERSONAL_SIGN       0x4a    // 个性签名
#define WX_INFO_48                  0x48    // unknown
#define WX_INFO_58                  0x58    // unknown
#define WX_INFO_WXHEAD_THUM_ATTR    0x62    // 微信头像缩略图属性 thumbnail
#define WX_INFO_WXHEAD_ATTR         0x6a    // 微信头像缩略图属性 thumbnail
#define WX_INFO_WXHEAD_THUM_URL     0x72    // 微信头像路径
#define WX_INFO_WXHEAD_URL          0x7a    // 微信头像属性
#define WX_INFO_82                  0x82    // unknown
#define WX_INFO_88                  0x88    // unknown
#define WX_INFO_A2                  0xa2    // unknown
#define WX_INFO_A8                  0xa8    // unknown
#define WX_INFO_PYQ_MSG_ID          0xaa    // 朋友圈信息id
#define WX_INFO_B0                  0xb0    // unknown
#define WX_INFO_B8                  0xb8    // unknown
#define WX_INFO_PYQ_BG_IMG          0xba    // 朋友圈背景图,结尾类型
#define WX_INFO_D0                  0xd0    // unknown
#define WX_INFO_E8                  0xe8    // unknown

/*********************************** WX info 消息类型 **********************************/
#define WX_INFO_UNKNOWN_END         0xd00100

#define WECHATID_LEN_MAX            20
#define WECHATID_LEN_MIN            6

#define WX_INFO_INCOMPLETE          "yvIncomplete"

#define  WX_INFO_TYPE_MAX_OFFSET    50          // 向前查找偏移标记位的最大长度

typedef struct wx_info_
{
    char        infotype[16];
    /*暂时先把所有的 buffer  大小都改为 256*/
    char        wxNickName[256];

    uint8_t     wxidLen;
    char        wxid[256];               // wxid

    uint8_t     weChatIdLen;
    char        weChatId[256];           //微信号

    uint8_t     remarkNameLen;
    char        remarkName[256];         // 备注名

    char        remarkNameSimple[256];

    char        remarkNameFull[256];

    uint8_t     wxNickNameFullLen;
    char        wxNickNameFull[256];

    char        wxNickNameSimple[256];

    uint8_t     wxHeadThumUrlLen;
    char        wxHeadThumUrl[256];     // 微信头像缩略图路径
    uint8_t     wxhtuFlag;

    uint8_t     wxHeadThumAttrLen;
    char        wxHeadThumAttr[256];     // 微信头像缩略图属性
    uint8_t     wxHeadUrlLen;
    char        wxHeadUrl[256];         // 微信头像图路径
    uint8_t     wxHeadAttrLen;
    char        wxHeadAttr[256];         // 微信头像属性

    uint8_t     wxPersonSignLen;        
    char        wxPersonSign[256];           //个性签名

    char        nest_elems[WX_INFO_MINI_BLOCK_MAX_LEN][256];
    uint8_t     nelts;

    char        wxGHAbout[256];

    char        wxLocSimple[256];           // 所在地简称


    char        resv1[1024];

}WXInfo;


typedef struct 
{
    dpi_str_t data;
    uint8_t type_pos;       // 第一个类型长度出现的未知
    uint8_t type_len;       // type 列表占用长度
}MiniBlock;


int dissect_weixin_info_mini(struct flow_info *flow, int direction, uint32_t seq, 
    const uint8_t *payload,  const uint32_t payload_len, uint8_t flag);
int write_wx_info_log(struct flow_info *flow, int direction, WXInfo *info);

// void identify_weixin_info_mini(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);

#endif