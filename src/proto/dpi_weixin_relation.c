#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include <string.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"

#include "dpi_weixin_relation.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

dpi_field_table weixin_rela_array_f[] = {
    DPI_FIELD_D(EM_WX_RELA_WXID,                    EM_F_TYPE_STRING,                 "wxid"),
    DPI_FIELD_D(EM_WX_RELA_UIN,                     EM_F_TYPE_STRING,                 "uin"),
};

static int write_wx_rela_log(struct flow_info *flow, int direction, WXRelaInfo *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    // struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_WX_RELA_MAX; i++)
    {
        switch(weixin_rela_array_f[i].index)
        {
        case EM_WX_RELA_WXID:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxid, strlen(info->wxid));
            break;
        case EM_WX_RELA_UIN:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->uin, strlen(info->wxid));
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    log_ptr->type        = TBL_LOG_WEIXIN_RELA;
    log_ptr->len         = idx;
    log_ptr->tid         = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}

static 
int dissect_weixin_rela(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, 
                            const uint32_t payload_len, uint8_t flag)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_RELA] == 0)
    {
        return 0;
    }

    /* 去重，为真说明当前流已经解析成功过一次信息 */
    if (flow->app_session) {
        return 0;
    }

    uint16_t i = 0;
    uint16_t offset = 0;
    for (i = 1; i < payload_len;) {
        offset = i;
        if (payload[i] != 0xFF) {
            i++;
            continue;
        }
        if (payload[i] != payload[i+1] || payload[i] != payload[i+2]) {
            i += 2;
            continue;
        }
        if (payload[i+3] != 0xFF) {
            i += 3;
            continue;
        }
        offset += 4;
        if ((get_uint32_ntohl(payload, offset) == WX_IDENT_1) &&
            (get_uint32_ntohl(payload, offset + 4) == WX_IDENT_2) &&
            (get_uint32_ntohl(payload, offset + 8) == WX_IDENT_1))
        {
            if (payload[offset + 12] != 0x00 &&
                payload[offset + 12] != 0x01) {
                    i +=3;
                    continue;
            }
            offset += 12;
            offset += 1;
            break;
        } else {
            i += 3;
            continue;
        }
    }

    /* 11 字节未知数据 */
    offset += 11;
    /* 24 字节未知数据 */
    offset += 24;

    WXRelaInfo info;
    memset(&info, 0, sizeof(info));

    if (((uint32_t)offset + WXRELA_STR_LEN) >= payload_len) {
        return -1;
    }

    uint8_t wxid_len = payload[offset + 24 -1];
    if (wxid_len > WXRELA_STR_LEN) {
        return -1;
    }
    /* 是否有脏数据或者误识别情况 */
    if (payload[offset + wxid_len] != 0) {
        return -1;
    }
    snprintf(info.wxid, wxid_len + 1, "%s", payload + offset);
    /* 去除非 utf-8的内容 */
    if (isUTF8(info.wxid, wxid_len) == 0) {
        return -1;
    }

    offset += 24;
    /* 未知字段 */
    offset += 24;

    uint8_t uin_len = 0;
    /* payload剩余长度若满24字节，则第24字节是uin长度，否则遍历直到遇到0为止 */
    if(payload_len - offset > 24) {
        uin_len = payload[offset + 24 - 1];
        
    } else {
        for (i = offset; i < payload_len; ++i) {
            if(payload[i] == 0x00) {
                break;
            }
            uin_len += 1;
        }
    }
    if (uin_len >WXRELA_STR_LEN) {
        return -1;
    }
    for (i = 0; i < uin_len; i++) {
        info.uin[i] = (payload[offset + i] << 4) >> 4;
    }
    info.uin[uin_len] = '\0';

    /* 去重，流中出现一条记录之后添加标记 */
    if (!flow->app_session) {
        char *p = dpi_malloc(8);
        strcpy(p, "yes");
        flow->app_session = p;
    }

    int   client_ip            = 0;
    int   server_ip            = 0;
    short client_port          = 0;
    short server_port          = 0;
    int   C2S                  = 0;
    struct sonwden sonwden;
    memset(&sonwden, 0, sizeof(sonwden));

    get_ip_port(&flow->pkt, &sonwden, &C2S);
    // get_ip_port_v4(flow, &client_ip, &server_ip, &client_port, &server_port, &C2S);
    
    ST_WXRELA value;
    memset(&value, 0, sizeof(ST_WXRELA));

    if (NULL == g_wx_rela_handle) {
        return 0;
    }

    // 设置建联 相关 参数
    dpi_TrailerParser(&value.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
    dpi_TrailerGetHWZZMAC(&value.trailer, (const char *)flow->ethhdr);
    dpi_TrailerGetMAC(&value.trailer, (const char*)flow->ethhdr, g_config.RT_model); // 解析戎腾MAC
    dpi_TrailerSetDev(&value.trailer, g_config.devname);        // 解析板号
    dpi_TrailerSetOpt(&value.trailer, g_config.operator_name);  // 运营商
    dpi_TrailerSetArea(&value.trailer, g_config.devArea);       // 设定 地域名
    dpi_TrailerUpdateTS(&value.trailer);

    memcpy(value.wxid, info.wxid, WXRELA_STR_LEN);
    memcpy(value.uin, info.uin, WXRELA_STR_LEN);
    memcpy(value.client_ip.ipv6, sonwden.client_ip.ipv6, 16);
    // value.client_ip.ipv4 = client_ip;
    value.ip_version = flow->pkt.ip_ver;

    wxc_sendMsg(g_wx_rela_handle, (const unsigned char*)&value, sizeof(ST_WXRELA), WXCS_WX_RELATION);
    // printf("ip=%u, wxid=%s, uin=%s\n", value.client_ip.ipv4, value.wxid, value.uin);
    // write_wx_rela_log(flow, direction, &info);

    return 0;
}


static 
void identify_weixin_rela(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_RELA] == 0)
    {
        return;
    }

    /* 每帧数据以d6 开头 */
    if (payload[0] != WX_IDENT_0) {
        return;
    }

    uint16_t i = 0;
    for (i = 1; i < payload_len;) {
        uint16_t offset = i;
        if (payload[i] != 0xFF) {
            i++;
            continue;
        }
        if (payload[i] != payload[i+1] || payload[i] != payload[i+2]) {
            i += 2;
            continue;
        }
        if (payload[i+3] != 0xFF) {
            i += 3;
            continue;
        }
        offset += 4;
        if ((get_uint32_ntohl(payload, offset) == WX_IDENT_1) &&
            (get_uint32_ntohl(payload, offset + 4) == WX_IDENT_2) &&
            (get_uint32_ntohl(payload, offset + 8) == WX_IDENT_1)) {

            if (payload[offset + 12] != 0x00 &&
                payload[offset + 12] != 0x01) {
                    i +=3;
                    continue;
            }
            flow->real_protocol_id = PROTOCOL_WEIXIN_RELA;
            break;
        } else {
            i += 3;
            continue;
        }
    }

    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_WEIXIN_RELA);
    return;
}

void init_weixin_rela_dissector(void)
{

    write_proto_field_tab(weixin_rela_array_f, EM_WX_RELA_MAX, "wx_rela");

    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_WEIXIN_MEDIA_CHAT
    // port_add_proto_head(IPPROTO_UDP, 80,    PROTOCOL_WEIXIN_RELA);

    // 协议为 PROTOCOL_WEIXIN_MEDIA_CHAT 的回调
    udp_detection_array[PROTOCOL_WEIXIN_RELA].proto         = PROTOCOL_WEIXIN_RELA;
    udp_detection_array[PROTOCOL_WEIXIN_RELA].identify_func = identify_weixin_rela;
    udp_detection_array[PROTOCOL_WEIXIN_RELA].dissect_func  = dissect_weixin_rela;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN_RELA].excluded_protocol_bitmask, PROTOCOL_WEIXIN_RELA);

    return;
}


uint8_t init_wx_relation(void) {
    if (NULL == g_wx_rela_handle) {
        wxc_init(&g_wx_rela_handle, g_config.wx_rela_ip,  g_config.wx_rela_port);
    }


    return 0;
}
