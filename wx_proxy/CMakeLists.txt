cmake_minimum_required(VERSION 3.0)

project(wx_proxy)

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -std=c11 -Wall -O0 -ggdb3")
#set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fno-omit-frame-pointer -static-libasan")  #GDB:b __asan_report_error
#set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=leak -fsanitize=undefined")
set(EXECUTABLE_OUTPUT_PATH "./")
set(CMAKE_VERBOSE_MAKEFILE OFF)

include_directories("/usr/include/glib-2.0/")
include_directories("/usr/lib64/glib-2.0/include/")
include_directories("../include/")


link_directories(${PROJECT_SOURCE_DIR}/../lib/)

add_executable(
    wx_proxy
    wx_proxy.c
    wx_proxy_common.c
    wx_proxy_mirror.c
    wx_proxy_thread.c
    wx_proxy_server.c
    wx_proxy_process.c
    wx_proxy_timer.c
    wx_proxy_signal.c
    wx_proxy_conf.c
    wx_proxy_getopt.c
    wx_proxy_version.c
    wx_proxy_log.c
    )

target_link_libraries(wx_proxy event_core)
target_link_libraries(wx_proxy event)
target_link_libraries(wx_proxy event_openssl)
target_link_libraries(wx_proxy event_extra)
target_link_libraries(wx_proxy event_pthreads)
target_link_libraries(wx_proxy glib-2.0)
target_link_libraries(wx_proxy iniparser)
target_link_libraries(wx_proxy ssl)
target_link_libraries(wx_proxy crypto)
target_link_libraries(wx_proxy pthread)
target_link_libraries(wx_proxy dl)
target_link_libraries(wx_proxy z)

