/****************************************************************************************
 * 文 件 名 : wxcs_types.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-03
* 编    码 : root      '2019-01-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_TYPES_H_
#define _WXCS_TYPES_H_

#define WXCS_PROTO_MAGIC_A 'C'
#define WXCS_PROTO_MAGIC_B 'S'


/* msg proto  */
typedef struct ST_wxcsProtoHdr
{
    char     magic[2];          /* 'C'，'S' */
    uint16_t msgLen;            /* 整个 msg 长度(hdr + payload)，主机序 */
    int      msgType;           /* Message 类型 */

    uint8_t  payload[0];
} ST_wxcsProtoHdr;

struct ST_rtl
{
    /* rtl 标签，内存结构与原始数据一致，可直接 memcpy */
    uint32_t  PersonTEID;
    uint32_t  PersonResv;
    uint32_t  PersonOUTTER_SRC;
    uint32_t  PersonOUTTER_DST;
    uint8_t   PersonMSISDN[7];
    uint8_t   PersonIMEI[7];
    uint8_t   PersonIMSI[7];
    uint16_t  PersonTAC;
    uint16_t  PersonPLMN_ID;
    uint32_t  PersonULI;
    uint8_t   PersonBS;

};
#endif /* _WXCS_TYPES_H_ */
