-- 对 wxf 新增字段进行适配，从 from 到 to 进行适配转换
-- Copyright (c) 2023 YVIEW infra.
-- 更新说明:
-- V0.1: at 2023.05.06
--      1) 完成基本的由 from 到 to 的适配转换;
-- V0.2: by xumh at 2023.05.10
--      1）添加了由fileid解析出的多个字段
-- V0.3: by xumh at 2023.05.15
--      1）优化代码逻辑与可读性
-- V0.4：by xumh at 2023.05.26
--      1）增加了对输入的容错梳理；优化了代码逻辑
-- V0.5: by xumh at 2023.05.29
--      1）在resv_6中输出了wxnum的长度，用来统计异常wxnum的占比
-- V0.6：by xumh at 2023.05.30
--      1）增加对fileid的长度检测，不再处理小于91字符长度的fileid
-- V0.7: by xumh at 2023.06.01
--      1）增加对二进制fileid的处理，并检测wxnum是否等于10000
-- V0.8: by xumh at 2023.06.07
--      1）在hex_to_str、decodeTLV等函数中增加了对输入的检测
-- V1.0：by xumh at 2023.06.25
--      1）增加对nestedTLV1[2].v、nestedTLV1的检测，保证其长度和值有效
--      2）将nestedTLV1_2_1[7].l修改为nestedTLV1_2_1[#nestedTLV1_2_1-1].l，确保不会取到nil值
-- V1.1: by xumh at 2023.08.22
--      1) 增加对wxnum的base64编码
-- V1.2: by xumh at 2024.01.16
--      1) 增加对nestedTLV1[2]的判断
-- V1.3: by wuby at 2024.02.04
--      1) 修复hex_to_str中对空的判断
-- V1.4: by xumh at 2024.02.20
--      1) 增加对ascii_part的空值判断和nestedTLV1_2_1的长度判断
-- V1.5: by xumh at 2024.02.22
--      1) 增加对nestedTLV1_2_1的长度判断逻辑
-- V1.6: by xumh at 2024.02.23
--      1) 增加对nestedTLV1_2[1]的判断
-- V1.7: by xumh at 2024.03.04
--      1) 增加对filetype的逻辑判断
-- V1.8: by xumh at 2024.03.05
--      1) 增加对nestedTLV1_2的空值判断
-- V1.9: by xumh at 2024.03.18
--      1) 增加对len_low的空值判断

local b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
function saveFile(filename,content)
    local file = io.open(filename,"a")
    if file then
      file:write("fileid = ".. content .. "\n")
      file:close()
    else
      print("无法打开文件" .. filename)
    end
end
function base64_encode(data)
	if data == "" then
        return ""
	end
	
    return ((data:gsub('.', function(x)
        local r, b ='', x:byte()
        for i = 8, 1, -1 do r = r .. (b % 2 ^ i - b % 2 ^ (i - 1) > 0 and '1' or '0') end
        return r;
    end)..'0000'):gsub('%d%d%d?%d?%d?%d?', function(x)
        if (#x < 6) then return '' end
        local c = 0
        for i = 1, 6 do c = c + (x:sub(i, i) == '1' and 2 ^ (6 - i) or 0) end
        return b:sub(c + 1, c + 1)
    end)..({ '', '==', '=' })[#data % 3 + 1])
end


function hex_to_str(hex)                          	   -- 将形如"32303334"的字符串转换为"2034"
	local str = ""
	if hex == "00" then
        return str
    end
	if hex ~= nil and #hex % 2 == 0 then
		for i = 1, #hex, 2 do
			local hex_part   = hex:sub(i, i+1)         -- 每次取出两个字符
			local ascii_part = tonumber(hex_part, 16)  -- 转换为 16 进制数字
			if ascii_part == nil then
				break
			end
			local char_part  = string.char(ascii_part) -- 转换为对应的 ASCII 字符
			str = str..char_part                  	   -- 将转换后的字符拼接到新的字符串中
		end
	end
	return str
end

function hex_to_ip(hex)                                -- 小端序形式的"a0b1c2d3"的字符串转换为***************
	local ip = ""
	local ip_flag = 1
	if hex == "00" then
        return ip
    end
	if hex ~= nil and #hex % 2 == 0 then
		local ip_parts = {}
		for i = 1, #hex, 2 do
			local hex_part     = hex:sub(i, i + 1)     -- 每次取出两个字符
			local dec_part = tonumber(hex_part, 16)    -- 转换为数字
			if dec_part == nil then
				ip_flag = 0
				break
			end
			table.insert(ip_parts, 1, dec_part)        -- 每次在表的开头插入值
		end
		if ip_flag == 1 then
			ip = table.concat(ip_parts, ".")
		end
	end
	return ip
end

function hexToStr(hex)                               --从16进制转成去掉了\x的字符串
    local str = ""
	if hex ~= "" then 
		for i = 1, #hex do
			str = str .. string.format("%02X", string.byte(hex, i))
		end
	end
    return str
end

function decodeTLV(input)                         	   -- tlv格式解析
	local i = 1
	local output = {}
	local correctType = {[2] = true, [4] = true, [48] = true}
	while i <= #input do
		local t = tonumber(input:sub(i, i + 1), 16)
		if correctType[t] then
			i = i + 2
			local len_high = tonumber(input:sub(i, i+1), 16)
			local len_low  = nil
			local l = nil
			
			if len_high ~= nil then
				if len_high < 0x80 then    -- 小于0x80，则是单字节的长度
					l = len_high
					i = i + 2
				elseif len_high >= 0x80 then               	   -- 大于等于0x80，则是双字节的长度
					len_low = tonumber(input:sub(i+2, i+3), 16)
					if len_low == nil then
						break
					end
					l = (len_high - 0x80) * 0x80 + (len_low - 0x80)
					i = i + 4
				end
			end

			local v = ""
			if l ~= nil then
				if l > 0 then
					v = input:sub(i, i + l * 2 - 1)
					i = i + l * 2
				end
			end
			table.insert(output, {t = t, l = l, v = v})
		else
			i = i + 20
		end

	end
	return output
end

function fileid_param(input)                            -- 解析fileid，提取字段
	f_wxnum          = ""
	f_originfiletype = ""
	f_srvIP          = ""
	f_timestamp      = ""
	f_filekey        = ""
	resv_6           = ""
	local fileid    = ""
	local temp_filekey   = ""
    local temp_filetype  = ""
	local temp_timestamp = ""
	local nilFlag = 0
	
	if input:sub(1, 2) == "30" then
		fileid = input
	elseif input:sub(1, 1) == "0" then
		fileid = hexToStr(input)
	end

	if #fileid > 91 then									--fileid长度检测，如果小于100则不处理
		local topLevelTLV    = decodeTLV(fileid)           -- tlv嵌套结构解析
		local nestedTLV1     = decodeTLV(topLevelTLV[1].v)
		if #nestedTLV1 < 2 then
			saveFile(".fileid.txt",fileid)
		end
		if #nestedTLV1 >= 2 then
			if nestedTLV1[2].v ~= nil then
				local nestedTLV1_2   = decodeTLV(nestedTLV1[2].v)
				--各种空值判断
				if nestedTLV1_2 == nil then
					nilFlag = 1
				elseif nestedTLV1_2[1] == nil then
					nilFlag = 1
				else
					if nestedTLV1_2[1].v ~= nil then
						nestedTLV1_2_1 = decodeTLV(nestedTLV1_2[1].v)
					end
					
					if nestedTLV1_2_1 ~= nil and #nestedTLV1_2_1 >= 2 then
						if nestedTLV1_2_1[2] ~= nil then
							local temp_wxnum     = nestedTLV1_2_1[2].v
							if nestedTLV1_2_1[4] ~= nil then
								local temp_srvIP     = nestedTLV1_2_1[4].v
								if nestedTLV1_2_1[5] ~= nil then
									temp_timestamp = nestedTLV1_2_1[5].v
								end
								if #nestedTLV1_2_1 >= 6 then 
									temp_filekey   = nestedTLV1_2_1[6].v
									temp_filetype  = nestedTLV1_2_1[#nestedTLV1_2_1-1].v
								end
								
								--处理wxnum
								local temp_len = nestedTLV1_2_1[2].l
								len_wxnum = tostring(temp_len)
								local hasNoValue = {[0] = true, [1] = true}
								if hasNoValue[temp_len] then
									temp_wxnum = ""
								else
									while #temp_wxnum < 8 and temp_wxnum ~= "2710" do     -- 小于4字节长度的wxnum需要在最高位前添加"f"，直至长度为4字节
										temp_wxnum = "f" .. temp_wxnum
									end
									temp_wxnum  = tonumber(temp_wxnum, 16)
									temp_wxnum  = tostring(temp_wxnum)
								end
						
								--处理timestamp
								if nestedTLV1_2_1[5] == nil or hasNoValue[nestedTLV1_2_1[5].l] then
									temp_timestamp = ""
								else
									temp_timestamp = tonumber(temp_timestamp, 16)
									temp_timestamp = os.date("%Y-%m-%d %H:%M:%S", temp_timestamp)
								end
								
								--处理filetype
								if #nestedTLV1 >= 3 and temp_filetype ~= "" then
									if nestedTLV1_2_1[#nestedTLV1_2_1-1].l ~= nil and nestedTLV1_2_1[#nestedTLV1_2_1-1].l > 0  and nestedTLV1[3].l ~= nil then
										temp_filetype = "0x" .. temp_filetype .. ";" .. "0x0" .. nestedTLV1[3].t .. "0" .. nestedTLV1[3].l     --单字节长度的fileid中，包含4字节的originfiletype，并且和最后的0x0400或0x0405一同输出
									elseif nestedTLV1[3].l ~= nil then
										temp_filetype = ";" .. "0x0" .. nestedTLV1[3].t .. "0" .. nestedTLV1[3].l
									end
								else
									if nestedTLV1_2_1[#nestedTLV1_2_1-1].l ~= nil and nestedTLV1_2_1[#nestedTLV1_2_1-1].l > 0 and tenp_filetype ~= "" then
										temp_filetype = "0x" .. temp_filetype .. ";"
									else
										temp_filetype = ""
									end
								end
						
								f_wxnum          = base64_encode(temp_wxnum)
								f_srvIP          = hex_to_ip(temp_srvIP)
								f_timestamp      = temp_timestamp
								f_filekey        = hex_to_str(temp_filekey)
								f_originfiletype = temp_filetype
							else
								f_wxnum = base64_encode(temp_wxnum)
							end
						else
							nilFlag = 1
						end	
					else
						nilFlag = 1
					end
				end
			else
				nilFlag = 1
			end
		else
			nilFlag = 1
		end
		
		if nilFlag == 1 then
			f_wxnum          = ""
		    f_srvIP          = ""
		    f_timestamp      = ""
		    f_filekey        = ""
		    f_originfiletype = ""
			len_wxnum        = ""
		end
	end



end






function adapt_weixin(to, from)
    precord.clone(to, from)
	to["fileid_1"] = ""
	to["fileid_2"] = ""
	to["fileid_3"] = ""
	to["fileid_4"] = ""
	to["resv_2"]   = ""
	to["resv_3"]   = ""
	to["resv_4"]   = ""
	to["resv_5"]   = ""
	
	fileid_param(from["fileid"]:as_string())
	to["resv_6"]  = len_wxnum
	to["f_wxnum"] = f_wxnum
	to["f_srvIP"] = f_srvIP
    to["f_timestamp"] = f_timestamp
    to["f_filekey"] = f_filekey
    to["f_originfiletype"] = f_originfiletype

end

-- 注册 adapt_weixin 协议适配器, 将 weixin 协议适配到 weixin 协议
yalua_register("weixin", "weixin", adapt_weixin)