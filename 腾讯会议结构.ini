腾讯会议结构
1.个人登录
tcp 80/443/8080端口
上行
四字节长度(指示单包总长高位在左)|0x00001770|0x00或0x01或0x02|两字节长度(指示info长度)|info|0x1400000016|18字节个人常规账号
下行
四字节长度(指示单包总长高位在左)|0x00001770|0x00或0x01或0x02|0x1400000016|18字节个人常规账号

2.个人会议
tcp/udp 443端口
0x28|四字节HeaderLen|四字节infoLen|Header(protobuf格式)|infoc|0x29
0x36|两字节长度(高位在左，包总长度)|0x000000|0x00或0x01或0x02|0x03a102|7字节随机数|4字节sessionID|0x00000000|3字节随机数|7字节0x00|info(protobuf格式)
0x51|0x0011|两字节随机数|0x02|7字节随机数|4字节sessionID|info(protobuf格式)

protobuf中的 {"2"：{"3": xxxxxxxxxxxx}} 为个人会议账号，使用接口`decode_protobuf`解码,使用dpi_cjson接口获取/2/3节点中的varint

编译说明
docker exec -ti centos-wx /bin/bash
在docker中的 /home/<USER>/WX/wx_dpi_cmake目录下 cmake3 -B build && make -C build