/****************************************************************************************
 * 文 件 名 : wxcs_session_keeper.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-04
* 编    码 : root      '2019-01-04
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_SESSION_KEEPER_H_
#define _WXCS_SESSION_KEEPER_H_

#include "wxcs_session.h"
#include "wxcs_utils.h"

#include <functional>
#include <unordered_map>
#include <mutex>

#if 0
struct FnvHash
{
    size_t operator()(uint64_t __val) const
        {
            return std::tr1::_Fnv_hash::hash(__val);
        }
};
#endif

template <typename PersonType>
class WxcsSessionKeeper
{
public:
    WxcsSessionKeeper(int sessionActiveDiffTimeMax, int size)
        : map_(size)
        , sessionActiveDiffTimeMax_(sessionActiveDiffTimeMax)
        {

            LOG_DEF->info("sessionActiveDiffTimeMax: {}", sessionActiveDiffTimeMax_);
            LOG_DEF->info("wxa hash table size : {}", size);

        }

public:
    SessionPtr<PersonType> getSessionOf(const std::string &strSessionId)
        {
            std::lock_guard<std::mutex> lck(mapMtx_);

            auto iter = map_.find(strSessionId);
            if (iter == std::end(map_))
            {
                return NULL;
            }

            return iter->second;
        }

    SessionPtr<PersonType> getSessionOf(uint8_t *sessionIdBuff, int len)
        {
            std::string strSessionId((char *)sessionIdBuff, len);

            return getSessionOf(strSessionId);
        }

    int addNewSession(const SessionPtr<PersonType>& session)
        {
            std::lock_guard<std::mutex> lck(mapMtx_);

            const std::string &strSessionId = session->getSessionId();
            map_[strSessionId] = session;

            LOG_DEF->debug( "创建会话:{} ", session->getPrintableSessionId());
            return 0;

        }

    int removeOneSession(const SessionPtr<PersonType>& session)
        {
            std::lock_guard<std::mutex> lck(mapMtx_);
            map_.erase(session->getSessionId());
            return 0;
        }

    int removeDeadSessions(std::function<int (uint32_t time, SessionPtr<PersonType> &)> onRemove)
        {
            time_t tmCurrent = time(NULL);

            // 轮询每个 Session, 有几个就超时几个
            for (auto iter = map_.begin(); iter != map_.end(); )
            {
                if (iter->second->wasDeadSession(sessionActiveDiffTimeMax_))
                {
                    LOG_DEF->debug("会话超时: {} ", iter->second->getPrintableSessionId());
                    auto session = iter->second;
                    {
                        std::lock_guard<std::mutex> lck(mapMtx_);
                        map_.erase(iter++);
                    }
                    // 这里对临时对象操作，函数内部无需考虑其线程安全性, 下同
                    onRemove(tmCurrent, session);
                }
                else
                {
                    iter++;
                }
            }

            return 0;

        }

    int removeAllSessions(std::function<int (uint32_t time, SessionPtr<PersonType> &)> onRemove)
    {
        time_t tmCurrent = time(NULL);

        for (auto iter = map_.begin(); iter != map_.end(); )
        {
            LOG_DEF->debug("会话删除: {} ", iter->second->getPrintableSessionId());
            auto session = iter->second;
            {
                std::lock_guard<std::mutex> lck(mapMtx_);
                map_.erase(iter++);
            }
            onRemove(tmCurrent, session);
        }

        return 0;
    }

protected:
    std::unordered_map<std::string, SessionPtr<PersonType> > map_;
    std::mutex          mapMtx_;

protected:
    int                 sessionActiveDiffTimeMax_;

protected:
    static uint64_t     s_lEntryCnt_;
};

/*! WxcsSessionKeeper模板无法满足需求，这里选择子类化 */
class WxcsQQSingleSessionKeeper : public WxcsSessionKeeper<WxcsQQSinglePerson>
{
public:
    WxcsQQSingleSessionKeeper(int sessionActiveDiffTimeMax, int size)
        : WxcsSessionKeeper<WxcsQQSinglePerson>(sessionActiveDiffTimeMax, size)
    {
        LOG_DEF->info("sessionActiveDiffTimeMax: {}", sessionActiveDiffTimeMax_);
        LOG_DEF->info("wxqqs hash table size : {}", size);
    }

public:
    SessionPtr<WxcsQQSinglePerson> findSession(const PersonPtr<WxcsQQSinglePerson>& person)
    {
        std::string sessionId;
        {
            std::lock_guard<std::mutex> lck(auxiliaryMapMtx_);
            auto it = auxiliaryMap_.find(person->getPersonID());
            if (it != auxiliaryMap_.end())
                sessionId = it->second;
        }

        std::lock_guard<std::mutex> lck(mapMtx_);
        auto it = map_.find(sessionId);
        return it != map_.end() ? it->second : SessionPtr<WxcsQQSinglePerson>();
    }

    void addAuxiliaryPerson(const PersonPtr<WxcsQQSinglePerson>& person, const SessionPtr<WxcsQQSinglePerson>& session)
    {
        std::lock_guard<std::mutex> lck(auxiliaryMapMtx_);
        auxiliaryMap_.emplace(person->getPersonID(), session->getSessionId());
    }

    void removeAuxiliaryPerson(const PersonPtr<WxcsQQSinglePerson>& person)
    {
        std::lock_guard<std::mutex> lck(auxiliaryMapMtx_);
        auxiliaryMap_.erase(person->getPersonID());
    }

    void addUnsessionPerson(const PersonPtr<WxcsQQSinglePerson>& person)
    {
        std::lock_guard<std::mutex> lck(unsessionMapMtx_);
        unsessionPersonMap_.emplace(person->getPersonID(), person);
        auxiliaryUnsessionPersonMap_.emplace(person->selfQQ_, person->getPersonID());
    }

    PersonPtr<WxcsQQSinglePerson> getUnsessionPerson(uint64_t qq)
    {
        PersonPtr<WxcsQQSinglePerson> person;
        {
            std::lock_guard<std::mutex> lck(unsessionMapMtx_);
            auto it1 = auxiliaryUnsessionPersonMap_.find(qq);
            if (it1 != auxiliaryUnsessionPersonMap_.end()) {
                uint64_t personId = it1->second;
                auto it2 = unsessionPersonMap_.find(personId);
                if (it2 != unsessionPersonMap_.end()) {
                    person = it2->second;
                    unsessionPersonMap_.erase(personId);
                }
                auxiliaryUnsessionPersonMap_.erase(qq);
            }
        }

        return person;
    }

private:
    std::mutex auxiliaryMapMtx_;
    std::unordered_map<uint64_t, std::string> auxiliaryMap_; // key: personID value: sessionID, 用于辅助查找person所属的session

    std::mutex unsessionMapMtx_;
    std::unordered_map<uint64_t, PersonPtr<WxcsQQSinglePerson> > unsessionPersonMap_; // key: personID value: Person，用于存放暂时无法确定session的人
    std::unordered_map<uint64_t, uint64_t> auxiliaryUnsessionPersonMap_; // key: qq value: personID，用于通过qq号辅助查找unsessionPersonMap_内的person
};

#endif /* _WXCS_SESSION_KEEPER_H_ */
