/****************************************************************************************
 * 文 件 名 : wxcs_voice.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-05-10
* 编    码 : chenzq         '2021-05-10
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "wxcs_voice.h"

#include <unordered_map>
#include <map>

#include "jhash.h"

#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

//////////////////// WxcsAudioPerson //////////////////////////////////////////
WxcsAudioPerson::WxcsAudioPerson()
{
}

// 初始化列表, 参数过长, 不优雅.(宁愿舍弃这点性能)
WxcsAudioPerson::WxcsAudioPerson(ST_wxAudioSessionAlive *p)
    : WxcsPerson(p->SessionID, sizeof(p->SessionID), p->SessionType,
                 p->client_port, p->server_port,
                 p->client_ip.ipv4, p->server_ip.ipv4,
                 p->PersonLastActiveTime,
                 &p->trailer)
{
    C2STransPackets     = p->PersonC2STransPackets;
    S2CTransPackets     = p->PersonS2CTransPackets;
    C2SVideoPackets     = p->PersonC2SVideoPackets;
    S2CVideoPackets     = p->PersonS2CVideoPackets;
    C2STransBytes       = p->PersonC2STransBytes;
    S2CTransBytes       = p->PersonS2CTransBytes;
    Calling             = p->SessionCalling;
    StartTime           = p->PersonFirstActiveTime;
    IsAnswered          = p->PersonIsAnswered;
    RingTime            = p->PersonRingTime;
    isTimeout           = p->isTimeout;

    PersonS2CUnknown    =p->PersonS2CUnknown;        // 流中包含的未识别的包数
    PersonC2SUnknown    =p->PersonC2SUnknown;        // 流中包含的未识别的包数
    PersonC2S_pkt_tcp   =p->PersonC2S_pkt_tcp;       // TCP 报文 块数
    PersonS2C_pkt_tcp   =p->PersonS2C_pkt_tcp;       // TCP 报文 块数
    Person_pkt_75       =p->Person_pkt_75;           // 前缀为75 报文数
    Person_pkt_76       =p->Person_pkt_76;           // 前缀为76 报文数
    Person_pkt_77       =p->Person_pkt_77;           // 前缀为77 报文数
    Person_pkt_95       =p->Person_pkt_95;           // 前缀为95 报文数
    Person_pkt_96       =p->Person_pkt_96;           // 前缀为96 报文数
    Person_pkt_97       =p->Person_pkt_97;           // 前缀为97 报文数
    Person_pkt_98       =p->Person_pkt_98;           // 前缀为97 报文数
    Person_pkt_drop     =p->Person_pkt_drop;         // 异常     报文数
    callflag_           = p->callflag;
    flowflag_           = p->flowflag;
    callflag_96_0_      = p->stat_callflag_96_0;
    callflag_96_1_      = p->stat_callflag_96_1;
    callflag_98_0_      = p->stat_callflag_98_0;
    callflag_98_1_      = p->stat_callflag_98_1;

    c2spkts_            = p->c2s_pkts;              // 话单数据包流自主计数
    s2cpkts_            = p->s2c_pkts;
    c2sd5pkts_          = p->PersonC2S_D5_Pcaket;   // 会话控制包数量
    s2cd5pkts_          = p->PersonC2S_D5_Pcaket;
    c2svoicepkts_       = p->c2s_voice_pkts;        // 话单语音包流自主计数
    s2cvoicepkts_       = p->s2c_voice_pkts;
    c2svideopkts_       = p->c2s_video_pkts;        // 话单视频包流自主计数

    s2cvideopkts_       = p->s2c_video_pkts;
    if(6 == p->ip_version)
    {
        ip_version = 6;
        memcpy(client_ipv6, p->client_ip.ipv6, 16);
        memcpy(server_ipv6, p->server_ip.ipv6, 16);
    }
}

// += 操作符重载
// wx 通话会有多流的情况，在 dpi 端，每个流被认为是一个用户，
// 在聚合操作的时候被聚合，重载 += 操作符只需对一些计数之类的累加即可
WxcsAudioPerson & WxcsAudioPerson::operator+=(const WxcsAudioPerson &rhs)
{
     PersonS2CUnknown   += rhs.PersonS2CUnknown;
     PersonC2SUnknown   += rhs.PersonC2SUnknown;
     PersonC2S_pkt_tcp  += rhs.PersonC2S_pkt_tcp;
     PersonS2C_pkt_tcp  += rhs.PersonS2C_pkt_tcp;
     Person_pkt_75      += rhs.Person_pkt_75;
     Person_pkt_76      += rhs.Person_pkt_76;
     Person_pkt_77      += rhs.Person_pkt_77;
     Person_pkt_95      += rhs.Person_pkt_95;
     Person_pkt_96      += rhs.Person_pkt_96;
     Person_pkt_97      += rhs.Person_pkt_97;
     Person_pkt_98      += rhs.Person_pkt_98;
     Person_pkt_drop    += rhs.Person_pkt_drop;
     C2STransPackets    += rhs.C2STransPackets;
     S2CTransPackets    += rhs.S2CTransPackets;
     C2SVideoPackets    += rhs.C2SVideoPackets;
     S2CVideoPackets    += rhs.S2CVideoPackets;
     C2STransBytes      += rhs.C2STransBytes;
     S2CTransBytes      += rhs.S2CTransBytes;
     RingTime           += rhs.RingTime;
     c2spkts_           += rhs.c2spkts_;
     s2cpkts_           += rhs.s2cpkts_;
     c2sd5pkts_         += rhs.c2sd5pkts_;
     s2cd5pkts_         += rhs.s2cd5pkts_;
     c2svoicepkts_      += rhs.c2svoicepkts_;
     s2cvoicepkts_      += rhs.s2cvoicepkts_;
     c2svideopkts_      += rhs.c2svideopkts_;
     s2cvideopkts_      += rhs.s2cvideopkts_;
     callflag_96_0_     += rhs.callflag_96_0_;
     callflag_96_1_     += rhs.callflag_96_1_;
     callflag_98_0_     += rhs.callflag_98_0_;
     callflag_98_1_     += rhs.callflag_98_1_;

     // 取最小值, 并且都不为0
     if (StartTime == 0) {
        StartTime = rhs.StartTime;
     } else if (rhs.StartTime != 0) {
        StartTime = std::min(StartTime, rhs.StartTime);
     }

     LastActiveTime     =  LastActiveTime > rhs.LastActiveTime ? LastActiveTime : rhs.LastActiveTime;

    //  if (GetPeersList().empty()) {
    //      peersinfo_ = rhs.peersinfo_;
    //  }

    return *this;
}

WxcsAudioPerson operator+(const WxcsAudioPerson &lhs, const WxcsAudioPerson &rhs)
{
    WxcsAudioPerson person = lhs;
    person += rhs;
    return person;
}

void WxcsAudioPerson::Update(const std::shared_ptr<WxcsAudioPerson> &person)
{
    if (Calling == CALLTYPE_UNSET && person->Calling != CALLTYPE_UNSET) {
        Calling =person->Calling;
    }
    flowflag_           =person->flowflag_;

    callflag_           = person->callflag_;
    C2STransPackets     = person->C2STransPackets;
    S2CTransPackets     = person->S2CTransPackets;
    C2SVideoPackets     = person->C2SVideoPackets;
    S2CVideoPackets     = person->S2CVideoPackets;
    C2STransBytes       = person->C2STransBytes;
    S2CTransBytes       = person->S2CTransBytes;
    StartTime           = person->StartTime;
    IsAnswered          = person->IsAnswered;
    RingTime            = person->RingTime;
    isTimeout           = person->isTimeout;

    PersonS2CUnknown    =person->PersonS2CUnknown;        // 流中包含的未识别的包数
    PersonC2SUnknown    =person->PersonC2SUnknown;        // 流中包含的未识别的包数
    PersonC2S_pkt_tcp   =person->PersonC2S_pkt_tcp;       // TCP 报文 块数
    PersonS2C_pkt_tcp   =person->PersonS2C_pkt_tcp;       // TCP 报文 块数
    Person_pkt_75       =person->Person_pkt_75;           // 前缀为75 报文数
    Person_pkt_76       =person->Person_pkt_76;           // 前缀为76 报文数
    Person_pkt_77       =person->Person_pkt_77;           // 前缀为77 报文数
    Person_pkt_95       =person->Person_pkt_95;           // 前缀为95 报文数
    Person_pkt_96       =person->Person_pkt_96;           // 前缀为96 报文数
    Person_pkt_97       =person->Person_pkt_97;           // 前缀为97 报文数
    Person_pkt_98       =person->Person_pkt_98;           // 前缀为97 报文数
    Person_pkt_drop     =person->Person_pkt_drop;         // 异常     报文数

    c2spkts_            = person->c2spkts_;              // 话单数据包流自主计数
    s2cpkts_            = person->s2cpkts_;
    c2sd5pkts_          = person->c2sd5pkts_;             // 会话控制包数量
    s2cd5pkts_          = person->s2cd5pkts_;
    c2svoicepkts_       = person->c2svoicepkts_;        // 话单语音包流自主计数
    s2cvoicepkts_       = person->s2cvoicepkts_;
    c2svideopkts_       = person->c2svideopkts_;        // 话单视频包流自主计数
    s2cvideopkts_       = person->s2cvideopkts_;

    callflag_96_0_      = person->callflag_96_0_;
    callflag_96_1_      = person->callflag_96_1_;
    callflag_98_0_      = person->callflag_98_0_;
    callflag_98_1_      = person->callflag_98_1_;

    LastActiveTime     =  LastActiveTime > person->LastActiveTime ? LastActiveTime : person->LastActiveTime;
}

// 获取 session 用户列表的 key
// 考虑到目前 wxa 存在多流的情况，用户数据合并之前需要做区分
// 区分方法是添加端口区分，同时还要考虑到 ipv6, 因此当前存储方式为:
//*  ipv4: 低48位存储ipv4相关key    ipv4(32bit) + 端口(16bit)
//*  ipv6: 高48位存储ipv6相关key    ipv6(hash之后产生一个32bit无符号整型) + 端口(16bit)
uint64_t WxcsAudioPerson::getPersonID() const
{
    if (ip_version == 4) {
        return ((client_ip << 8) | client_port);
    } else if (ip_version == 6) {
        uint64_t val = jhash(client_ipv6, strlen((char *)client_ipv6), 0);
        return ((val << 32) | client_port);
    }

    return 0;
}


uint32_t WxcsAudioPerson::getC2STransPackets() const
{
    return C2STransPackets;
}

uint32_t WxcsAudioPerson::getS2CTransPackets() const
{
    return S2CTransPackets;
}

/* 写一个人的完整信息
    注意: 字段个数发生变更时
          toStrBlankRecord 对应的也要修改
*/
std::string WxcsAudioPerson::toStrRecord(char sep) const
{
    // 填充 保留字段
    std::string strResv = reserv_ + " ";
    struct
    {
        const char       *name;
        uint32_t          value;
    } arr[] = {
        {"ip_version",  ip_version       },
        {"C2S_Unknown", PersonS2CUnknown },
        {"S2C_Unknown", PersonC2SUnknown },
        {"C2S_TCP",     PersonC2S_pkt_tcp},
        {"S2C_TCP",     PersonS2C_pkt_tcp},
        {"PKT_75",      Person_pkt_75    },
        {"PKT_76",      Person_pkt_76    },
        {"PKT_77",      Person_pkt_77    },
        {"PKT_95",      Person_pkt_95    },
        {"PKT_96",      Person_pkt_96    },
        {"PKT_97",      Person_pkt_97    },
        {"PKT_98",      Person_pkt_98    },
        {"PKT_drop",    Person_pkt_drop  },
        {NULL,          0                },
    };

    for(int i = 0, yes = 0; arr[i].name; i++)
    {
        if(0 == arr[i].value)
        {
            continue;
        }

        yes++;
        const char *s = (1 == yes)? "":" ";
        strResv += s;
        strResv += arr[i].name;
        strResv += "=";
        strResv += std::to_string(arr[i].value);
    }


    //一个用户被分成多条流时, 需要合并,但要用逗号分隔开
    std::string resv1;
    const std::map<std::string, std::string> resvers = {
        {"callingflag",     callflag_},
        {"flowflag",        flowflag_},
        {"clientip",        resv_clientip_},
        {"serverip",        resv_serverip_},
        {"serverport",      resv_serverport_},
        {"clientport",      resv_clientport_},
        {"c2spkt",          resv_c2spkt_},
        {"s2cpkt",          resv_s2cpkt_},
        {"c2sbyte",         resv_c2sbyte_},
        {"s2cbyte",         resv_s2cbyte_},
        {"flownum",         std::to_string(resv_flownum_)},
        {"ttl",             peersinfo_.ttl}
    };

    for (auto & res : resvers) {
        if (res.second.empty()) {
            continue;
        }
        strResv += " " + res.first + "=" + res.second;
    }

    //这是统计信息
    const std::map<std::string, uint32_t> resver1 = {
        {"c2spkts",         c2spkts_},
        {"s2cpkts",         s2cpkts_},
        {"c2svoicepkts",    c2svoicepkts_},
        {"s2cvoicepkts",    s2cvoicepkts_},
        {"c2svideopkts",    c2svideopkts_},
        {"s2cvideopkts",    s2cvideopkts_},
        {"callflag_96_0",   callflag_96_0_},
        {"callflag_96_1",   callflag_96_1_},
        {"callflag_98_0",   callflag_98_0_},
        {"callflag_98_1",   callflag_98_1_},
    };

    for (auto & res : resver1) {
        if (res.second == 0) {
            continue;
        }
        resv1 += " " + res.first + "=" + std::to_string(res.second);
    }
    ////////// END /////////////

    // 22 fields
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // statistics
    CONS_RECORD_FIELD_NUM(strRecord,    ip_version,             sep);
    CONS_RECORD_FIELD_NUM(strRecord,    C2STransPackets,        sep);
    CONS_RECORD_FIELD_NUM(strRecord,    S2CTransPackets,        sep);
    CONS_RECORD_FIELD_NUM(strRecord,    C2STransBytes,          sep);
    CONS_RECORD_FIELD_NUM(strRecord,    S2CTransBytes,          sep);
    // date
    CONS_RECORD_FIELD_TIME(strRecord,   StartTime,              sep);
    CONS_RECORD_FIELD_TIME(strRecord,   LastActiveTime,         sep);

    CONS_RECORD_FIELD_TEXT(strRecord,   wxId,                   sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   wxUin,                  sep);

    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.peerlist,    sep);
    CONS_RECORD_FIELD_NUM(strRecord,    peersinfo_.ipcount,     sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.country,     sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.province,    sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.publicip,    sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.privateip,   sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.ipfor4G,     sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.iptype,      sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   peersinfo_.a3list,      sep);
    CONS_RECORD_FIELD_NUM(strRecord,    peersinfo_.a3totoal,    sep);


    //保留字段
    CONS_RECORD_FIELD_TEXT(strRecord, strResv, sep);
    CONS_RECORD_FIELD_TEXT(strRecord, resv1, sep);
    CONS_RECORD_FIELD_TEXT(strRecord, peersinfo_.ipport, sep);


    return strRecord;
}

// 人数不够时, 写入空的数据, 注意每个人的字段数
std::string WxcsAudioPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 46; i++)
    {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}


//一个用户被分成多条流时, 需要合并,但要用逗号分隔开.
void  WxcsAudioPerson::UpdateResvInfo(std::shared_ptr<WxcsAudioPerson>  person)
{
    std::string prefix = resv_serverip_.empty() ? "" : ",";
    resv_clientip_      += prefix + person->getClientIP();
    resv_serverip_      += prefix + person->getServerIP();
    resv_serverport_    += prefix + std::to_string(person->server_port);
    resv_clientport_    += prefix + std::to_string(person->client_port);
    resv_c2spkt_        += prefix + std::to_string(person->C2STransPackets);
    resv_s2cpkt_        += prefix + std::to_string(person->S2CTransPackets);
    resv_c2sbyte_       += prefix + std::to_string(person->C2STransBytes);
    resv_s2cbyte_       += prefix + std::to_string(person->S2CTransBytes);
    resv_flownum_       += 1;
}

void  WxcsAudioPerson::SetResvInfo(std::vector<std::shared_ptr<WxcsAudioPerson>> &_persons)
{
    std::string serverip;
    std::string serverport;
    std::string clientport;
    std::string callflag;
    std::string flowflag;
    uint8_t emptyflag = 0;
    std::string flownum = "";
    std::string c2spkt;
    std::string s2cpkt;
    std::string c2sbyte;
    std::string s2cbyte;

    if (_persons.size() < 2) {
        return;
    }

    flownum = std::to_string(_persons.size());

    // 第一个是各个流的总和，所以从 1 开始 统计其他
    for (size_t i = 1; i < _persons.size(); ++i) {
        auto &person = _persons[i];
        std::string prefix = emptyflag ? "" : ",";
        serverip    += prefix + person->getServerIP();
        serverport  += prefix + std::to_string(person->server_port);
        clientport  += prefix + std::to_string(person->client_port);
        c2spkt      += prefix + std::to_string(person->C2STransPackets);
        s2cpkt      += prefix + std::to_string(person->S2CTransPackets);
        c2sbyte     += prefix + std::to_string(person->C2STransBytes);
        s2cbyte     += prefix + std::to_string(person->S2CTransBytes);
        emptyflag = 0;
    }

    const std::map<std::string, std::string> resvers = {
        {"serverip",        serverip},
        {"serverport",      serverport},
        {"clientport",      clientport},
        {"c2spkt",          c2spkt},
        {"s2cpkt",          s2cpkt},
        {"c2sbyte",         c2sbyte},
        {"s2cbyte",         s2cbyte},
        {"flownum",         flownum}
    };

    for (auto & res : resvers) {
        if (res.second.empty()) {
            continue;
        }
        reserv_ += " " + res.first + "=" + res.second;
    }
}

/* 1 template specialization for WxcsAudioPerson */
template<>
std::string WxcsSessionBase<WxcsAudioPerson>::getColumnList(char sep)
{
    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo"            , sep);
    WX_COLUMN_TEXT(strColumns, "LineNo"           , sep);
    WX_COLUMN_TEXT(strColumns, "CapDate"          , sep);

    if(CFG->GetValueOf<int>("SESSION_ID_WXA_ENABLE", 0))
    {
        WX_COLUMN_TEXT(strColumns, "SessionID"        , sep);
    }

    WX_COLUMN_TEXT(strColumns, "SessionType"      , sep);
    WX_COLUMN_TEXT(strColumns, "isVideo"          , sep);
    WX_COLUMN_TEXT(strColumns, "isGroup"          , sep);
    WX_COLUMN_TEXT(strColumns, "Calling"          , sep);
    WX_COLUMN_TEXT(strColumns, "Called"           , sep);
    WX_COLUMN_TEXT(strColumns, "SessionStartTime" , sep);
    WX_COLUMN_TEXT(strColumns, "SessionStopTime"  , sep);
    WX_COLUMN_TEXT(strColumns, "isAnswered"       , sep);
    WX_COLUMN_TEXT(strColumns, "RingTime"         , sep);
    WX_COLUMN_TEXT(strColumns, "SessionDuration"  , sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount"      , sep);
    WX_COLUMN_TEXT(strColumns, "PersonList"       , sep);
    WX_COLUMN_TEXT(strColumns, "PairSuccess"       , sep);

    for (int i = 0; i<SESSION_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonIPVersion_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransPackets_"     + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransPackets_"     + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxId_"                + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxUin_"               + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersList_"         + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersCount_"        + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersCountry_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersProvince_"     + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersPublicIP_"     + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersPrivateIP_"    + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersIPfor4G_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersIPtype_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersa3List_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonWxPeersa3Total_"      + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv_"                + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv1_"                + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv2_"                + index, sep);
    }

    return strColumns;
}


/******************* 微信话单 输出TBL 注释已经非常完善 ************************/
/* template specialization for WxcsAudioPerson */
template<>
std::string WxcsSessionBase<WxcsAudioPerson>::getPrintableSessionId() const
{
    if(0XFFFFFFFF == *(uint32_t*)(((uint8_t *)byteSessionId_.data())+4))
    {
        return  "";
    }
    else
    {
        return   bytes_to_hexstring((uint8_t *)byteSessionId_.data(), byteSessionId_.length());
    }
}


template<>
bool WxcsSessionBase<WxcsAudioPerson>::wasDeadSession(int activeDiffMax) const
{
    // 1 dpi 离线检测, 只要任意一个 DPI离线, 相关这个Session则超时掉
    for (auto &kv : personMap_)
    {
        if(time(NULL) - get_wxcs_timestamp(kv.second->getDPI_NodeID()) > activeDiffMax)
        {
            return true;
        }
    }

    // 2 isTimeout 加速
    bool all_timeout = true;
    for (auto &kv : personMap_)
    {
        if(1 != kv.second->isTimeout)
        {
            all_timeout = false;
            break;
        }
    }
    if(all_timeout)//如果全部已标记超时
    {
        return true;
    }

    // 3 Session 超时检测
    all_timeout = true;
    for (auto &kv : personMap_)
    {
        if(get_node_timestamp(kv.second->getDPI_NodeID()) - kv.second->getLastActiveTime() < activeDiffMax)
        {
            all_timeout = false;
            break;
        }
    }
    if(all_timeout)//如果所有Person已超时
    {
        return true;
    }

    return false;
}

void WxcsSession<WxcsAudioPerson>::LogMonitorInfo(std::shared_ptr<WxcsAudioPerson> & person,
                        const MonitorFlags & flags,
                        const std::string & action) const
{
  if (flags.msisdn) {
    LOG_INTST->warn("[{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{},   PeerLocation:{}",
        action, person->getMsisdn(), person->getImsi(), person->getImei(),
        person->getDevName(), person->getOperator(),
        person->getPrintableSessionID(), person->GetPeersList());
  } else if (flags.imsi) {
    LOG_INTST->warn("[{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
        action, person->getMsisdn(), person->getImsi(), person->getImei(),
        person->getDevName(), person->getOperator(),
        person->getPrintableSessionID());
  } else if (flags.imei) {
    LOG_INTST->warn("[{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
        action, person->getMsisdn(), person->getImsi(), person->getImei(),
        person->getDevName(), person->getOperator(),
        person->getPrintableSessionID());
  }
}

void WxcsSession<WxcsAudioPerson>::VerifyCall()
{
    if (isgroup_ == 1) return;
    bool down_flag = true;
    for (auto &person : real_person_) {
        if (person.second->s2cpkts_ > 0) {
            down_flag = false;
            break;
        }
    }

    if (down_flag) {
        called_ = "";
        calling_ = "";
    }

}

/**
* @brief 处理通话角色,填充主被叫。一对一通话只要一方确定即可填充另一方
*      | 主叫              | 被叫                 | 操作
*      | CallTYpe_Calling | CallType_Called     | 不操作
*      | CALLTYPE_UNSET   | CallType_Called    | 填充主叫
*      | CallTYpe_Calling | CALLTYPE_UNSET     | 填充被叫
*      | CALLTYPE_UNSET   | CALLTYPE_UNSET     | 不操作
*/
void WxcsSession<WxcsAudioPerson>::ProcessCallRoles()
{
    if (GetSessionType() != WXA_SESSION_PERSON) { return; }

    for (auto & elem : real_person_) {
        auto & person = elem.second;
        if (CallTYpe_Calling == person->Calling) {
            calling_ = person->getPrintablePersonID();
        } else if (CallType_Called == person->Calling) {
            called_ = person->getPrintablePersonID();
        }
    }

    if (calling_.empty() == called_.empty()) {
        return;
    }

    auto targetRole = calling_.empty() ? called_ : calling_;

    for (auto & elem : real_person_) {
        auto & person = elem.second;
        auto person_id = person->getPrintablePersonID();

        if (person_id == targetRole) { continue; }

        if (calling_.empty()) {
            calling_ = person_id;
        } else {
            called_ = person_id;
        }
    }
}

void WxcsSession<WxcsAudioPerson>::DealPersonInfo() {
    for (auto & mulitperson : real_person_) {
        auto & person = mulitperson.second;
        ProcessCallRoles();

        int video_pkt_num = CFG->GetValueOf<int>("SESSION_PACKET_VIDEO_NUM", 10);
        if (!isvideo_) {
            isvideo_ = (person->c2svideopkts_ > video_pkt_num && person->s2cvideopkts_ > video_pkt_num) ? 1 : 0;
        }

        // 获取最早通话时间和通话最晚结束时间
        if (starttime_ == 0 ) {
            starttime_ = person->GetStartTime();
        } else if (person->GetStartTime() != 0) {
            starttime_ = std::min(starttime_, person->GetStartTime());
        }
        if (stoptime_ == 0) {
            stoptime_ = person->getLastActiveTime();
        } else if (person->getLastActiveTime() != 0) {
            stoptime_ = std::max(stoptime_, person->getLastActiveTime());
        }

        isanswed_ = isanswed_ ? isanswed_ : person->IsAnswered;
        // ringtime_ = person->RingTime > ringtime_ ? person->RingTime : ringtime_;
        ringtime_ = person->RingTime;
        personcount_ = real_person_.size();

        std::string prefix = personlist_.empty() ? "" : ",";
        personlist_ += prefix + person->getPrintablePersonID();
    }

    durationtime_ = (stoptime_ - starttime_) ? (stoptime_ - starttime_) : 0;

    /**
    * 一对一通话，出现对端 ip 之后，双方对端 ip 是否匹配
    * 只有在 本地一对一通话，双方都有记录的情况下才做对端 ip 正确性检查
    * */
    if (personcount_ == 2) {
        const auto person0  = real_person_.begin()->second;
        const auto person1  = (real_person_.begin()++)->second;
        auto ipfor4G0       = person0->GetPeers4GIP();
        auto srcip0         = person0->getSrcIP();
        auto ipfor4G1       = person1->GetPeers4GIP();
        auto srcip1         = person1->getSrcIP();
        if (ipfor4G0.find(srcip1) != std::string::npos ||
            ipfor4G1.find(srcip0) != std::string::npos) {
            pairsucc_ = 1;
        }
    }

    // 以下为群组操作
    if (!isgroup_) {
        return;
    }

    uint64_t groupcalling = 0;
    uint32_t ringtime = 0;
    // 群通话只有一人时，设定 3s 响铃误差
    if (real_person_.size() <= 1) {
        auto & person = real_person_.begin()->second;
        if (person->RingTime > 3) {
            calling_ = person->getPrintablePersonID();
        } else {
            called_ = person->getPrintablePersonID();
        }
    } else {
        //  群通话多人时候，响铃时长最长的为主叫
        for (auto & mulitperson : real_person_) {
            auto &person = mulitperson.second;

            if (ringtime <= person->RingTime) {
                groupcalling = mulitperson.first;
                ringtime = person->RingTime;
            }
        }

        for (auto & mulitperson : real_person_) {
            auto &person = mulitperson.second;
            if (groupcalling == mulitperson.first) {
                calling_ = person->getPrintablePersonID();
            } else {
                std::string prefix = called_.empty() ? "" : ",";
                called_ += prefix + person->getPrintablePersonID();
            }
        }
    }
}

bool WxcsSession<WxcsAudioPerson>::CheckOutput()
{
    if (real_person_.empty()) {
        return false;
    }

     // 是否输出未接听
    if (CFG->GetValueOf<int>("TBL_OUT_MISSED_CALLS", 0) == 0 && isanswed_ == 0 && isgroup_ != 0) {
        return false;
    }

    return true;
}


// 清洗数据
void WxcsSession<WxcsAudioPerson>::WashData()
{
    /**************************************************************************/
    // 删除脏数据之前输出布控信息
    // 遍历Session, 拎出每一个Person, 比对MSISDN, IMSI, IMEI
    for (auto & mulitperson : real_person_) {
        auto & person = mulitperson.second;
        auto flag = CheckMonitorFlags(person);
        if (flag.HashAnyFlag()) {
            LogMonitorInfo(person, flag, "tbl_found");
        }
    }

    // 脏数据 key
    std::set<uint64_t> dirtykeys;
    for (auto & mulitperson : real_person_) {
        auto & person = mulitperson.second;

        // 高优先级. IOS发起群通话, 对方接听前, 只有与服务器的双向D5报文
        if (person->getC2STransPackets() == 0 && person->getS2CTransPackets() == 0) {
            continue;
        }

        // 高优先级. Android发起群通话, 对方接听前, 没有S2C的Packet
        if (person->getC2STransPackets() > CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20) &&
          person->getS2CTransPackets() == 0 && person->GetSessionType() == 1) {
            continue;
        }

        if (CFG->GetValueOf<int>("WXA_RECOGNITION", 0)) {
            //判断报文个数
            if(person->getC2STransPackets() < CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20) ||
            person->getS2CTransPackets() < CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20) )
            {
                // 一对一单向流，人数为 1 的时候，如果没有对端 ip 丢弃
                // 人数为 2 的时候，如果其中一个有对端 ip 则全部保留
                // 群通话 一对一单向流直接丢弃
                if (isgroup_ == 0 && real_person_.size() == 1) {
                    if (!person->GetPeersList().empty()) {
                        continue;
                    }
                } else if (isgroup_ == 0 && real_person_.size() == 2) {
                    std::vector<std::shared_ptr<WxcsAudioPerson>> singlepersons;
                    for (auto & mulit : real_person_) {
                        singlepersons.push_back(mulit.second);
                    }
                    if (!singlepersons[0]->GetPeersList().empty() || !singlepersons[1]->GetPeersList().empty()) {
                        continue;
                    }
                }
                dirtykeys.emplace(mulitperson.first);
            }
        } else {
            //判断报文个数
            if(person->getC2STransPackets() < CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20) &&
            person->getS2CTransPackets() < CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20) )
            {
                // 一对一单向流，人数为 1 的时候，如果没有对端 ip 丢弃
                // 人数为 2 的时候，如果其中一个有对端 ip 则全部保留
                // 群通话 一对一单向流直接丢弃
                if (isgroup_ == 0 && real_person_.size() == 1) {
                    if (!person->GetPeersList().empty()) {
                        continue;
                    }
                } else if (isgroup_ == 0 && real_person_.size() == 2) {
                    std::vector<std::shared_ptr<WxcsAudioPerson>> singlepersons;
                    for (auto & mulit : real_person_) {
                        singlepersons.push_back(mulit.second);
                    }
                    if (!singlepersons[0]->GetPeersList().empty() || !singlepersons[1]->GetPeersList().empty()) {
                        continue;
                    }
                }
                dirtykeys.emplace(mulitperson.first);
            }
        }

        if (CFG->GetValueOf<int>("WXA_RECOGNITION", 0)) {
                        // // 双向 d5 包必须满足一定数量
            if (person->GetS2CD5Pkts() < 3 || person->GetS2CD5Pkts() < 3) {
                dirtykeys.emplace(mulitperson.first);
            }
        }

    }

    // 删除脏数据
    for (auto key : dirtykeys) {

        auto persons = real_person_.find(key);
        auto  & person = persons->second;

        LOG_INTST->debug("[tbl_DROP_MSISDN:C2S{},S2C{},C2Sd5{},S2Cd5:{}] SRCIP:{}, DSTIP:{} MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                    person->getC2STransPackets(),
                    person->getS2CTransPackets(),
                    person->GetC2SD5Pkts(),
                    person->GetS2CD5Pkts(),
                    person->getClientIP(),
                    person->getServerIP(),
                    person->getMsisdn(),
                    person->getImsi(),
                    person->getImei(),
                    person->getDevName(),
                    person->getOperator(),
                    person->getPrintableSessionID());

        // auto & person = mulitpersons[key][0];

        auto flags = CheckMonitorFlags(person);
        if (flags.HashAnyFlag()) {
            LogMonitorInfo(person, flags, "tbl_drop");
        }

        real_person_.erase(key);
    }
}


// 处理附加数据
// 对端 ip  数据插入
// 对端ip   双向关联
// wxid     数据插入
void WxcsSession<WxcsAudioPerson>::DealSubInfo()
{
    if (isgroup_ == 0 && real_person_.size() == 2) {
        std::vector<std::shared_ptr<WxcsAudioPerson>> persons;
        for (auto & mulit : real_person_) {
            persons.push_back(mulit.second);
        }
        auto src0 = persons[0]->getClientIP();
        auto peerlist0 = persons[0]->GetPeersList();
        auto src1 = persons[1]->getClientIP();
        auto peerlist1 = persons[1]->GetPeersList();
        auto find0 = peerlist0.find(src1) == std::string::npos ? 0 : 1;
        auto find1 = peerlist1.find(src0) == std::string::npos ? 0 : 1;
        pairsucc_ = std::to_string(find0) + "," + std::to_string(find1);
    }


    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N 个报文)
    for (auto it = real_person_.begin(); it != real_person_.end(); ++it)
    {
        /* 更新 uin wxid字段 仅操作 一对一情况*/

        if (GetSessionType()  == 0) {
            TimeRange range;
            PersonPeersInfo peersinfo;
            range.begin = it->second->GetStartTime();
            range.end = it->second->getLastActiveTime();
            auto clientip = it->second->getClientIP();
            auto msisdn = it->second->getMsisdn();
            LOG_DEF->debug("finding peer ip!!");
            bool isfind = WXAKS->GetPeersInfo(clientip, msisdn, range, peersinfo);
            if (isfind) {
                it->second->SetPeersInfo(peersinfo);
                LOG_DEF->debug("update to wxa!！ c:{}-pro:{}-pri:{}-pub:{}-4g:{}",
                peersinfo.country, peersinfo.province, peersinfo.privateip, peersinfo.publicip, peersinfo.ipfor4G);
            }
        }

    }
}

int WxcsSession<WxcsAudioPerson>::updatePersonInfo(const PersonPtr<WxcsAudioPerson> &person, bool bInterestingPerson)
{
    auto pOldPersonInfo = findPerson(person);
    if (!pOldPersonInfo)
    {
        return -1;
    }

    if (bInterestingPerson)
    {
        LOG_INTST->warn("活跃目标:{}, session:{}, 解析板:{}, 运营商:{}",
                        person->getPersonID(), getPrintableSessionId(),
                        person->getDevName(), person->getOperator());
    }
    else
    {
        LOG_DEF->debug("活跃用户:{}, session {}, 解析板:{}, 运营商:{}",
                        person->getPersonID(), getPrintableSessionId(),
                        person->getDevName(), person->getOperator());
    }

    std::lock_guard<std::mutex> lck(personMtx_);
    sessionLastActiveTime_  = person->getLastActiveTime();
    personMap_[person->getPersonID()]->Update(person);

    return 0;
}

void WxcsAudioPerson::DebugPrint()
{
    LOG_INTST->debug("{}:msisdn:{}, imsi:{}, imei:{}, srcip:{}, srcport:{}, starttime:{}, endtime:{}",getPrintableSessionID(), getMsisdn(), getImsi(), getImei(), getClientIP(), getSrcPort(), StartTime, LastActiveTime);
    LOG_INTST->debug("{}:S2CUnknown:{}, C2SUnknown:{}, C2S_pkt_tcp:{}, S2C_pkt_tcp:{}, pkt_75:{}, pkt_76:{}, pkt_77:{}, pkt_95:{}, 96:{}, pkt_97:{}, pkt_98:{}, pkt_drop:{}",
    getPrintableSessionID(),
    PersonS2CUnknown, PersonC2SUnknown, PersonC2S_pkt_tcp, PersonS2C_pkt_tcp,
    Person_pkt_75, Person_pkt_76, Person_pkt_77,
    Person_pkt_95, Person_pkt_96, Person_pkt_97,
    Person_pkt_98, Person_pkt_drop);
}

void WxcsSession<WxcsAudioPerson>::OnRemove()
{
    // bool flag = false;
    // for (auto & person : personMap_) {
    //     if (person.second->getMsisdn() == 0) {
    //         flag = true;
    //         break;
    //     }
    // }
    // if (flag) {
    //     for (auto & person : personMap_) {
    //         person.second->DebugPrint();
    //     }
    // }
    // 相同用户合并
    Combine();
    // 对象数据整理
    DealPersonInfo();
    VerifyCall();
    // 清洗不符合条件数据
    WashData();
    DealSubInfo();
}

void WxcsSession<WxcsAudioPerson>::PreProcess()
{
  int pkt_min = CFG->GetValueOf<int>("SESSION_PACKET_AT_LEAST", 20);
  for (auto it = personMap_.begin(); it != personMap_.end();) {
    if (it->second->S2CTransPackets < pkt_min &&
        it->second->C2STransPackets < pkt_min) {
        it = personMap_.erase(it);
    } else {
        ++it;
    }
  }
}

#if 0
void WxcsSession<WxcsAudioPerson>::Combine()
{
    for (auto & pair : personMap_) {
        bool findflag = false;
        auto & person = pair.second;
        auto id = person->getPersonID();
        auto msisdn = person->getMsisdn();
        auto clientip = person->getClientIP();

        for (auto & elem : real_person_) {
            auto & tmperson = elem.second;
            // 排除自己
            if (tmperson->getPersonID() == id) {
                continue;
            }

            auto tmpmsisdn = tmperson->getMsisdn();
            auto tmpclientip = tmperson->getClientIP();

             // 手机号或者客户端ip 任一 相同就合并
            if (tmpmsisdn == msisdn && tmpmsisdn != 0) {
                *tmperson += *person;
                findflag = true;
                tmperson->UpdateResvInfo(person);

                break;
            }

             // 手机号或者客户端ip 任一 相同就合并
            if (tmpclientip == clientip) {
                *tmperson += *person;
                findflag = true;
                tmperson->UpdateResvInfo(person);

                break;
            }
        }

        if (!findflag) {
            real_person_[id] = person;
            real_person_[id]->UpdateResvInfo(person);
        }


    }
}
#else
void WxcsSession<WxcsAudioPerson>::Combine()
{
    struct MergeKey {
        uint64_t msisdn;
        std::string clientip;

        MergeKey(uint64_t m, const std::string &ip): msisdn(m), clientip(ip) {}

        bool operator==(const MergeKey &other) const {
            if (msisdn != 0 && other.msisdn != 0) {
                return msisdn == other.msisdn;
            }

            return clientip == other.clientip;
        }
    };

    struct MergeKeyHash
    {
        std::size_t operator()(const MergeKey &key) const {
            if (key.msisdn != 0) {
                return std::hash<uint64_t>{}(key.msisdn);
            }

            return std::hash<std::string>{}(key.clientip);
        }
    };

    std::unordered_map<MergeKey, std::shared_ptr<WxcsAudioPerson>, MergeKeyHash> merge_map;

    // PreProcess();

    for (auto &pair : personMap_) {
        auto &person = pair.second;

        MergeKey key(person->getMsisdn(), person->getClientIP());

        auto it = merge_map.find(key);
        if (it != merge_map.end()) {
            *(it->second) += *person;
            it->second->UpdateResvInfo(person);
        } else {
            merge_map[key] = person;
            person->UpdateResvInfo(person);
        }
    }

    real_person_.clear();
    // real_person_.reserve(merge_map.size());
    for (auto &pair : merge_map) {
        auto id = pair.second->getPersonID();
        real_person_[id] = pair.second;
    }
}
#endif

std::string WxcsSession<WxcsAudioPerson>::toStrRecordLine(char sep) const
{
    std::string strLine;        // TBL 记录
    // 写 TBL 头部内容
    CONS_RECORD_FIELD_TEXT(strLine , "wxcs"                                     , sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine , "007"                                      , sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine , time(NULL)                                 , sep); // CapDate

    if(CFG->GetValueOf<int>("SESSION_ID_WXA_ENABLE", 0))
    {
        CONS_RECORD_FIELD_TEXT(strLine , getPrintableSessionId()                    , sep); // SessionID
    }

    CONS_RECORD_FIELD_NUM(strLine,      GetSessionType(),   sep); // SessionType
    CONS_RECORD_FIELD_NUM(strLine,      isvideo_,       sep); // isvideo
    CONS_RECORD_FIELD_NUM(strLine,      isgroup_,       sep); // isgroup
    CONS_RECORD_FIELD_TEXT(strLine,     calling_,       sep); // calling
    CONS_RECORD_FIELD_TEXT(strLine,     called_,        sep); // called
    CONS_RECORD_FIELD_TIME(strLine,     starttime_,     sep); // SessionStartTime
    CONS_RECORD_FIELD_TIME(strLine,     stoptime_,      sep); // SessionStopTime
    CONS_RECORD_FIELD_NUM(strLine,      isanswed_,      sep); // 是否接听
    CONS_RECORD_FIELD_NUM(strLine,      ringtime_,      sep); // 响铃时长
    CONS_RECORD_FIELD_NUM(strLine,      durationtime_,  sep); // SessionDuration
    CONS_RECORD_FIELD_NUM(strLine,      personcount_,   sep); // PersonCount
    CONS_RECORD_FIELD(strLine,          personlist_,    sep); // PersonList
    CONS_RECORD_FIELD(strLine,          pairsucc_,      sep); // SessionDuration


    // 写 TBL 每个人
    int iChecked = 0;    //人数计数器  累加
    for (auto & mulitperson : real_person_) {
        auto & persons = mulitperson.second;
        strLine += persons->toStrRecord(sep);
        iChecked++;
    }
    // 人数不够, TBL 补齐
    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
    {
        strLine += WxcsAudioPerson::toStrBlankRecord(sep);
    }

    return strLine;
}

void WxcsAudioSessionKeeper::ProcessWXPeer(const unsigned char*pdata, int len _U_)
{
    ST_WXPEERS *wx_peer = (ST_WXPEERS *)pdata;

    char clientIP_[64] = {0};
    char peerIP_[64] = {0};
    int af = wx_peer->ip_version == 4 ? AF_INET : AF_INET6;
    std::string clientIP = inet_ntop(af, wx_peer->client_ip.ipv6, clientIP_, 64);
    std::string peerIP = inet_ntop(af, wx_peer->peer_ip.ipv6, peerIP_, 64);


    {
        auto msisdn = wx_peer->trailer.MSISDN;
        bool flag = interestmsisdn_.find(msisdn) != interestmsisdn_.end();
        if (flag) {
            LOG_INTST->warn("[find_phone: get mesage!!!] MSISDN:{}, clientip:{}, peerip:{}, a3_packet:{}",
                msisdn, clientIP, peerIP, (int)wx_peer->a3_count);
        }
    }


    // LOG_DEF->debug("get=>{},{},{}", clientIP, peerIP, wx_peer->location);

    wx_peers_.DealPeerInfo(wx_peer);
}


bool WxcsAudioSessionKeeper::GetPeersInfo(std::string & _clientip, uint64_t _msisdn, TimeRange & _range,  PersonPeersInfo & _peersinfo)
{
    return wx_peers_.GetPeersInfo(_clientip, _msisdn, _range, _peersinfo);
}

