#include "wxcs_def.h"
#include <yaProtoRecord/precord.h>
#include "dpi_protorecord.h"
#ifndef __DPI_WEIXIN__
#define __DPI_WEIXIN__

#define   WX_OK         1
#define   WX_ERROR      0

#define WEIXIN_PORT_0  443
#define WEIXIN_PORT_1  80
#define WEIXIN_PORT_2  8080

#define WEIXIN_NOTHTTP_HEADER_LEN  25
#define WEIXIN_MIN_HEADER_LEN      16
#define WEIXIN_COMMON_LENGTH       4


#define MAX_FIELD_LEN     64
#define CHECK_HTTP_HEADER_LEN   50
#define MAX_HTTP_HEADER_LEN   256
#define WX_HASH_SIZE       200

#define WEIFIN_ONE_PKT_LEN   1440

#define WEIXIN_FILTER_MAX_NUM       10
#define WEIFIN_RANGEEND_MAX_LEN     20

#define WX_DATA_IDENTIFY            0x2afd

#define WEIXIN_FIELD_ARGS(index, type, field_str)   {(index), (type), (field_str), (sizeof(field_str)-1)}		  


typedef enum _em_weixin_type{
    EM_WX_FIRST_CAPTATE,
    EM_WX_LAST_CAPTATE,
    EM_WX_WXF_TOTALLEN,
    EM_WX_WXF_RESV1,
    EM_WX_WXF_RESV2,
    EM_WX_WXF_RESV3,
    EM_WX_WXF_RESV4,
    EM_WX_WXF_MSGLEN,
    EM_WX_WXF_UNKNOWN,
    EM_WX_HTTP_METHOD,
    EM_WX_HTTP_URI,
    EM_WX_HTTP_VERSION,
    EM_WX_HTTP_CONTENTLENGTH,
    EM_WX_VER,
    EM_WX_WEIXINNUM,
    EM_WX_SEQ,
    EM_WX_CLIENTVERSION,
    EM_WX_CLIENTOSTYPE,
    EM_WX_TOUSER,
    EM_WX_WXCHATTYPE,
    EM_WX_NETTYPE,
    EM_WX_APPTYPE,
    EM_WX_LASTIP,
    EM_WX_WXMSGFLAG,
    EM_WX_LOCALNAME,
    EM_WX_SCENE,
    EM_WX_URL,
    EM_WX_FILEID,
    EM_WX_OLD_FILEID,
    EM_WX_FILEMD5,
    EM_WX_FILEKEY,
    EM_WX_FILETYPE,
    EM_WX_FILECRC,
    EM_WX_FILEBITMAP,
    EM_WX_OFFSET,
    EM_WX_TOTALSIZE,
    EM_WX_RAWTOTALSIZE,
    EM_WX_BLOCKMD5,
    EM_WX_RAWFILEMD5,
    EM_WX_FILEDATAMD5,
    EM_WX_DATACHECKMD5,
    EM_WX_DATACHECKSUM,
    EM_WX_FILEURL,
    EM_WX_REFERER_URL,
    EM_WX_COMPRESSTYPE,
    EM_WX_RECVLEN,
    EM_WX_SRCSIZE,
    EM_WX_HASTHUMB,
    EM_WX_NEEDTHUMBFLAG,
    EM_WX_THUMBFLAG,
    EM_WX_THUMBTOTALSIZE,
    EM_WX_THUMBCRC,
    EM_WX_THUMBDATA,
    EM_WX_THUMBURL,
    EM_WX_RAWTHUMBSIZE,
    EM_WX_RAWTHUMBMD5,
    EM_WX_ENCTHUMBCRC,
    EM_WX_HITTYPE,
    EM_WX_EXISTFLAG,
    EM_WX_ENABLEHIT,
    EM_WX_EXISTANCECHECK,
    EM_WX_RANGESTART,
    EM_WX_RANGEEND,
    EM_WX_IMGHEIGHT,
    EM_WX_IMGWIDTH,
    EM_WX_JPEGSCANCOUNT,
    EM_WX_JPEGSCANLIST,
    EM_WX_JPEGCRCLIST,
    EM_WX_MIDIMGLEN,
    EM_WX_MIDIMGRAWSIZE,
    EM_WX_MIDIMGTOTALSIZE,
    EM_WX_MIDIMGCHECKSUM,
    EM_WX_MIDIMGMD5,
    EM_WX_MIDIMGDATA,
    EM_WX_RSPPICFORMAT,
    EM_WX_SETOFPICFORMAT,
    EM_WX_LARGESVIDEO,
    EM_WX_VIDEOFORMAT,
    EM_WX_SMALLVIDEOFLAG,
    EM_WX_ADVIDEOFLAG,
    EM_WX_MP4IDENTIFY,
    EM_WX_DROPRATEFLAG,
    EM_WX_SOURCEFLAG,
    EM_WX_AUTHKEY,
    EM_WX_SAFEPROTO,
    EM_WX_RSAVER,
    EM_WX_RSAVALUE,
    EM_WX_NOCHECKAESKEY,
    EM_WX_CLIENTRSAVER,
    EM_WX_CLIENTRSAVAL,
    EM_WX_BFRSAVER,
    EM_WX_BFRSAVALUE,
    EM_WX_SKEYRESP,
    EM_WX_SKEYBUF,
    EM_WX_AESKEY,
    EM_WX_SESSIONBUF,
    EM_WX_ISOVERLOAD,
    EM_WX_ISGETCDN,
    EM_WX_ISSTOREVIDEO,
    EM_WX_ISRETRY,
    EM_WX_RETRYCNT,
    EM_WX_RETRYSEC,
    EM_WX_X_CLIENTIP,
    EM_WX_X_SNSVIDEOFLAG,
    EM_WX_X_ENCFLAG,
    EM_WX_X_ENCLEN,
    EM_WX_IPSEQ,
    EM_WX_ACCEPTDUPACK,
    EM_WX_WXAUTOSTART,
    EM_WX_SIGNAL,
    EM_WX_REDIRECT,
    EM_WX_REDIRECTFAIL,
    EM_WX_LASTRETCODE,
    EM_WX_RETCODE,
    EM_WX_RETMSG,
    EM_WX_WXMSGSIGNATURE,
    EM_WX_ELST,
    EM_WX_ETL,
    EM_WX_DYNAMICETL,
    EM_WX_SUBSTITUTEFTYPE,
    EM_WX_VIEW,
    EM_WX_FILEDATA,
    EM_WX_FILEID_1,
    EM_WX_FILEID_2,
    EM_WX_FILEID_3,
    EM_WX_FILEID_4,
    EM_WX_PATH_MP4,
    EM_WX_PATH_JPG,
    EM_WX_MD5_MP4,
    EM_WX_MD5_JPG,
    
    EM_WX_PYQ_MUL_URL,
    EM_WX_PYQ_UIN,
    
    EM_WX_REFERER_VERSION,

    EM_WX_U_PROVINCE,
    EM_WX_U_CITY,
    EM_WX_U_ISP,
    EM_WX_TASKID,
    EM_WX_CLI_QUIC_FLAG,
    EM_WX_DOWNPICFORMAT,
    EM_WX_VIDEOCDNMSG,
    EM_WX_ISPCODE,

    EM_WX_TP,
    EM_WX_TOKEN,
    EM_WX_IDX,
    EM_WX_LENGTH, 
    EM_WX_WIDTH,
    
    EM_WX_USERAGENT,
    EM_WX_UNKNOWN_VAL,

    EM_WX_VALUE_ORIGIN,
    EM_WX_DATA,
    EM_WX_RESV1,
    EM_WX_RESV2,
    EM_WX_RESV3,
    EM_WX_RESV4,
    EM_WX_RESV5,
    EM_WX_RESV6,

    
    EM_WX_MAX
}em_weixin_type;

typedef enum _em_wx_relation_type
{
  EM_WX_RELATION_TIME,
  EM_WX_RELATION_RESV_VAL1,
  EM_WX_RELATION_RESV_VAL2,
  EM_WX_RELATION_RESV_1,
  EM_WX_RELATION_RESV_2,
  EM_WX_RELATION_RESV_3,
  EM_WX_RELATION_RESV_4,

  EM_WX_RELATION_MAX
}em_wx_relation_type;


typedef enum _em_wx_relation_field_type
{
    EM_WX_RELA_FIELD_URL,
    EM_WX_RELA_FIELD_FILEURL,
    EM_WX_RELA_FIELD_VIDEOCDNMSG,
    EM_WX_RELA_FIELD_MAX,
}em_wx_relation_field_type;



typedef struct _weixin_info_t {
	uint8_t       *value;
	int64_t       value_len;
    void         (*free)(void *ptr);
}weixin_info_t;


typedef struct _weixin_field_t {
	uint8_t  index;
	uint8_t  type;
	uint8_t  field[MAX_FIELD_LEN];
	uint32_t field_len;
}weixin_field_t;

typedef struct flag_to_name
{
    char   flag[1024];
    char   path[1024];
    char   md5 [16];
    int    size;
} flag_to_name;

enum wxf_type{
    EM_WX_WXF_WEIXIN,
    EM_WX_WXF_WEIXIN_PYQ,
};

#define GQUIC_WX_LINEINFO_MAX   4
typedef struct gquic_wx_session_
{
  uint8_t               cid[GQUIC_WX_LINEINFO_MAX][64];     // cid记录，下标和line_info一一对应
  uint8_t               cnt;    // 一条流中 I/O 复用计数
  weixin_info_t         line_info[GQUIC_WX_LINEINFO_MAX][EM_WX_MAX];
}GquicWXSession;

typedef struct st_weixin_file_session
{
    int                     wxf_type;
    weixin_info_t           line_info[EM_WX_MAX];

    //ST_weixin_moments_Video moments_Video;
    int                     reassemble_type;
    struct flag_to_name     jpg;
    struct flag_to_name     mp4;
    size_t                  time_first;
    size_t                  time_last;
    int                     tid;
    int                     fileid_direction;
    int                     fileid_server_done;
    uint64_t                frist_pkt_timestamp;
    // weixin data
    uint8_t                 wx_direction;   // 记录下行方向
    // 记录文件名，文件md5  文件之间分号分隔，文件 md5 逗号分隔
    // eg  file1,file1md5;file2,file2md5
    char                    file_info[2048]; 
    precord_t               *record;
    // gquic
    GquicWXSession          gquic_session;
} ST_weixin_file_session;

struct wx_data_split
{
    int rangebegin;
    int rangeend;
    int split_total;
};

typedef struct wx_data_info
{
    FILE   *fp;
    char    md5[16];
    char    filepath[256];    // 文件名以及路径
    char    filename[256];    // 文件名
    char    suffix[32];       // 文件后缀名
    char    tmp_filepath[256];  // 文件写完之前，临时文件名
    char    file_path[256];
    int     currsize;
    int     totalsize;
    int     cur_rangestart;
    int     cur_rangeend;

    uint8_t  is_finish;       // 文件写入完成

    struct wx_data_split splits[16];
    uint8_t split_cnt;
}WxDataInfo;


typedef struct wx_relation_info
{
    uint32_t    time;
    uint64_t   wxnum;        // weixin pyq 解析出的 wxnum
    char        wxnum_base64[64];
    uint64_t   url_wxnum;    // url 或者 filedata 中解析出的 wxnum
    char        url_wxnum_base64[64];
    uint64_t   videocdnmsg_wxnum;
    char        videocdnmsg_wxnum_base64[64];

    char        wxnum_hex[64];
    uint8_t     wxvideo_flag;
}WxRealtionInfo;

const char *dissect_wx_url_key(const char *data, int len,const char *key, int *value_len);

int dissect_referer_key_value(weixin_info_t  *line_info);
int dissect_fileurl_key_value(weixin_info_t  *line_info);

int weixin_process_fileid(weixin_info_t *line_info);

uint32_t parse_weixin_packet_info(struct flow_info *flow, int C2S, const uint8_t *wx_payload, const uint32_t payload_len,
        weixin_info_t  *line_info);

int dissect_weixin_relation(struct flow_info * flow, weixin_info_t * line_info);
void init_wxpyq_relation(void);
void init_wx_relation_dissector(void);
void free_info_ref(weixin_info_t *line_info);
int write_weixin_log_pyq(struct flow_info *flow, int direction,  weixin_info_t  *line_info);

#endif
