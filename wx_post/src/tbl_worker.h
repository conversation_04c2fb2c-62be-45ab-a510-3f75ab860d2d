#pragma once
/****************************************************************************************
 * 文 件 名 : tbl_worker.h
 * 项目名称 : 
 * 模 块 名 : TblWorker
 * 功    能 : 每种协议对应一个worker,每个writer管理该协议对应字段的分配方式
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <stdio.h>
#include "tbl_config.h"
#include "tbl_writer.h"
#include "tbl_value.h"

#include <vector>
#include <map>
#include <unordered_map>
#include <set>
#include <string>

class TblWorker
{
public:
    TblWorker(std::string &);
    ~TblWorker();
public:
    void init();
    void work();
    void setTblData(std::vector<std::string> &);

private:
    /* 以下属性为初始化化时赋值 */
    std::string                                    field_dir_;       // 字段表总路径                                                          // 与value有关
    std::string                                    work_mode_;       // 模块工作模式                                                          
    std::string                                    proto_name_;      // 模块包含的协议名称                                                    //  与value有关
    std::string                                    use_field_;       // 配置指定的字段名称                                                    //  与value有关*
    std::string                                    check_field_;     // 实际从字段表中和字段值列表中匹配用的字段名称                            //  与value有关*         *准确的说，这两项中有一项与value有关

    std::set<std::string>                          outputDirs_;      // 匹配工作完成后，得到的输出目录集合
    std::vector<std::pair<std::string, int>>       protoFields_;     // 该协议分配用的字段名，对应字段表和tbl第几个                             //  与value有关

    std::map<std::string, std::set<std::string>>   outputRecoder_;   // 记录字段值范围和输出路径; 格式：v1:dir1,dir2; v2:dir1,dir3
    std::map<std::string, std::set<std::string>>   configRecoder_;   // 记录输出路径和字段值范围; 格式：dir1:v1,v2; dir2:v1; dir3:v2

    uint32_t                                       size_;            // 该协议对应的字段值数目                                                //  与value有关
    /* 以下属性为运行时赋值 */
    std::vector<std::string>                       tbl_data_queue_;  // tbl数据存储队列


private:
    int  init_workmode(std::string &work_mode);
    int  init_fieldcheck();
    bool tbl_check();

    int  load_field();

private:
//  这里传入得data是每一行的原始tbl数据

    int   match_work2  (std::string &);

    int   range_work1  (std::string &);

    int   contains_work(std::string &);

    int   not_match_work(std::string &);

private:
    TblFieldValue* p_FieldValue;
    TblWriter      writer_;

    std::function<int(std::string &)> work_func;
    std::function<int(std::string &)> field_value_check;

};


/********************************************************************
   先对tbl字段值数据进行检测
   然后开始对特定tbl值进行检测，正确的输出到正确的路径，
 
 
 
 
 ********************************************************************/