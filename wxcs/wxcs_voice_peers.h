/****************************************************************************************
 * 文 件 名 : wxcs_voice_peers.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-05-10
* 编    码 : chenzq         '2021-05-10
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_VOICE_PEERS_H
#define _WXCS_VOICE_PEERS_H

#include <memory>
#include <unordered_map>
#include <mutex>
#include <set>
#include <map>

#include "wxcs_def.h"

#include "wxcs_utils.h"
#include "wxcs_logger.h"

struct TimeRange
{
    uint32_t begin;
    uint32_t end;
};

struct PortAttr
{
    uint32_t begin;
    uint32_t end;
};

struct PersonPeersInfo
{
    std::string peerlist;       // 对端 ip 列表
    uint8_t ipcount = 0;        // 对端 ip 数量
    std::string privateip;      // 内网 ip
    std::string publicip;       // 公网 ip
    std::string ipfor4G;        // 4G ip
    std::string iptype;         // ip 类型集合
    std::string country;        // 国家集合
    std::string province;       // 省份集合
    std::string a3list;         // 对端 ip  a3 数量统计列表
    uint16_t a3totoal = 0;      // a3 总量
    std::string ipport;

    std::string ttl;
};

/* wxcs中每个用户信息 */
struct WxpeerInfo
{
    uint32_t    timestamp       = 0; //用户活跃
    uint32_t    begin_time      = 0;
    uint32_t    end_time        = 0;
    uint32_t    count           = 0;
    uint8_t     ip_type         = 0;
    uint16_t    a3count         = 0;
    uint64_t    msisdn          = 0;
    std::string country;
    std::string location;
    std::string province;
    std::string clientip;
    std::string peerip;

    std::string privateip;
    std::string publicip;
    std::string ipfor4G;
    // std::set<uint16_t> peerports;
    std::map<uint16_t, PortAttr> peerports;

    std::string ttl;
} ;


void PeersInfoConcat(std::unordered_map<std::string, WxpeerInfo> &, PersonPeersInfo &);

enum class FindType
{
    client_peer = 0,
    client_no_peer,
    no_client,
    msidn_peer,
    msidn_no_peer,
    no_msisdn,
    undefine
}; 

class WXPeers
{
    using PublicIP = std::unordered_map<std::string, WxpeerInfo>;
    using MsisdnList = std::unordered_map<uint64_t, PublicIP>;
    using ClientList = std::unordered_map<std::string, PublicIP>;
public:
    WXPeers() = default;
    ~WXPeers() = default;
public:
    std::shared_ptr<WxpeerInfo> getPeerInfo(std::string _clientIP, std::string _peerIp);
    inline void EraseClient(std::string & _clientip) { clientList_.erase(_clientip); }

    bool GetPeersInfo(std::string & _clientip, TimeRange & _range,  PersonPeersInfo & _peersinfo);
    bool GetPeersInfo(std::string & _clientip, uint64_t _msisdn, TimeRange & _range,  PersonPeersInfo & _peersinfo);

    void    DealPeerInfo(ST_WXPEERS * _wxpeers);
    bool    InsertClient(ST_WXPEERS * _wxpeers);
    bool    UpdateClient(ST_WXPEERS * _wxpeers);
    bool    InsertMsisdn(ST_WXPEERS * _wxpeers);
    bool    UpdateMsisdn(ST_WXPEERS * _wxpeers);
    FindType  FindClientPeer(std::string, std::string);
    FindType  FindMsisdnPeer(uint64_t, std::string);

private:
    ClientList clientList_;         // 以客户端 ip 为 key 的对端 ip 存储列表
    MsisdnList msisdnlist_;         // 以手机号为 key 的对端 ip 存储列表
    uint64_t    ipv4cnt_ = 0;       // ip 列表中 v4 对端占比
    uint64_t    ipv6cnt_ = 0;       // ip 列表中 v6 对端占比
    uint64_t    msisdn_v4cnt_ = 0;  // msisdn 列表中 v4 对端占比
    uint64_t    msisdn_v6cnt_ = 0;  // msisdn 列表中 v6 对端占比
    uint64_t    timestamp_ = 0; 
    std::mutex mtx_;
};

#endif