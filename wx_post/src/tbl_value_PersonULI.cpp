#include "tbl_value_PersonULI.h"
#include "tbl_config.h"
#include "tbl_logger.h"
#include "tbl_utils.h"
#include <iostream>

TblValuePersonULI::TblValuePersonULI():TblFieldValue("RT_ULI")
{
    
}

TblValuePersonULI::TblValuePersonULI(const std::string& field_name):TblFieldValue(field_name)
{
    
}

TblValuePersonULI::TblValuePersonULI(const std::string& field_name, const std::string& etr_field):TblFieldValue(field_name)
{
    extra_field = etr_field;
}

void TblValuePersonULI::setFieldAndProto(const std::string & field_name, const std::string & proto_name)
{
    std::string sub_field_name_ = field_name.find('.') != std::string::npos ? field_name.substr(field_name.find_first_of('.') + 1) : field_name;  

    if(sub_field_name_ != field_name && sub_fields.find(sub_field_name_) != sub_fields.end())
    {
        //std::cout << "field_name" << field_name << ","<< "field_name_"<< field_name_ <<","<< "sub_field_name_"<< sub_field_name_ << std::endl;
        proto_subfield_map.emplace(proto_name, sub_field_name_);
    }

    proto_format_map.emplace(proto_name, getMatchFormat(proto_name));
}

std::string TblValuePersonULI::getMatchFormat(const std::string &proto) 
{
    std::string && t = CFG.getCurrentConfigValue({"work_conf", proto, "field_name"}, std::string());
    if(strBeginwith(t,field_name_))
    {
        return CFG.getCurrentConfigValue({"work_conf", proto, "field_type"}, std::string(""));
    }
    if(!base_format.empty())
    {
        return base_format.begin()->first;
    }
    return "";
}

std::string TblValuePersonULI::getRequestFieldValue(std::string &proto, std::string &data, int index) 
{

    const std::string & raw_value = getStrNthChar(data, '|', index);                   // 获取原始值
    const std::string & RT_BS_str = getStrNthChar(data, '|', field_index_map[index]);  // 获取指定子字段的字段值
    // if(!raw_value.empty())
    // {
    //     std::cout << raw_value <<','<< index << "   "<< RT_BS_str <<"   " << field_index_map[index]<< std::endl;
    // }
    std::string use_type_         = "";
    std::map<std::string, std::string>           sub_values;

    if(proto_format_map.find(proto) != proto_format_map.end())
    {
        use_type_= proto_format_map[proto];
    } 

    if(proto_subfield_map.find(proto) == proto_subfield_map.end())                           // 如果没有子字段，则返回字段原始值
    {
        return raw_value;
    }

    if(format_check(use_type_, raw_value))                                                   // 对原始值进行检测
    {
        if(use_type_ == "string")                                
        {
            std::string::size_type subfield_index = raw_value.find(proto_subfield_map[proto]);

            if(subfield_index != std::string::npos)            
            {
                subfield_index = subfield_index + proto_subfield_map[proto].size() + 1;
                
                for(std::string::size_type i = subfield_index; i < raw_value.size(); ++i)
                {
                    if(raw_value[i] == ',')
                    {
                        LOG_DEF->info("this is a string format tbl!");
                        return raw_value.substr(subfield_index, i - subfield_index);          // 返回解析出的字段值
                    }
                }               
            }
            return raw_value;  
        }
        else if (use_type_ == "hex")
        {
            uint32_t ULI_le = strtol(raw_value.c_str(), NULL, 0); 
            uint32_t RT_BS  = strtol(RT_BS_str.c_str(), NULL, 0); 
            //std::cout << "ULI: "<<  ULI_le << " BS: " <<  RT_BS << std::endl;                                                         

            switch (RT_BS & 0x0F)
            {
                case 0xd:
                    // ULI:0xabcdefg0: 0xabcde eNodeBid, 0xfg CellId
                    // 4G: hex,eNodeBid:xxxx,CellId:xxx
                    //std::cout << "4G";
                    sub_values["eNodeBid"] = std::to_string(ULI_le >> 12);
                    sub_values["CellId"]   = std::to_string((ULI_le >> 4) & 0xff);
                    break;

                case 0xc:
                    // ULI:0xabcdefgh: 0xabcd LAC, 0xefgh SAC
                    // 3G: hex,LAC:xxxx,SAC:xxxx
                    //std::cout << "3G";
                    sub_values["LAC"] = std::to_string(ULI_le >> 16);
                    sub_values["SAC"] = std::to_string(ULI_le & 0xffff);
                    break; 

                default:
                    return "";
            }
            //std::cout << proto <<":" << proto_subfield_map[proto] <<":" << sub_values[proto_subfield_map[proto]] << std::endl;
            return sub_values[proto_subfield_map[proto]];
        }
        else
        {
            return raw_value;
        }
    }
    return "";    
}

void TblValuePersonULI::setFieldTable(std::vector<std::pair<std::string, int>> &raw_list)          // 获取字段表中指定字段名下标，以便后面获取
{
    std::string tmp_field     = "";
    int tmp_field_index       = -1;
    std::string tmp_ext_field = "";
    int tmp_field_ext_index   = -1;

    for(auto &it: raw_list)
    {
        if(strBeginwith(it.first, field_name_))
        {
            tmp_field       = it.first;
            tmp_field_index = it.second;
            // << "main"<<tmp_field << std::endl;
        }
        if(strBeginwith(it.first, extra_field))
        {
            tmp_ext_field       = it.first;
            tmp_field_ext_index = it.second;
            //std::cout << "ext" <<tmp_ext_field << std::endl;
        }
        if((!tmp_field.empty() && !tmp_ext_field.empty()) && (tmp_field.back() == tmp_ext_field.back() || (isalpha( tmp_field.back()) && isalpha(tmp_ext_field.back()))))
        {
            // << tmp_field<<":"<<tmp_field_index << ",     "<<tmp_ext_field <<":"<< tmp_field_ext_index<< std::endl;

            field_index_map.emplace(tmp_field_index, tmp_field_ext_index);
            tmp_field.clear();
            tmp_ext_field.clear();
            tmp_field_index     = -1;
            tmp_field_ext_index = -1;
        }
    }
}

void TblValuePersonULI::destoryItem() //         此函数重写，不得销毁！
{


}