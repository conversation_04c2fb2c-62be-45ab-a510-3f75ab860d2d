/****************************************************************************************
 * 文 件 名 : libwxc.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019.01.03
* 编    码 : zhengsw      '2019.01.03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <stddef.h>

#include "muduo/net/TcpClient.h"
#include "muduo/net/EventLoop.h"
#include "muduo/net/EventLoopThread.h"
#include <boost/bind.hpp>
#include <iostream>

#include <wxcs_def.h>
#include "wxcs_types.h"

using namespace boost;
using namespace muduo;
using namespace muduo::net;

struct ST_wxc_context
{
    muduo::net::TcpClient       *pClient_;
    muduo::net::EventLoop       *pEloop_;
    muduo::net::EventLoopThread *pLoopThread_;
    muduo::net::TcpConnectionPtr conn_;

public:
    void start()
        {
                pClient_->enableRetry();    // 启用自动重连
                pClient_->connect();
        }

    void onConnection(const muduo::net::TcpConnectionPtr& conn)
        {
            if (conn->connected())
            {
                conn_ = conn;
                conn->setTcpNoDelay(true);

                std::cout<<"connected to server!!"<<std::endl;
            }
            else if (conn->disconnected())
            {
                std::cout<<"closed the conn and begin to reconnect." << std::endl;
            }
        }

    void onClose()
        {
            delete pClient_;
        }

public:
    int sendMsg(Buffer *pBuff)
    {
        if (conn_) {
            conn_->send(pBuff);
        }
        return 0;
    }
};

int wxc_init(wxc_handle *pHandle, char *strServerIp, uint16_t port)
{
    ST_wxc_context *pCtx = new ST_wxc_context;
    pCtx->pLoopThread_   = new EventLoopThread(NULL, "wxc_client");
    pCtx->pEloop_        = pCtx->pLoopThread_->startLoop();
    pCtx->pClient_       = new TcpClient(pCtx->pEloop_,
                                         InetAddress(strServerIp, port),
                                         "WxcsClient");                     // 最大重连间隔

    pCtx->pClient_->setConnectionCallback(std::bind(&ST_wxc_context::onConnection, pCtx, std::placeholders::_1));
    pCtx->pEloop_->runAfter(0, bind(&ST_wxc_context::start, pCtx));

    *pHandle = pCtx;
    return 0;
}

int wxc_fini(wxc_handle handle)
{
    ST_wxc_context *pCtx = handle;

    pCtx->pEloop_->runInLoop(bind(&ST_wxc_context::onClose, pCtx));

    delete pCtx->pLoopThread_;
    delete pCtx;

    return 0;
}

int wxc_sendMsg(wxc_handle handle, const unsigned char *pdata, int len, int msgType)
{
    ST_wxc_context *pCtx = handle;
    ST_wxcsProtoHdr hdr;

    hdr.magic[0] = WXCS_PROTO_MAGIC_A;
    hdr.magic[1] = WXCS_PROTO_MAGIC_B;
    hdr.msgLen   = len + sizeof hdr;
    hdr.msgType  = msgType;

    Buffer buffer;
    buffer.append(&hdr,   sizeof hdr);
    buffer.append(pdata, len);

    return pCtx->sendMsg(&buffer);
}
