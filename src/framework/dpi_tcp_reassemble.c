/****************************************************************************************
 * 文 件 名 : dpi_tcp_reassemble.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdlib.h>
#include <string.h>
#include <rte_mempool.h>

#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"

extern struct rte_mempool *tcp_reassemble_mempool;

#if 0
int tcp_reassemble_add_item(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq, const uint8_t *payload, const uint16_t payload_len)
{
    uint8_t f_add;
    uint16_t i;
    uint16_t rsm_num;
    uint16_t rsm_len;
    struct tcp_reassemble *pos;
    struct tcp_reassemble *new_item;

    if (payload_len == 0)
        return 0;
    
    rsm_num = (payload_len - 1) / TCP_PAYLOAD_MAX_LEN + 1;

    for (i = 0; i < rsm_num; i++) {
        f_add = 0;
        if (i < rsm_num - 1)
            rsm_len = TCP_PAYLOAD_MAX_LEN;
        else
            rsm_len = payload_len - (rsm_num - 1) * TCP_PAYLOAD_MAX_LEN;
        
        if (rte_mempool_get(tcp_reassemble_mempool, (void **)&new_item) < 0) {
            DPI_LOG(DPI_LOG_WARNING, "not enough memory: tcp_reassamble_mempool");
            return -1;
        }
        
        new_item->seq = seq + i * TCP_PAYLOAD_MAX_LEN;
        new_item->payload_len = rsm_len;
        memcpy(new_item->payload, payload + i * TCP_PAYLOAD_MAX_LEN, new_item->payload_len);
        INIT_LIST_HEAD(&new_item->node);

        list_for_each_entry(pos, head, node) {
            if (pos->seq == new_item->seq) {
                f_add = 1;
                rte_mempool_put(tcp_reassemble_mempool, (void *)new_item);
                break;
            } else if (pos->seq < new_item->seq) {
                f_add = 1;
                list_add(&new_item->node, pos->node.prev);
                *rsm_total_len += new_item->payload_len;
                break;
            }
        }
        if (f_add == 0) {
            list_add(&new_item->node, pos->node.prev);
            *rsm_total_len += new_item->payload_len;
        }
    }
    
    return 0;
}
#else
int tcp_reassemble_add_item(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq, const uint8_t *payload, const uint16_t payload_len)
{
    uint8_t f_add;
    uint16_t i;
    uint16_t rsm_num;
    uint16_t rsm_len;
    struct tcp_reassemble *pos;
    struct tcp_reassemble *new_item;
    uint32_t pre_seq=0;

    if (payload_len == 0)
        return 0;
    
    rsm_num = (payload_len - 1) / TCP_PAYLOAD_MAX_LEN + 1;

    for (i = 0; i < rsm_num; i++) {
        f_add = 0;
        if (i < rsm_num - 1)
            rsm_len = TCP_PAYLOAD_MAX_LEN;
        else
            rsm_len = payload_len - (rsm_num - 1) * TCP_PAYLOAD_MAX_LEN;
        
        if (rte_mempool_get(tcp_reassemble_mempool, (void **)&new_item) < 0) {
            DPI_LOG(DPI_LOG_WARNING, "not enough memory");
            return -1;
        }
        
        new_item->seq = seq + i * TCP_PAYLOAD_MAX_LEN;
        new_item->payload_len = rsm_len;
        memcpy(new_item->payload, payload + i * TCP_PAYLOAD_MAX_LEN, new_item->payload_len);
        INIT_LIST_HEAD(&new_item->node);

        list_for_each_entry(pos, head, node) {
            /* 丢掉完全重复报文和重复子报文 */
            if (pos->seq == new_item->seq && pos->payload_len >= new_item->payload_len) {
                //printf("[1]--完全重复报文或子报文\n");
                f_add = 1;
                rte_mempool_put(tcp_reassemble_mempool, (void *)new_item);
                break;
            } 
            /* seq相同，但是长度比原队列长 */
            else if(pos->seq == new_item->seq && pos->payload_len < new_item->payload_len) {
                f_add = 1;
                if((pre_seq==0 )|| (pre_seq!=0 && pre_seq>=new_item->seq+new_item->payload_len)){
                    pos->payload_len=new_item->payload_len;
                    memcpy(pos->payload,new_item->payload,new_item->payload_len);
                }
                rte_mempool_put(tcp_reassemble_mempool, (void *)new_item);
                break;
            }
            /* 丢掉部分重复报文，且该报文属于pos的一部分*/
            else if(new_item->seq > pos->seq && new_item->seq < pos->seq+pos->payload_len
                     && new_item->seq+new_item->payload_len <= pos->seq+pos->payload_len ){
                f_add = 1;
                rte_mempool_put(tcp_reassemble_mempool, (void *)new_item);
                break;
            }
            /* 该报文seq>pos, 长度在报文之外*/ 
            else if(new_item->seq > pos->seq && new_item->seq<pos->seq+pos->payload_len
                     && new_item->seq+new_item->payload_len > pos->seq+pos->payload_len){
                f_add = 1;
                uint32_t repeat_len=pos->seq+pos->payload_len-new_item->seq;
                new_item->seq+=repeat_len;
                new_item->payload_len-=repeat_len;
               if( (pre_seq==0) || (pre_seq!=0 && pre_seq >= new_item->seq+new_item->payload_len)){
                    memmove(new_item->payload, (char*)&new_item->payload+repeat_len, new_item->payload_len);
                    list_add(&new_item->node, pos->node.prev);
                    *rsm_total_len +=  new_item->payload_len;
                }else{
                    rte_mempool_put(tcp_reassemble_mempool, (void *)new_item);
                }
                break;
            }
            else if (pos->seq < new_item->seq) {
                f_add = 1;
                list_add(&new_item->node, pos->node.prev);
                *rsm_total_len += payload_len;
                break;
            }

            pre_seq = pos->seq;
        }
        if (f_add == 0) {
            list_add(&new_item->node, pos->node.prev);
            *rsm_total_len += payload_len;
        }
    }
    
    return 0;
}
#endif

int tcp_reassemble_do_guesslen(struct list_head *head, uint32_t *result_len, uint32_t *expect_len)
{
    struct tcp_reassemble *pos;
    
    //uint32_t remainlen = *result_len;
    uint32_t index = 0;

    uint32_t pkt_count=0;
    uint32_t total_len=0,first_seq=0;

    list_for_each_entry_reverse(pos, head, node){
        if(pkt_count==0){
            first_seq=pos->seq;
            pkt_count=1;
        }
        index += pos->payload_len;
        total_len = pos->seq-first_seq+pos->payload_len;
    } 
    
    *result_len = index;
    *expect_len = total_len;
    
    return 0;
}


/*
int tcp_reassemble_less_than_seq(struct list_head *head, uint32_t seq, uint8_t *result, uint32_t *result_len)
{
    struct tcp_reassemble *pos;

    uint32_t remainlen = *result_len;
    uint32_t index = 0;
        
    list_for_each_entry_reverse(pos, head, node) {
        if (pos->seq > seq)
            break;
        if (remainlen >= pos->payload_len) {
            memcpy(result + index, pos->payload, pos->payload_len);
            index += pos->payload_len;
            remainlen -= pos->payload_len;
        } else 
            break;
    }

    *result_len = index;
    return 0;
}
*/

int tcp_reassemble_free_less_than_seq(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq)
{
    struct tcp_reassemble *pos;
    struct tcp_reassemble *n;
    
    *rsm_total_len = 0;

    if (head == NULL)
        return 0;
    
    list_for_each_entry_safe(pos, n, head, node) {
        if (pos->seq < seq) {
            list_del(&pos->node);
            rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
        } else {
            *rsm_total_len += pos->payload_len;
        }
    }
    
    return 0;
}

int tcp_reassemble_do(struct list_head *head, uint8_t *result, uint32_t *result_len)
{
    struct tcp_reassemble *pos = NULL;
    struct tcp_reassemble *prev = NULL;

    uint32_t remainlen = *result_len;
    uint32_t index = 0;
        
    list_for_each_entry_reverse(pos, head, node) {
        prev = list_entry(pos->node.prev, typeof(*prev), node);
        if (remainlen >= pos->payload_len) {
            memcpy(result + index, pos->payload, pos->payload_len);
            index += pos->payload_len;
            remainlen -= pos->payload_len;
            if (prev && &prev->node != head && pos->seq + pos->payload_len != prev->seq) {
                index = 0;
                break;
            }
        } else {
            break;
        }
    }

    *result_len = index;
    return 0;
}


int tcp_reassemble_do_surport_miss(struct list_head *head, uint8_t *result, uint32_t *result_len)
{
    struct tcp_reassemble *pos;

    uint32_t remainlen = *result_len;
    uint32_t index = 0;
        
    list_for_each_entry_reverse(pos, head, node) {
        if (remainlen >= pos->payload_len) {
            memcpy(result + index, pos->payload, pos->payload_len);
            index += pos->payload_len;
            remainlen -= pos->payload_len;
        } else 
            break;
    }

    *result_len = index;
    return 0;
}





int tcp_reassemble_free(struct list_head *head, uint32_t *rsm_total_len)
{
    struct tcp_reassemble *pos;
    struct tcp_reassemble *n;

    if (head == NULL)
        return 0;
    
    list_for_each_entry_safe(pos, n, head, node) {
        list_del(&pos->node);
        rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
    }
    *rsm_total_len = 0;

    return 0;
}


