/****************************************************************************************
 * 文 件 名 : wxcs_config.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_CONFIG_H_
#define _WXCS_CONFIG_H_

#include <bits/stringfwd.h> // For string forward declarations.
#include <stdint.h>

#include <vector>

typedef struct _dictionary_  dictionary ;


#define CFG                     WxcsConfig::GetInstance()

typedef const char *            CSTR;

class WxcsConfig
{
public:
    static WxcsConfig *GetInstance();

public:
    int prepareEnv();

    int ParseConfig(const char *configFile, int argc, char *argv[]);

public:
    // 注意：
    // 当配置文件不存在，或者 parse 出错时，会返回相应类型的默认值：
    // int 为 0
    // const char * 为 NULL
    // bool 为 false
    template <typename T>
        T GetValueOf(const std::string &strConfigName, T t = T())
        {
            if (nullptr ==  ini_)
            {
                return t;
            }

            return GetValueOf_inner<T>(toGlobalConfigName(strConfigName), t);
        }

public:
    int ParseConfigFile(const std::string &strConfigFilePath);

    int ParseCmdLineOpts(int argc, char *argv[]);

public:
    bool OnHelpMode()
    {
        return bHelpMode_;
    }

    bool logToTerminal()
    {
        return bLogToTerminal_;
    }

    const std::vector<uint64_t> &getInterestingMsisdn()
    {
        return vecInterestingMsisdn_;
    }
    const std::vector<uint64_t> &getInterestingImsi()
    {
        return vecInterestingImsi_;
    }
    const std::vector<uint64_t> &getInterestingImei()
    {
        return vecInterestingImei_;
    }

private:
    WxcsConfig();
    ~WxcsConfig();

private:
    const std::string toGlobalConfigName(const std::string &strConfigName);
    template <typename T>
    T GetValueOf_inner(const std::string &strConfigName, T def);

private:
    dictionary  *ini_;

private:
    bool bHelpMode_;
    bool bLogToTerminal_;
    std::vector<uint64_t> vecInterestingMsisdn_;
    std::vector<uint64_t> vecInterestingImsi_;
    std::vector<uint64_t> vecInterestingImei_;
};

#endif /* _WXCS_CONFIG_H_ */
