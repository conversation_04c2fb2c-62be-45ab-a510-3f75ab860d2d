/****************************************************************************************
 * 文 件 名 : version.h.in
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-11-08
* 编    码 : root      '2018-11-08
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _VERSION_H_
#define _VERSION_H_

#define _STR(x) #x
#define _VER_STR(a, b, c, d)        _STR(a.b.c.d)

#define PROJECT_VERSION_MAJOR       ${PROJECT_VERSION_MAJOR}
#define PROJECT_VERSION_MINOR       ${PROJECT_VERSION_MINOR}
#define PROJECT_VERSION_RELEASE     ${PROJECT_VERSION_RELEASE}
#define PROJECT_VERSION_BUILD       ${PROJECT_VERSION_BUILD}

#define PROJECT_VERSION_STR         _VER_STR(PROJECT_VERSION_MAJOR,     \
                                             PROJECT_VERSION_MINOR,     \
                                             PROJECT_VERSION_RELEASE,   \
                                             PROJECT_VERSION_BUILD)

#define PROJECT_VERSION_DATE_STR    PROJECT_VERSION_STR " build at " __DATE__

#endif /* _VERSION.H_H_ */
