/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#define _GNU_SOURCE         /* See feature_test_macros(7) */
#include <sys/sysinfo.h>

#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <libgen.h>

#include <ctype.h>
#include <stdio.h>
#include <event2/bufferevent.h>
#include <glib.h>

#include "iniparser/dictionary.h"
#include "iniparser/iniparser.h"
#include "wx_proxy_conf.h"
#include "wx_proxy_log.h"
#include "wx_proxy_common.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy.h"

/*****************************************************************
*Function    : skip_empty
*Description : skip of the empty byte.
*Input       : string end with '\0'
*Output      : none
*Return      : pointer to string
*Others      : none
*****************************************************************/
static const char*
skip_empty(const char*str)
{
    while(isspace(*str))
    {
        str++;
    }
    return str;
}

/*****************************************************************
*Function    : parse_addr
*Description : parse string to socketaddr, string example: "127.0.0.1:1080 *******:53,***********:80"
*Input       : string
*Output      : socketaddr
*Return      : [0: OK, others: ERROR]
*Others      : none
*****************************************************************/
static int
parse_addr(const char *addr, struct sockaddr *sa)
{
    int                ret;
    int                sa_len;

    addr = skip_empty(addr);

    sa_len = sizeof(*sa);
    ret = evutil_parse_sockaddr_port(addr, sa, &sa_len);
    if(0 != ret)
    {
        log_write(ERROR, "Fuck addr %s", addr);
        pexit(EXIT_FAILURE);
    }
    return 0;
}

#if 0
/*****************************************************************
*Function    : parse_log_server
*Description : parse string to socketaddr, string example: "127.0.0.1:1080"
*Input       : string
*Output      : socketaddr
*Return      : [0: ok, others: ERROR]
*Others      : none
*****************************************************************/
static int
parse_log_server(const char *ip_port, struct sockaddr *sa)
{
    int sa_len;
    int ret;

    if(ip_port && strlen(ip_port) > 9)
    {
        sa_len = sizeof(*sa);
        ret = evutil_parse_sockaddr_port(ip_port, sa, &sa_len);
        what(0 != ret, return -1, "Fuck addr %s", ip_port);
        return 0;
    }
    return -1;
}
#endif

/*****************************************************************
*Function    : parse_thread_num
*Description : set work thread num, if 0 is set, cpu num is thread num
*Input       : [0 ~ 64]
*Output      : real value of thread num
*Return      : [0: OK, others: ERROR]
*Others      : none
*****************************************************************/
static int
parse_thread_num(int num, int *value)
{
    if(num < 0)
    {
        return -1;
    }

    if(num > 64)
    {
        return -1;
    }

    if(0 == num)
    {
        *value = 2 * get_nprocs() + 1; // +1 for master
    }
    else
    {
        *value = num;
    }

    return 0;
}

/*****************************************************************
*Function    : parse_int_limit_en
*Description : check value not equal  between min and  max
*Input       : value, out, limits
*Output      : none
*Return      : [0: OK, others: ERROR]
*Others      : none
*****************************************************************/
static int
parse_int_limit_en(int in, int *out, int min, int max)
{
    if(in < min)
    {
        return -1;
    }

    if(in > max)
    {
        return -1;
    }

    *out = in;
    return 0;
}

/*****************************************************************
*Function    : parse_int_limit_eq
*Description : check value equal  between min and  max
*Input       : value, out, limits
*Output      : none
*Return      : [0: OK, others: ERROR]
*Others      : none
*****************************************************************/
static int
parse_int_limit_eq(int in, int *out, int min, int max)
{
    if(in <= min)
    {
        return -1;
    }

    if(in >= max)
    {
        return -1;
    }

    *out = in;
    return 0;
}

/*****************************************************************
*Function    : proxy_conf_parse
*Description : parse conf from file
*Input       : file path
*Output      : conf
*Return      : [pointer of conf, EROOR is NULL]
*Others      : none
*****************************************************************/
struct conf*
proxy_conf_parse(const char *file, struct conf* conf)
{
    dictionary        *d;
    const char        *result;
    int                value;
    int                ret;

    d = iniparser_load(file);
    what(NULL == d, pexit(-1), "ERROR: access file:[%s]", file);

    /**** Get Server Pool *****/
    value = iniparser_getint(d,     ":LISTEN", 0);
    conf->listen = value;

    /**** Get Server Pool *****/
    result = iniparser_getstring(d, ":SLAVE_1", "");
    ret = parse_addr(result, &conf->slave_1);

    result = iniparser_getstring(d, ":SLAVE_2", "");
    ret = parse_addr(result, &conf->slave_2);

    /**** Get Log parameters ****/
    value = iniparser_getint(d,     ":LOG_LEVEL", 0);
    ret = parse_int_limit_eq(value, &conf->log_level, LOG_MIN, LOG_MAX);
    what(0 != ret, pexit(-1) ,"ERROR: LOG Level=%d", value);

    value = iniparser_getint(d,     ":LOG_SIZE", 10);
    ret = parse_int_limit_en(value*1024*1024, &conf->log_size, 1024*1024*1, 1024*1024*1025);// 1MB ~ 1G
    what(0 != ret, pexit(-1) ,"ERROR: LOG_SIZE=%dMB", value);

    value = iniparser_getint(d,     ":LOG_NUM", 3);
    ret = parse_int_limit_en(value, &conf->log_num, 1, 20);
    what(0 != ret, pexit(-1) ,"ERROR: LOG_SIZE=%d", value);

    result = iniparser_getstring(d, ":LOG_FILE", "wx_proxy.log");
    snprintf(conf->log_filepath, sizeof(conf->log_filepath), "%s", result);

    /**** Get thread  parameters ****/
    value = iniparser_getint(d,     ":WORK_THREAD_NUM", 0);
    ret = parse_thread_num(value, &conf->thread_num);
    what(0 != ret, pexit(-1), "ERROR: WORK_THREAD_NUM=%d", value);

    /**** Release iniparser handler ****/
    iniparser_freedict(d);

    return conf;
}

