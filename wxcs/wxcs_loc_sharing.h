/****************************************************************************************
 * 文 件 名 : wxcs_loc_sharing.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-12-09
* 编    码 : chenzq         '2021-12-09
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#ifndef _WXCS_LOC_SHARING_H
#define _WXCS_LOC_SHARING_H

#include <memory>

#include "wxcs_voice_peers.h"
#include "wxcs_session_keeper.h"


/* WxcsAudioPerson */
class WxcsLocSharingPerson : public WxcsPerson
{
public:
    WxcsLocSharingPerson() {}
    WxcsLocSharingPerson(ST_WXLocSharing *pAliveMsg);

public:
    std::string toStrRecord(char sep) const;
    static  std::string toStrBlankRecord(char sep);

    inline uint32_t     GetStartTime() { return this->begintime_; }
    inline uint32_t     GetEndTime() { return this->endtime_; }

    // void  UpdateResvInfo(std::shared_ptr<WxcsAudioPerson> person);
public:
    uint32_t    c2spkts_;
    uint32_t    s2cpkts_;
    uint32_t    begintime_;
    uint32_t    endtime_;

    std::string resv00_;
    std::string resv01_;
};


template<>
class WxcsSession<WxcsLocSharingPerson> : public WxcsSessionBase<WxcsLocSharingPerson>
{
public:
    using WxcsSessionBase<WxcsLocSharingPerson>::WxcsSessionBase;

public:
    std::string toStrRecordLine(char sep) const;

public:
    std::set<uint64_t>      interestingmsisdn_;
    std::set<uint64_t>      interestingimsi_;
    std::set<uint64_t>      interestingimei_;

private:
    mutable uint8_t         isgroup_ = 0;
    mutable std::string     personlist_;
    mutable uint8_t         personcnt_;
    mutable uint32_t        begintime_ = 0;
    mutable uint32_t        endtime_ = 0;
    mutable uint16_t        duration_;
    mutable std::string     resv00_;
};


class WxcsLocSharingKeeper : public WxcsSessionKeeper<WxcsLocSharingPerson>
{
public:
    ~WxcsLocSharingKeeper() {}
    WxcsLocSharingKeeper(const WxcsLocSharingKeeper &) = delete;
    WxcsLocSharingKeeper & operator=(const WxcsLocSharingKeeper &) = delete;

    static std::shared_ptr<WxcsLocSharingKeeper> & GetInstance() {
        static std::shared_ptr<WxcsLocSharingKeeper> keeper(new WxcsLocSharingKeeper);
        return keeper;
    }

private:
    WxcsLocSharingKeeper() 
      : WxcsSessionKeeper<WxcsLocSharingPerson>(CFG->GetValueOf<int>("WXLS_SESSION_TIMEOUT_IN_SECOND", 90),
          CFG->GetValueOf<int>("WXLS_HASH_TABLE_SIZE", 5 * 1024 * 1024)) {

        // LOG_DEF->info("sessionActiveDiffTimeMax: {}", sessionActiveDiffTimeMax_);
        // LOG_DEF->info("wxqqs hash table size : {}", size);

        // 把配置文件中的 MSISDN到 监视的 MSISDN
        for (auto msisdn : CFG->getInterestingMsisdn()) {
            interestmsisdn_.emplace(msisdn);
        }
    }

public:
    void ProcessWXLS(const unsigned char *pdata, int len);

private:
    std::set<uint64_t>      interestmsisdn_;
};


#define WXLSKS         WxcsLocSharingKeeper::GetInstance()


#endif