#! /usr/bin/bash
# 将 lua 脚本编译输出为 yalua 格式
# usage yaluaGen.sh xxx.lua

if [ $# -eq 0 ]
then
    echo "usage: yaluaGen.sh xxx.lua"
    exit
fi

file=$1
file_basename=$(basename -s ".lua" $file)
luac_out_file=$file_basename.luac
yalua_out_file=$file_basename.yalua

# 去掉 ".lua" 后缀后与原始文件名相同，说明它不是以 ".lua" 结尾
if [ $file = $file_basename ]
then
    echo "error: 输入文件必须是 '.lua' 文件"
    exit
fi

luac -o $luac_out_file $file
# luac 编译失败
if [ $? -ne 0 ]
then
    exit 1
fi

# 增加 magic number
echo -en "\x05\x09\x04\x01" >$yalua_out_file
cat $luac_out_file >>$yalua_out_file

# 清理临时文件
rm -rf $luac_out_file
