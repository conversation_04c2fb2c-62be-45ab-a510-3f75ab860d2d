/****************************************************************************************
 * 文 件 名 : dpi_vtysh.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <string.h>
#include <sys/types.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <time.h>
#include <sys/time.h>
#include <unistd.h>

#include <rte_lcore.h>
#include <rte_timer.h>
#include <rte_cycles.h>
#include <rte_mempool.h>
#include <rte_malloc.h>
#include <rte_ethdev.h>

#include "dpi_vtysh_msg.h"
#include "dpi_detect.h"
#include "dpi_log.h"

#define MAX_CLIENT 6
#define MAX_LINE 1024

extern struct global_config g_config;

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct traffic_stats stat_dpdk[DEV_MAX_NUM][TRAFFIC_NUM];
extern const char *protocol_name_array[PROTOCOL_MAX];
extern struct rte_mempool *flow_mempool;
extern struct rte_mempool *tcp_reassemble_mempool;

extern struct rte_hash *conversation_hash_exact;
extern struct rte_hash *conversation_hash_no_addr2;
extern struct rte_hash *conversation_hash_no_port2;

extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];
extern uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];
extern uint64_t g_inc_flow[TRAFFIC_NUM];

rte_atomic64_t drop_pkts;
rte_atomic64_t drop_bytes;
rte_atomic64_t receive_pkts;
rte_atomic64_t receive_bytes;
rte_atomic64_t dpi_fail_pkts;
rte_atomic64_t tbl_fail_pkts;
int socket_main(void);

static const char *cal_bytes_speed(uint64_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gb/s", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mb/s", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kb/s", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf b/s", (double)speed);

    return res;
}

static const char *cal_bytes_speed1(uint64_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gb/s", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mb/s", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kb/s", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf b/s", (double)speed);

    return res;
}

static const char *cal_pkts_speed(uint32_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gpps", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mpps", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kpps", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf pps", (double)speed);

    return res;
}

static const char *cal_pkts_speed1(uint32_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gpps", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mpps", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kpps", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf pps", (double)speed);

    return res;
}

#if 0
static const char *cal_bytes(uint64_t bytes)
{
    static char res[64] = {0};

    res[0] = 0;
    if (bytes > 1024 * 1024 * 1024)
        snprintf(res, sizeof(res), "%.2lf GB", (double)bytes / (1024 * 1024 * 1024));
    else if (bytes > 1024 * 1024)
        snprintf(res, sizeof(res), "%.2lf MB", (double)bytes / (1024 * 1024));
    else if (bytes > 1024)
        snprintf(res, sizeof(res), "%.2lf KB", (double)bytes / (1024));
    else
        snprintf(res, sizeof(res), "%.2lf B", (double)bytes);

    return res;
}
#endif

static void update_global_time(void)
{
	struct timeval tv;
	int ret;

	g_config.g_now_time = time(NULL);
	ret = gettimeofday(&tv, NULL);
	if (ret == 0)
		g_config.g_now_time_usec = (uint64_t)tv.tv_sec * 1000 * 1000 + tv.tv_usec;

	g_config.timeout_index = (g_config.g_now_time) % TIMEOUT_MAX;

}

static void send_str_client(int sockfd, const char *str, int len)
{
	if (len > 0)
		send(sockfd, str, len, 0);
}

static void _show_thread_info(char *msg, int len)
{
	int idx = 0;
	unsigned int thread_id;
	uint32_t flow_count_total = 0;
	uint64_t pkts_count_total = 0;
	uint64_t bytes_count_total = 0;

	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		flow_count_total += flow_thread_info[thread_id].stats.flow_count;
		pkts_count_total += flow_thread_info[thread_id].stats.raw_packet_count;
		bytes_count_total += flow_thread_info[thread_id].stats.total_wire_bytes;

		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "thread %2u has %10u flows, received %20llu pkts , %25llu Bytes\n",
					thread_id, flow_thread_info[thread_id].stats.flow_count,
					(long long unsigned int)flow_thread_info[thread_id].stats.raw_packet_count,
					(long long unsigned int)flow_thread_info[thread_id].stats.total_wire_bytes);
	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "total     has %10u flows, received %20llu pkts , %25llu Bytes\n",
				flow_count_total, (long long unsigned int)pkts_count_total, (long long unsigned int)bytes_count_total);
}

static void _show_flow_detail_info(char *msg, int len)
{
	int i;
	int idx = 0;
	unsigned int thread_id;
	double percent;
	uint64_t flow_count_total = 0;
	uint64_t flow_detail[PROTOCOL_MAX] = {0};

	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		for (i = 0; i < PROTOCOL_MAX; i++) {
			flow_detail[i] += flow_thread_info[thread_id].stats.flow_stats[i];

		}
		flow_count_total += flow_thread_info[thread_id].stats.flow_count;
	}

	for (i = 0; i < PROTOCOL_MAX; i++) {
		if (flow_count_total == 0)
			percent = 0;
		else
			percent = (double)flow_detail[i] * 100 /flow_count_total;
		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu;   %.2lf%% percent\n",
					protocol_name_array[i], flow_detail[i], percent);
	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu\n", "TOTAL", flow_count_total);
}

static void _show_flow_total_info(char *msg, int len)
{
	int i;
	int idx = 0;
	double percent;
	double percent_pkts;
	double percent_bytes;
	unsigned int thread_id;
	uint64_t flow_count_total = 0;
	uint64_t pkts_count_total = 0;
	uint64_t bytes_count_total = 0;

	uint64_t flow_detail[PROTOCOL_MAX] = {0};
	uint64_t flow_detail_pkts[PROTOCOL_MAX] = {0};
	uint64_t flow_detail_bytes[PROTOCOL_MAX] = {0};

	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		pkts_count_total += flow_thread_info[thread_id].stats.raw_packet_count;
		bytes_count_total += flow_thread_info[thread_id].stats.total_wire_bytes;
		for (i = 0; i < PROTOCOL_MAX; i++) {
			flow_detail[i] += flow_thread_info[thread_id].stats.flow_stats_total[i];
			flow_detail_pkts[i] += flow_thread_info[thread_id].stats.flow_stats_total_pkts[i];
			flow_detail_bytes[i] += flow_thread_info[thread_id].stats.flow_stats_total_bytes[i];
			flow_count_total += flow_thread_info[thread_id].stats.flow_stats_total[i];
		}
	}

	idx = snprintf(msg , len,
			"%32s %16s %16s %16s %16s %16s %16s\n",
			"PROTOCOL_NAME", "NUM", "PERCENT", "PKTS", "PERCENT", "BYTES", "PERCEMT");

	for (i = 0; i < PROTOCOL_MAX; i++) {
		if (flow_count_total == 0)
			percent = 0;
		else
			percent = (double)flow_detail[i] * 100 /flow_count_total;

		if (pkts_count_total == 0)
			percent_pkts = 0;
		else
			percent_pkts = (double)flow_detail_pkts[i] * 100 /pkts_count_total;

		if (bytes_count_total == 0)
			percent_bytes = 0;
		else
			percent_bytes = (double)flow_detail_bytes[i] * 100 /bytes_count_total;

		if (idx < len)
				idx += snprintf(msg + idx, len - idx,
				"%32s %16lu %15.2lf%% %16lu %15.2lf%% %16lu %15.2lf%%\n",
				protocol_name_array[i], flow_detail[i], percent,
				flow_detail_pkts[i], percent_pkts,
				flow_detail_bytes[i], percent_bytes);

	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu; pkts is :%lu;  bytes is %lu \n", "TOTAL",
				flow_count_total, pkts_count_total, bytes_count_total);
}

static void _clean_flow_total_info(void)
{
	int i;
	unsigned int thread_id;
	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		for (i = 0; i < PROTOCOL_MAX; i++) {
			memset(&flow_thread_info[thread_id].stats, 0, sizeof(flow_thread_info[thread_id].stats));
			/*
			flow_thread_info[thread_id].stats.flow_stats_total[i] = 0;
			flow_thread_info[thread_id].stats.flow_stats_total_pkts[i] = 0;
			flow_thread_info[thread_id].stats.flow_stats_total_bytes[i] = 0;
			flow_thread_info[thread_id].stats.raw_packet_count = 0;
			flow_thread_info[thread_id].stats.total_wire_bytes = 0;*/
		}
	}
	rte_atomic64_set(&receive_bytes, 0);
	rte_atomic64_set(&drop_bytes, 0);
	rte_atomic64_set(&receive_pkts, 0);
	rte_atomic64_set(&drop_pkts, 0);
	return;
}

static void _show_tbl_log_info(char *msg, int len)
{
	int i;
	int idx = 0;
	uint64_t total = 0;
	uint64_t total_speed = 0;

	for (i = 0; i < TBL_LOG_MAX; i++) {
		total += log_total[i][0];
		total_speed += log_total[i][1] - log_total[i][2];
		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%-12s protocol tbl log num is :%lu, speed is %lu/s\n",
					tbl_log_array[i].protoname, log_total[i][0], log_total[i][1] - log_total[i][2]);
	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu, speed is %lu/s", "TOTAL", total, total_speed);
}

static void _clean_tbl_log_info(void)
{
	int i, j;

	for (i = 0; i < TBL_LOG_MAX; i++) {
		for (j = 0; j < TRAFFIC_NUM; j++) {
			log_total[i][j] = 0;
		}
	}

	return;
}

static const char *_show_conversation_hash(struct rte_hash *hash, const char *name)
{
	int num = 0;
	unsigned int idle_iter = 0;
	const void *next_key;
	void *next_data;

	static char result[256];

	while (rte_hash_iterate(hash, &next_key, &next_data, &idle_iter) >= 0) {
		num++;
	}
	snprintf(result, sizeof(result), "%s hash num is :%u\n", name, num);
	return result;
}

static void _show_flow_hash_info(char *msg, int len)
{
	unsigned int i = 0;
	unsigned int idle_iter;
	int idx = 0;
	int num;
	const void *next_key;
	void *next_data;
	int total_num = 0;
	for (i = 0; i < g_config.dissector_thread_num; i++) {
		num = 0;
		idle_iter = 0;
		struct work_process_data *process = &flow_thread_info[i];
		while (rte_hash_iterate(process->hash, &next_key, &next_data, &idle_iter) >= 0) {
			num++;
		}
		total_num += num;

		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%-12u thread flow hash num is :%u\n", i, num);
	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%-12s thread flow hash num is :%u\n", "TOTAL", total_num);

	const char *res = _show_conversation_hash(conversation_hash_exact, "conversation_hash_exact");
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%s", res);
	res = _show_conversation_hash(conversation_hash_no_addr2, "conversation_hash_no_addr2");
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%s", res);
	res = _show_conversation_hash(conversation_hash_no_port2, "conversation_hash_no_port2");
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%s", res);
}

static void _show_content_info(char *msg, int len)
{
    const char *protocol_content[EM_CONTENT_MAX] =
    {
        "WXGH",
    };

    int i;
    int idx = 0;
    unsigned int thread_id;
    uint32_t content_total[EM_CONTENT_MAX] = {0};
    uint32_t content_actual[EM_CONTENT_MAX] = {0};
    uint32_t content_repeat[EM_CONTENT_MAX] = {0};
    uint32_t content_s_wxcs[EM_CONTENT_MAX] = {0};
    uint32_t percent_100[EM_CONTENT_MAX] = {0};
    uint32_t percent_90[EM_CONTENT_MAX] = {0};
    uint32_t percent_80[EM_CONTENT_MAX] = {0};
    uint32_t percent_other[EM_CONTENT_MAX] = {0};
    float    actual_percent[EM_CONTENT_MAX] = {0.0};
    float    complete_percent[EM_CONTENT_MAX] = {0.0};


    for (i = 0; i < EM_CONTENT_MAX; i++) {
        content_total[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].total_number);
        content_actual[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].actual_number);
        percent_100[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].percent_100_number);
        percent_90[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].percent_90_number);
        percent_80[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].percent_80_number);
        percent_other[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].percent_other_number);

        content_repeat[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].repeat_number);
        content_s_wxcs[i] = rte_atomic32_read(&g_config.reassemble_info.content_statics[i].s_wxcs_number);
    }

    for (i = 0; i < EM_CONTENT_MAX; i++) {
        if(content_total[i]>0){
            actual_percent[i]=(content_actual[i]/(content_total[i]*1.0))*100;
        }else{
            actual_percent[i]=0;
        }

        if(content_actual[i]>0){
            complete_percent[i]=(percent_100[i]/(content_actual[i]*1.0))*100;
        }else{
            complete_percent[i]=0;
        }
    }


    idx = snprintf(msg , len,
                        "%32s  %16s %16s %16s %16s %16s\n",
                        "PROTOCOL", "EXPECT_NUM", "ACTUAL_NUM", "REPEAT_NUM", "S_WXCS_NUM", "COMPLETE_PERCENT");

    for (i = 0; i < EM_CONTENT_MAX; i++) {
        if (idx < len){
            idx += snprintf(msg + idx, len - idx,
                            "%32s %16u %16u %16u %16u %15.2lf%%\n",
                            protocol_content[i],
                            content_total[i],
                            content_actual[i],
                            content_repeat[i],
                            content_s_wxcs[i],
                            actual_percent[i]);
        }
    }
}




struct _msg
{
	char *msg;
	int idx;
	int len;
};

static void
walk_cb(struct rte_mempool *mp, void *userdata)
{
	struct _msg *detail_msg = (struct _msg *)userdata;
	uint32_t in_use_num = rte_mempool_in_use_count(mp);
	uint32_t free_num = rte_mempool_avail_count(mp);

	if (detail_msg->idx < detail_msg->len)
		detail_msg->idx += snprintf(detail_msg->msg + detail_msg->idx, detail_msg->len - detail_msg->idx,
				"%32s %16u %16u %16u %16u %16llu %16llu %16llu\n",
				mp->name, mp->elt_size,
				mp->size, in_use_num, free_num,
				(unsigned long long)mp->elt_size * mp->size, (unsigned long long)mp->elt_size * in_use_num, (unsigned long long)mp->elt_size * free_num);
}

static void _show_all_mempool_detail_info(char *msg, int len)
{
	struct _msg detail_msg;
	detail_msg.msg = msg;
	detail_msg.len = len;

	detail_msg.idx = snprintf(detail_msg.msg , detail_msg.len,
			"%32s %16s %16s %16s %16s %16s %16s %16s\n",
			"MEMPOOL_NAME", "ELEMENT_SIZE", "TOTAL_NUM", "IN_USE_NUM", "FREE_NUM", "TOTAL_SIZE", "IN_USE_SIZE", "FREE_SIZE");

	rte_mempool_walk(walk_cb, &detail_msg);

	memcpy(msg, detail_msg.msg, len);
}


static void _show_rtemalloc_info(char *msg, int len)
{
    int idx = 0;
    uint32_t i=0;
    uint32_t socket_num=0;
    const char socket_name[]="SOCKET";

    //socket_num=rte_socket_count();
    socket_num=2;
    struct rte_malloc_socket_stats socket_stats[socket_num];
    for(i=0;i<socket_num;i++){
        rte_malloc_get_socket_stats(i, &socket_stats[i]);
    }

    idx = snprintf(msg , len,
                        "%32s  %16s %16s %16s %16s %16s %16s\n",
                        "SOCKET", "TOTAL_SIZE", "USING_SIZE", "FREE_SIZE", "ALLOC_NUM", "FREE_NUM","BIG_FREE_SIZE");

    for (i = 0; i < socket_num; i++) {
        if (idx < len){
            idx += snprintf(msg + idx, len - idx,
                            "%32s_%02u %16zu %16zu %16zu %16u %16u %16zu\n",
                            socket_name,
                            i,
                            socket_stats[i].heap_totalsz_bytes,
                            socket_stats[i].heap_allocsz_bytes,
                            socket_stats[i].heap_freesz_bytes,
                            socket_stats[i].alloc_count,
                            socket_stats[i].free_count,
                            socket_stats[i].greatest_free_size);

        }
    }
}



extern int tbl_log_queue_num;
static void _show_all_ring_detail_info(char *msg, int len)
{
	unsigned int i;
	int idx = 0;

	idx = snprintf(msg , len,
			"%32s %16s %16s %16s\n",
			"RING_NAME", "TOTAL_NUM", "IN_USE_NUM", "FREE_NUM");

	for (i = 0; i < g_config.dissector_thread_num; i++) {
		char packet_ring_name[64] = {0};
		snprintf(packet_ring_name, sizeof(packet_ring_name), "packet_ring_%d", i);
		struct rte_ring *r = rte_ring_lookup(packet_ring_name);
		if (r == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "error while lookup packet ring");
      continue;
		}

		if (idx < len) {
			idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
							packet_ring_name,
							rte_ring_get_capacity(r),
							rte_ring_count(r),
							rte_ring_free_count(r));
		}
	}
	for (i = 0; i < (unsigned)tbl_log_queue_num; i++) {
		char ring_name[64] = {0};
		snprintf(ring_name, sizeof(ring_name), "tbl_ring_%d", i);
		struct rte_ring *r = rte_ring_lookup(ring_name);
		if (r == NULL) {
			// DPI_LOG(DPI_LOG_ERROR, "error while lookup packet ring");
      // 由于tbl_ring只有一个，这里的for循环是无用的，暂时continue
      continue;
		}

		if (idx < len) {
			idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
							ring_name,
							rte_ring_get_capacity(r),
							rte_ring_count(r),
							rte_ring_free_count(r));
		}

	}
  	for (i = 0; i < (unsigned)g_config.log_thread_num; i++) {
		char ring_name[64] = {0};
		snprintf(ring_name, sizeof(ring_name), "tbl_record_ring_%d", i);
		struct rte_ring *r = rte_ring_lookup(ring_name);
		if (r == NULL) {
			DPI_LOG(DPI_LOG_ERROR, "error while lookup packet ring");
      continue;
		}

		if (idx < len) {
			idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
							ring_name,
							rte_ring_get_capacity(r),
							rte_ring_count(r),
							rte_ring_free_count(r));
		}

	}

	return;
}

static void _show_inc_flow(char *msg, int len)
{
	int i;
	int idx = 0;

	for (i = 1; i < TRAFFIC_NUM; i++) {

		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%d seconds ago, flow increase speed is %lu/s\n",
					TRAFFIC_NUM - i,
					g_inc_flow[TRAFFIC_NUM - i - 1] - g_inc_flow[TRAFFIC_NUM - i]);
	}
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "total create flow num is %lu\n",
				g_inc_flow[0]);
}

static void _show_dev_traffic_speed_info(char *msg, int len)
{
	uint16_t idx_port;
	int i;
	int idx = 0;
	unsigned int thread_id;
	struct traffic_stats total[TRAFFIC_NUM];
	memset(&total, 0, sizeof(total));

	RTE_ETH_FOREACH_DEV(idx_port) {
		if (idx_port >= DEV_MAX_NUM) break;
		struct rte_eth_dev *dev;
		dev = &rte_eth_devices[idx_port];
		for (i = 1; i < TRAFFIC_NUM; i++) {
			uint64_t missed_pkts = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].imissed - stat_dpdk[idx_port][TRAFFIC_NUM - i].imissed;
			uint64_t error_pkts = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ierrors - stat_dpdk[idx_port][TRAFFIC_NUM - i].ierrors;
			uint64_t total_pkts = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].pkts - stat_dpdk[idx_port][TRAFFIC_NUM - i].pkts;
			uint64_t tmp_total = missed_pkts + error_pkts + total_pkts;
			double missed_per;
			double error_per;
			if (tmp_total == 0) {
				missed_per = 0;
				error_per = 0;
			}
			else {
				missed_per = missed_pkts * 100.0 / tmp_total;
				error_per = error_pkts * 100.0 / tmp_total;
			}

			if (idx < len)
				idx += snprintf(msg + idx, len - idx, "port %d %s : %d seconds ago, speed is %s, %s, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                        idx_port,
						dev->device->name,
						TRAFFIC_NUM - i,
						cal_bytes_speed(8 * (stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].bytes - stat_dpdk[idx_port][TRAFFIC_NUM - i].bytes)),
						cal_pkts_speed(total_pkts),
						missed_pkts, error_pkts,
						missed_per, error_per);

		}
	}

	RTE_ETH_FOREACH_DEV(idx_port) {
		if (idx_port >= DEV_MAX_NUM) break;

		struct rte_eth_dev *dev;
		dev = &rte_eth_devices[idx_port];

		uint64_t missed_pkts = stat_dpdk[idx_port][0].imissed;
		uint64_t error_pkts = stat_dpdk[idx_port][0].ierrors;
		uint64_t tmp_total = missed_pkts + error_pkts + stat_dpdk[idx_port][0].pkts;
		double missed_per;
		double error_per;
		if (tmp_total == 0) {
			missed_per = 0;
			error_per = 0;
		}
		else {
			missed_per = missed_pkts * 100.0 / tmp_total;
			error_per = error_pkts * 100.0 / tmp_total;
		}

		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%s :total (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
					dev->device->name,
					missed_pkts, error_pkts,
					missed_per, error_per);
	}

}

static void _show_traffic_speed_info(char *msg, int len)
{
	uint16_t idx_port;
	int i;
	int idx = 0;
	unsigned int thread_id;
	struct traffic_stats total[TRAFFIC_NUM];
	uint64_t bytes_total[TRAFFIC_NUM];
	uint64_t pkts_total[TRAFFIC_NUM];
	uint64_t miss_total[TRAFFIC_NUM];
	uint64_t error_total[TRAFFIC_NUM];
	memset(&total, 0, sizeof(total));
	memset(&bytes_total, 0, sizeof(bytes_total));
	memset(&pkts_total, 0, sizeof(pkts_total));
	memset(&miss_total, 0, sizeof(miss_total));
	memset(&error_total, 0, sizeof(error_total));

	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		for (i = 0; i < TRAFFIC_NUM; i++) {
			total[i].pkts += flow_thread_info[thread_id].stats.traffic[i].pkts;
			total[i].bytes += flow_thread_info[thread_id].stats.traffic[i].bytes;
		}
	}
	for (i = 0; i < TRAFFIC_NUM; i++) {
			RTE_ETH_FOREACH_DEV(idx_port) {
		    if (idx_port >= DEV_MAX_NUM) break;
			  pkts_total[i] += stat_dpdk[idx_port][i].pkts;
			  bytes_total[i] += stat_dpdk[idx_port][i].bytes;
			  miss_total[i] += stat_dpdk[idx_port][i].imissed;
			  error_total[i] += stat_dpdk[idx_port][i].ierrors;
		}
	}

	for (i = 1; i < TRAFFIC_NUM; i++) {
		uint64_t missed_pkts = miss_total[TRAFFIC_NUM - i - 1] - miss_total[TRAFFIC_NUM - i];
		uint64_t error_pkts = error_total[TRAFFIC_NUM - i - 1] - error_total[TRAFFIC_NUM - i];
		uint64_t total_pkts = pkts_total[TRAFFIC_NUM - i - 1] - pkts_total[TRAFFIC_NUM - i];
		uint64_t tmp_total = missed_pkts + error_pkts + total_pkts;
		double missed_per;
		double error_per;
		if (tmp_total == 0) {
			missed_per = 0;
			error_per = 0;
		}
		else {
			missed_per = missed_pkts * 100.0 / tmp_total;
			error_per = error_pkts * 100.0 / tmp_total;
		}

		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "%d seconds ago, speed is %s, %s, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%), detect speed is %s, %s\n",
					TRAFFIC_NUM - i,
					cal_bytes_speed(8 * (bytes_total[TRAFFIC_NUM - i - 1] - bytes_total[TRAFFIC_NUM - i])),
					cal_pkts_speed(total_pkts),
					missed_pkts, error_pkts,
					missed_per, error_per,
					cal_bytes_speed1(8 * (total[TRAFFIC_NUM - i - 1].bytes - total[TRAFFIC_NUM - i].bytes)),
					cal_pkts_speed1(total[TRAFFIC_NUM - i - 1].pkts - total[TRAFFIC_NUM - i].pkts));
	}

	uint64_t missed_pkts = miss_total[0];
	uint64_t error_pkts = error_total[0];
	uint64_t tmp_total = missed_pkts + error_pkts + pkts_total[0];
	double missed_per;
	double error_per;
	if (tmp_total == 0) {
		missed_per = 0;
		error_per = 0;
	}
	else {
		missed_per = missed_pkts * 100.0 / tmp_total;
		error_per = error_pkts * 100.0 / tmp_total;
	}

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "total receive %llu Bytes, %llu pkts, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%); dpi receive %llu Bytes, %llu pkts; detected %llu Bytes, %llu pkts; drop %llu Bytes, %llu pkts\n",
				(long long unsigned int)bytes_total[0], (long long unsigned int)pkts_total[0], missed_pkts, error_pkts, missed_per, error_per,
				(long long unsigned int)rte_atomic64_read(&receive_bytes), (long long unsigned int)rte_atomic64_read(&receive_pkts),
				(long long unsigned int)total[0].bytes, (long long unsigned int)total[0].pkts,
				(long long unsigned int)rte_atomic64_read(&drop_bytes), (long long unsigned int)rte_atomic64_read(&drop_pkts));
}

static void _show_fail_info(char *msg, int len)
{
	int idx = 0;
	unsigned port;
	uint16_t idx_port;


	unsigned long long forward_pkts = 0,
							missed_pkts  = 0,
							error_pkts   = 0,
							dpi_receive, dpi_fail, tbl_pkts, tbl_bytes;
	struct rte_eth_stats stat_info;

	RTE_ETH_FOREACH_DEV(idx_port) {
		if (idx_port >= DEV_MAX_NUM) break;
		if(rte_eth_stats_get(idx_port, &stat_info) == 0){
				forward_pkts += stat_info.ipackets;
				missed_pkts  += stat_info.imissed;
				error_pkts   += stat_info.ierrors;
		}
	}

	dpi_receive = rte_atomic64_read(&receive_pkts);
	dpi_fail    = rte_atomic64_read(&dpi_fail_pkts);
	tbl_pkts    = rte_atomic64_read(&tbl_fail_pkts);

	idx += snprintf(msg+idx, len-idx, "DPDK     : total forward  %llupkts,\ttotal missed  %llupkts,\ttotal errors  %llupkts, fail percent  %lf%%\n",
											forward_pkts, missed_pkts, error_pkts, forward_pkts + missed_pkts + error_pkts ==  0 ? 0 : (missed_pkts + error_pkts) * 100.0 / (forward_pkts + missed_pkts + error_pkts));
	if(idx < len)
			idx += snprintf(msg+idx, len-idx, "DPI      : total receive  %llupkts,\tfail enqueue  %llupkts,\tfail percent  %lf%%\n",
													dpi_receive, dpi_fail, dpi_receive ? dpi_fail * 100.0 / dpi_receive : 0);

	if(idx < len)
			idx += snprintf(msg+idx, len-idx, "TBL      : fail  enqueue  %llupkts\n",
													tbl_pkts);
	return;
}

static void _show_current_traffic_speed(char *msg, int len)
{
	int i;
	uint16_t idx_port;
	uint64_t bytes_total[TRAFFIC_NUM];
	unsigned nb_ports = rte_eth_dev_count_avail();
	memset(&bytes_total, 0, sizeof(bytes_total));

	for (i = 0; i < TRAFFIC_NUM; i++) {
		for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
			bytes_total[i] += stat_dpdk[idx_port][i].bytes;
		}
	}
	snprintf(msg, len, "%lu", 8 * (bytes_total[0] - bytes_total[1]));
}

static void _show_flow_timeout(char *msg, int len)
{
	int idx = 0;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "tcp  timeout is %u seconds\n", g_config.tcp_flow_timeout);
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "udp  timeout is %u seconds\n", g_config.udp_flow_timeout);
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "sctp timeout is %u seconds\n", g_config.sctp_flow_timeout);
}

static void _set_tcp_timeout(uint16_t timeout, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.tcp_flow_timeout;

	if (timeout >= TIMEOUT_MAX) {
		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
		return;
	}
	if (timeout > 0)
		g_config.tcp_flow_timeout = timeout;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "tcp old timeout is %u seconds, now is %u seconds\n", old, g_config.tcp_flow_timeout);
}

static void _set_udp_timeout(uint16_t timeout, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.udp_flow_timeout;
	if (timeout >= TIMEOUT_MAX) {
		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
		return;
	}
	if (timeout > 0)
		g_config.udp_flow_timeout = timeout;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "udp old timeout is %u seconds, now is %u seconds\n", old, g_config.udp_flow_timeout);
}

static void _set_sctp_timeout(uint16_t timeout, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.sctp_flow_timeout;
	if (timeout >= TIMEOUT_MAX) {
		if (idx < len)
			idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
		return;
	}
	if (timeout > 0)
		g_config.sctp_flow_timeout = timeout;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "sctp old timeout is %u seconds, now is %u seconds\n", old, g_config.sctp_flow_timeout);
}

static void _show_flow_identify_pkt_num(char *msg, int len)
{
	int idx = 0;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "tcp  identify pkts num is %u\n", g_config.tcp_identify_pkt_num);
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "udp  identify pkts num is %u\n", g_config.udp_identify_pkt_num);
	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "sctp identify pkts num is %u\n", g_config.sctp_identify_pkt_num);
}

static void _set_tcp_identify_pkt_num(uint16_t num, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.tcp_identify_pkt_num;

	if (num > 0)
		g_config.tcp_identify_pkt_num = num;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "tcp old identify pkt num is %u, now is %u\n", old, g_config.tcp_identify_pkt_num);
}

static void _set_udp_identify_pkt_num(uint16_t num, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.udp_identify_pkt_num;

	if (num > 0)
		g_config.udp_identify_pkt_num = num;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "udp old identify pkt num is %u, now is %u\n", old, g_config.udp_identify_pkt_num);
}

static void _set_sctp_identify_pkt_num(uint16_t num, char *msg, int len)
{
	int idx = 0;
	uint16_t old = g_config.sctp_identify_pkt_num;

	if (num > 0)
		g_config.sctp_identify_pkt_num = num;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "sctp old identify pkt num is %u, now is %u\n", old, g_config.sctp_identify_pkt_num);
}

static void _set_protocol_identify(const char *recv_str, char *msg, int len, int _set)
{
	int idx = 0;
	const char *tmp;

	if (_set == 0)
		tmp = "disable";
	else
		tmp = "enable";

	if (strcmp(recv_str, "HTTP") == 0)
		g_config.protocol_switch[PROTOCOL_HTTP] = _set;
	else if (strcmp(recv_str, "WEIXIN") == 0)
		g_config.protocol_switch[PROTOCOL_WEIXIN] = _set;


	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "%s protocol %s success", tmp, recv_str);


}

static void _disable_protocol_identify(const char *recv_str, char *msg, int len)
{
	_set_protocol_identify(recv_str, msg, len, 0);
}

static void _enable_protocol_identify(const char *recv_str, char *msg, int len)
{
	_set_protocol_identify(recv_str, msg, len, 1);
}

static void _show_protocol_identify(char *msg, int len)
{
	int idx = 0;
	int i;

	for (i = 0; i < PROTOCOL_MAX; i++) {
		if (g_config.protocol_switch[i]) {
			if (idx < len)
				idx += snprintf(msg + idx, len - idx, "%s enable\n", protocol_name_array[i]);
		} else {
			if (idx < len)
				idx += snprintf(msg + idx, len - idx, "%s disable\n", protocol_name_array[i]);
		}
	}
}

static void _show_log_level(char *msg, int len)
{
	int idx = 0;
	char level[32] = {0};
	switch (g_config.log_output_level) {
		case DPI_LOG_DEBUG:
			strncpy(level, "DEBUG", sizeof(level));
			break;

		case DPI_LOG_WARNING:
			strncpy(level, "WARNING", sizeof(level));
			break;

		case DPI_LOG_ERROR:
			strncpy(level, "ERROR", sizeof(level));
			break;

		default:
			break;
	}

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "system log level is %s\n", level);
}

static void _set_log_level(const char *recv_str, char *msg, int len)
{
	int idx = 0;
	int old = g_config.log_output_level;
	char level[32] = {0};
	switch (old) {
		case DPI_LOG_DEBUG:
			strncpy(level, "DEBUG", sizeof(level));
			break;

		case DPI_LOG_WARNING:
			strncpy(level, "WARNING", sizeof(level));
			break;

		case DPI_LOG_ERROR:
			strncpy(level, "ERROR", sizeof(level));
			break;

		default:
			break;
	}

	if (strcmp(recv_str, "DEBUG") == 0)
		g_config.log_output_level = DPI_LOG_DEBUG;
	else if (strcmp(recv_str, "WARNING") == 0)
		g_config.log_output_level = DPI_LOG_WARNING;
	else if (strcmp(recv_str, "ERROR") == 0)
		g_config.log_output_level = DPI_LOG_ERROR;
	else
		return;

	if (idx < len)
		idx += snprintf(msg + idx, len - idx, "system log old level is %s, now level is %s\n", level, recv_str);

}

static void dpi_stop_rcv_pkts(void)
{
	g_config.stop_rcv_pkts = 1;
  g_config.dpdk_stop_receive = 1;
}

static void dpi_start_rcv_pkts(void)
{
	g_config.stop_rcv_pkts = 0;
}

static void dpi_exit(void)
{
	unsigned int thread_id;

	g_config.exit = 1;

	for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
		do_all_flow_free(thread_id);
	}

	exit(0);
}

extern struct rte_eth_dev rte_eth_devices[RTE_MAX_ETHPORTS];
static void _show_eth_dev_info(char *msg, int len)
{
	int ret = 0;
	unsigned i = 0;
	ret += snprintf(msg+ret, len-ret, "%16s %16s %8s %12s %16s %16s %16s %8s %24s\n", "ETH_DEV_INDEX", "BUS_NODE_NAME", "PORT_ID", "NUMA_NODE"
					, "DRIVER_NAME", "RX_QUEUE_NUM", "TX_QUEUE_NUM", "MTU", "RX_MBUF_ALLOC_FAILED");

	for (i = 0; i < RTE_MAX_ETHPORTS; i++) {
		if (rte_eth_devices[i].state != RTE_ETH_DEV_ATTACHED)
			continue;
		ret += snprintf(msg+ret, len-ret, "%16u %16s %8u %12u %16s %16u %16u %8u %24lu\n"
				, i, rte_eth_devices[i].data->name, rte_eth_devices[i].data->port_id
				, rte_eth_devices[i].data->numa_node, rte_eth_devices[i].device->driver->name, rte_eth_devices[i].data->nb_rx_queues
				, rte_eth_devices[i].data->nb_tx_queues, rte_eth_devices[i].data->mtu, rte_eth_devices[i].data->rx_mbuf_alloc_failed);
	}
}

static const char* get_rss_type(uint16_t portid)
{
	switch(g_config.rss_use_type[portid])
	{
	case RSS_USE_CONF:
		return "RSS_USE_CONF";
	case RSS_USE_FILTER_CTRL:
		return "RSS_USE_FILTER_CTRL";
	case RSS_USE_CUSTOM:
		return "RSS_USE_CUSTOM";
	default:
		return "UNKNOW_RSS_TYPE";
	}
}

static void _show_port_rss_info(char *msg, int len)
{
	int ret = 0;
	unsigned i = 0;
	ret += snprintf(msg+ret, len-ret, "%16s %16s %24s\n", "PORT_ID", "DRIVER_NAME", "RSS_TYPE");

	for (i = 0; i < RTE_MAX_ETHPORTS; i++) {
		if (rte_eth_devices[i].state != RTE_ETH_DEV_ATTACHED)
			continue;
		ret += snprintf(msg+ret, len-ret, "%16u %16s %24s\n", rte_eth_devices[i].data->port_id
				, rte_eth_devices[i].device->driver->name, get_rss_type(rte_eth_devices[i].data->port_id));
	}
}

/*
uint64_t _get_now_time(void)
{
	uint64_t cur_tsc = rte_rdtsc();
	uint64_t hz = rte_get_tsc_hz();

	return (cur_tsc / hz);
}
*/

static int do_server_statistic(int sockfd, const char *str, unsigned int len)
{
	char msg[MSG_MAX_LEN];
	char argv[32];
	int rcv_len;
	uint16_t value;

	int type = *(const int *)&str[0];

	if (len < sizeof(int))
		return -1;

	switch(type) {
		case MSG_SHOW_THREAD_INFO:
			_show_thread_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_FLOW_DETAIL_INFO:
			_show_flow_detail_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_FLOW_TOTAL_INFO:
			_show_flow_total_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_FLOW_INC_INFO:
			_show_inc_flow(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_CLEAN_FLOW_TOTAL_INFO:
			_clean_flow_total_info();
			send_str_client(sockfd, "OK", strlen("OK"));
			break;

		case MSG_SHOW_FLOW_HASH_INFO:
			_show_flow_hash_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

	    case MSG_SHOW_CONTENT_INFO:   // add by liugh
            _show_content_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

		case MSG_SHOW_MEMPOOL_DETAIL_INFO:
			_show_all_mempool_detail_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

        case MSG_SHOW_RTEMALLOC_INFO:   // add by liugh
            _show_rtemalloc_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

		case MSG_SHOW_RING_DETAIL_INFO:
			_show_all_ring_detail_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_TRAFFIC_SPEED_INFO:
			_show_traffic_speed_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_CURRENT_TRAFFIC_SPEED:
			_show_current_traffic_speed(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_DEV_TRAFFIC_SPEED_INFO:
			_show_dev_traffic_speed_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_FLOW_TIMEOUT:
			_show_flow_timeout(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_FLOW_IDENTIFY_PKT_NUM:
			_show_flow_identify_pkt_num(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_TCP_TIMEOUT:
			value = *(const uint16_t *)&str[4];
			_set_tcp_timeout(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_UDP_TIMEOUT:
			value = *(const uint16_t *)&str[4];
			_set_udp_timeout(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_SCTP_TIMEOUT:
			value = *(const uint16_t *)&str[4];
			_set_sctp_timeout(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_TCP_IDENTIFY_PKT_NUM:
			value = *(const uint16_t *)&str[4];
			_set_tcp_identify_pkt_num(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_UDP_IDENTIFY_PKT_NUM:
			value = *(const uint16_t *)&str[4];
			_set_udp_identify_pkt_num(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_SCTP_IDENTIFY_PKT_NUM:
			value = *(const uint16_t *)&str[4];
			_set_sctp_identify_pkt_num(value, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_DISABLE_PROTOCOL_IDENTIFY:
			rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
			strncpy(argv, str + sizeof(int), rcv_len);
			argv[rcv_len] = 0;
			_disable_protocol_identify(argv, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_ENABLE_PROTOCOL_IDENTIFY:
			rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
			strncpy(argv, str + sizeof(int), rcv_len);
			argv[rcv_len] = 0;
			_enable_protocol_identify(argv, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SHOW_PROTOCOL_IDENTIFY:
			_show_protocol_identify(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_DISABLE_CONVERSATION_IDENTIFY:
			g_config.conversation_switch = 0;
			send_str_client(sockfd, "disable conversation identify", strlen("disable conversation identify"));
			break;

		case MSG_ENABLE_CONVERSATION_IDENTIFY:
			g_config.conversation_switch = 1;
			send_str_client(sockfd, "enable conversation identify", strlen("enable conversation identify"));
			break;

		case MSG_SHOW_LOG_LEVEL:
			_show_log_level(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_SET_LOG_LEVEL:
			rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
			strncpy(argv, str + sizeof(int), rcv_len);
			argv[rcv_len] = 0;
			_set_log_level(argv, msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_STOP_RCV_PKTS:
			send_str_client(sockfd, "stop rcv pkts", strlen("stop rcv pkts"));
			dpi_stop_rcv_pkts();
			break;

		case MSG_START_RCV_PKTS:
			send_str_client(sockfd, "start rcv pkts", strlen("start rcv pkts"));
			dpi_start_rcv_pkts();
			break;

		case MSG_SHOW_TBL_LOG_INFO:
			_show_tbl_log_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

		case MSG_CLEAN_TBL_LOG_INFO:
			_clean_tbl_log_info();
			send_str_client(sockfd, "OK", strlen("OK"));
			break;

		case MSG_SHOW_ETH_DEV_INFO:
			_show_eth_dev_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;
		case MSG_SHOW_PORT_RSS_INFO:
			_show_port_rss_info(msg, MSG_MAX_LEN);
			send_str_client(sockfd, msg, strlen(msg));
			break;

    case MSG_SHOW_FAIL_INFO:
      _show_fail_info(msg, MSG_MAX_LEN);
      send_str_client(sockfd, msg, strlen(msg));
      break;

		case MSG_NORMAL_EXIT:
			send_str_client(sockfd, "dpi exit", strlen("dpi exit"));
			dpi_exit();
			break;

		default:
			break;
	}
	return 0;
}

static int init_server_socket(int port)
{
	int sockfd;
	struct sockaddr_in serv_addr;

	if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
		perror("socket");
		return 0;
	}

	bzero((char *)&serv_addr, sizeof(serv_addr));
	serv_addr.sin_family = AF_INET;
	serv_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
	serv_addr.sin_port = htons(port);

	int yes = 1;
	setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &yes, sizeof(yes));

	if (bind(sockfd, (struct sockaddr *)&serv_addr, sizeof(serv_addr)) < 0) {
		perror("bind");
		return 0;
	}
	return sockfd;
}

static int user_free_index(int *user_link, int len)
{
	int i = 0;
	while (user_link[i] != 0 && i < len)
		i++;

	if (i == len)
		return -1;

	return i;
}

/*
*管理线程的主要工作：
	1，更新全局时间戳
	2，每秒的timer统计函数调用
	3，socket监听，处理消息
*/
int socket_main(void)
{
	int i;
	int sockfd;
	int new_sockfd;
	int user_link[MAX_CLIENT];
	int userfd[MAX_CLIENT];
	char line[MAX_LINE];
	int userindex = 0;
	fd_set sockset;
	int maxfd = 0;
	struct timeval tv;

	struct sockaddr_in cli_addr;
	unsigned int cli_len;

	memset(&tv, 0, sizeof(tv));
	tv.tv_usec = 100;

	sockfd = init_server_socket(SOCKET_PORT);
	if (sockfd == 0) {
		perror("init server socket error\n");
		exit(-1);
	}

	listen(sockfd, MAX_CLIENT);

	for (i = 0; i < MAX_CLIENT; i++) {
		user_link[i] = 0;
	}

	FD_ZERO(&sockset);
	FD_SET(sockfd, &sockset);

	maxfd = DPI_MAX(maxfd, sockfd + 1);

	while(1) {
		update_global_time();
		rte_timer_manage();
		select(maxfd, &sockset, NULL, NULL, &tv);

		if (FD_ISSET(sockfd, &sockset) && (userindex = user_free_index(user_link, MAX_CLIENT)) >= 0) {
            cli_len = sizeof(struct sockaddr_in);
			new_sockfd = accept(sockfd, (struct sockaddr *) &cli_addr, &cli_len);
			if (new_sockfd <= 0)
				perror("accept");
			else {
				user_link[userindex] = 1;
				userfd[userindex] = new_sockfd;
				FD_SET(new_sockfd, &sockset);
				maxfd = DPI_MAX(maxfd, new_sockfd + 1);
			}
		}

		int ifd = 0;
		for (i = 0; i < MAX_CLIENT; i++) {
			if (user_link[i] == 1 && (FD_ISSET(userfd[i], &sockset))) {
				memset(line, 0 ,MAX_LINE);
				unsigned int length = recv(userfd[i], line, MAX_LINE, 0);
				ifd++;
				if (length <= 0) {
					close(userfd[i]);
					user_link[i] = 0;
					FD_CLR(userfd[i], &sockset);
				} else {
					do_server_statistic(userfd[i], line, length);
				}
			}
		}

		FD_ZERO(&sockset);
		FD_SET(sockfd, &sockset);
		int j;
		for (j = 0; j < MAX_CLIENT; j++) {
			if (user_link[j] == 1)
				FD_SET(userfd[j], &sockset);
		}
	}

	return 0;
 }

