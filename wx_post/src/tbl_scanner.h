#pragma once
/****************************************************************************************
 * 文 件 名 : tbl_scanner.h
 * 项目名称 : 
 * 模 块 名 : TblScanner
 * 功    能 : 在指定路径下扫描指定后缀的文件
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-08-01
* 编    码 : root      '2019-08-27
* 修    改 : 从传filename改传fp
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <queue>
#include <string>
#include <vector>
#include <thread>
#include <mutex>
#include <unordered_map>
#include <stdio.h>

class TblScanner
{
private:
    std::string tblDirPath_;
    std::string processEnd_;
    std::string afterReadEnd_;
    bool deleteTbl_;

    std::thread tblScan_t_;                                                                     //扫描线程
    std::mutex mtx_;
    std::unordered_map<std::string, std::pair<std::string, FILE *>> tblRecorder_;                                              // 记录tbl 

    TblScanner(/* args */)
    {
        init();
        tblScan_t_ = std::thread(std::bind(&TblScanner::scan, this));
    }
    ~TblScanner() = default;

public:
    static TblScanner & getInstance()
    {
        static TblScanner TblScanner_;
        return TblScanner_;
    }

public:
    int init();

    int scan();

    int work();

    int shouldProcessTbl(const char *tbl_name, const char *proto_name);

    int shouldDeleteTbl(const char *tbl_name);
};