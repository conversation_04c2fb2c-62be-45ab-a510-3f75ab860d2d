#pragma once

#include <string>
#include <set>
#include <vector>
#include <initializer_list>

#include "json.hpp"

using json = nlohmann::json;

class TblConfig
{
public:
    static TblConfig &getInstance()
    {
        static TblConfig tblConfig_;
        return tblConfig_;
    }
    static void      setWorkMode(int);
    static int       getWorkMode(void);

public:
    std::set<std::string> protoRecord_;     // 从配置文件中导入的协议
    std::set<std::string> allOutputDir_;    // 从配置文件中导入的全部输出路径

public:
    int       setConfigPath(std::string &);
    int       setFieldPath(std::string &);

    bool      getLoggerOut();
    uint16_t  getWorkThreadNum();

    int  ParseCmdLineOpts(int argc, char *argv[]);

    int init();
    int reset();

    std::string getTblPath();
    std::string getFieldPath();

public:

template <class T>
auto getCurrentConfigValue(std::initializer_list<std::string> args, T defaultValue)->decltype(defaultValue)
{
    json v = json_conf;
    for(const auto &it:args)
    {
        if(v.find(it) != v.end())
        {
            v = v[it];
        }
        else
        {
            return defaultValue;
        }
        
    }
    if(v.empty())
    {
        return defaultValue;
    }
    else
    {
        return v.get<T>();
    }
}

    std::vector<json> getCurrentArray(std::initializer_list<std::string>);     // 获取指定的数组
    std::vector<std::string> getKeyArray(std::initializer_list<std::string>);  // 获取指定json中的key

private:
    std::string configPath_ = "config.json";
    std::string fieldPath_;

private:
    nlohmann::json json_conf;

private:
    TblConfig()  = default;
    ~TblConfig() = default;

    int  loadConfig();
    int  loadConstant();
    bool format_check(const std::string &);

private:
    uint16_t threadNum_      = 1;
    bool     bLogToTerminal_ = false;

private:
    static volatile int workState_;

};


// TblConfig* TblConfig::getInstance()
// {
//     static TblConfig tblConfig_;
//     return &tblConfig_;
// }

extern TblConfig &CFG;