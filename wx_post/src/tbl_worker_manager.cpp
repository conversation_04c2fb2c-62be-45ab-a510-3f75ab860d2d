#include "tbl_manager.h"
#include "tbl_logger.h"

TblWorkerManager::TblWorkerManager()
{
    for(auto &it : CFG.protoRecord_)
    {
        if(worker_manager.find(it) == worker_manager.end())
        {
            worker_manager[it] = new TblWorker(const_cast<std::string &>(it));
            worker_manager[it]->init();
        }
    }
}

TblWorkerManager::~TblWorkerManager()
{
    if(!worker_manager.empty())
    {
        for(auto &it : worker_manager)
        {
            if(it.second != nullptr)
            {
                delete it.second;
                it.second = nullptr;
            }
        }
        worker_manager.clear();
    }
}

void TblWorkerManager::setProtoAndData(std::string& proto, std::vector<std::string> & rst)
{
    if(worker_manager.find(proto)!=worker_manager.end())
    {
        worker_manager[proto]->setTblData(rst);
        worker_manager[proto]->work();
    }
    else
    {
        LOG_DEF->debug("cannot find proto {}, tbl data will drop.", proto);
    }
}