#include "tbl_writer.h"
#include "tbl_config.h"
#include "tbl_utils.h"
#include "tbl_logger.h"
#include "tbl_statistics.h"

#include <stdio.h>

std::string TblWriter::defaultDir_ ;
std::string TblWriter::invalidDir_ ;

TblWriter::TblWriter(std::string & proto) : protoName_(proto)
{
    std::string && field_name = CFG.getCurrentConfigValue({"work_conf", proto, "field_name"}, std::string(""));          // 获取字段名
    
    for(auto it:CFG.getKeyArray({"work_conf", proto, field_name}))                                                       // 获取输出路径
    {
        if (access(it.c_str(), R_OK) != 0)
        {
            makeDir(it.c_str());
        }
        outputTblList_.emplace(it+'/'+proto, std::make_tuple(nullptr, 0, 0 ,""));
    }

    std::string && default_dir = CFG.getCurrentConfigValue({"output_conf", "default_dir"}, std::string());              // 获取默认输出路径
    if(!default_dir.empty())
    {
        if (access(default_dir.c_str(), R_OK) != 0)
        {
            makeDir(default_dir.c_str());
        }
        outputTblList_.emplace(default_dir+'/'+proto, std::make_tuple(nullptr, 0, 0 ,""));
    }
    defaultDir_ = default_dir;

    std::string && invalid_dir = CFG.getCurrentConfigValue({"output_conf", "invalid_dir"}, std::string());             // 获取非正常数据输出路径
    if(!invalid_dir.empty())
    {
        if (access(invalid_dir.c_str(), R_OK) != 0)
        {
            makeDir(invalid_dir.c_str());
        }
        outputTblList_.emplace(invalid_dir+'/'+proto, std::make_tuple(nullptr, 0, 0 ,""));
    }
    invalidDir_   = invalid_dir;

    tblNumLimit_  = CFG.getCurrentConfigValue({"work_conf", proto, "file_limit"}, 500);

    tblTimeLimit_ = CFG.getCurrentConfigValue({"work_conf", proto, "file_timeout"}, 60);

    createAllTbl();
}

void TblWriter::generateFileName(const std::string & strTblFileDir_)
{
    char *pFileName = fileNameBuf_;
    time_t unixTime = time(0);
    tm     tm       = *localtime((time_t *)&unixTime);
    lThreadNO_      = getpid()%1000;

    // 补全目录
    pFileName += sprintf(pFileName, "%s/", strTblFileDir_.c_str());

    // date string
    pFileName += strftime(pFileName, sizeof fileNameBuf_, "%Y%m%d%H%M%S", &tm);

    // etc
    pFileName += sprintf(pFileName, "_%05d_%s_%03d.tbl",
                         hundredThousandthSecond(),
                         protoName_.c_str(),
                         int(lThreadNO_));

    sprintf(fileNameBufWriting_, "%s%s", fileNameBuf_, TBL_WRITING_SUFFIX);
}

int TblWriter::createAllTbl()
{
        // 创建 xdr 目录
    for(auto & it: outputTblList_)
    {
        if (access(it.first.c_str(), R_OK) != 0)
        {
            makeDir(it.first.c_str());
        }
        /* 
        // 生成文件名
        generateFileName((it.first));
        // 创建文件
        FILE *tblFile_ = fopen(fileNameBufWriting_, "a");

        if (nullptr == tblFile_)
        {
            printf("create file error: %s\n", fileNameBufWriting_);
            return -1;
        }
        it.second = std::make_tuple(tblFile_, 0, time(NULL), std::string(fileNameBuf_));
        */
    }

    return 0;
}

int TblWriter::createDirTbl(const std::string &outPutDir)
{
    if(outputTblList_.find(outPutDir) != outputTblList_.end())
    {
        ensureDirExist(outPutDir.c_str());
        
        generateFileName(outPutDir);

        FILE *tblFile_ = fopen(fileNameBufWriting_, "w");
        
        if (nullptr == tblFile_)
        {
            printf("create file error: %s\n", fileNameBufWriting_);
            return -1;
        }
        outputTblList_[outPutDir] = std::make_tuple(tblFile_, 0, time(NULL), std::string(fileNameBuf_));
    }
    return 0;
}

int TblWriter::writeToFile(const std::string & outputDir, std::string &strRecordLine)
{
    if(strRecordLine.length() < 1)
    {
        LOG_DEF->debug("invalid data!");
        return 0;
    }
    std::string &&real_path = outputDir + '/' + protoName_;

    if(outputTblList_.find(real_path) == outputTblList_.end())
    {
        LOG_DEF->debug("invalid outputDir {}!",real_path);
        return 0;
    }

    if(std::get<0>(outputTblList_[real_path]) == nullptr)
    {
        LOG_DEF->debug("tbl file is not create!");
        //return 0;
        createDirTbl(real_path);

        if(std::get<0>(outputTblList_[real_path]) == nullptr)
        {
            LOG_DEF->error("tbl file create failed!");
            return -1;
        }
    }
    LOG_DEF->debug("write a tbl into {}", real_path);

    // 写入一条记录
    //fprintf(std::get<0>(outputTblList_[real_path]), "%s\n", strRecordLine.c_str());

    fputs((strRecordLine + '\n').c_str(),std::get<0>(outputTblList_[real_path]));

    LOG_ST.addWrite(protoName_, outputDir);
    
    // 计数还未达到上限且未超时
    if ((++ std::get<1>(outputTblList_[real_path]) < tblNumLimit_) && (time(NULL) - std::get<2>(outputTblList_[real_path]) < tblTimeLimit_) )
    {
        LOG_DEF->debug("Thers is no need to create a new file in {}.", real_path);
        return 0;
    }

    if(std::get<1>(outputTblList_[real_path]) == 0)
    {
        std::get<2>(outputTblList_[real_path]) = time(NULL);
    }

    fflush(std::get<0>(outputTblList_[real_path]));
    writeCurrentFileDone(real_path);
    return createDirTbl(real_path);
}

void TblWriter::writeCurrentFileDone(const std::string &outputDir)
{
    if (std::get<0>(outputTblList_[outputDir]) != nullptr)
    {
        LOG_DEF->debug("close {}", outputDir);
    
        fclose(std::get<0>(outputTblList_[outputDir]));
    
        std::get<0>(outputTblList_[outputDir]) = nullptr;
    }

    // tblCount = 0;
    std::get<1>(outputTblList_[outputDir]) = 0;

    // tblCreateTime = 0
    std::get<2>(outputTblList_[outputDir]) = 0;

    // rename file name (del its ".writing")
    rename((std::get<3>(outputTblList_[outputDir]) + TBL_WRITING_SUFFIX).c_str(), std::get<3>(outputTblList_[outputDir]).c_str());
}

void TblWriter::writeCurrentFileDone()
{
    for(auto &it:outputTblList_)
    {
        writeCurrentFileDone(it.first);
    }
    outputTblList_.clear();
}

std::string TblWriter::getDefaultDir()
{
    return defaultDir_;
}

std::string TblWriter::getInvalidDir()
{
    return invalidDir_;
}