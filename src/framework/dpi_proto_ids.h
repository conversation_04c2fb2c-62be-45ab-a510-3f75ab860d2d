#ifndef __DPI_PROTO_IDS_H__
#define __DPI_PROTO_IDS_H__


#include <stdio.h>



enum tbl_log_type {
    TBL_LOG_HTTP,
    TBL_LOG_HTTP_QQACC,
    TBL_LOG_HTTP_WXPH,
    TBL_LOG_HTTP_WX_MSG,
    TBL_LOG_WEIXIN,
    TBL_LOG_WEIXIN_MEDIA_CHAT,
    TBL_LOG_WEIXIN_PYQ,
    TBL_LOG_WEIXIN_POSITION,
    TBL_LOG_WEIXIN_GROUP_HEAD,
    TBL_LOG_WEIXIN_GROUP_HEAD_CONTENT,
    TBL_LOG_WEIXIN_MESSAGE,
    TBL_LOG_WEIXIN_HTTP_POST,
    TBL_LOG_QQ_VOIP,
    TBL_LOG_QQ_FILE,
    TBL_LOG_SKYPE_MEDIA_CHAT,
    TBL_LOG_ZOOM_CONFERENCE,
    TBL_LOG_WEIXIN_RELA,
    TBL_LOG_WEIXIN_INFO,
    TBL_LOG_WX_PEERS,
    TBL_LOG_QQ_EVENT,
    TBL_LOG_WX_LOCSHARING,
    TBL_LOG_WEIXIN_MISC,
    TBL_LOG_RELATION,
    TBL_LOG_GQUIC_WEIXIN,
    TBL_LOG_RTP,
    TBL_LOG_DOUYIN,
    TBL_LOG_WXID,
    TBL_LOG_WXPAY,
    TBL_LOG_ALIPAY,
    TBL_LOG_TENCENT_MEETING,
    TBL_LOG_MAX
};



enum PROTOCOL_TYPE {
    PROTOCOL_UNKNOWN,
    PROTOCOL_HTTP,
    PROTOCOL_HTTP_QQACC,
    PROTOCOL_HTTP_WXPH,
    PROTOCOL_HTTP_WX_MSG,
    PROTOCOL_WEIXIN,
    PROTOCOL_WEIXIN_MEDIA_CHAT,
    PROTOCOL_WEIXIN_PYQ,
    PROTOCOL_WEIXIN_POSITION,
    PROTOCOL_WEIXIN_GROUP_HEAD,
    PROTOCOL_WEIXIN_GROUP_HEAD_CONTENT,
    PROTOCOL_WEIXIN_MESSAGE,
    PROTOCOL_WEIXIN_HTTP_POST,
    PROTOCOL_QQ_VOIP,
    PROTOCOL_QQ_FILE,
    PROTOCOL_SKYPE_MEDIA_CHAT,
    PROTOCOL_ZOOM_CONFERENCE,
    PROTOCOL_WEIXIN_RELA,
    PROTOCOL_WEIXIN_INFO,
    PROTOCOL_WX_PEERS,
    PROTOCOL_QQ_EVENT,
    PROTOCOL_WX_LOCSHARING,
    PROTOCOL_WEIXIN_MISC,
    PROTOCOL_RELATION,
    PROTOCOL_GQUIC_WEIXIN,
    PROTOCOL_RTP,
    PROTOCOL_DOUYIN,
    PROTOCOL_WXID,
    PROTOCOL_WXPAY,
    PROTOCOL_ALIPAY,
    PROTOCOL_TENCENT_MEETING,
    PROTOCOL_MAX
};



typedef void (*call_dissector_init_func)(void);




struct tbl_file
{
    FILE                     *fp_tbl;
    char                      filename[256];
    unsigned int              log_num;
    unsigned int              ts;
};

struct tbl_log_file
{
    enum tbl_log_type         type;
    const char               *protoname;
    call_dissector_init_func  init_func;
    struct tbl_file           tbl[64];
};




extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];



#endif
