# 启动命令,需要搭配 setup_dpdk_env.sh -b
CMD_LINE = "./wx_dpi --vdev="pcap_dir,dir=/root/pcaps" -l 0-1 -- -r 1 -p 2 -l 3"

# DPDK 相关参数
RXQ_NUM = 4
DISSECTOR_THREAD_NUM = 16
MEMPOOL_CACHE_SIZE = 256
PACKET_RING_SIZE = 32768
MAX_PKT_LEN = 2000
#PACKET_RING_SIZE = 1073741824

# DPI 框架相关
TCP_FLOW_TIMEOUT = 30               # TCP 流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
UDP_FLOW_TIMEOUT = 30               # UDP 流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
SCTP_FLOW_TIMEOUT = 30              # STCP流超时时间. 如果一个流再没有新的报文进来, 到了时间就进入超时状态
TCP_IDENTIFY_PKT_NUM = 10           # 协议识别 TCP 包个数, 超过此值还未识别成功,就不再识别
UDP_IDENTIFY_PKT_NUM = 100          # 协议识别 UDP 包个数, 超过此值还未识别成功,就不再识别
SCTP_IDENTIFY_PKT_NUM = 10          # 协议识别 SCTP包个数, 超过此值还未识别成功,就不再识别


TCP_RESSEAMBLE_MAX_NUM = 32         # TCP 重组最多报文个数
LOG_MAX_NUM = 10000                 # TBL文件输出行数
IDLE_SCAN_PERION = 10000            # usec

TBL_FIELD_TABLE_DIR =  /root/program/field/                       #TBL字段表输出路径
TBL_WXF_FILTER_SWITCH = 1

# [resource usage config for poor machine (read from eth)]         #这里的minimum是读pcap包的值，从网卡读时需改大
NB_MBUF = 2048                                                    # default value: 1048576  ,minimum value 512
MAX_FLOW_NUM = 2048                                               # default value: 16777216 ,minimum value 1024
TCP_REASSEMBLE_MEMPOOL_NUM = 2048                                 # default value: 1048576  ,必须为大于 768 的 2 的幂 ,minimum value 512
MAX_HASH_NODE_PER_THREAD = 2048                                   # default value: 4194304  ,minimum value 64
MAX_CONVERSATION_HASH_NODE = 2048                                 # default value: 1048576  ,minimum value 256
TBL_RING_SIZE = 2048                                              # default value: 65536    ,必须为 2 的幂 ,minimum value 512
TBL_LOG_CONTENT_256K = 512                                        # default value: 262144   ,minimum value 256

LOG_OUTPUT_LEVEL = ERROR                                          #日志文件输出级别
TBL_OUT_DIR      = /tmp/tbls                                      #TBL文件输出路径
DEVNAME          = Blade_204                                      #计算板板号 标识
OPERATOR         = CMCC                                           #三大运营商标识 CMCC CUCC CTCC (AUTO 则自动探测)
RT_MODEL         = "RTL6402"                                      #戎腾设备型号  :[RTL6402,RTL9800]
DEVAREA          = "浙江杭州"                                     #计算板所在地域标识, 双引号括起来
TRAILER_TYPE     = 0                                              #Trailer 标签类型 0:无标签, 1:戎腾, 2:恒为

#微信解析功能开关[1:开 / 0:关]
PROTOCOL_SWITCH_WEIXIN_MEDIA_CHAT = 1 # 微信话单
PROTOCOL_SWITCH_QQ_VOIP           = 1 # QQ  话单
PROTOCOL_SWITCH_HTTP              = 1 # HTTP 协议
PROTOCOL_TBL_HTTP                 = 0 # HTTP 协议 产生 TBL文件   (默认关闭HTTP产生TBL数据)
PROTOCOL_SWITCH_WEIXIN_POSITION   = 1 # 微信分享位置             (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN_GROUP_HEAD = 1 # 微信群图像               (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN_GROUP_HEAD_CONTENT = 1 # 微信群图像内容还原(依赖 HTTP协议)
PROTOCOL_SWITCH_QQ_FILE           = 1 # QQ  文件传输             (依赖 HTTP协议)
PROTOCOL_SWITCH_WEIXIN            = 1 # 微信文件协议
PROTOCOL_SWITCH_WEIXIN_PYQ        = 1 # 朋友圈行为单独输出TBL文件(依赖 微信文件协议)
PROTOCOL_SWITCH_WEIXIN_MOMMENTS   = 1 # 朋友圈视频上传与观看行为 (依赖 微信文件协议)

# 输出调试信息到屏幕
DEBUG_WEIXIN_VOIP                 = 0
DEBUG_QQ_VOIP                     = 0
DEBUG_QQ_FILE                     = 0
DEBUG_WEIXIN_GROUP_HEAD           = 0
DEBUG_WEIXIN_POSITION_SHARE       = 0
DEBUG_WEIXIN_MOMMENTS_VIDEO       = 0

#设置聚合服务器: QQ文件 QQ话单 微信位置 微信群图像 朋友圈视频 --> 默认使用[微信话单]的ip port
WEXIN_VOICE_SESSION_SERVER_IP   = 127.0.0.1                       # 微信话单 聚合程序服务 IP
WEXIN_VOICE_SESSION_SERVER_PORT = 1024                            # 微信话单 聚合程序服务 port
#QQ_VOIP_SESSION_SERVER_IP       = 127.0.0.1                       # QQ 聚合程序服务 IP
#QQ_VOIP_SESSION_SERVER_PORT     = 1024                            # QQ 聚合程序服务 Port
#WEXIN_GROUP_SESSION_SERVER_IP   = 127.0.0.1                       # 微信群图像  ip
#WEXIN_GROUP_SESSION_SERVER_PORT = 1024                            # 微信群图像  port
#WEXIN_POSITION_SHARE_SERVER_IP  = 127.0.0.1                       # 微信位置分享 ip
#WEXIN_POSITION_SHARE_SERVER_PORT= 1024                            # 微信位置分享 port
#QQ_FILE_SERVER_IP               = 127.0.0.1                       # QQ 文件传输
#QQ_FILE_SERVER_PORT             = 1024                            # QQ文件传输
#WEIXIN_MOMENTS_SERVER_IP        = 127.0.0.1                       # 微信朋友圈视频 ip
#WEIXIN_MOMENTS_SERVER_PORT      = 1024                            # 微信朋友圈视频 port

#微信解析的专有设置
WEXIN_VOICE_SESSION_TIMELOOP    = 5                               #微信话单 间隔发送所有会话到聚合端的时间 (单位秒)
WEXIN_VOICE_SESSION_TIMEOUT     = 10                              #微信话单 会话超时时间 (单位秒)
WEXIN_VOICE_SESSION_DROP        = 1                               #微信话单 SessionID 前4个字节为0的检查(1, 检查. 0 不检查)
WEXIN_VOICE_SESSION_PKT_SIZE    = 50                              #微信话单 UDP报文最小值(UDP报文小于此值, 不会进入解析)
WEXIN_VOICE_SESSION_RING_SIZE   = 100                             #微信话单 1对1呼叫等待 响铃包最大值
WEXIN_VOICE_SESSION_FLAGS_PKT   = 3                               #微信话单 UDP D5数据包最小值(D5报文个数少于此值, 不会进入解析)
WEXIN_VOICE_SESSION_SEQ_JITTER  = 1024                            #微信话单 Sequence num 检测, 抖动值超过此值,不会进入解析
WEXIN_VOICE_SESSION_PHONE_WATCH = "8612312341234,8612343214321"   #限290个手机号,英文逗号隔开,无空格(被监视的手机号, 调试信息直接打印到终端)

#微信群头像
WEXIN_GROUP_HEAD_TIMEOUT        = 30                              #微信群头像上传(wxcs)超时时间，具有相同链接的图片上传一次后，超过该时间允许再次上传
#WEXIN_HEAD_IMAGE_OUT_DIR = /tmp/img/wx_head                       #暂定下行图片输出路径(目前无用)


