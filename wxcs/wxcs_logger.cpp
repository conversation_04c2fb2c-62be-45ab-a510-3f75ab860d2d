/****************************************************************************************
 * 文 件 名 : wxcs_logger.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-06
* 编    码 : root      '2019-01-06
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_logger.h"
#include "wxcs_config.h"
#include "wxcs_utils.h"

#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/async.h>

void WxcsLogger::createLogger(const std::string &strLogDir, bool bLogToFile)
{
    ensureDirExist(strLogDir.c_str());

    if (bLogToFile)
    {
        auto wxcsLogger     = spdlog::rotating_logger_mt<spdlog::async_factory>("wxcs",     strLogDir + "/log.txt",         CFG->GetValueOf<int>("LOG_MAX_SIZE", 104857600), 3);
        auto interestLogger = spdlog::rotating_logger_mt<spdlog::async_factory>("interest", strLogDir + "/interesting.txt", CFG->GetValueOf<int>("LOG_INTEREST_MAX_SIZE", 104857600),  3);
        spdlog::set_default_logger(wxcsLogger);
    }
    else
    {
        auto consoleLogger  = spdlog::stdout_color_mt("interest");
    }

    // log gloable level
    spdlog::set_level(spdlog::level::from_str(CFG->GetValueOf<CSTR>("LOG_LEVEL", "info")));

    // logger 每次遇到 warn 级别日志均 flush
    spdlog::get("interest")->flush_on(spdlog::level::warn);
}

std::shared_ptr<spdlog::logger> WxcsLogger::defaultLogger()
{
    return spdlog::default_logger();
}

std::shared_ptr<spdlog::logger> WxcsLogger::interestingLogger()
{
    return spdlog::get("interest");
}
