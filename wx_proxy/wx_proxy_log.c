/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */
#define _GNU_SOURCE
#include <pthread.h>
#include <time.h>
#include <sys/syscall.h>   /* For SYS_xxx definitions */
#include <sys/types.h>
#include <unistd.h>
#include <sys/stat.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "glib.h"

#include "wx_proxy_log.h"

#ifndef _GNU_SOURCE
#define _GNU_SOURCE             /* See feature_test_macros(7) */
#endif

#define ATOMIC_ADD_FETCH(a)    __sync_add_and_fetch(a, 1)
#define ATOMIC_SUB_FETCH(a)    __sync_sub_and_fetch(a, 1)
#define ATOMIC_ADD_NUM(a,n)    __sync_add_and_fetch(a, n)
#define ATOMIC_SUB_NUM(a,n)    __sync_sub_and_fetch(a, n)
#define ATOMIC_COM_SWAP(a,o,n) __sync_val_compare_and_swap(a,o,n)
#define ATOMIC_SET(a,n)        __sync_lock_test_and_set(a,n)
#define ATOMIC_ZERO(a)         __sync_lock_release(a)

#define new                  pthread_create
#define delete               pthread_detach
#define names                pthread_setname_np

#define MAX_LEN  256
#define MAX_LINE (1024*(1024/MAX_LEN))

struct log_line
{
    int      level;
    int      strlen;
    size_t   pid;
    size_t   tid;
    char     string[MAX_LEN];
};

struct log
{
    int                   run;
    int                   level;
    int                   has_tty;
    int                   max_size;
    int                   max_num;

    size_t                read;
    size_t                write;
    struct log_line       str[MAX_LINE];

    FILE                 *file;
    const char           *filepath;

    GAsyncQueue          *queue;
    pthread_t             thread;
};


static
struct log s_log;

static struct log_line*
log_get_node(void)
{
    struct log      *log  = &s_log;
    int             write = 0;
    if(log->write > (log->read + MAX_LINE))
    {
        return NULL; // Full
    }

    write = ATOMIC_ADD_FETCH(&log->write);
    return log->str + (write % MAX_LINE);
}

static int
log_push(struct log_line *line)
{
    struct log  *log  = &s_log;
    g_async_queue_push(log->queue, line);
    return 0;
}

static int
log_pop(struct log_line **line)
{
    struct log *log = &s_log;

    *line = g_async_queue_timeout_pop(log->queue, 1000*1000); // 100 ms
    if(NULL != *line)
    {
        ATOMIC_ADD_FETCH(&log->read);
    }
    return 0;
}

static const char*
log_color_red(void)
{
    return "\033[40;31;1m";
}

static const char*
log_color_green(void)
{
    return "\033[40;32;1m";
}

static const char*
log_color_yellow(void)
{
    return "\033[40;33;1m";
}

static const char*
log_color_5(void)
{
    return "\033[40;34;1m";
}

static const char*
log_color_6(void)
{
    return "\033[40;35;1m";
}

static const char*
log_color_7(void)
{
    return "\033[40;36;1m";
}

static const char*
log_color_gray(void)
{
    return "\033[40;37;1m";
}

static const char*
log_color_8(void)
{
    return "\033[40;38;1m";
}

static const char*
log_color_9(void)
{
    return "\033[40;39;1m";
}

static const char*
log_color_0(void)
{
    return "\033[40;30;1m";
}

static const char*
log_color_reset(void)
{
    return "\033[0m";
}

static int
log_write_get_color(int level, const char **s, int *sl, const char **e, int *el)
{
    const char* color_str;

    switch(level)
    {
        case DEBUG   :
            color_str = log_color_gray();
            break;
        case INFO    :
            color_str = log_color_green();
            break;
        case WARN :
            color_str = log_color_yellow();
            break;
        case ERROR   :
            color_str = log_color_red();
            break;
        case 5:
            color_str = log_color_5();
            break;
        case 6:
            color_str = log_color_6();
            break;
        case 7:
            color_str = log_color_7();
            break;
        case 8:
            color_str = log_color_8();
            break;
        case 9:
            color_str = log_color_9();
            break;
        case 0:
            color_str = log_color_0();
            break;
        default:
            color_str = log_color_red();
            break;
    }

    *s  = color_str;
    *sl = strlen(color_str);

    *e  = log_color_reset();
    *el = strlen(*e);

    return 0;
}

static const char*
log_write_get_time(char *s, int *sl)
{
    time_t        time_curr;
    struct   tm  *time_info;

    time(&time_curr);
    time_info = localtime(&time_curr);
    *sl = strftime(s, *sl, "%Y-%m-%d %H:%M:%S ", time_info);
    return s;
}

static int
log_write_color(int fd, size_t pid, size_t tid, int level, const char *str, int len)
{
    const char   *color_str;
    int           color_str_len;
    const char   *color_end;
    int           color_end_len;

    char          time_buff[32];
    int           time_len = sizeof(time_buff);

    char          tid_buff[64];
    int           tid_len;

    log_write_get_time(time_buff, &time_len);

    tid_len = snprintf(tid_buff, sizeof(tid_buff), "\033[%d;%zd;01m[%zd]\033[0m ", 40, ((tid+31)%9)+30, tid);

    log_write_get_color(time(NULL)%10, &color_str, &color_str_len, &color_end, &color_end_len);
    write(fd, color_str, color_str_len);
    write(fd, time_buff, time_len);
    write(fd, color_end, color_end_len);
    write(fd, tid_buff, tid_len);

    log_write_get_color(level, &color_str, &color_str_len, &color_end, &color_end_len);
    write(fd, color_str, color_str_len);
    write(fd, str, len);
    write(fd, color_end, color_end_len);
    write(fd, "\n", 1);
    return 0;
}

static int
log_rename(const char *file, int max)
{
    char        file_max[128];
    char        file_new[128];
    char        file_old[128];
    int         ret;
    struct stat st;

    snprintf(file_max, sizeof(file_max), "%s.%d", file, max);
    ret = stat(file_max, &st);
    if(0 == ret)
    {
        unlink(file_max);
    }

    while(max > 1)
    {
        snprintf(file_new, sizeof(file_old), "%s.%d", file, max);
        snprintf(file_old, sizeof(file_new), "%s.%d", file, max-1);
        if (0 == stat(file_old, &st)  && 0 != stat(file_new, &st))
        {
            rename(file_old, file_new);
        }
        max--;
    }
    rename(file, file_old);
    return 0;
}

static int
log_write_file(int fd, int pid, int tid, int level, const char *str, int len)
{
    char             buff[32];
    int              size = sizeof(buff);
    size_t           filesize;
    struct log      *log;

    log      = &s_log;
    filesize = lseek(fd, 0, SEEK_CUR);
    if(filesize >= (size_t)log->max_size)
    {
        close(fd);
        log_rename(log->filepath, log->max_num);
        log->file = fopen(log->filepath, "a+");
        if(NULL == log->file)
        {
            printf("HELP: Disk is fulll\n");
            return -1;
        }
    }

    log_write_get_time(buff, &size);
    write(fd, buff, size);

    size = snprintf(buff, sizeof(buff), "[%d][%d] ", pid, tid);
    write(fd, buff, size);

    write(fd, str, len);
    write(fd, "\n", 1);
    return  0;
}

static void
log_free(void)
{
    struct log *log;
    log = &s_log;

    if(NULL != log->file)
    {
        fclose(log->file);
        log->file = NULL;
    }

    if(NULL != log->queue)
    {
        g_async_queue_unref(log->queue);
        log->queue = NULL;
    }
}


static void*
log_queue(void* p)
{
    struct log        *log;
    struct log_line   *str;
    int    stop        = 0;

    log = p;

    log_write(INFO, "日志线程已运行...");
    while(log->run)
    {
        log_pop(&str);
        if(NULL == str)
        {
            continue;
        }

        if(STOP == str->level)
        {
            stop       = 1;
            str->level = ERROR;
        }

        if(1 == log->has_tty)
        {
            log_write_color(fileno(stdout), str->pid, str->tid, str->level, str->string, str->strlen);
        }
        else
        {
            if(NULL == log->file)
            {
                printf("HELP: can't open log file\n");
                continue;
            }
            log_write_file(fileno(log->file), str->pid, str->tid, str->level, str->string, str->strlen);
        }

        if(stop)
        {
            exit(0);
        }
    }

    log_free();
    log_write(INFO, "日志线程 正常退出");
    return p;
}

int
log_init(int level, const char *filepath, int max_size, int max_num)
{
    struct log* log;

    log         = &s_log;
    memset(log, 0, sizeof(*log));

    log->run             = 0;
    log->level           = level;
    log->max_size        = max_size;
    log->max_num         = max_num;
    log->has_tty         = isatty(fileno(stdout));
    log->file            = fopen(filepath, "a+");
    log->filepath        = filepath;
    log->queue           = g_async_queue_new();
    log->run             = 1;

    what(NULL == log->file,  goto ERROR, "ERROR fopen %s", filepath);
    what(NULL == log->queue, goto ERROR, "ERROR create queue");

    log_write(DEBUG, "log_FILE  %p", log->file);
    log_write(DEBUG, "log_Queue %p", log->queue);

    new(&log->thread,  NULL, log_queue, log);
    names(log->thread, "log_thread");

    return 0;

ERROR:
    log_write(ERROR, "log thread ERROR");
    return -1;
}

void
log_exit(void)
{
    s_log.run = 0;
    pthread_join(s_log.thread, NULL);
}

int
log_write(int level, const char *format, ...)
{
    struct log_line  *line;
    struct log       *log;

    log = &s_log;

    if(NULL == format || level < log->level)
    {
        return -1;
    }

    if(0 == log->run)
    {
        char buff[1024];
        int  len;
        va_list args;
        va_start(args, format);
        len  = vsnprintf(buff, sizeof(buff), format, args);
        va_end(args);
        log_write_color(fileno(stdout), getpid(), syscall(__NR_gettid), level, buff, len);
        return 0;
    }

    line = log_get_node();
    if(NULL == line)
    {
        return -1;
    }

    va_list args;
    va_start(args, format);
    vsnprintf(line->string, MAX_LEN, format, args);
    va_end(args);

    line->level  = level;
    line->strlen = strlen(line->string);
    line->pid    = getpid();
    line->tid    = syscall(__NR_gettid);
    log_push(line);
    return line->strlen;
}





