#ifndef _DPI_WX_VOICE_PEERS_H
#define _DPI_WX_VOICE_PEERS_H

#include <stdio.h>

#include "wxcs_def.h"
#include "dpi_string.h"

#define DPI_WX_PEERS_DATA_MINLEN    64 
#define DPI_WX_PEERS_DATA_MAXLEN    256 

#define DPI_WX_PEERS_PERFIX         0xa3
#define DPI_WX_PEERS_FLAG           0x18
#define DPI_WX_PEERS_FLAG0          0x18302230
#define DPI_WX_PEERS_FLAG1          0x18282228
#define DPI_WX_PEERS_SUFFIX         0x3800

// typedef struct {
//     size_t len;
//     unsigned char *data;
// } dpi_str_t;

void init_wx_peers(void);


#endif /*_DPI_WX_VOICE_PEERS_H*/
