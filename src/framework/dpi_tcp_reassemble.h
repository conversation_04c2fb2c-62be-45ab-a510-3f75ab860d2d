/****************************************************************************************
 * 文 件 名 : dpi_tcp_reassemble.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TCP_REASSEMBLE_H_
#define _DPI_TCP_REASSEMBLE_H_

#include <sys/types.h>
#include <stdint.h>

#include "list.h"

#define TCP_PAYLOAD_MAX_LEN 1500

struct tcp_reassemble
{
	uint32_t seq;
	uint16_t payload_len;
	char payload[TCP_PAYLOAD_MAX_LEN];

	struct list_head node;
//	struct tcp_reassemble *next;
};

int tcp_reassemble_add_item(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq, const uint8_t *payload, const uint16_t payload_len);
int tcp_reassemble_do_guesslen(struct list_head *head, uint32_t *result_len, uint32_t *expect_len);

int tcp_reassemble_do(struct list_head *head, uint8_t *result, uint32_t *result_len);
int tcp_reassemble_do_surport_miss(struct list_head *head, uint8_t *result, uint32_t *result_len);

int tcp_reassemble_free(struct list_head *head, uint32_t *rsm_total_len);
int tcp_reassemble_free_less_than_seq(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq);

#endif
