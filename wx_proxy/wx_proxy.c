/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#define _GNU_SOURCE
#include <unistd.h>
#include <unistd.h>
#include <libgen.h>

#include <string.h>
#include <stdio.h>
#include <stdlib.h>

#include <event2/bufferevent.h>
#include <event2/listener.h>

#include <glib.h>

#include "wx_proxy_common.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_version.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_thread.h"
#include "wx_proxy_signal.h"
#include "wx_proxy_getopt.h"
#include "wx_proxy_timer.h"
#include "wx_proxy_server.h"
#include "wx_proxy_log.h"


/*****************************************************************
*Function    : main
*Description : game begin ...
*Input       : cmd line
*Output      : none
*Return      : exit code
*Others      : none
*****************************************************************/
int
main(int argc, char **argv)
{
    /***** Fight ! *******/
    struct evconnlistener *listener;
    struct context        *ctx;
    struct context         gctx;
    int                    ret;
    char                   buffer[BUFSIZ];

    ctx = &gctx;
    memset(ctx, 0, sizeof(*ctx));

    /**** cmd  parameters parse ****/
    proxy_parse_argv(argc, argv,  ctx);

    /**** confg file parse ****/
    proxy_conf_parse(ctx->conf_file_path, &ctx->conf);
    log_write(INFO, "workdir:[%s]", getcwd(buffer, sizeof(buffer)));

    /**** log init ****/
    ret = log_init(ctx->conf.log_level, ctx->conf.log_filepath, ctx->conf.log_size, ctx->conf.log_num);
    what(0 != ret, pexit(-1), "ERROR init log");

    /**** create master Event ****/
	ctx->master_base = event_base_new();
    what(NULL == ctx->master_base, pexit(-1), "ERROR create master event base");

    /**** catch signal  ****/
    signal_init(ctx);

    listener     = create_server    (ctx->conf.listen, ctx);
    what(NULL == listener, pexit(-1), "ERROR create server");

    /**** master timer ****/
    ctx->master_tv.tv_sec  =  30;
    ctx->master_tv.tv_usec =  0;
    ctx->master_timer      = evtimer_new(ctx->master_base, do_master_timer, ctx);
    evtimer_add(ctx->master_timer, &ctx->master_tv);

    /**** work thread init ****/
    ctx->create = proxy_thread_entry;
    ret = proxy_thread_init(ctx->conf.thread_num, ctx);
    what(0 != ret, pexit(-1), "ERROR thread init");

    /**** Event Process ****/
	event_base_dispatch(ctx->master_base);

    /**** game over ****/
    proxy_thread_wait(ctx);

	evconnlistener_free(listener);

    signal_free(ctx);
    evtimer_del(ctx->master_timer);
    event_free (ctx->master_timer);
	event_base_free (ctx->master_base);

    log_write(STOP, "master thread normal exit");
	return 0;
}

