
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <iconv.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"

#include "dpi_wx_info.h"

#include "dpi_wxmisc.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

dpi_field_table weixin_info_array_f[] = {
    DPI_FIELD_D(EM_WX_INFOTYPE,                    EM_F_TYPE_STRING,                 "wxInfoType"),
    DPI_FIELD_D(EM_WX_INFO_WXID,                    EM_F_TYPE_STRING,                 "wxid"),
    DPI_FIELD_D(EM_WX_INFO_WECHAT_ID,               EM_F_TYPE_STRING,                 "weChatId"),
    DPI_FIELD_D(EM_WX_INFO_NICK_NAME,               EM_F_TYPE_STRING,                 "wxNickName"),
    DPI_FIELD_D(EM_WX_INFO_NICK_NAME_SIMPLE,        EM_F_TYPE_STRING,                 "wxNickNameSimple"),
    DPI_FIELD_D(EM_WX_INFO_NICK_NAME_FULL,          EM_F_TYPE_STRING,                 "wxNickNameFull"),
    DPI_FIELD_D(EX_EX_INFO_REMARK_NAME,             EM_F_TYPE_STRING,                 "wxRemarkName"),
    DPI_FIELD_D(EX_WX_INFO_REMARK_NAME_SIMPLE,      EM_F_TYPE_STRING,                 "wxRemarkNameSimple"),
    DPI_FIELD_D(EM_WX_INFO_REMARK_NAME_FULL,        EM_F_TYPE_STRING,                 "wxRemarkNameFull"),
    DPI_FIELD_D(EM_WX_INFO_HEAD_THUM_ATTR,          EM_F_TYPE_STRING,                 "wxHeadThumAttr"),
    DPI_FIELD_D(EM_WX_INFO_HEAD_THUM_URL,           EM_F_TYPE_STRING,                 "wxHeadThumUrl"),
    DPI_FIELD_D(EM_WX_INFO_HEAD_ATTR,               EM_F_TYPE_STRING,                 "wxHeadAttr"),
    DPI_FIELD_D(EM_WX_INFO_HEAD_URL,                EM_F_TYPE_STRING,                 "wxHeadUrl"),
    DPI_FIELD_D(EM_WX_INFO_COUNTRY,                 EM_F_TYPE_STRING,                 "wxCountry"),
    DPI_FIELD_D(EM_WX_INFO_PROVINCE_FULL,           EM_F_TYPE_STRING,                 "wxProvinceFull"),
    DPI_FIELD_D(EM_WX_INFO_CITY_FULL,               EM_F_TYPE_STRING,                 "wxCityFull"),
    DPI_FIELD_D(EM_WX_INFO_LOCATION_SIMPLE,         EM_F_TYPE_STRING,                 "wxLocationSimple"),
    DPI_FIELD_D(EM_WX_INFO_PERSON_SIGN,             EM_F_TYPE_STRING,                 "wxPersonSign"),
    DPI_FIELD_D(EM_WX_INFO_GH_ABOUT,                EM_F_TYPE_STRING,                 "wxGhAbout"),
    DPI_FIELD_D(EM_WX_INFO_PYQ_MSG_ID,              EM_F_TYPE_STRING,                 "wxPyqMsgId"),
    DPI_FIELD_D(EM_WX_INFO_PYQ_BG_IMG,              EM_F_TYPE_STRING,                 "wxPyqBgImg"),
    DPI_FIELD_D(EM_WX_INFO_RESV0,                   EM_F_TYPE_STRING,                 "wxInfoResv0"),
    DPI_FIELD_D(EM_WX_INFO_RESV1,                   EM_F_TYPE_STRING,                 "wxInfoResv1"),
    DPI_FIELD_D(EM_WX_INFO_RESV2,                   EM_F_TYPE_STRING,                 "wxInfoResv2"),
};


static
uint32_t code_convert(char *inbuf, size_t inlen, char *outbuf, size_t outlen)
{
        iconv_t cd;
        int rc;
        char **pin = &inbuf;
        char **pout = &outbuf;

        //cd = iconv_open("UTF-8", "GB2312");
        cd = iconv_open("UTF-8", "GBK");
        if (cd == 0) {
                return -1;
        }
        memset(outbuf, 0, outlen);
        if (iconv(cd, pin, &inlen, pout, &outlen) == ((size_t)-1)) {
                iconv_close(cd);
                return -1;
        }
        iconv_close(cd);

        return 0;
}

static
uint8_t incomplete_add_prefix(char * data, char * original, uint8_t length, uint8_t flag) {
    if (flag == 1) {
        if (g_config.wx_info_incomplete == 1) {
            snprintf(data, length + sizeof(WX_INFO_INCOMPLETE), "%s%s", WX_INFO_INCOMPLETE, original);
        } else {
            memset(data, 0, length);
        }
    } else {
        strcpy(data, original);
    }

    return 0;
}

static
uint8_t is_illegal(const char * data, uint8_t data_len)
{
    if (data == NULL) {
        return true;
    }

    uint8_t i = 0;
    // wxid 和 weChatId 一般为数字、字母和下划线的组合，可根据这个判定是否合法
    for (; i < data_len - 1; ++i) {
        // if (!isalnum(data[i]) && data[i] != '_') {
        if (!isgraph(data[i])) {
            return true;
        }
    }

    return false;
}

static
gboolean is_important_datas_illegal(WXInfo *info)
{
   uint8_t bit_flag = 0;
   uint8_t step = 0;

    //按 bit 位存储，存储变量长度是否为 0 标记
   if (strlen(info->wxid) == 0) {
       bit_flag = bit_flag | (1 << step);
       step += 1;
   } else if (is_illegal(info->wxid, info->wxidLen)) {
       return FALSE;
   }

   if (strlen(info->weChatId) == 0) {
       bit_flag = bit_flag | (1 << step);
       step += 1;
   } else if (is_illegal(info->weChatId, info->weChatIdLen)) {
       return FALSE;
   }


   if (strlen(info->remarkName) == 0) {
       bit_flag = bit_flag | (1 << step);
       step += 1;
   }
//    else if (is_illegal(info->remarkName, info->remarkNameLen)) {
//        return FALSE;
//    }


   if (!bit_flag) {
       return TRUE;
   }

   return FALSE;

}


static
void get_ip_string(struct flow_info *flow, int direction, char *ip_string)
{
    char src_str[64];
    char dst_str[64];

    if (direction == FLOW_DIR_SRC2DST){
        if (flow->ip_version == 4)
            get_ip4string(src_str, sizeof(src_str), flow->tuple.inner.ip_src.ip4);
        else
            get_ip6string(src_str, sizeof(src_str), flow->tuple.inner.ip_src.ip6);
    }else{
        if (flow->ip_version == 4)
            get_ip4string(src_str, sizeof(src_str), flow->tuple_reverse.inner.ip_src.ip4);
        else
            get_ip6string(src_str, sizeof(src_str), flow->tuple_reverse.inner.ip_src.ip6);
    }

    if (direction == FLOW_DIR_SRC2DST){
        if (flow->ip_version == 4)
            get_ip4string(dst_str, sizeof(dst_str), flow->tuple.inner.ip_dst.ip4);
        else
            get_ip6string(dst_str, sizeof(dst_str), flow->tuple.inner.ip_dst.ip6);
    }else{
        if (flow->ip_version == 4)
            get_ip4string(dst_str, sizeof(dst_str), flow->tuple_reverse.inner.ip_dst.ip4);
        else
            get_ip6string(dst_str, sizeof(dst_str), flow->tuple_reverse.inner.ip_dst.ip6);
    }

    snprintf(ip_string, 128, "%s_%s", src_str, dst_str);
}

static
void export_data_to_file(struct flow_info *flow, int direction, const char *data, uint32_t payload_len)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);

    char file_name[128] = { 0 };
    char ip_string[128] = { 0 };
    get_ip_string(flow, direction, ip_string);
    snprintf(file_name, 128, "%s_%ld.dat", ip_string, (tv.tv_sec * 1000 + tv.tv_usec));

    FILE *fp;

    char file_path[256] = { 0 };
    char dir_path[256] = { 0 };

    snprintf(dir_path, 256,  "%s/%s/data", g_config.tbl_out_dir, tbl_log_array[TBL_LOG_WEIXIN_INFO].protoname);
    snprintf(file_path, 256, "%s/%s", dir_path, file_name);
    if (access(dir_path, F_OK) == -1) {
        mkdir(dir_path, 0755);
    }

    fp = fopen(file_path, "ab+");

    if (fp == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "erro while fopen wx_info debug out file");
        return;
    }

    fwrite(data, payload_len, 1, fp);
    // fputs(data, fp);
    // printf("ok!!");

    // fprintf(fp, "%s\n", data);

    fclose(fp);

}


int write_wx_info_log(struct flow_info *flow, int direction, WXInfo *info)
{
    int idx = 0,i;
    struct tbl_log *log_ptr;
    // struct http_session *session = (struct http_session *)flow->app_session;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }
    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0; i<EM_WX_INFO_MAX; i++)
    {
        switch(weixin_info_array_f[i].index)
        {
        case EM_WX_INFOTYPE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->infotype, strlen(info->infotype));
            break;
        case EM_WX_INFO_WXID:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxid, strlen(info->wxid));
            break;
        case EX_EX_INFO_REMARK_NAME:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->remarkName, strlen(info->remarkName));
            break;
        case EM_WX_INFO_WECHAT_ID:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->weChatId, strlen(info->weChatId));
            break;
        case EM_WX_INFO_NICK_NAME:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxNickName, strlen(info->wxNickName));
            break;
        case EM_WX_INFO_NICK_NAME_SIMPLE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxNickNameSimple, strlen(info->wxNickNameSimple));
            break;
        case EM_WX_INFO_NICK_NAME_FULL:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxNickNameFull, strlen(info->wxNickNameFull));
            break;
        case EM_WX_INFO_HEAD_THUM_ATTR:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxHeadThumAttr, strlen(info->wxHeadThumAttr));
            break;
        case EM_WX_INFO_HEAD_THUM_URL:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxHeadThumUrl, strlen(info->wxHeadThumUrl));
            break;
        case EM_WX_INFO_HEAD_ATTR:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxHeadAttr, strlen(info->wxHeadAttr));
            break;
        case EM_WX_INFO_HEAD_URL:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxHeadUrl, strlen(info->wxHeadUrl));
            break;
        case EM_WX_INFO_PERSON_SIGN:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxPersonSign, strlen(info->wxPersonSign));
            break;
        case EM_WX_INFO_LOCATION_SIMPLE:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxLocSimple, strlen(info->wxLocSimple));
            break;
        case EM_WX_INFO_GH_ABOUT:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->wxGHAbout, strlen(info->wxGHAbout));
            break;
        case EM_WX_INFO_RESV1:
            write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, info->resv1, strlen(info->resv1));
            break;
        default:
            write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY,NULL, 1);
            break;
        }
    }


    log_ptr->type        = TBL_LOG_WEIXIN_INFO;
    log_ptr->len         = idx;
    log_ptr->tid          = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1)
    {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


static
int decode_previous(const uint8_t *payload, uint32_t begin_pos, uint32_t end_pos, WXInfo *info)
{
    uint32_t i = 0;
    for (i = begin_pos; i < end_pos; i++) {
        if (payload[i] != WX_INFO_WXID) {
            continue;
        }
        uint8_t wxidLen = get_uint8_t(payload, i + 1);
        if (wxidLen + i + 1 + 1 > end_pos) {
            return 0;
        }
        strncpy(info->wxid, (const char *)payload + i + 1 +1, wxidLen);

    }

    return 0;
}


/*
* 提高过滤精度，减少误识别的情况
*/
static
uint8_t filter_dissect(const uint8_t *payload, const uint32_t payload_len, uint16_t pos)
{
    uint8_t type = 0, length = 0;
    uint32_t offset = pos;
    uint8_t type_arr[6] = {0x32, 0x12, 0x18, 0x2a, 0x3a, 0x42};
    uint8_t i = 0;
    for (; i < 6; i++) {
        type = get_uint8_t(payload, offset);
        offset += 1;
        length = get_uint8_t(payload, offset);
        offset += 1;
        if (type != type_arr[i]) {
            return 0;
        }

        if (type == WX_INFO_18) {
            continue;
        }

        offset += length;

        if (offset > payload_len) {
            return 0;
        }
    }

    return 1;
}

static int
decode_wxid(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxidLen = length;
    strncpy(info->wxid, (const char *)payload + offset, length);
    return 0;
}

static int
decode_wxnum(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->weChatIdLen = length;
    strncpy(info->weChatId, (const char *)payload + offset, length);
    return 0;
}

static int
decode_nickname_full(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxNickNameFullLen = length;
    strncpy(info->wxNickNameFull, (const char *)payload + offset, length);
    return 0;
}

static int
decode_sremark_scountry(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    if (payload[offset + length] == WX_INFO_WXNUM) {
        info->remarkNameLen = length;
        strncpy(info->remarkName, (const char *)payload + offset, length);
    } else {

    }
    return 0;
}

static int
decode_sremark_fpov(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    return 0;
}

static int
decode_fremark_fcity(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    return 0;
}

static int
decode_personal_signature(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    // char dst[128] = { 0 };
    // code_convert(payload + offset, length, dst, sizeof(dst));
    // if (dst[0] != '\0') {
    //     strncpy(info->wxPersonSign, dst, strlen(dst));
    // }
    char original[256] = { 0 };
    strncpy(original, (const char *)payload + offset, length);

    incomplete_add_prefix(info->wxPersonSign, original, length, flag);
    // strncpy(info->wxPersonSign, payload + offset, length);

    return 0;
}

static int
decode_wxhead_thumbnail_attr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxHeadThumAttrLen = length;
    strncpy(info->wxHeadThumAttr, (const char *)payload + offset, length);
    return 0;
}

static int
decode_wxhead_attr(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxHeadAttrLen = length;
    strncpy(info->wxHeadAttr, (const char *)payload + offset, length);
    return 0;
}

static int
decode_wxhead_thumbnail_url(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxHeadThumUrlLen = length;
    // strncpy(info->wxHeadThumUrl, payload + offset, length);

    char original[256] = { 0 };
    strncpy(original, (const char *)payload + offset, length);

    incomplete_add_prefix(info->wxHeadThumUrl, original, length, flag);
    return 0;
}

static int
decode_wxhead_url(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    info->wxHeadUrlLen = length;
    // strncpy(info->wxHeadUrl, payload + offset, length -1 );
    // memcpy(info->wxHeadUrl, payload + offset, length);

    char original[256] = { 0 };
    strncpy(original, (const char *)payload + offset, length);

    incomplete_add_prefix(info->wxHeadUrl, original, length, flag);
    return 0;
}

static int
decode_pyq_message_id(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    return 0;
}

static int
decode_pyq_background_img(const uint8_t *payload, const uint16_t payload_len, uint32_t offset,
                        uint16_t length,  uint8_t flag, WXInfo *info)
{
    return 0;
}

typedef struct _wx_opt {
    int optcode;
    int (*decode) (const uint8_t *, const uint16_t, uint32_t, uint16_t, uint8_t, WXInfo *);
} wx_opt_t;

static const wx_opt_t wx_opt[] = {
    /* 0x0a */ {WX_INFO_WXID,                   decode_wxid},
    /* 0x12 */ {WX_INFO_WXNUM,                  decode_wxnum},
    /* 0x2a */ {WX_INFO_NICKNAME_FULL,          decode_nickname_full},
    /* 0x32 */ {WX_INFO_32,                     decode_sremark_scountry},
    /* 0x3a */ {WX_INFO_3A,                     decode_sremark_fpov},
    /* 0x42 */ {WX_INFO_42,                     decode_fremark_fcity},
    /* 0x4a */ {WX_INFO_PERSONAL_SIGN,          decode_personal_signature},
    /* 0x62 */ {WX_INFO_WXHEAD_THUM_ATTR,       decode_wxhead_thumbnail_attr},
    /* 0x6a */ {WX_INFO_WXHEAD_ATTR,            decode_wxhead_attr},
    /* 0x72 */ {WX_INFO_WXHEAD_THUM_URL,        decode_wxhead_thumbnail_url},
    /* 0x7a */ {WX_INFO_WXHEAD_URL,             decode_wxhead_url},
    /* 0xaa */ {WX_INFO_PYQ_MSG_ID,             decode_pyq_message_id},
    /* 0xba */ {WX_INFO_PYQ_BG_IMG,             decode_pyq_background_img},
    /* END  */ {0,                              NULL},
};

/*
*   d6包中，昵称和 wxid 有不完整的情况，考虑从完整的 tlv 字段开始作为判定条件
*   即查找第一个符合 tlv 格式的字符，通过长度偏移，若偏移之后的字符等于 0x12 则认为可以开始
*
 */
static
uint8_t find_begin(const uint8_t *data, uint16_t len, uint8_t *pos)
{
    uint16_t i = 0, j = 0, k = 0;
    uint8_t offset = 0;

    // 数据中第一个可能会出现的类型列表
    // 最后 3 个字符为确认字节，不作为类型遍历
    const uint8_t possible_arrs[POSIBLE_ARR_LEN] = {0x0a, 0x32, 0x12, 0x18, 0x2a, 0x3a, 0x42, 0x48};
    // 找到某一个类型之后向后对比的类型数
    const uint8_t comp_num = 4;
    for (i = 0; i < POSIBLE_ARR_LEN - comp_num; ++i) {
        for (j = 0; j < len; ++j) {
            if (data[j] != possible_arrs[i]) {
                continue;
            }
            offset = j;
            offset += 1;

            uint8_t type = 0;
            uint8_t flag = 1;
            // 向后匹配44个类型
            for (k = 0; k < comp_num; ++k) {
                uint8_t len = get_uint8_t(data, offset);
                offset += 1;
                if (type == WX_INFO_18) {
                    len = 0;
                }
                offset += len;
                type = get_uint8_t(data, offset);
                offset += 1;
                if (type != possible_arrs[i + k + 1]) {
                    flag = 0;
                    break;
                }
            }

            if (flag) {
                *pos = j;
                return true;
            }
        }
    }

    return false;
}

/*
* 过滤数据中间的未知无用字节，重新定位 tlv 循环解析 pos 的位置
*/
static
uint8_t find_mid_begin(const uint8_t *payload, const uint32_t payload_len, uint32_t *offset) {
    uint32_t pos = *offset;
    const uint8_t comp_arrs[3] = {0x32, 0x3a, 0x42};
    const uint8_t complen = 3;
    int i = 0;
    for (; i < FIND_MID_MAX_LEN; i++) {
        if (pos + i > payload_len) {
            return false;
        }
        if (payload[pos + i] != WX_INFO_20) {
            continue;
        }

        // 偏移 type
        uint32_t tmp_offset = pos + i + 1;

        int j = 0;
        uint8_t find_flag = 0;
        for (; j < complen; ++j) {
            uint8_t len = get_uint8_t(payload, tmp_offset);
            tmp_offset += 1;
            tmp_offset += len;
            uint8_t type = get_uint8_t(payload, tmp_offset);
            if (type != comp_arrs[j]) {
                break;
            }
            tmp_offset += 1;

            find_flag = find_flag | (1 << j);
        }

        // find_flag 和  0111 比较
        if (find_flag == 7) {
            *offset = tmp_offset - 1;
            return true;
        } else {
            continue;
        }
    }

    return false;
}

static
int dissect_weixin_info_unknown(const uint8_t *payload, const uint32_t payload_len,
                                uint32_t *offset, uint8_t type, WXInfo *info)
{
    uint8_t find_flag = find_mid_begin(payload, payload_len, offset);
    if (!find_flag) {
        *offset = payload_len;
    }

    return 0;
}

static
int dissect_weixin_info_each(const uint8_t *payload, const uint32_t payload_len,
                                uint32_t *offset, uint8_t type, WXInfo *info)
{
    uint32_t length = 0;
    if (type == WX_INFO_PYQ_MSG_ID
        || type == WX_INFO_PYQ_BG_IMG) {
            *offset += 1;
    }

    if (*offset >= payload_len) {
        return -1;
    }

    length = get_uint8_t(payload, *offset);
    *offset += 1;

    if (type == WX_INFO_WXHEAD_THUM_URL || type == WX_INFO_WXHEAD_URL) {
        if (payload[*offset] == 0x01) {
            *offset += 1;
        }
    }

    for (int i = 0; wx_opt[i].decode; i++)
    {
        if (wx_opt[i].optcode == type)
        {
            uint8_t flag = 0;
            if ((int)(*offset + length - payload_len) >= 0) {
                length = payload_len - (*offset);
                flag = 1;
            }

            /* 为了防止内存拷贝越界，在这里统一进行长度判定 */
            (*wx_opt[i].decode) (payload, payload_len, *offset, length, flag, info);
        }
    }

    return length;

}

static
int dissect_weixin_info_gengral(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
                            const uint32_t payload_len, uint8_t flag)
{
    uint32_t offset = 0;
    int32_t length = 0;
    const uint8_t * misc_data = NULL;
    uint32_t  misc_len = 0;
    uint8_t   misc_flag  = 0;

    /* 每帧数据以d6 开头 */
    if (payload[0] != WX_INFO_IDENT_0) {
        return 0;
    }

    offset += 1;
    /* unknown bytes */
    offset += 4;

    uint32_t head_len = get_uint32_ntohl(payload, offset);
    offset += 4;

    if (offset + head_len > payload_len) {
        return 0;
    }
    uint16_t identify = get_uint16_ntohs(payload, offset + head_len - 2);
    offset += head_len;

    uint8_t pos = 0;
    uint8_t find_flag =  find_begin(payload + offset, FIND_BEGIN_MAX_LEN, &pos);
    if (!find_flag) {
        misc_data = payload + offset;
        misc_len = payload_len - offset;
        goto misc;
    }

    // uint8_t filter_flag = filter_dissect(payload, payload_len, pos + offset);
    // if (filter_flag == 0) {
    //     return -1;
    // }

    WXInfo info;
    memset(&info, 0, sizeof(info));

    uint32_t begin_pos = offset;
    uint32_t end_pos = offset + pos;

    // decode_previous(payload, begin_pos, end_pos, &info);

    offset += pos;

    /* 中间暂时发现无意义的字段暂时跳过 */
    while (offset < payload_len) {
        uint8_t type = get_uint8_t(payload, offset);
        length = 0;

        offset += 1;

        switch (type) {
        case WX_INFO_18:
        case WX_INFO_20:
            offset += 1;
            break;
        case WX_INFO_48:
            offset += 3;
            break;
        case WX_INFO_82:
        case WX_INFO_88:
            dissect_weixin_info_unknown(payload, payload_len, &offset, type, &info);
            break;
        // case WX_INFO_A2:
        case WX_INFO_B0:
            offset += 2;
            break;
        #if 0
        case WX_INFO_A8:
            /* 该类型后面有4个字节不是必现 根据下一个类型来判断是否出现
            *  请不要颠倒 if 运行逻辑，此方法能避免脏数据误判*/
            if (payload[offset + 2 + 2 + 1] == WX_INFO_B0) {
                offset += 4;
            } else {
                offset += 2;
            }
            break;
        case WX_INFO_B8:
            offset += 8;
            break;
        case WX_INFO_D0:
            /* 该类型后面有4个字节不是必现 根据下一个类型来判断是否出现
            *  请不要颠倒 if 运行逻辑，此方法能避免脏数据误判*/
            if (payload[offset + 2 + 4 + 1] == WX_INFO_B0) {
                offset += 6;
            } else {
                offset += 2;
            }
            break;
        #endif
        default:
            length = dissect_weixin_info_each(payload, payload_len, &offset, type, &info);
            break;
        }

        /* 原意想直接丢掉，后来仔细想想，里面可能也会包含部分有用的个人信息，所以还是决定输出到tbl */
        if (length < 0) {
            break;
        }

         /* 到达最后一个类型，后面的数据未知，直接跳出循环 */
        if (type == WX_INFO_PYQ_BG_IMG) {
            misc_data = payload + offset;
            misc_len = payload_len - offset;
            misc_flag  = 1;
            break;
        }


        offset += length;
    }

    strcpy(info.infotype, "info");

    uint8_t illegal_flag = 0;
    // if (strlen(info.weChatId) == 0 && strlen(info.wxid) == 0) {
    //     illegal_flag = 0;
    // } else {
    //     if (is_illegal(info.weChatId, info.weChatIdLen) || is_illegal(info.wxid, info.wxidLen)) {
    //         illegal_flag = 1;
    //     }
    // }

    // 重要数据合法性判断
    illegal_flag =  is_important_datas_illegal(&info);

    if (illegal_flag) {
        if (g_config.wx_info_export_illegal == 1) {
            // export payload to file
            write_wx_info_log(flow, direction, &info);
            export_data_to_file(flow, direction, (const char *)payload, payload_len);
        }
    } else {
        write_wx_info_log(flow, direction, &info);
    }

    // misc 继续解析剩余报文
    if (misc_flag) goto misc;

    misc:
        dissect_weixin_misc(flow, direction, seq, misc_data, misc_len, flag);
    return 0;
}

static
int dissect_weixin_info(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload,
    const uint32_t payload_len, uint8_t flag)
{
    if (payload_len < WX_INFO_MIN_LEN) {
        return 0;
    }

    if (g_config.protocol_switch[PROTOCOL_WEIXIN_INFO] == 0 && g_config.protocol_switch_wxinfo_mini == 0) {
        return 0;
    }

    const char g_mini_flag[] = {0x08, 0x08, 0x0d, 0x0d, 0x0d, '\0'};
    int pos = dpi_strstr(payload, payload_len, g_mini_flag, strlen(g_mini_flag));
    if (pos < 0) {
        if (g_config.protocol_switch[PROTOCOL_WEIXIN_INFO]) {
            dissect_weixin_info_gengral(flow, direction, seq, payload, payload_len, flag);
        }
    } else {
        if (g_config.protocol_switch_wxinfo_mini) {
            dissect_weixin_info_mini(flow, direction, seq, payload, payload_len, flag);
        }
    }

    return 0;
}

static
gboolean identify_wxinfo(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    uint8_t offset = 0;

    if (payload_len < WX_INFO_MIN_LEN) {
        return false;
    }

    offset += 1;
    /* unknown bytes */
    offset += 4;
    if (payload_len < offset) {
        return false;
    }

    uint32_t head_len = get_uint32_ntohl(payload, offset);
    offset += 4;
    if (head_len > payload_len || offset > payload_len) {
        return false;
    }
    if (head_len + offset > payload_len) {
        return false;
    }

    uint16_t identify = get_uint16_ntohs(payload, offset + head_len - 2);
    if (identify != WX_INFO_IDENT_1) {
        return false;
        // flow->real_protocol_id = PROTOCOL_WEIXIN_INFO;
    }

    return true;
}

static
void identify_weixin_info(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    /* 每帧数据以d6 开头 */
    if (payload[0] != WX_INFO_IDENT_0) {
        return;
    }

    uint16_t type = 0;
    // printf("%d\n", identify_wxinfo(flow, payload, payload_len));

    if (identify_wxinfo(flow, payload, payload_len)) type = EM_TYPE_INFO;
    switch (type)
    {
    case EM_TYPE_INFO:
        flow->real_protocol_id = PROTOCOL_WEIXIN_INFO;
        break;
    default:
        // flow->real_protocol_id = PROTOCOL_WEIXIN_MISC;
        break;
    }

    return;
}


void init_weixin_info_dissector(void)
{

    write_proto_field_tab(weixin_info_array_f, EM_WX_INFO_MAX, "wx_info");

    // 将以下 UDP 协议的这些端口, 添加关联协议  PROTOCOL_WEIXIN_MEDIA_CHAT
    // port_add_proto_head(IPPROTO_UDP, 80,    PROTOCOL_WEIXIN_RELA);

    udp_detection_array[PROTOCOL_WEIXIN_INFO].proto         = PROTOCOL_WEIXIN_INFO;
    udp_detection_array[PROTOCOL_WEIXIN_INFO].identify_func = identify_weixin_info;
    udp_detection_array[PROTOCOL_WEIXIN_INFO].dissect_func  = dissect_weixin_info;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_WEIXIN_INFO].excluded_protocol_bitmask, PROTOCOL_WEIXIN_INFO);

    return;
}
