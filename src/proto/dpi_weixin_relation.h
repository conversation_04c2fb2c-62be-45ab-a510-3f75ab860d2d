#ifndef _DPI_WEIXIN_RELATION_H
#define _DPI_WEIXIN_RELATION_H


#include "wxcs_def.h"

#define WX_IDENT_0          0xd6
#define WX_IDENT_1          0xFFFFFFFF
#define WX_IDENT_2          0x00000000
// #define WXRELA_STR_LEN      24
typedef enum _em_weixin_rela_type{
    EM_WX_RELA_WXID,
    EM_WX_RELA_UIN,
    EM_WX_RELA_MAX
}EM_WX_RELA_TYPE;

typedef struct _wx_rela_info
{
    char wxid[32];
    char uin[32];
} WXRelaInfo;


wxc_handle g_wx_rela_handle = NULL;

uint8_t init_wx_relation(void);


#endif