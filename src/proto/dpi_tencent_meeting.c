#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_cjson.h"
#include "dpi_common.h"
#include "wxcs_def.h"
#include <glib.h>
#include <pthread.h>

extern struct global_config g_config;

// 全局变量
static wxc_handle g_tencent_meeting_handle = NULL;
static GHashTable* g_tencent_meeting_user_hash = NULL;
static pthread_rwlock_t g_tencent_meeting_rwlock = PTHREAD_RWLOCK_INITIALIZER;

// 用户信息结构体，用于关联个人常规账号和个人会议账号
typedef struct {
    uint64_t msisdn;
    uint32_t ip;
    char selfLoginNum[18];
    uint32_t lastActiveTime;
} TencentMeetingUser;

// protobuf解析函数声明
uint8_t decode_protobuf(char *input, int input_size, char **output);

// 腾讯会议信息解析接口
typedef struct {
    uint64_t meetingNum;
    uint64_t sessionId;
    int parseResult;  // 0: 失败, 1: 成功
} TencentMeetingInfo;

// 抽象的腾讯会议信息解析接口
static int parse_tencent_meeting_info(const uint8_t *payload, uint32_t payload_len,
                                      uint32_t info_offset, uint32_t info_len,
                                      TencentMeetingInfo *meeting_info) {
    if (!payload || !meeting_info || info_len == 0) {
        return 0;
    }

    memset(meeting_info, 0, sizeof(TencentMeetingInfo));

    // 解析protobuf格式的info部分
    char *json_output = NULL;
    uint8_t decode_result = decode_protobuf((char*)(payload + info_offset), info_len, &json_output);

    if (decode_result && json_output) {
        cJSON *json = dpi_cjson_parse_json(json_output);
        if (json) {
            // 获取个人会议账号 {"2":{"3": xxxxxxxxxxxx}}
            cJSON *node2 = dpi_cjson_get_object_field(json, "2");
            if (node2) {
                cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
                if (node3 && cJSON_IsNumber(node3)) {
                    meeting_info->meetingNum = (uint64_t)node3->valuedouble;
                    meeting_info->parseResult = 1;
                }
            }
            dpi_cjson_free_json(json);
        }
        free(json_output);
    }

    return meeting_info->parseResult;
}
typedef struct Tencent_meeting_session_t {
  int FlagPacketC2S;
  int FlagPacketS2C;
  int SessionType;
  uint64_t sessionId;
  char selfMeetingNum[18];
  char selfLoginNum[18];
  uint32_t firstActiveTime;
  uint32_t lastActiveTime;
  uint32_t c2sPackCount;
  uint32_t c2sByteCount;
  uint32_t s2cPackCount;
  uint32_t s2cByteCount;
} Tencent_meeting_session;

// 初始化全局hash表
static void init_tencent_meeting_hash(void) {
    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
    if (!g_tencent_meeting_user_hash) {
        g_tencent_meeting_user_hash = g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, g_free);
        if (!g_tencent_meeting_user_hash) {
            DPI_LOG(DPI_LOG_ERROR, "error on create tencent meeting user hash");
            exit(-1);
        }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
}

// 查找用户信息
static TencentMeetingUser* find_user_by_msisdn_or_ip(uint64_t msisdn, uint32_t ip) {
    TencentMeetingUser* user = NULL;

    pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_user_hash) {
        // 优先通过MSISDN查找
        if (msisdn != 0) {
            user = (TencentMeetingUser*)g_hash_table_lookup(g_tencent_meeting_user_hash, GUINT_TO_POINTER(msisdn));
        }
        // 如果MSISDN找不到，通过IP查找
        if (!user && ip != 0) {
            user = (TencentMeetingUser*)g_hash_table_lookup(g_tencent_meeting_user_hash, GUINT_TO_POINTER(ip));
        }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

    return user;
}

// 添加或更新用户信息
static void add_or_update_user(uint64_t msisdn, uint32_t ip, const char* selfLoginNum) {
    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_user_hash) {
        TencentMeetingUser* user = (TencentMeetingUser*)malloc(sizeof(TencentMeetingUser));
        if (user) {
            user->msisdn = msisdn;
            user->ip = ip;
            if (selfLoginNum) {
                strncpy(user->selfLoginNum, selfLoginNum, sizeof(user->selfLoginNum) - 1);
                user->selfLoginNum[sizeof(user->selfLoginNum) - 1] = '\0';
            } else {
                user->selfLoginNum[0] = '\0';
            }
            user->lastActiveTime = time(NULL);

            // 使用MSISDN作为key，如果没有MSISDN则使用IP
            gpointer key = GUINT_TO_POINTER(msisdn != 0 ? msisdn : ip);
            g_hash_table_replace(g_tencent_meeting_user_hash, key, user);
        }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
}

// 解析个人会议数据 (0x28开头)
static int dissect_tencent_meeting_28(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {

  if (payload_len < 20) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析Header长度和Info长度
  uint32_t headerLen = get_uint32_ntohl(payload, 1);
  uint32_t infoLen = get_uint32_ntohl(payload, 5);

  if (payload_len < 9 + headerLen + infoLen + 1) {
    return 0;
  }

  // 检查结尾是否为0x29
  if (payload[9 + headerLen + infoLen] != 0x29) {
    return 0;
  }

  // 使用抽象接口解析会议信息
  TencentMeetingInfo meeting_info;
  if (parse_tencent_meeting_info(payload, payload_len, 9 + headerLen, infoLen, &meeting_info)) {
    snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);

    // 更新会话信息
    session->lastActiveTime = time(NULL);
    if (session->firstActiveTime == 0) {
      session->firstActiveTime = session->lastActiveTime;
    }
  }

  return 0;
}
// 解析个人会议数据 (0x36开头)
static int dissect_tencent_meeting_36(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {

  if (payload_len < 20) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析包长度
  uint16_t packetLen = get_uint16_ntohs(payload, 1);
  if (payload_len < packetLen) {
    return 0;
  }

  // 检查固定字段
  if (get_uint32_ntohl(payload, 3) != 0x00000000) {
    return 0;
  }

  // 检查0x03a102
  if (get_uint16_ntohs(payload, 8) != 0x03a1 || payload[10] != 0x02) {
    return 0;
  }

  // 解析sessionID (4字节)
  uint32_t sessionID = get_uint32_ntohl(payload, 18);
  session->sessionId = sessionID;

  // 使用抽象接口解析会议信息
  uint32_t infoOffset = 29; // 跳过前面的固定字段
  if (payload_len > infoOffset) {
    uint32_t infoLen = payload_len - infoOffset;
    TencentMeetingInfo meeting_info;
    if (parse_tencent_meeting_info(payload, payload_len, infoOffset, infoLen, &meeting_info)) {
      snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);
    }
  }

  // 更新会话信息
  session->lastActiveTime = time(NULL);
  if (session->firstActiveTime == 0) {
    session->firstActiveTime = session->lastActiveTime;
  }

  return 0;
}
// 解析个人会议数据 (0x51开头)
static int dissect_tencent_meeting_51(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {

  if (payload_len < 16) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 检查固定字段 0x0011
  if (get_uint16_ntohs(payload, 1) != 0x0011) {
    return 0;
  }

  // 检查0x02
  if (payload[5] != 0x02) {
    return 0;
  }

  // 解析sessionID (4字节)
  uint32_t sessionID = get_uint32_ntohl(payload, 13);
  session->sessionId = sessionID;

  // 使用抽象接口解析会议信息
  uint32_t infoOffset = 17; // 跳过前面的固定字段
  if (payload_len > infoOffset) {
    uint32_t infoLen = payload_len - infoOffset;
    TencentMeetingInfo meeting_info;
    if (parse_tencent_meeting_info(payload, payload_len, infoOffset, infoLen, &meeting_info)) {
      snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);
    }
  }

  // 更新会话信息
  session->lastActiveTime = time(NULL);
  if (session->firstActiveTime == 0) {
    session->firstActiveTime = session->lastActiveTime;
  }

  return 0;
}
// 解析个人常规账号 (TCP)
static int dissect_tencent_meeting_tcp(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {

  if (payload_len < 32) {
    return 0;
  }

  // 检查标识符 0x00001770
  if (get_uint32_ntohl(payload, 4) != 0x00001770) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析个人常规账号
  char selfLoginNum[19] = {0};
  uint32_t accountOffset = 0;

  // 根据方向确定账号位置
  if (direction == 0) { // 上行
    // 查找0x1400000016标识
    for (uint32_t i = 8; i < payload_len - 18; i++) {
      if (get_uint32_ntohl(payload, i) == 0x14000000 && get_uint16_ntohs(payload, i + 4) == 0x0016) {
        accountOffset = i + 6;
        break;
      }
    }
  } else { // 下行
    // 查找0x1400000016标识
    for (uint32_t i = 8; i < payload_len - 18; i++) {
      if (get_uint32_ntohl(payload, i) == 0x14000000 && get_uint16_ntohs(payload, i + 4) == 0x0016) {
        accountOffset = i + 6;
        break;
      }
    }
  }

  if (accountOffset > 0 && accountOffset + 18 <= payload_len) {
    memcpy(selfLoginNum, payload + accountOffset, 18);
    selfLoginNum[18] = '\0';
    strncpy(session->selfLoginNum, selfLoginNum, sizeof(session->selfLoginNum) - 1);
    session->selfLoginNum[sizeof(session->selfLoginNum) - 1] = '\0';

    // 解析trailer获取MSISDN
    ST_trailer tmp_trailer;
    memset(&tmp_trailer, 0, sizeof(ST_trailer));

    dpi_TrailerParser(&tmp_trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);

    uint64_t msisdn = tmp_trailer.MSISDN;
    uint32_t ip = 0;

    // 获取IP地址
    if (flow->pkt.ip_ver == 4) {
      ip = flow->pkt.src_ip.ipv4;
    }

    // 添加到全局hash表
    add_or_update_user(msisdn, ip, selfLoginNum);

    // 更新会话信息
    session->lastActiveTime = time(NULL);
    if (session->firstActiveTime == 0) {
      session->firstActiveTime = session->lastActiveTime;
    }
  }

  return 0;
}

// 发送腾讯会议数据到wxcs
static void send_tencent_meeting_data(struct flow_info *flow, Tencent_meeting_session *session) {
  if (!session || !g_tencent_meeting_handle) {
    return;
  }

  ST_TecentMeeting person_info;
  memset(&person_info, 0, sizeof(ST_TecentMeeting));

  // 解析trailer
  dpi_TrailerParser(&person_info.trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
  dpi_TrailerGetMAC(&person_info.trailer, (const char*)flow->ethhdr, g_config.RT_model);
  dpi_TrailerGetHWZZMAC(&person_info.trailer, (const char*)flow->ethhdr);
  dpi_TrailerSetDev(&person_info.trailer, g_config.devname);
  dpi_TrailerSetOpt(&person_info.trailer, g_config.operator_name);
  dpi_TrailerSetArea(&person_info.trailer, g_config.devArea);

  // 设置IP和端口信息
  if (flow->pkt.ip_ver == 4) {
    person_info.srcIp = flow->pkt.src_ip.ipv4;
    person_info.dstIp = flow->pkt.dst_ip.ipv4;
  }
  person_info.srcPort = ntohs(flow->tuple.inner.port_src);
  person_info.dstPort = ntohs(flow->tuple.inner.port_dst);

  // 设置会话信息
  person_info.sessionId = session->sessionId;
  strncpy(person_info.selfMeetingNum, session->selfMeetingNum, sizeof(person_info.selfMeetingNum) - 1);
  strncpy(person_info.selfLoginNum, session->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);

  person_info.firstActiveTime = session->firstActiveTime;
  person_info.lastActiveTime = session->lastActiveTime;
  person_info.c2sPackCount = session->c2sPackCount;
  person_info.c2sByteCount = session->c2sByteCount;
  person_info.s2cPackCount = session->s2cPackCount;
  person_info.s2cByteCount = session->s2cByteCount;
  person_info.isTimeout = 0;

  // 尝试关联个人常规账号
  uint64_t msisdn = person_info.trailer.MSISDN;
  uint32_t ip = person_info.srcIp;

  TencentMeetingUser* user = find_user_by_msisdn_or_ip(msisdn, ip);
  if (user && user->selfLoginNum[0] != '\0') {
    strncpy(person_info.selfLoginNum, user->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);
  }

  // 发送数据
  wxc_sendMsg(g_tencent_meeting_handle, (const unsigned char*)&person_info, sizeof(ST_TecentMeeting), WXCS_TENCENT_MEETING);
}

static int dissect_tencent_meeting_chat(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  if (NULL == flow->app_session) {
    Tencent_meeting_session *tcmeeting_session;
    tcmeeting_session = malloc(sizeof(Tencent_meeting_session));
    if (NULL == tcmeeting_session) {
      DPI_LOG(DPI_LOG_ERROR, "error on malloc Tencent_meeting_session");
      return 0;
    }
    memset(tcmeeting_session, 0, sizeof(Tencent_meeting_session));
    flow->app_session = tcmeeting_session;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;

  // 更新包计数
  if (direction == 0) { // C2S
    session->c2sPackCount++;
    session->c2sByteCount += payload_len;
  } else { // S2C
    session->s2cPackCount++;
    session->s2cByteCount += payload_len;
  }

  int result = 0;
  switch (payload[0]) {
    case 0x28:
      result = dissect_tencent_meeting_28(flow, direction, seq, payload, payload_len);
      break;
    case 0x36:
      result = dissect_tencent_meeting_36(flow, direction, seq, payload, payload_len);
      break;
    case 0x51:
      result = dissect_tencent_meeting_51(flow, direction, seq, payload, payload_len);
      break;
    case 0x41:
      return 0;
      break;
    default:
      if (get_uint32_ntohl(payload, 4) != 0x00001770)
        return 0;
      result = dissect_tencent_meeting_tcp(flow, direction, seq, payload, payload_len);
      break;
  }

  // 如果解析到了sessionID和个人会议账号，发送数据
  if (session->sessionId != 0 && session->selfMeetingNum[0] != '\0') {
    send_tencent_meeting_data(flow, session);
  }

  return result;
}

static void identify_tencent_meeting_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return;
  }

  if (payload_len < 20) {
    return;
  }
  /* 判断报文的目标端口  */
  int port_src = ntohs(flow->tuple.inner.port_src);
  int port_dst = ntohs(flow->tuple.inner.port_dst);
  if (flow->tuple.inner.proto == 6) {
    if ((80 == port_dst || 80 == port_src || 443 == port_dst || 443 == port_src || 8080 == port_dst || 8080 == port_src) &&
        (get_uint32_ntohl(payload, 4) == 0x00001770)) {
      //tcp个人常规
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    } else if ((443 == port_dst || 443 == port_src) && (payload[0] == 0x28)) {
      // tcp个人会议
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
  } else {
    if ((443 == port_dst || 443 == port_src) &&
        (payload[0] == 0x28 || payload[0] == 36 || payload[0] == 28 || payload[0] == 41 || payload[0] == 51)) {
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
    return;
  }
}

void init_tencent_meeting_chat_dissector(void) {
  // 初始化wxc连接
  if (NULL == g_tencent_meeting_handle) {
    wxc_init(&g_tencent_meeting_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
  }

  // 初始化hash表
  init_tencent_meeting_hash();

  port_add_proto_head(IPPROTO_UDP, 443, PROTOCOL_TENCENT_MEETING);

  udp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
  port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_TENCENT_MEETING);

  tcp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;

  DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
  DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);

  return;
}
