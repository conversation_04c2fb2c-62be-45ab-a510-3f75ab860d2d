/****************************************************************************************
 * 文 件 名 : dpi_wx_msg.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: zhengquan  		2020/01/06
编码: zhengquan			2020/01/06
修改: chenzq            2020/12/16
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <string.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <pthread.h>
#include <glib.h>
#include <unistd.h>
#include <rte_mbuf.h>
#include <sys/time.h>

#include "dpi_wx_msg.h"

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_typedefs.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
// #include "dpi_wx_action_data.h"

//wx语音消息端口
#define WX_MSG_PORT_0					443
#define WX_MSG_PORT_1				    80
#define WX_MSG_PORT_2					8080

/*wx文字消息识别识别前3个字节*/
//0x 17 f1 03
#define IDENTIFY_TYPE					    0x17
#define IDENTIFY_VERSION				    0xf103

struct rte_mempool *g_wxtm_head_mempool;

const struct int_to_string wx_action_str[] = {
    {EM_SEND_RED_PACKET,      	"SendRedPacket"},
    {EM_SEND_VOICE_MSG,        	"SendVoiceMsg"},
    {EM_RECV_VOICE_MSG,       	"RecvVoiceMsg"},
    {EM_SEND_TXT_MSG,           "SendTxtMsg"},
    {0,           NULL}
};

static dpi_field_table  wxtm_field_array[] = {
	DPI_FIELD_D(EM_WXTM_START_TIME,				EM_F_TYPE_STRING,               "StartTime"),
	DPI_FIELD_D(EM_WXTM_ACTION,					EM_F_TYPE_STRING,               "Action"),
};

/* hash 查找节点 */
static
void * hash_find(void* hash, pthread_rwlock_t *rwlock, uint32_t key)
{
	if (NULL == hash) {
		return NULL;
	}

	pthread_rwlock_wrlock(rwlock);     //写锁
	void* ret = g_hash_table_lookup((GHashTable *)hash, GUINT_TO_POINTER(key));
	pthread_rwlock_unlock(rwlock);     //解锁
	return ret;
}

/* hash 添加节点 */
static 
int hash_insert(void* hash, pthread_rwlock_t *rwlock, uint32_t key, void* value)
{
	if (NULL == hash ||  NULL == value) {
		return -1;
	}

	pthread_rwlock_wrlock(rwlock);     //写锁
	int ret = g_hash_table_insert((GHashTable *)hash, GUINT_TO_POINTER(key), value);
	pthread_rwlock_unlock(rwlock);     //解锁
	
	return  ret;
}

static 
int hash_remove(void* hash, pthread_rwlock_t *rwlock, uint32_t key)
{
	// 开始删除超时的节点
	pthread_rwlock_wrlock(rwlock);    //写锁
	int ret = g_hash_table_remove((GHashTable *)hash, GUINT_TO_POINTER(key));
	pthread_rwlock_unlock(rwlock);    //解锁

	return ret;
}

static 
void copy_action_head(ST_trailer *trailer, UserHead *head) {
	head->IMEI                   = trailer->IMEI;
	head->IMSI                   = trailer->IMSI;
	head->MSISDN                 = trailer->MSISDN;
	head->PLMNID                 = trailer->PLMN_ID;
	head->TAC                    = trailer->TAC;
	//head->UserIp                 = trailer->hash_key;
}


static
gboolean wx_voice_timeout_callback(gpointer key, gpointer value, gpointer user_data) {
	WxMsgData *data = (WxMsgData *)value;
	if (!data )
		return FALSE;
	struct timeval tv;	
	gettimeofday(&tv, NULL);
	if (abs(tv.tv_sec - data->time) > WX_MSG_USER_OLD) {
		// g_free(data);
		return TRUE;
	}

	return FALSE;
}


static
void * wx_voice_timeout_thread(void *arg) {

	// 每隔固定时间遍历哈希表，超时老化删除key
	while (1) {
		pthread_rwlock_wrlock(&g_wx_voice_hash.rwlock);
		if (!g_hash_table_size(g_wx_voice_hash.hash)) {
			// 哈希表为空，解锁，休眠 5S
			pthread_rwlock_unlock(&g_wx_voice_hash.rwlock);
			sleep(3);
			continue;
		}

		guint del_key_num = g_hash_table_foreach_remove(g_wx_voice_hash.hash, wx_voice_timeout_callback, NULL);
		// printf("del_voice_key_num=%d\n", del_key_num);
		
		pthread_rwlock_unlock(&g_wx_voice_hash.rwlock);
		// 休眠 5s
		sleep(3);
	}

	return NULL;
}


static
gboolean wx_rp_timeout_callback(gpointer key, gpointer value, gpointer user_data) {
	WxRPData *data = (WxRPData *)value;
	if (!data) 
		return FALSE;
	struct timeval tv;	
	gettimeofday(&tv, NULL);
	if (abs(tv.tv_sec - data->time) > WX_MSG_USER_OLD) {
		// g_free(data);
		return TRUE;
	}

	return FALSE;
}

static
void * wx_rp_timeout_thread(void *arg) {
	// 每隔固定时间遍历哈希表，超时老化删除key
	while (1) {
		pthread_rwlock_wrlock(&g_wx_rp_hash.rwlock);
		if (!g_hash_table_size(g_wx_rp_hash.hash)) {
			// 哈希表为空，解锁，休眠 5S
			pthread_rwlock_unlock(&g_wx_rp_hash.rwlock);
			sleep(3);
			continue;
		}

		guint del_key_num = g_hash_table_foreach_remove(g_wx_rp_hash.hash, wx_rp_timeout_callback, NULL);
		// printf("del_rp_key_num=%d\n", del_key_num);
		
		pthread_rwlock_unlock(&g_wx_rp_hash.rwlock);
		// 休眠 5s
		sleep(3);
	}

	return NULL;
}

int init_wx_tm_data(void)
{
    uint8_t is_init = 0;
    pthread_rwlock_init(&g_wx_voice_hash.rwlock, NULL);
	pthread_rwlock_init(&g_wx_rp_hash.rwlock, NULL);

    //哈希表初始化
	pthread_rwlock_wrlock(&g_wx_voice_hash.rwlock);     //写锁
	//g_person_hash.person_hash = g_hash_table_new_full(g_int_hash, g_int_equal, NULL, NULL);
	g_wx_voice_hash.hash = g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, g_free);
	pthread_rwlock_unlock(&g_wx_voice_hash.rwlock);     //解锁

	if (g_config.wx_red_packet_switch == 1) {
		//哈希表初始化
		pthread_rwlock_wrlock(&g_wx_rp_hash.rwlock);     //写锁
		//g_person_hash.person_hash = g_hash_table_new_full(g_int_hash, g_int_equal, NULL, NULL);
		g_wx_rp_hash.hash = g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, g_free);
		pthread_rwlock_unlock(&g_wx_rp_hash.rwlock);     //解锁
		pthread_t tid_rp;
		if (g_config.wx_red_packet_precise == 1){
			int err_rp = pthread_create(&tid_rp, NULL, wx_rp_timeout_thread, NULL);
		}
	}
	

    //内存池初始化  
	// init_wxtm_head_mempool();

	pthread_t tid;
	int err = pthread_create(&tid, NULL, wx_voice_timeout_thread, NULL);

    //时间轮初始化
    // yv_TimerInit(&g_wxtm_timer_handle, 50, malloc, free);
	return 0;
}


static
void debug_print_value(WxActionData *line_info)
{
#if 1 /* add by zhengsw */	
	char _str[32] = { 0 };
	const char *type = NULL;
	uint32_t tmpip = line_info->head->UserIp;
	type = val_to_string(line_info->action_flag, wx_action_str);

	snprintf(_str, sizeof(_str), "%u.%u.%u.%u",(tmpip >> 24) &255, (tmpip >> 16) & 255, (tmpip >> 8) & 255, tmpip & 255); 
	printf("id:%s    ", _str);

	printf("type:%s    ", type);

	printf("time:%u\n", line_info->time);
#endif	
}


//基站写tbl
static int write_wx_action_log(struct flow_info *flow, int direction, WxMsgData *line_info)
{
    int idx = 0, i;
	struct tbl_log *log_ptr;
	//char str[512] = { 0 };
	char _str[512] = { 0 };
    char str[20480] = { 0 };
	const char *vstr;
    uint32_t tmpip;
	// char *p = NULL;

	if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
		DPI_LOG(DPI_LOG_WARNING, "not enough memory");
		return PKT_OK;
	}

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

	for (i = 0; i < EM_WXTM_MAX; i++) {
		switch (wxtm_field_array[i].index) {
		case EM_WXTM_START_TIME:
			timet_to_datetime((time_t)line_info->time, _str, sizeof(_str));
			const char *p = (const char *)(0 == line_info->time ? "" : _str);
			write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, p, strlen(p));
			//write_uint64_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info->time);
			break;
        case EM_WXTM_ACTION:
			vstr = val_to_string(line_info->action_flag, wx_action_str);
			if (vstr != NULL) 
			{
				write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, vstr, strlen(vstr));
			} else {
				write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
			}
            
            break;
		default:
			break;
		}
	}

    log_ptr->type = TBL_LOG_WEIXIN_MESSAGE;
	log_ptr->len  = idx;
	log_ptr->tid  = flow->thread_id;

	if (tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }


	return 0;
}

static
void wx_action_send_voice(struct flow_info *flow, int direction, uint32_t key, WxActionInfo *info)
{

#if 1
    // WxActionData *value;
    WxMsgData *value;
    //uint32_t hash_key = info->hash_key;
    //value = dpi_hash_lookup_int(&g_wx_hash, hash_key, 1, 0);
    // value = hash_find(g_wx_voice_hash.hash, &g_wx_voice_hash.wx_rwlock, key);
	pthread_rwlock_wrlock(&g_wx_voice_hash.rwlock);
	value = g_hash_table_lookup((GHashTable *)g_wx_voice_hash.hash, GUINT_TO_POINTER(key));
    if (value == NULL) {
        //开辟内存空间
		value = g_malloc0(sizeof(WxMsgData));
		// value = malloc(sizeof(WxMsgData));
		if (NULL == value) {
			printf("*********************************************[Driver Info] not enough memory\n");
			return;
		}

		value->action_flag = info->action_flag;
		//第一次创建key的时候直接把时间戳赋予开始时间
		value->time   = info->time_stamp;

		// hash_insert(g_wx_voice_hash.hash, &g_wx_voice_hash.wx_rwlock, key, (void *)value);
		g_hash_table_insert((GHashTable *)g_wx_voice_hash.hash, GUINT_TO_POINTER(key), (void *)value);

    } else {
		// pthread_rwlock_wrlock(&value->wxtm_rwlock);

		if ((info->time_stamp - value->time) < WX_TIME_INTERVAL) {
			value->time = info->time_stamp;

			/* mod by zhengsw */
			pthread_rwlock_unlock(&g_wx_voice_hash.rwlock);
			return;
		}
		value->time = info->time_stamp;
    }
	pthread_rwlock_unlock(&g_wx_voice_hash.rwlock);
    

	if (value != NULL) {
		// debug_print_value(value);
		write_wx_action_log(flow, direction, value);
	}
#endif
}


void wx_action_red_packet(struct flow_info *flow, int direction, uint32_t key, WxActionInfo *info)
{
#if 1
	uint8_t is_write = 0;
	uint8_t free_flag = 0;
	WxMsgData line_info;
    WxRPData *value;
    // value = hash_find(g_wx_rp_hash.hash, &g_wx_rp_hash.wx_rwlock, key);

	if (g_config.wx_red_packet_precise == 1) {
		pthread_rwlock_wrlock(&g_wx_rp_hash.rwlock);
		value = g_hash_table_lookup((GHashTable *)g_wx_rp_hash.hash, GUINT_TO_POINTER(key));
		if (value == NULL) {
			value = g_malloc0(sizeof(WxRPData));
			if (NULL == value) {
				printf("[Driver Info] not enough memory\n");
				return;
			}
			value->time   			= info->time_stamp;
			value->content_length 		= info->content_length;
			snprintf(value->uri_buff, sizeof(value->uri_buff), "%s", (const char *)info->uri_buff);

			// hash_insert(g_wx_rp_hash.hash, &g_wx_rp_hash.rwlock, key, (void *)value);
			g_hash_table_insert((GHashTable *)g_wx_rp_hash.hash, GUINT_TO_POINTER(key), value);
			// pthread_rwlock_unlock(&g_wx_rp_hash.rwlock);
		} else {
			uint8_t diff = 0;
			uint8_t tflag = 0;

			value->action_flag = info->action_flag;

			// if (g_config.wx_red_packet_precise == 1) {
			diff = abs(info->content_length - value->content_length);
			tflag = strncmp(info->uri_buff, value->uri_buff, sizeof(info->uri_buff));

			if ((diff < 5 || diff > 10) && tflag != 0) {
				value->content_length = info->content_length;
				value->time = info->time_stamp;
				snprintf(value->uri_buff, sizeof(value->uri_buff), "%s", info->uri_buff);
			} else {
				line_info.action_flag = info->action_flag;
				line_info.time = info->time_stamp;
				write_wx_action_log(flow, direction, &line_info);
				// hash_remove(g_wx_rp_hash.hash, &g_wx_rp_hash.wx_rwlock, key);
				g_hash_table_remove((GHashTable *)g_wx_rp_hash.hash, GUINT_TO_POINTER(key));
				// g_free(value);
				// free_flag = 1;
			}
			
		}
		pthread_rwlock_unlock(&g_wx_rp_hash.rwlock);
		// if (free_flag == 1 && value != NULL) {
		// 	free(value);
		// }
	} else {
		line_info.action_flag = info->action_flag;
		line_info.time = info->time_stamp;

		// debug_print_value(&value);
		write_wx_action_log(flow, direction, &line_info);
	}

#endif
}


static
void wx_action_recv_voice(struct flow_info *flow, int direction, uint32_t key, WxActionInfo *info)
{
	if (info->length < WX_VOICE_RECV_FLAG_LEN) {
		return;
	}

	WxMsgData value;
	//初始化
	//memset(value, 0, sizeof(WxActionData));
	value.time 			= info->time_stamp;
	value.action_flag 	= info->action_flag;

	write_wx_action_log(flow, direction, &value);
}

static
void wx_action_txt_msg(uint32_t key,WxActionInfo *info)
{
	if (info->length != WX_TXT_MSG_FLAG_LEN) {
		return;
	}

	WxActionData value;
	//初始化
	//memset(value, 0, sizeof(WxActionData));
	value.head = (UserHead *)malloc(sizeof(UserHead));

	copy_action_head(&info->trailer, value.head);
	value.head->UserIp 	= info->hash_key;
	value.time 			= info->time_stamp;
	value.action_flag 	= info->action_flag;

	debug_print_value(&value);
	// write_wx_action_log(flow, direction, &value);


	free(value.head);
}

static
void wx_action_data(struct flow_info *flow, int direction, uint32_t key, WxActionInfo *info)
{
	if (info->length == WX_VOICE_SEND_FLAG_LEN) {
		wx_action_send_voice(flow, direction, key, info);
	} else if (info->length >= WX_VOICE_RECV_FLAG_LEN) {
		wx_action_recv_voice(flow, direction, key, info);
	}else if (info->length == WX_TXT_MSG_FLAG_LEN){
		// wx_action_txt_msg(key, info);
	} else {
		return ;
	}
}


static
int dissect_wx_tm(struct flow_info *flow, int _c2s, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
	/* 协议的开关检查 */
	if (g_config.protocol_switch[PROTOCOL_WEIXIN_MESSAGE] == 0) {
		return 0;
	}

    WxActionInfo info;
    memset(&info, 0, sizeof(info));


	uint32_t ip_number = 0;
	int32_t hash_key = 0;
	uint32_t time_stamp = 0;
    uint16_t src_port;
    
    //uint8_t c2s = 0;
    uint16_t port = 0;
	time_t tm;
	struct timeval tv;
    uint8_t type = 0;
    uint16_t version = 0;
    uint32_t send_len = payload_len;
    uint32_t recv_len;
    //uint32_t t_payload_len = 0;
    uint16_t fsport = 0;
    uint16_t fdport = 0;
    uint32_t fsip = 0;
    uint32_t fdip = 0;
    uint8_t fc2s = 0;

    fsport = ntohs(flow->tuple.inner.port_src);
    fdport = ntohs(flow->tuple.inner.port_dst);

    if (fsport < fdport) {
        fsip = ntohl(flow->tuple.inner.ip_dst.ip4);
        fdip = ntohl(flow->tuple.inner.ip_src.ip4);
        fc2s = 0;
    } else {
        fsip = ntohl(flow->tuple.inner.ip_src.ip4);
        fdip = ntohl(flow->tuple.inner.ip_dst.ip4);
        fc2s = 1;
    }

    info.hash_key = fsip;

    // if (flow->direction == FLOW_DIR_DST2SRC && flow->direction == _c2s) {
    //     ip_number = fdip;
    //     info.c2s = 0;
    // }

    if (_c2s == FLOW_DIR_SRC2DST) {
       // ip_number   = fsip;
        info.c2s    = fc2s;
    } else {
      //  ip_number   = fdip;
        info.c2s    = !fc2s;
    }

    
    //判定流量只需要判定s2c即可
    if (info.c2s == 1) {

         return 0;
    } 
    
    type = get_uint8_t(payload, 0);
    if (type != IDENTIFY_TYPE) {
        return 0;
    }
    version = get_uint16_ntohs(payload, 1);
    if (version != IDENTIFY_VERSION) {
        return 0;
    }

    recv_len = get_uint16_ntohs(payload, 3);

    if (payload_len == WX_VOICE_SEND_FLAG_LEN ) {
        info.length         = WX_VOICE_SEND_FLAG_LEN;
        info.action_flag    = EM_SEND_VOICE_MSG;
    } else if (recv_len >= WX_VOICE_RECV_FLAG_LEN) {
        info.length         = recv_len;
        info.action_flag    = EM_RECV_VOICE_MSG;
    }else if (recv_len == WX_TXT_MSG_FLAG_LEN){
        info.length         = recv_len;
        info.action_flag    = EM_SEND_TXT_MSG;
    } else {
        return 0;
    }

	// struct timeval tv;	
	gettimeofday(&tv, NULL);
	//info.time_stamp = tv.tv_sec * 1000000 + tv.tv_usec;
    info.time_stamp = tv.tv_sec;

	//推送至相应的哈希表进行维护
	wx_action_data(flow, _c2s, info.hash_key, &info);

	return 0;
}

static 
void identify_wx_tm(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
	//协议开关，在config.ini中可配
	if (g_config.protocol_switch[PROTOCOL_WEIXIN_MESSAGE] == 0) {
		return;
	}

	uint8_t type = 0;
    uint16_t version = 0;
    
    
    type = get_uint8_t(payload, 0);
    if (type != IDENTIFY_TYPE) {
        return;
    }
    version = get_uint16_ntohs(payload, 1);
    if (version != IDENTIFY_VERSION) {
        return;
    }
    
	/* 判定是否为wx消息*/
	flow->real_protocol_id = PROTOCOL_WEIXIN_MESSAGE;
	return;
}

static 
void init_wx_msg_dissector(void)
{
	port_add_proto_head(IPPROTO_TCP, WX_MSG_PORT_0, PROTOCOL_WEIXIN_MESSAGE);
    port_add_proto_head(IPPROTO_TCP, WX_MSG_PORT_1, PROTOCOL_WEIXIN_MESSAGE);
    port_add_proto_head(IPPROTO_TCP, WX_MSG_PORT_2, PROTOCOL_WEIXIN_MESSAGE);

    write_proto_field_tab(wxtm_field_array, EM_WXTM_MAX, "wx_msg");


	tcp_detection_array[PROTOCOL_WEIXIN_MESSAGE].proto = PROTOCOL_WEIXIN_MESSAGE;
	tcp_detection_array[PROTOCOL_WEIXIN_MESSAGE].dissect_func = dissect_wx_tm;
	tcp_detection_array[PROTOCOL_WEIXIN_MESSAGE].identify_func = identify_wx_tm;

	DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN_MESSAGE].excluded_protocol_bitmask, PROTOCOL_WEIXIN_MESSAGE);

	return;
}

static __attribute((constructor)) void     before_init_wx_tm(void) {
	register_tbl_array(TBL_LOG_WEIXIN_MESSAGE, 0, "wx_msg", init_wx_msg_dissector);
}