#pragma once

#include "tbl_worker.h"
#include "tbl_writer.h"

class TblWorkerManager
{
public:
    TblWorkerManager();
    ~TblWorkerManager();
    void setProtoAndData(std::string& proto, std::vector<std::string> & rst);



private:
    std::map<std::string, TblWorker *> worker_manager;
};


// class TblWriterManager
// {
// public:
//     TblWriterManager();
//     ~TblWriterManager();

//     static TblWriter * getCurrentWriter(const std::string &proto);

// private:

//     static std::map<std::string, std::shared_ptr<TblWriter> > writer_manager;
// };



