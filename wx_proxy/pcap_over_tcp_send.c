#include <arpa/inet.h> // inet_addr()
#include <netdb.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h> // bzero()
#include <sys/socket.h>
#include <unistd.h> // read(), write(), close()
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <string.h>
#include <pcap/pcap.h>
#include <libgen.h>
#define PKT_MAX 10000
size_t cnt = 0;
size_t length = 0;

int pkt_arrived(void *user, const char *p, struct pcap_pkthdr *pkthdr)
{
    int sock = *(int *)user;
    int status = 0;

    cnt++;
    length += pkthdr->len;
    length += 4;
    status = write(sock, &pkthdr->len, 4);
    if(4 != status)
    {
        printf("Error Write on pkt len = %d\n", pkthdr->len);
        return 0;
    }

    status = write(sock, p, pkthdr->len);
    if(status != pkthdr->len)
    {
        printf("Error Write on data len = %d\n", pkthdr->len);
        return 0;
    }

    return 0;
}

int open_pcap_file(const char *filename, int (*func)(void *user, const char *pkt, struct pcap_pkthdr* pkthdr), void *user)
{
    printf("处理文件:%s\n", filename);
    char errbuf[PCAP_ERRBUF_SIZE];

    pcap_t * file_pcap = pcap_open_offline(filename, errbuf);
    if(NULL == file_pcap)
    {
        printf("ERROR:%s\n", errbuf);
        return -1;
    }

    if(DLT_EN10MB != pcap_datalink(file_pcap))
    {
        printf("ERROR: unsupport link type\n");
        return -1;
    }

    const char *pkt = NULL;
    struct pcap_pkthdr pkthdr;
    while((pkt = pcap_next(file_pcap, &pkthdr)))
    {
        func(user, pkt, &pkthdr);
    }

    pcap_close(file_pcap);
}

#include <sys/types.h>
#include <dirent.h>
#include <errno.h>
int listdir(const char *dirname, int (*cb)(const char *filename, void *user), void *user)
{
    DIR *dir = NULL;
    struct dirent *entry = NULL;
    char   path[1024] = {0};

    dir = opendir(dirname);
    if(NULL == dir)
    {
        printf("opendir %s:%s\n", dirname, strerror(errno));
        return -1;
    }

    while ((entry = readdir(dir)) != NULL)
    {
        memset(path, 0, sizeof(path));
        snprintf(path, sizeof(path), "%s/%s", dirname, entry->d_name);
        if (entry->d_type == DT_DIR)
        {
            if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
            {
                continue;
            }
            //目录
            listdir(path, cb, user);
        }
        else
        {
            //文件
            cb(path, user);
        }
    }

    closedir(dir);
    return 0;
}


int gotfile(const char *filename, void *user)
{
    open_pcap_file(filename, pkt_arrived, user);
}

#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
int func(int sockfd, const char *filename, int loop)
{
    int status;
    struct stat buf;
    int i = 0;
    for(i = 0; i < loop; i++)
    {
        status = stat(filename, &buf);
        if(0!= status)
        {
            printf("stat %s:%s\n", filename, strerror(errno));
            return -1;
        }

        if(S_ISREG(buf.st_mode))
        {
            open_pcap_file(filename, pkt_arrived, &sockfd);
        }
        else
        if(S_ISDIR(buf.st_mode))
        {
            listdir(filename, gotfile, &sockfd);
        }
    }
}

int show_info()
{
    printf("发送计数:%zu(pkt) 大小:%zu(Byte)\n", cnt, length);
}

void on_signal(int sig)
{
    show_info();
    abort();
}

#include <signal.h>
int main(int argc, const char *args[])
{
    const char *connectIP = NULL;
    int connectPort = 0;
    const char *filename = NULL;
    int sockfd, connfd;
    struct sockaddr_in servaddr;
    int     loop = 0;

    if(argc != 5)
    {
        printf("Usage:%s <ServerIP> <ServerPort> <PcapFile|DirName> <loop>\n", args[0]);
        return -1;
    }

    signal(SIGINT,   on_signal);
    signal(SIGTERM,  on_signal);
    signal(SIGQUIT,  on_signal);

    connectIP = args[1];
    connectPort = atoi(args[2]);
    filename = args[3];
    loop = atoi(args[4]);

    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd == -1)
    {
        printf("socket creation failed...\n");
        return -1;
    }

    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = inet_addr(connectIP);
    servaddr.sin_port = htons(connectPort);

    int optval = 1;
    setsockopt(sockfd, SOL_SOCKET, SO_REUSEPORT, &optval, sizeof(optval));
    setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

    if (connect(sockfd, (struct sockaddr*)&servaddr, sizeof(servaddr)) != 0)
    {
        printf("connection with the server failed...\n");
        return -1;
    }

    func(sockfd, filename, loop);

    show_info();

    close(sockfd);
}

