#  this script defines a cmake macro:
#  PROJECT_VERSION(<version> <dir> <file_template> <file_out>)
#  PROJECT_VERSION extract version from <version> and using <file_template>
#  as template to generate version info (include build version number) to
#  file_out.
#
# Example usage: generage version info to version.h
# set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR})
# include(ProjectVersion)
# PROJECT_VERSION(1.0.21 ${CMAKE_SOURCE_DIR} version.h.in version.h)

find_program(Subversion_SVN_EXECUTABLE svn DOC "subversion command line client")

if(Subversion_SVN_EXECUTABLE)

  macro(PROJECT_VERSION version dir cfg_file_in cfg_file_out)

    set(prefix "PROJECT")

    # version major
    string(REGEX REPLACE "^([0-9]+)\\.[0-9]+\\.[0-9]+\$"
      "\\1" ${prefix}_VERSION_MAJOR "${version}")

    # version minor
    string(REGEX REPLACE "^[0-9]+\\.([0-9]+)\\.[0-9]+\$"
      "\\1" ${prefix}_VERSION_MINOR "${version}")

    # version PATCH
    string(REGEX REPLACE "^[0-9]+\\.[0-9]+\\.([0-9]+)\$"
      "\\1" ${prefix}_VERSION_RELEASE "${version}")

    execute_process(COMMAND ${Subversion_SVN_EXECUTABLE} info ${dir}
      OUTPUT_VARIABLE ${prefix}_WC_INFO
      ERROR_VARIABLE Subversion_svn_info_error
      RESULT_VARIABLE Subversion_svn_info_result
      OUTPUT_STRIP_TRAILING_WHITESPACE)

    if(NOT ${Subversion_svn_info_result} EQUAL 0)
      message(SEND_ERROR "Command \"${Subversion_SVN_EXECUTABLE} info ${dir}\" failed with output:\n${Subversion_svn_info_error}")
    else()

      string(REGEX REPLACE "^(.*\n)?URL: ([^\n]+).*"
        "\\2" ${prefix}_WC_URL "${${prefix}_WC_INFO}")
      string(REGEX REPLACE "^(.*\n)?Repository Root: ([^\n]+).*"
        "\\2" ${prefix}_WC_ROOT "${${prefix}_WC_INFO}")
      string(REGEX REPLACE "^(.*\n)?Revision: ([^\n]+).*"
        "\\2" ${prefix}_VERSION_BUILD "${${prefix}_WC_INFO}")
      string(REGEX REPLACE "^(.*\n)?Last Changed Author: ([^\n]+).*"
        "\\2" ${prefix}_WC_LAST_CHANGED_AUTHOR "${${prefix}_WC_INFO}")
      string(REGEX REPLACE "^(.*\n)?Last Changed Rev: ([^\n]+).*"
        "\\2" ${prefix}_WC_LAST_CHANGED_REV "${${prefix}_WC_INFO}")
      string(REGEX REPLACE "^(.*\n)?Last Changed Date: ([^\n]+).*"
        "\\2" ${prefix}_WC_LAST_CHANGED_DATE "${${prefix}_WC_INFO}")

    # translate configure file
    configure_file(${dir}/${cfg_file_in} ${dir}/${cfg_file_out})

    endif()

  endmacro()

endif()
