/****************************************************************************************
 * 文 件 名 : dpi_tcp_rsm.c
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : 无数个挑灯的夜晚,工匠只为反复打磨. 感动自己版
 * 设    计 : chunli      2020/04/11
 * 思    想 : 为上层提供有序PKT,及MISS通知
 * 高能预警 : 算法较复杂(乱序容忍 + 分治重组 + 交织输出)
            : 挑战1. 与操作系统级别的TCP重组不同: HTTP的响应早于HTTP请求这种IP报文乱序情景也要纠正过来.
            : 挑战2. 与操作系统级别的TCP重组不同: 中间某个报文缺失, 也要继续重组下去.
            : 挑战3. 与操作系统级别的TCP重组不同: 到达的 SYN/ACK/RST/FIN 因乱序而不可信. 本算法是借鉴RST/FIN, 忽略SYN
            : 难点1. 快速重组(实测O小于log(n)), 创新TCP重组分治算法, 支持 前踩踏,后踩踏, 跨越多包.
            : 难点2. 省吃俭用,只对乱序报文重组.实践证明只有2%的报文是乱序的.
            : 难点3. 支持SEQ回且支持丢包检测(Wireshark不支持回绕时丢包检测), 高于业界标准
            : 难点4. 支持TCP port reuse 检测
            : 难点5. 支持 FIN/RST 的乱序纠正
            : 难点6. 支持 双向剧烈乱序 的纠正. 超越wireshark.高于业界标准
            : 难点7. 支持双向交织(ACK -> SEQ 咬合)输出. 精准.
            : 弃用 . 删除 TCP 有序确认 对 PSH 标志的依赖.
            : 极致 . 我的追求.
*****************************************************************************************/

#include <stdlib.h>
#include <string.h>
#include <rte_mempool.h>
#include "list.h"
#include "dpi_tcp_rsm.h"
#include "dpi_detect.h"
#define GT(x, y) ((int32_t)((y) - (x)) < 0)
#define LT(x, y) ((int32_t)((x) - (y)) < 0)
#define GE(x, y) ((int32_t)((y) - (x)) <= 0)
#define LE(x, y) ((int32_t)((x) - (y)) <= 0)
#define EQ(x, y) (x) == (y)

static struct tcp_status g_tcp_status;
extern struct global_config g_config;

struct tcp_rsm;
struct tcp_rsm_list
{
    uint32_t          seq;
    uint32_t          ack;
    uint32_t          hold;
    uint8_t           report;
    struct list_head  head;
    struct tcp_status stat;
    int               C2S;
    struct tcp_rsm_list *peer;
    struct tcp_rsm * rsm;
};

struct tcp_rsm
{
    uint32_t            jitter;      // 允许抖动范围
    void*               user;        // user_data
    struct tcp_rsm_list list[2];
    int (*pkt)   (void *user, int C2S, const uint8_t *p, uint16_t l);
    int (*event) (void *user, int C2S, uint32_t len);
};

/* TCP 重组初始化 设置 */
TCP_RSM *tcp_rsm_init(int (*pkt)  (void *user, int C2S, const uint8_t *p, uint16_t len),
                      int (*event)(void *user, int C2S, uint32_t len),
                                   void *user, uint32_t jitter)
{
    struct tcp_rsm *rsm = (struct tcp_rsm *)malloc(sizeof(struct tcp_rsm));
    if(NULL == rsm)
    {
        return NULL;
    }
    memset(rsm, 0, sizeof(struct tcp_rsm));

    rsm->pkt                = pkt;
    rsm->event              = event;
    rsm->user               = user;
    rsm->jitter             = jitter;

    INIT_LIST_HEAD(&rsm->list[0].head);
    INIT_LIST_HEAD(&rsm->list[1].head);

    rsm->list[1].peer =  rsm->list + 0;
    rsm->list[0].peer =  rsm->list + 1;

    rsm->list[1].rsm =  rsm;
    rsm->list[0].rsm =  rsm;

    int C2S = 1;
    rsm->list[C2S].C2S = C2S;
    rsm->list[!C2S].C2S = !C2S;

    return (TCP_RSM*)rsm;
}

/* 返回最小的 SEQ */
static int tcp_rsm_min(struct tcp_rsm_list *list)
{
    struct    list_head *head    = &list->head;
    struct tcp_rsm_node *pos     = NULL;

    list_for_each_entry_reverse(pos, head, node)
    {
        return pos->seq;
    }
    return 0;
}

// 节点 生成器
static struct tcp_rsm_node* tcp_rsm_new(uint32_t seq, const uint8_t *p, int len)
{
    struct tcp_rsm_node *new = NULL;
    if (rte_mempool_get(tcp_reassemble_mempool, (void **)&new) < 0)
    {
        return NULL;
    }

    if(len > TCP_LEN_MAX)
    {
       len = TCP_LEN_MAX;
    }

    INIT_LIST_HEAD(&new->node);
    new->seq  = seq;
    new->len  = len;
    memcpy(new->p, p, len);
    return new;
}

// 分解巨帧
static int tcp_rsm_insert(uint32_t seq, const uint8_t *p, int len,  struct tcp_rsm_node *pos, struct tcp_rsm_list *list)
{
    int                  offset  = 0;
    struct tcp_rsm_node *new     = NULL;

    do
    {
        new = tcp_rsm_new(seq + offset, p + offset, len - offset);
        if(new)
        {
            list_add_tail(&new->node, &pos->node);
            list->hold++;
            list->stat.rsm++;
            list->stat.rsm_len += len;
            offset += new->len;
            pos     = new;
        }
        else
        {
            list->stat.fail++;
            list->stat.fail_len += len;
            return -1;
        }
    }
    while(offset < len);
    return 0;
}


static int tcp_rsm_dump_fprint(struct tcp_rsm_list *list, FILE *pf)
{
    struct   list_head    *head    = &list->head;
    struct tcp_rsm_node   *pos     = NULL;
    uint32_t               EXP     = 0;
    int                    index   = 0;
    int                    ERR     = 0;

    list->report = 1;

    if(list_empty(head))
    {
        return 0;
    }

    list_for_each_entry_reverse(pos, head, node)
    {
        if(pos->node.prev == head)
        {
            fprintf(pf,"TCP SEQ_DUMP:%6u %u+%u=%u\n", ++index, pos->seq, pos->len, pos->seq + pos->len);
            fprintf(pf,"TCP SEQ_DUMP:[The_End_%s]\n", (ERR>1)?"MISS":"WONDERFUL");
        }
        else
        {
            ERR+= (pos->seq != EXP);
            if(0 != EXP && EXP != pos->seq)
            {
                fprintf(pf,"TCP SEQ_缺包:      %6u bytes missing\n", pos->seq - EXP);
            }
            fprintf(pf,"TCP SEQ_DUMP:%6u %u+%u=%u\n", ++index, pos->seq, pos->len, pos->seq + pos->len);
            EXP = pos->seq + pos->len;
        }
    }
    return 0;
}


static int tcp_rsm_error_report(struct tcp_rsm_list *list, uint32_t seq, uint32_t len)
{
    char filename[128] = {0};
    snprintf(filename, sizeof(filename), "./tcp_rsm_error_detect_%zu.log", (size_t)list->rsm);
    FILE *pf = fopen(filename, "a+");
    if(NULL == pf)
    {
      return -1;
    }

    //描述原因
    char buff[128] = {0};
    snprintf(buff, sizeof(buff), "tcp_rsm_error_detect_C2S_%u_seq_%u_len_%u\n", list->C2S, seq, len);
    fwrite(buff,  strlen(buff), 1, pf);

    //开始打印链表
    fprintf(pf,"当前方向");
    tcp_rsm_dump_fprint(list, pf);
    fprintf(pf,"对方方向");
    tcp_rsm_dump_fprint(list->peer, pf);

    fclose(pf);

    return 0;
}
/*
 * TCP 重组精髓. 分治算法
 *
 * 1. 单包重复(头含头 且 尾含尾)
 * 2. 两包重叠(头超头 或 尾超尾)
 * 3. 多包跨越(头超头 且 尾超尾)
 * 4. SEQ 回滚(精妙处理)
 * 5. 超越 Wireshark TCP 追踪流功能
 */
static int tcp_rsm_wonderful(struct tcp_rsm_list *list, uint32_t seq, const uint8_t *p, uint32_t len)
{
    struct list_head    *head    = &list->head;
    struct tcp_rsm_node *new     = NULL;
    struct tcp_rsm_node *pos     = NULL;
    uint32_t             offset  = 0;

   if(0 == len || len > 2048)
   {
      if(g_config.tcp_rsm_report_err)
          tcp_rsm_error_report(list, seq, len);
      return 0;
   }

    list_for_each_entry(pos, head, node)
    {
        // 头超头, 尾超尾
        if(LT(seq, pos->seq) && GT(seq + len, pos->seq + pos->len))
        {
            offset = pos->seq-seq;
            tcp_rsm_wonderful(list, seq, p, offset);
            tcp_rsm_wonderful(list, seq+offset, p+offset, len-offset);
            break;
        }
        else
        // 头超头
        if(LT(seq, pos->seq) && GT(seq + len, pos->seq) && LE(seq + len, pos->seq + pos->len))
        {
            offset = pos->seq - seq;
            tcp_rsm_wonderful(list, seq, p, offset);
            tcp_rsm_wonderful(list, seq+offset, p+offset, len - offset);
            break;
        }
        else
        // 尾超尾
        if(GT(seq + len, pos->seq + pos->len) && LT(seq, pos->seq + pos->len) && GE(seq, pos->seq))
        {
            offset = pos->seq + pos->len - seq;
            tcp_rsm_wonderful(list, seq + offset, p + offset, len - offset);
            tcp_rsm_wonderful(list, seq, p, offset);
            break;
        }
        else
        // 包含
        if(GE(seq, pos->seq) && LT(seq, pos->seq + pos->len) && LE(seq + len, pos->seq + pos->len))
        {
            break;
        }
        else
        // 空位
        if(GT(seq, pos->seq))
        {
            tcp_rsm_insert(seq, p, len, pos, list);
            break;
        }
    }

    //最小
    if(&pos->node == head)
    {
        tcp_rsm_insert(seq, p, len, pos, list);
    }
    return 0;
}

static int tcp_rsm_dump_(struct tcp_rsm_list *list)
{
    struct   list_head    *head    = &list->head;
    struct tcp_rsm_node   *pos     = NULL;
    uint32_t               EXP     = 0;
    int                    index   = 0;
    int                    ERR     = 0;

    list->report = 1;

    if(list_empty(head))
    {
        return 0;
    }

    list_for_each_entry_reverse(pos, head, node)
    {
        if(pos->node.prev == head)
        {
            printf("TCP SEQ_DUMP:%6u %u+%u=%u\n", ++index, pos->seq, pos->len, pos->seq + pos->len);
            printf("TCP SEQ_DUMP:[The_End_%s]\n", (ERR>1)?"MISS":"WONDERFUL");
        }
        else
        {
            ERR+= (pos->seq != EXP);
            if(0 != EXP && EXP != pos->seq)
            {
                printf("TCP SEQ_缺包:      %6u bytes missing\n", pos->seq - EXP);
            }
            printf("TCP SEQ_DUMP:%6u %u+%u=%u\n", ++index, pos->seq, pos->len, pos->seq + pos->len);
            EXP = pos->seq + pos->len;
        }
    }
    return 0;
}

void tcp_rsm_dump(TCP_RSM *tcp)
{
    struct tcp_rsm        *rsm     = (struct tcp_rsm*)tcp;
    tcp_rsm_dump_(rsm->list + 0);
    tcp_rsm_dump_(rsm->list + 1);
}

static void tcp_rsm_report(struct tcp_rsm_list *list)
{
    struct tcp_status *s = &list->stat;
    uint64_t num  = s->count;
    if(s->count)
    {
        printf("TCP报文分类计数:\n"      );
        printf("     ACK %u %zu%%\n"             , s->ack, s->ack*100/num);
        printf("    重复 %u %zu%%\n"             , s->dated, s->dated*100/num);
        printf("    蓄能 %u %zu%%\n"             , s->wait, s->wait*100/num);
        printf("    期望 %u %zu%%\n"             , s->exp, s->exp*100/num);
        printf("    乱序 %u %zu%%\n"             , s->jitter, s->jitter*100/num);
        printf("    总计 %zu\n"                  , s->count);
        printf("TCP入队失败: 个数/长度: %u %u\n" , s->fail, s->fail_len);
        printf("TCP性能评估: 快启/中断: %u %u\n" , s->first, s->interrupt);
        printf("TCP缺包信息: 块数/长度: %u %u\n" , s->miss, s->miss_len);
        printf("TCP重组信息: 重组/组报: %u %zu\n", s->rsm, s->rsm_len);
        printf("TCP序号起始: 开始/结束: %u %u\n" , s->seq_start, s->seq_end);
        printf("\n");
    }
}

static int tcp_rsm_walk(TCP_RSM *tcp, int C2S)
{
    struct      tcp_rsm      *rsm       = (struct tcp_rsm*)tcp;
    struct      tcp_rsm_list *list      = rsm->list + C2S;
    struct      tcp_rsm_list *peer      = rsm->list + !C2S;
    struct   list_head       *head      = &list->head;
    struct  tcp_rsm_node     *pos       = NULL;
    struct  tcp_rsm_node     *tmp       = NULL;
    int                       rc        = 0;
    int                       miss      = 0;

    list_for_each_entry_safe_reverse(pos, tmp, head, node)
    {
        // 并不是只要有序, 就直接处理掉. 只有当存在1条流时才能这么做
        if(0 == peer->ack && EQ(list->seq, pos->seq))
        {
            //printf("WALK单向 peer_ACK %u pos->seq %u list->seq %u list->hold %u rsm_jitter %u\n",
            //        peer->ack, pos->seq, list->seq, list->hold, rsm->jitter);
            list->seq += pos->len;
            rc = rsm->pkt(rsm->user, C2S, pos->p, pos->len);
            list_del(&pos->node);
            rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
            list->hold--;
        }
        else
        // 对方的 ACK 大于我的 SEQ.
        if((0 != peer->ack && GT(peer->ack, pos->seq)) || list->hold >= rsm->jitter)
        {
            miss = (0 == list->seq) ? 0 : pos->seq - list->seq;
            //printf("WALK惊喜 peer_ACK %u pos->seq %u list->seq %u list->hold %u rsm_jitter %u miss %u\n",
            //        peer->ack, pos->seq, list->seq, list->hold, rsm->jitter, miss);
            if(list->seq && miss > 0) // 就绪完成
            {
                rsm->event(rsm->user, C2S, miss);
                list->stat.miss++;
                list->stat.miss_len += miss;
            }
            else
            {
                list->stat.first = list->hold; // 记录首次启动
            }

            rc = rsm->pkt(rsm->user, C2S, pos->p, pos->len);
            list_del(&pos->node);
            rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
            list->seq = pos->seq + pos->len;
            list->hold--;
        }
        else
        {
            list->stat.interrupt++; // 缺包中断
            break;
        }
    }
    return 0;
}

// 清仓处理
static int tcp_rsm_flush(TCP_RSM *tcp, int C2S)
{
    struct      tcp_rsm      *rsm    = (struct tcp_rsm*)tcp;
    struct      tcp_rsm_list *list   = rsm->list + C2S;
    struct   list_head    *head      = &list->head;
    struct  tcp_rsm_node  *pos       = NULL;
    struct  tcp_rsm_node  *tmp       = NULL;
    int                    rc        = 0;
    int                    miss      = 0;

    list_for_each_entry_safe_reverse(pos, tmp, head, node)
    {
        //遇到期望报文
        if(list->seq == pos->seq)
        {
            list->seq += pos->len;
            rsm->pkt(rsm->user, C2S, pos->p, pos->len);
            list_del(&pos->node);
            rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
            list->hold--;
        }
        // 发生MISS
        else
        {
            miss = (0 == list->seq) ? 0 : pos->seq - list->seq;
            if(list->seq) // 已完成初始化
            {
                rsm->event(rsm->user, C2S, miss);
                list->stat.miss++;
                list->stat.miss_len += miss;
            }

            rsm->pkt(rsm->user, C2S, pos->p, pos->len);
            list_del(&pos->node);
//            printf("");
            rte_mempool_put(tcp_reassemble_mempool, (void *)pos);
            list->seq = pos->seq + pos->len;
            list->hold--;
        }
    }

    //再通知一次
    rsm->event(rsm->user, C2S, 0);
    return 0;
}

int tcp_rsm_add(TCP_RSM *tcp, int C2S, uint32_t seq, uint32_t ack, uint8_t tcp_flag, const uint8_t *p, uint32_t len)
{
    struct tcp_rsm      *rsm          = (struct tcp_rsm*)tcp;
    struct tcp_rsm_list *list         = rsm->list +C2S;
    struct tcp_rsm_list *peer         = rsm->list->peer;
    struct list_head    *head         = &list->head;
    uint32_t             offset       = 0;

    if(unlikely(0 == list->stat.fin && TCP_FIN & tcp_flag))
    {
        list->stat.fin++;
    }

    if(unlikely(0 == list->stat.rst && TCP_RST & tcp_flag))
    {
        list->stat.rst++;
    }

    // 统计信息
    list->stat.count++;
    list->stat.seq_end = seq;
    if(0 == list->stat.seq_start)
    {
        list->stat.seq_start = seq;
    }

    // 曲速引擎
    if(0 == rsm->jitter)
    {
        rsm->pkt(rsm->user, C2S, p, len);
        return 0;
    }

    // 无用的
    if(len == 0)
    {
        list->stat.ack++;
        return 0;
    }

    // ACK更新,通知对端
    if(GT(ack, list->ack) || 0 == list->ack)
    {
        list->ack = ack;
        tcp_rsm_walk(tcp, !C2S);
    }

    // 过时的
    if(0 != list->seq && LE(seq + len, list->seq))
    {
        list->stat.dated++;
        return 0;
    }

    // 过渡的
    if(0 != list->seq && LT(seq, list->seq)  && GT(seq + len, list->seq))
    {
        offset = list->seq - seq;
        len   -= offset;
        p     += offset;
        seq   += offset;
    }

    // 期望的 (单向)
    if(0 == list->hold && EQ(list->seq, seq) && 0 == peer->ack)
    {
        rsm->pkt(rsm->user, C2S, p, len);
        list->seq += len;
        list->stat.rsm++;
        list->stat.rsm_len += len;
        list->stat.exp++;
    }
    else
    // 期望的 (双向)
    if(0 == list->hold && EQ(list->seq, seq) && 0 != peer->ack && GT(peer->ack, seq))
    {
        rsm->pkt(rsm->user, C2S, p, len);
        list->seq += len;
        list->stat.rsm++;
        list->stat.rsm_len += len;
        list->stat.exp++;
    }
    else
    // 超前的
    {
        tcp_rsm_wonderful(list, seq, p, len);

        if(list->seq)
        {
            list->stat.jitter++; // 乱序
        }
        else
        {
            list->stat.wait++; // 就绪
        }
    }

    // 走你
    return tcp_rsm_walk(tcp, C2S);
}


int tcp_rsm_status(TCP_RSM *tcp, int C2S, struct tcp_status *status)
{
    struct tcp_rsm      *rsm          = (struct tcp_rsm*)tcp;
    struct tcp_rsm_list *list         = rsm->list +  C2S;

    memcpy(status, &list->stat, sizeof(struct tcp_status));
    return 0;
}

void tcp_rsm_free(TCP_RSM *tcp)
{
    struct      tcp_rsm        *rsm    = (struct tcp_rsm*)tcp;
    struct      tcp_rsm_list *list     = NULL;

    tcp_rsm_flush(tcp, 0);
    tcp_rsm_flush(tcp, 1);

    if(rsm->list[0].report)
    {
        tcp_rsm_report(rsm->list + 0);
    }

    if(rsm->list[1].report)
    {
        tcp_rsm_report(rsm->list + 1);
    }

    free(tcp);
}

int tcp_rsm_port_reuse(TCP_RSM *tcp, int C2S, uint32_t ack)
{
    struct      tcp_rsm *rsm            = (struct tcp_rsm*)tcp;
    struct tcp_rsm_list *list_C         = rsm->list + 1;
    struct tcp_rsm_list *list_S         = rsm->list + 0;
    struct tcp_rsm_list *list           = rsm->list + C2S;
    uint32_t diff;

    // TCP port  reuse
    // [预防针] FIN 报文比数据报文来得更早一些
    if(GT(ack, list->ack))
    {
        diff = ack - list->ack;
    }
    else
    {
        diff = list->ack - ack;
    }

    // 审判
    if(diff > 8000 && list->ack > 0)
    {
        return list_C->stat.rst || list_C->stat.fin || list_S->stat.rst || list_S->stat.fin;
    }
    return 0;
}


