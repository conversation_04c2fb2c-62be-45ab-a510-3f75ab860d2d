/****************************************************************************************
 * 文 件 名 : dpi_rtp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/10/24
 修改: chenzq          2022/10/24
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>
#include <fcntl.h>
#include <pcap.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
// #include "dpi_sdp.h"
#include "dpi_dissector.h"

#include "dpi_conversation.h"
#include "dpi_douyin.h"


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;
extern struct rte_mempool *tbl_log_content_mempool_4k;

extern int session_protocol_st_size[PROTOCOL_MAX];


/* Version is the first 2 bits of the first octet*/
#define RTP_VERSION(octet)  ((octet) >> 6)

/* Padding is the third bit; No need to shift, because true is any value
   other than 0! */
#define RTP_PADDING(octet)  (((octet) & 0x20) == 0x20 ? 1 : 0)

/* Extension bit is the fourth bit */
#define RTP_EXTENSION(octet)    ((octet) & 0x10)

/* CSRC count is the last four bits */
#define RTP_CSRC_COUNT(octet)   ((octet) & 0xF)

/* Marker is the first bit of the second octet */
#define RTP_MARKER(octet)   ((octet) & 0x80)

/* Payload type is the last 7 bits */
#define RTP_PAYLOAD_TYPE(octet) ((octet) & 0x7F)

#define FIRST_RTCP_CONFLICT_PAYLOAD_TYPE 64
#define LAST_RTCP_CONFLICT_PAYLOAD_TYPE  95

#if 0
/* "libpcap" record header. */
struct pcappkt_hdr {
    unsigned int tv_sec;      /* timestamp seconds */
    unsigned int tv_usec;     /* timestamp microseconds */
    unsigned int caplen;      /* number of octets of packet saved in file */
    unsigned int len;          /* actual length of packet */
};
#endif

enum rtp_index_em{
    EM_RTP_VERSION,
    EM_RTP_PADDING,
    EM_RTP_EXTENSION,
    EM_RTP_CC,
    EM_RTP_MAKER,
    EM_RTP_PAYLOAD_TYPE,
    EM_RTP_SEQ,
    EM_RTP_TIMESTAMP,
    EM_RTP_SSRC,
    EM_RTP_ITEM,

    EM_RTP_MAX
};

static dpi_field_table  rtp_field_array[] = {
    DPI_FIELD_D(EM_RTP_VERSION,                    EM_F_TYPE_UINT8,                 "Version"),
    DPI_FIELD_D(EM_RTP_PADDING,                    EM_F_TYPE_UINT8,                 "Padding"),
    DPI_FIELD_D(EM_RTP_EXTENSION,                  EM_F_TYPE_UINT8,                 "Extention"),
    DPI_FIELD_D(EM_RTP_CC,                         EM_F_TYPE_UINT8,                 "Cc"),
    DPI_FIELD_D(EM_RTP_MAKER,                      EM_F_TYPE_UINT8,                 "Maker"),
    DPI_FIELD_D(EM_RTP_PAYLOAD_TYPE,               EM_F_TYPE_UINT8,                 "PayloadType"),
    DPI_FIELD_D(EM_RTP_SEQ,                        EM_F_TYPE_UINT16,                "Sequence"),
    DPI_FIELD_D(EM_RTP_TIMESTAMP,                  EM_F_TYPE_UINT32,                "TimeStamp"),
    DPI_FIELD_D(EM_RTP_SSRC,                       EM_F_TYPE_UINT32,                "SSRC"),
    DPI_FIELD_D(EM_RTP_ITEM,                       EM_F_TYPE_STRING,                "CsrcItem"),
};

typedef struct _rtp_hdr rtp_hdr;
struct _rtp_hdr {
#if __BYTE_ORDER == __BIG_ENDIAN
    unsigned char version:2;   /**< protocol version */
    unsigned char p:1;         /**< padding flag */
    unsigned char x:1;         /**< header extension flag */
    unsigned char cc:4;        /**< CSRC count */
    unsigned char m:1;         /**< marker bit */
    unsigned char pt:7;        /**< payload type */
#elif __BYTE_ORDER == __LITTLE_ENDIAN
    unsigned char cc:4;        /**< CSRC count */
    unsigned char x:1;         /**< header extension flag */
    unsigned char p:1;         /**< padding flag */
    unsigned char version:2;   /**< protocol version */
    unsigned char pt:7;        /**< payload type */
    unsigned char m:1;         /**< marker bit */
#else
# error "Please fix <bits/endian.h>"
#endif
    unsigned short seq;        /**< sequence number */
    unsigned int ts;           /**< timestamp */
    unsigned int ssrc;         /**< synchronization source */
} __attribute__((__packed__));

typedef struct  _st_rtp_info st_rtp_session;
struct _st_rtp_info {
    uint32_t      info_version;
    uint8_t       info_padding_set;
    uint8_t       info_marker_set;
    uint8_t       info_extension_set;
    uint8_t       info_item[160];
    uint32_t      info_media_types;
    uint32_t      info_payload_type;
    uint32_t      info_padding_count;
    uint16_t      info_seq_num;
    uint32_t      info_timestamp;
    uint32_t      info_sync_src;
    uint32_t      info_csrc_count;
    uint32_t      info_data_len;       /* length of raw rtp data as reported */
    uint8_t       info_all_data_present; /* FALSE if data is cut off */
    uint32_t      info_payload_offset; /* start of payload relative to info_data */
    uint32_t      info_payload_len;    /* length of payload (incl padding) */
    uint8_t       info_is_srtp;
    uint32_t      info_setup_frame_num; /* the frame num of the packet that set this RTP connection */
    const uint8_t* info_data;           /* pointer to raw rtp data */
    const char   *info_payload_type_str;
    int          info_payload_rate;
    uint8_t      info_is_ed137;
    const char   *info_ed137_info;
    /*
    * info_data: pointer to raw rtp data = header + payload incl. padding.
    * That should be safe because the "epan_dissect_t" constructed for the packet
    *  has not yet been freed when the taps are called.
    * (destroying the "epan_dissect_t" will end up freeing all the tvbuffs
    *  and hence invalidating pointers to their data).
    * See "add_packet_to_packet_list()" for details.
    */
};

/*************************************************************************************************/
static u_int8_t isValidMSRTPType(uint8_t payloadType) {
  switch(payloadType) {
  case 0: /* G.711 u-Law */
  case 3: /* GSM 6.10 */
  case 4: /* G.723.1  */
  case 5: // extend
  case 6: // extend
  case 7: // extend
  case 8: /* G.711 A-Law */
  case 9: /* G.722 */
  case 10: // extend
  case 11: // extend
  case 12: // extend
  case 13: /* Comfort Noise */
  case 14: // extend
  case 15: // extend
  case 16: // extend
  case 17: // extend
  case 18: // extend
  
 // case 96: /* Dynamic RTP */
 // case 97: /* Redundant Audio Data Payload */
 // case 101: /* DTMF */
 // case 103: /* SILK Narrowband */
 // case 104: /* SILK Wideband */
 // case 111: /* Siren */
 // case 112: /* G.722.1 */
 // case 114: /* RT Audio Wideband */
 // case 115: /* RT Audio Narrowband */
 // case 116: /* G.726 */
 // case 117: /* G.722 */
 // case 118: /* Comfort Noise Wideband */
 // case 34: /* H.263 [MS-H26XPF] */
 // case 121: /* RT Video */
 // case 122: /* H.264 [MS-H264PF] */
 // case 123: /* H.264 FEC [MS-H264PF] */
 // case 127: /* x-data */
    return(1 /* RTP */);
    break;
    
  case 200: /* RTCP PACKET SENDER */
  case 201: /* RTCP PACKET RECEIVER */
  case 202: /* RTCP Source Description */
  case 203: /* RTCP Bye */
    return(2 /* RTCP */);
    break;
    
  default:
    return(0);
  }
}


static int dissect_rtp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
  if (g_config.protocol_switch[PROTOCOL_RTP] == 0)
    return 0;

  dissect_dy_from_rtp(flow, direction, seq, payload, payload_len, flag);
  return PKT_OK;
}

static void identify_rtp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_RTP] == 0)
        return;
    if(payload_len < 12){
        return;
    }
    uint16_t d_port = 0;
    uint32_t ssrc = 0;
    d_port = ntohs(flow->tuple.inner.port_dst);
    if(d_port <= 1023){
        return;
    }

    unsigned int version;
    version = RTP_VERSION( get_uint8_t( payload, 0) );
    if(!(version==2)){
        return;
    }

    uint8_t payload_type=payload[1]&0x7F;
    uint8_t octet2= get_uint8_t( payload, 1);
    uint8_t marker_set= RTP_MARKER(octet2);
    if (marker_set && payload_type >= 64 && payload_type <=  95) {
        return ;
    }

    ssrc = get_uint32_ntohl(payload, 8);
    if (flow->user_data[0] == '\0') {
      snprintf(flow->user_data, sizeof(flow->user_data), "%u", ssrc);
    } else {
      uint32_t tmp_ssrc = atoi(flow->user_data);
      if (tmp_ssrc == ssrc)
        flow->real_protocol_id = PROTOCOL_RTP;
    }
    // if (flow->pkt_cnt == 0 ) {
    //   snprintf(flow->user_data, sizeof(flow->user_data), "%u", ssrc);
    //   flow->pkt_cnt = 1;
    // } else {
    //   uint32_t tmp_ssrc = atoi(flow->user_data);
    //   if (tmp_ssrc == ssrc) {
    //     flow->pkt_cnt++;
    //   }

    //   if (flow->pkt_cnt == 3) {
    //     flow->real_protocol_id = PROTOCOL_RTP;
    //     flow->pkt_cnt = 0;
    //   }
    // }
    return;
}


static void init_rtp_dissector(void)
{

    write_proto_field_tab(rtp_field_array,EM_RTP_MAX,"rtp");

    /*
    port_add_proto_head(IPPROTO_TCP, 5060, PROTOCOL_SIP);
    tcp_detection_array[PROTOCOL_SIP].proto = PROTOCOL_SIP;    
    tcp_detection_array[PROTOCOL_SIP].identify_func = identify_sip;
    tcp_detection_array[PROTOCOL_SIP].dissect_func = dissect_sip;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_SIP].excluded_protocol_bitmask, PROTOCOL_SIP);
    */

    //port_add_proto_head(IPPROTO_UDP, 5060, PROTOCOL_RTP);
    /* add by liugh*/
    //session_protocol_st_size[PROTOCOL_RTP]=sizeof(struct sip_session);

    udp_detection_array[PROTOCOL_RTP].proto = PROTOCOL_RTP;
    udp_detection_array[PROTOCOL_RTP].identify_func = identify_rtp;
    udp_detection_array[PROTOCOL_RTP].dissect_func = dissect_rtp;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_RTP].excluded_protocol_bitmask, PROTOCOL_RTP);

    return;
}

static __attribute((constructor)) void     before_init_rtp(void){
    register_tbl_array(TBL_LOG_RTP, 0, "rtp", init_rtp_dissector);
}
