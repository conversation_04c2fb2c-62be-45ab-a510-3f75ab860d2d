/****************************************************************************************
 * 文 件 名 : wxcs_utils.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019-01-03
* 编    码 : zhengsw      '2019-01-03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef WXCS_UTILS_H
#define WXCS_UTILS_H

#include <muduo/net/Endian.h>
#include <string.h>

#include <unistd.h>
#include <errno.h>
#include <dirent.h>
#include <stdlib.h>
#include <sys/stat.h>

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include <string>

#define _U_                 __attribute__((unused))

/* macros */
#define dimen_of(x) (sizeof(x) / sizeof((x)[0]))

// check_nor_exit
#define CHECK_NOR_EXIT(cond, retCode, ...) if ((cond)) \
    {                                                  \
        fprintf(stderr, __VA_ARGS__);                  \
        return (retCode);                              \
    }

#define EXIT_IF_ERROR(cond, retCode, ...) if ((cond)) \
    {                                                 \
        fprintf(stderr, __VA_ARGS__);                 \
        perror(" ");                                  \
        return (retCode);                             \
    }

union UN_sevenBytes
{
    uint64_t num;
    uint8_t  bytes[8];
};

static inline uint64_t uint64_from_7bytesBE(const uint8_t *data)
{
    UN_sevenBytes sv = { 0 };
    memcpy(&sv.bytes[1], data, 7);
    return muduo::net::sockets::networkToHost64(sv.num);
}


static inline char
low_nibble_of_octet_to_hex(uint8_t oct)
{
	static const char hex_digits[16] =
	{ '0', '1', '2', '3', '4', '5', '6', '7',
	  '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

	return hex_digits[oct & 0xF];
}


static inline std::string ip_addr_to_str(uint32_t ip)
{
    return inet_ntoa(*(struct in_addr*)(&ip));
}

static inline std::string bytes_to_hexstring(const uint8_t *in, int len)
{
    if (len == 0) return "0x0";

    std::string strHex;
    strHex.reserve(2 * len + 2);  // 预分配空间

    bool first_non_zero = false;

    for (int i = 0; i < len; i++)
    {
        uint8_t high_nibble = (in[i] >> 4) & 0x0F;
        uint8_t low_nibble = in[i] & 0x0F;

        // 如果还没遇到非零字符
        if (!first_non_zero)
        {
            if (high_nibble != 0)
            {
                if (strHex.empty()) strHex = "0x";
                strHex += low_nibble_of_octet_to_hex(high_nibble);
                strHex += low_nibble_of_octet_to_hex(low_nibble);
                first_non_zero = true;
            }
            else if (low_nibble != 0)
            {
                if (strHex.empty()) strHex = "0x";
                strHex += low_nibble_of_octet_to_hex(low_nibble);
                first_non_zero = true;
            }
        }
        else
        {
            // 已经开始输出，后续所有字符都要输出
            strHex += low_nibble_of_octet_to_hex(high_nibble);
            strHex += low_nibble_of_octet_to_hex(low_nibble);
        }
    }

    // 如果全部都是0
    if (strHex.empty()) strHex = "0x0";

    return strHex;
}


template <typename T>
static std::string bytes_to_hexstring(const T &data)
{
    return bytes_to_hexstring((uint8_t *)&data, sizeof data);
}

static inline std::string unix_time_to_str(uint32_t time)
{
    time_t u64Time = time;
    char buff[30]  = { 0 };
    tm     *tm     = localtime((time_t *)&u64Time);

    // date string
    strftime(buff, sizeof buff, "%Y-%m-%d %H:%M:%S", tm);
    return buff;
}

inline
uint hundredThousandthSecond()
{
    struct timespec ts;

    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_nsec / 10000;
}

static inline
int makeDir(const char* path)
{
    int beginCmpPath;
    int endCmpPath;
    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = {0};

    //相对路径
    if('/' != path[0])
    {
        //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");
        beginCmpPath = strlen(currentPath);
        strcat(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        endCmpPath = strlen(currentPath);
    }
    else
    {
        //绝对路径
        int pathLen = strlen(path);
        strcpy(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        beginCmpPath = 1;
        endCmpPath = strlen(currentPath);
    }
    //创建各级目录
    for(int i = beginCmpPath; i < endCmpPath ; i++ )
    {
        if('/' == currentPath[i])
        {
            currentPath[i] = '\0';
            if(access(currentPath, 0) != 0)
            {
                if(mkdir(currentPath, 0755) == -1)
                {
                    perror("mkdir error %s\n");
                    return -1;
                }
            }
            currentPath[i] = '/';
        }
    }
    return 0;
}

/* return dir string with final backslash */
static inline
std::string getAppDir()
{
    std::string strAppPath;
    if (!strAppPath.empty())
    {
        return strAppPath;
    }

    char pathBuff[PATH_MAX];
    uint cnt = readlink("/proc/self/exe", pathBuff, sizeof(pathBuff));
    if (cnt >= sizeof(pathBuff))
    {
        return "";
    }

    // terminate the path
    pathBuff[cnt] = '\0';

    strAppPath = pathBuff;
    strAppPath = strAppPath.substr(0, strAppPath.find_last_of('/') + 1);
    return strAppPath ;
}

inline
int ensureDirExist(const char *dirPath)
{
    int lSts = 0;

    if (access(dirPath, R_OK) != 0)
    {
        lSts = makeDir(dirPath);
        EXIT_IF_ERROR(lSts < 0, lSts, dirPath);
    }

    return 0;
}

static inline
bool WxcsStrConcat(std::string & _original, std::string & _str, const char _split)
{
    if (_str.empty() || _str == "0") {
        return false;
    }
    if (_original.empty()) {
        _original = _str;
        return false;
    }
    if (_original.find(_str, 0) != std::string::npos) {
        return false;
    }

    _original = _original + _split + _str;

    return true;
}

#endif /*  */
