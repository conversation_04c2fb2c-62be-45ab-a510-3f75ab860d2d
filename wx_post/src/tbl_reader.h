#pragma once

#include <queue>
#include <string>
#include <vector>
#include <thread>
#include <mutex>
#include <map>

#include <stdio.h>
#include <atomic>

#include "tbl_worker.h"
#include "tbl_manager.h"

class TblReader
{
private:
    static std::queue<std::tuple<std::string, std::string, FILE *>> tbl_file_queue;/* 记录tbl名称 */
    //static std::map<std::string, TblWorker *> worker_manager;

private:
    static int readTbl(std::vector<std::string> &, std::string &);

private:
    static std::mutex mtx_;
    //TblWriterManager tbl_writer_manager_;

public:
    static int addTblName(std::string &&,std::string &&, FILE *);
    int work(TblWorkerManager * worker_manager);

public:
    Tbl<PERSON>eader();
    ~TblReader();
};