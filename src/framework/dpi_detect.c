/****************************************************************************************
 * 文 件 名 : dpi_detect.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
//#include <linux/if_ether.h>

#include "dpi_typedefs.h"
#include "dpi_detect.h"
#include "dpi_proto_ids.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_dissector.h"
#include "wxcs_def.h"


/* mask for FCF */
#define	WIFI_DATA                        0x2    /* 0000 0010 */
#define FCF_TYPE(fc)     (((fc) >> 2) & 0x3)    /* 0000 0011 = 0x3 */
#define FCF_SUBTYPE(fc)  (((fc) >> 4) & 0xF)    /* 0000 1111 = 0xF */
#define FCF_TO_DS(fc)        ((fc) & 0x0100)
#define FCF_FROM_DS(fc)      ((fc) & 0x0200)

/* mask for Bad FCF presence */
#define BAD_FCS                         0x50    /* 0101 0000 */

extern struct rte_mempool * flow_mempool;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;

/* add by liugh */
extern int session_protocol_st_size[PROTOCOL_MAX];



static int DPI_BITMASK_COMPARE(DPI_PROTOCOL_BITMASK a, DPI_PROTOCOL_BITMASK b) {
	unsigned int i;

	for(i = 0; i < DPI_NUM_FDS_BITS; i++) {
		if(a.fds_bits[i] & b.fds_bits[i])
			return 1;
	}
	return 0;
}

/*
*会话销毁前，如果有待重组的数据包，则做最后一次解析
*/
static void tcp_reassemble_do_final(struct flow_info *flow)
{
	if (!list_empty(&flow->reassemble_src2dst_head) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
		uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
		uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
		tcp_reassemble_do(&flow->reassemble_src2dst_head, reassemble_result, &reassemble_result_len);

		if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_SRC2DST, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);

		tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
	}

	if (!list_empty(&flow->reassemble_dst2src_head) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
		uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
		uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
		tcp_reassemble_do(&flow->reassemble_dst2src_head, reassemble_result, &reassemble_result_len);

		if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_DST2SRC, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);

		tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);
	}

	if (!list_empty(&flow->rsm_head[FLOW_DIR_SRC2DST]) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
		uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
		uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
		tcp_reassemble_do(&flow->rsm_head[FLOW_DIR_SRC2DST], reassemble_result, &reassemble_result_len);

		if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_SRC2DST, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);

		tcp_reassemble_free(&flow->rsm_head[FLOW_DIR_SRC2DST], &flow->rsm_len[FLOW_DIR_SRC2DST]);
	}

	if (!list_empty(&flow->rsm_head[FLOW_DIR_DST2SRC]) && flow->real_protocol_id != PROTOCOL_UNKNOWN) {
		uint8_t reassemble_result[REASSEMBLE_LEN_MAX];
		uint32_t reassemble_result_len = sizeof(reassemble_result) / sizeof(reassemble_result[0]);
		tcp_reassemble_do(&flow->rsm_head[FLOW_DIR_DST2SRC], reassemble_result, &reassemble_result_len);

		if (flow->tuple.inner.proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			tcp_detection_array[flow->real_protocol_id].dissect_func(flow, FLOW_DIR_DST2SRC, 0, reassemble_result, reassemble_result_len, DISSECT_PKT_FIANL);

		tcp_reassemble_free(&flow->rsm_head[FLOW_DIR_DST2SRC], &flow->rsm_len[FLOW_DIR_DST2SRC]);
	}

	return;
}




void port_add_proto_head(uint8_t tcp_or_udp, uint16_t port, uint16_t protocol)
{
	int i;
	struct guess_proto_data *array;

	if (tcp_or_udp == IPPROTO_TCP)
		array = tcp_port_proto;
	else if (tcp_or_udp == IPPROTO_UDP)
		array = udp_port_proto;
	else {
		DPI_LOG(DPI_LOG_ERROR, "unknown ip protocol");
		return;
	}

	if (array[port].proto[PORT_PROTO_NUM_MAX - 1] != 0) {
		DPI_LOG(DPI_LOG_ERROR, "port %u has already registered %d protocol", port, PORT_PROTO_NUM_MAX);
		return;
	}

	for (i = PORT_PROTO_NUM_MAX - 1; i > 0; i--) {
		array[port].proto[i] = array[port].proto[i - 1];
	}
	array[port].proto[0] = protocol;
	return;
}



/*
*协议识别的主函数
*/
static void check_dpi_flow_func(struct flow_info *flow,
					    const unsigned char *payload,
					    const unsigned short paylod_len)
{
	int i;
	uint16_t sport, dport;
	uint16_t litter_port;// bigger_port;
	uint16_t app_proto;
	struct guess_proto_data *port_array;
	struct check_proto_data *detection_array;

	if (flow->real_protocol_id != PROTOCOL_UNKNOWN)
		return;

	sport = ntohs(flow->tuple.inner.port_src);
	dport = ntohs(flow->tuple.inner.port_dst);

	if (sport > dport) {
		litter_port = dport;
//		bigger_port = sport;
	} else {
		litter_port = sport;
//		bigger_port = dport;
	}

	if (flow->tuple.inner.proto == IPPROTO_TCP) {
		port_array = tcp_port_proto;
		detection_array = tcp_detection_array;
	}
	else if (flow->tuple.inner.proto == IPPROTO_UDP) {
		port_array = udp_port_proto;
		detection_array = udp_detection_array;
	}
	else
		return;

	for (i = 0; i < PORT_PROTO_NUM_MAX; i++) {
		app_proto = port_array[litter_port].proto[i];
		if (app_proto == 0)
			break;
		if (g_config.protocol_switch[app_proto] == 0)
			continue;

		detection_array[app_proto].identify_func(flow, payload, paylod_len);
		if (flow->real_protocol_id != PROTOCOL_UNKNOWN)
			return;
	}

	for (i = 0; i < PROTOCOL_MAX; i++) {
		if (g_config.protocol_switch[i] == 0)
			continue;
		if (detection_array[i].identify_func &&  DPI_BITMASK_COMPARE(flow->excluded_protocol_bitmask,
			       detection_array[i].excluded_protocol_bitmask) == 0)
			detection_array[i].identify_func(flow, payload, paylod_len);
		if (flow->real_protocol_id != PROTOCOL_UNKNOWN)
			return;
	}
}



// rt biao qian
static void parser_eth_trailer(struct flow_info *flow, const uint8_t *trailer, uint16_t trailer_len, const struct dpi_ethhdr  *ethhdr)
{

    // 太小, 认定为 无效
	if (trailer_len < 20)
		return;


    if(NULL == flow->trailer)
    {
        flow->has_trailer = (TRAILER_NO == g_config.trailertype) ? TRAILER_NO : 1;
        flow->trailerlen  = trailer_len;
        flow->trailer     = memdup(trailer, trailer_len);
    }

    if(NULL == flow->ethhdr)
    {
        flow->ethhdr      = memdup(ethhdr, sizeof(struct dpi_ethhdr));
    }
}



#define SET_IPV4_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id) 	\
			{																			\
				tuple.proto = protocol;													\
				tuple.ip_version = 4;													\
				tuple.ip_src.ip4 = srcip;												\
				tuple.ip_dst.ip4 = dstip;												\
				tuple.port_src = srcport;												\
				tuple.port_dst = dstport;												\
				tuple.sctp_id = _sctp_id;												\
			}

#define SET_IPV6_FLOW_TUPLE(tuple, protocol, srcip, dstip, srcport, dstport, _sctp_id) 	\
			{																			\
				tuple.proto = protocol;													\
				tuple.ip_version = 6;													\
				memcpy(tuple.ip_src.ip6, srcip, sizeof(tuple.ip_src.ip6));				\
				memcpy(tuple.ip_dst.ip6, dstip, sizeof(tuple.ip_dst.ip6));				\
				tuple.port_src = srcport;												\
				tuple.port_dst = dstport;												\
				tuple.sctp_id = _sctp_id;												\
			}

/*
*查找和创建会话的函数
*会话的正反两个方向都会加入到hash表中
*/
static struct flow_info *_find_create_flow_ipv4(struct work_process_data * workflow,
				const struct five_tuple *outer, const struct dpi_iphdr *iph,
				uint16_t sport, uint16_t dport, uint16_t sctp_id, uint8_t *src_to_dst_direction)
{
	struct flow_key key;
	memset(&key, 0, sizeof(key));
	struct timespec time_now={0, 0};


	if (outer) {
		if (outer->ip_version == 4)
			SET_IPV4_FLOW_TUPLE(key.outer, outer->proto, outer->ip_src.ip4, outer->ip_dst.ip4, outer->port_src, outer->port_dst, sctp_id)
		else
			SET_IPV6_FLOW_TUPLE(key.outer, outer->proto, outer->ip_src.ip6, outer->ip_dst.ip6, outer->port_src, outer->port_dst, sctp_id)
	}
	SET_IPV4_FLOW_TUPLE(key.inner, iph->protocol, iph->saddr, iph->daddr, sport, dport, sctp_id)

	struct flow_info *flow_find;
	int pos = rte_hash_lookup_data(workflow->hash, &key, (void **)&flow_find);

	if(pos < 0) {
		struct flow_info *newflow;
		if (rte_mempool_get(flow_mempool, (void **)&newflow) < 0) {
			DPI_LOG(DPI_LOG_WARNING, "not enough memory");
			return (NULL);
		}

		workflow->num_allocated_flows++;
		memset(newflow, 0, sizeof(struct flow_info));
		INIT_LIST_HEAD(&newflow->node_timeout);
		INIT_LIST_HEAD(&newflow->reassemble_src2dst_head);
		INIT_LIST_HEAD(&newflow->reassemble_dst2src_head);
		INIT_LIST_HEAD(&newflow->rsm_head[FLOW_DIR_SRC2DST]);
		INIT_LIST_HEAD(&newflow->rsm_head[FLOW_DIR_DST2SRC]);

		clock_gettime(CLOCK_REALTIME, &time_now);
		newflow->flow_id=time_now.tv_sec*1000000000 + time_now.tv_nsec;
		newflow->pkt_rx_timestamp_first	= workflow->timestamp;

		if (outer) {
			if (outer->ip_version == 4) {
				SET_IPV4_FLOW_TUPLE(newflow->tuple.outer, outer->proto, outer->ip_src.ip4, outer->ip_dst.ip4, outer->port_src, outer->port_dst, sctp_id)
				SET_IPV4_FLOW_TUPLE(newflow->tuple_reverse.outer, outer->proto, outer->ip_dst.ip4, outer->ip_src.ip4, outer->port_dst, outer->port_src, sctp_id)
			} else {
				SET_IPV6_FLOW_TUPLE(newflow->tuple.outer, outer->proto, outer->ip_src.ip6, outer->ip_dst.ip6, outer->port_src, outer->port_dst, sctp_id)
				SET_IPV6_FLOW_TUPLE(newflow->tuple_reverse.outer, outer->proto, outer->ip_dst.ip6, outer->ip_src.ip6, outer->port_dst, outer->port_src, sctp_id)
			}
		}
		SET_IPV4_FLOW_TUPLE(newflow->tuple.inner, iph->protocol, iph->saddr, iph->daddr, sport, dport, sctp_id)
		SET_IPV4_FLOW_TUPLE(newflow->tuple_reverse.inner, iph->protocol, iph->daddr, iph->saddr, dport, sport, sctp_id)

		newflow->ip_version = 4;
		newflow->ttl = iph->ttl;
		snprintf((char*)newflow->ip_flag, sizeof(newflow->ip_flag), "%#X", iph->frag_off);
		newflow->thread_id = workflow->thread_id;

		if (ntohs(sport) < ntohs(dport))
			newflow->direction = FLOW_DIR_DST2SRC;
		else
			newflow->direction = FLOW_DIR_SRC2DST;

		*src_to_dst_direction = FLOW_DIR_SRC2DST;

		int retval = rte_hash_add_key_data(workflow->hash, &newflow->tuple, newflow);
		if (retval < 0) {
			DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
			rte_mempool_put(flow_mempool, (void *)newflow);
			newflow = NULL;
		} else {
			retval = rte_hash_add_key_data(workflow->hash, &newflow->tuple_reverse, newflow);
			if (retval < 0) {
				DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
				retval = rte_hash_del_key(workflow->hash, &newflow->tuple);
				if (retval < 0)
					DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple to hash");
				rte_mempool_put(flow_mempool, (void *)newflow);
				newflow = NULL;
			} else {
				workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]++;
				workflow->stats.flow_stats[PROTOCOL_UNKNOWN]++;
				workflow->stats.flow_count++;
			}
		}
		workflow->stats.inc_flow_num++;
		return newflow;
	} else {
		if (flow_find->tuple.inner.ip_src.ip4 == iph->saddr && flow_find->tuple.inner.port_src == sport) {
			*src_to_dst_direction = FLOW_DIR_SRC2DST;
		} else {
			*src_to_dst_direction = FLOW_DIR_DST2SRC;
		}
		flow_find->ttl = iph->ttl;
		return flow_find;
	}

}

#define SCTP_CHUNCK_HEADER_LEN 16

static void dpi_dissect_sctp(struct work_process_data *workflow,
								const uint64_t time,
								const struct dpi_iphdr *iph,
								uint16_t ipsize, uint16_t rawsize)
{
	UNUSED(ipsize);

	uint16_t offset = 0;
	uint8_t type;
	//uint8_t tag;
	uint16_t len;
//	uint32_t t_seq_num;
	uint16_t s_id;
//	uint16_t s_seq_num;
	uint32_t p_protocol;
	uint16_t chunck_total_len;
	struct flow_info * flow;
	uint8_t src_to_dst_direction;

	uint16_t l4_len = ntohs(iph->tot_len) - (iph->ihl * 4);
	const struct dpi_sctphdr *sctph = (const struct dpi_sctphdr *)((const uint8_t *)iph + (iph->ihl * 4));

	const uint8_t *chunck_data = (const uint8_t *)sctph + sizeof(const struct dpi_sctphdr);
	chunck_total_len = l4_len - sizeof(const struct dpi_sctphdr);

	while (offset + 4 < chunck_total_len) {
		type = get_uint8_t(chunck_data, offset);
		//tag = get_uint8_t(chunck_data, offset + 1);
		len = get_uint16_ntohs(chunck_data, offset + 2);
		len -= 16;

		if (len < 4 || offset + len > chunck_total_len)
			break;
		//we only dissect data type
		if (type != 0) {
			offset += len;
			continue;
		}

//		t_seq_num = get_uint32_ntohl(chunck_data, offset + 4);
		s_id = get_uint16_ntohs(chunck_data, offset + 8);
//		s_seq_num = get_uint16_ntohs(chunck_data, offset + 10);
		p_protocol = get_uint32_ntohl(chunck_data, offset + 12);
		flow = _find_create_flow_ipv4(workflow, NULL, iph, sctph->source, sctph->dest, s_id, &src_to_dst_direction);
		if (flow) {
			uint16_t index = dpi_get_flow_timeout_index(IPPROTO_SCTP);
			if (list_empty(&flow->node_timeout)) {
				list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
			} else {
				list_del(&flow->node_timeout);
				list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
			}
			//  sctp_detection_array[PROTOCOL_S1AP].dissect_func(flow, src_to_dst_direction, ntohl(tcph->seq), payload, payload_len, DISSECT_PKT_ORIGINAL);
		}

		if (p_protocol == 18) {
			//  call s1ap dissector
			//  todo
		} else if (p_protocol == 3) {
			//  dissect_m3ua(flow, src_to_dst_direction, chunck_data + offset + SCTP_CHUNCK_HEADER_LEN, len, s_id);
			//	call m3ua dissector
			//	todo
		}
		offset += len;
		continue;

	//	dissect_s1ap(flow, src_to_dst_direction, s_seq_num, chunck_data + offset + SCTP_CHUNCK_HEADER_LEN, len, DISSECT_PKT_ORIGINAL);

		if(flow != NULL) {
			workflow->stats.ip_packet_count++;
			workflow->stats.total_ip_bytes += rawsize;

			if(src_to_dst_direction == FLOW_DIR_SRC2DST)
				flow->src2dst_packets++, flow->src2dst_bytes += rawsize;
			else
				flow->dst2src_packets++, flow->dst2src_bytes += rawsize;

			flow->last_seen = time;
		} else {
			workflow->stats.total_discarded_pkts++;
		}

		offset += len;
	}
}

static int
flow_session_free(struct flow_info *flow)
{
    // 先超时处理各自的回调
    if(flow->flow_EOF)
    {
        flow->flow_EOF(flow, flow->app_session);
        flow->flow_EOF = NULL;
    }

    //再一一释放内存
    if(flow->trailer)
    {
        free(flow->trailer);
        flow->trailer = NULL;
    }

    if(flow->ethhdr)
    {
        free(flow->ethhdr);
        flow->ethhdr = NULL;
    }

    if (flow->app_session)
    {
        dpi_free(flow->app_session);
        flow->app_session = NULL;
    }
    return 0;
}



static struct flow_info *dpi_find_create_flow_ipv4(struct work_process_data * workflow,
				const struct five_tuple *outer,
				const struct dpi_iphdr *iph,
				uint16_t ipsize,
				uint16_t l4_packet_len,
				const struct dpi_tcphdr **tcph,
				const struct dpi_udphdr **udph,
				uint16_t *sport, uint16_t *dport,
				uint8_t *proto,
				const uint8_t **payload,
				uint16_t *payload_len,
				uint8_t *src_to_dst_direction) {
	uint32_t l4_offset;
	const uint8_t *l3, *l4;

	if (ipsize < 20)
		return NULL;

	if ((iph->ihl * 4) > ipsize || ipsize < ntohs(iph->tot_len)
	/* || (iph->frag_off & htons(0x1FFF)) != 0 */)
		return NULL;

	l4_offset = iph->ihl * 4;
	l3 = (const uint8_t*)iph;

	if (outer == NULL) {
		if (l4_packet_len < 64)
			workflow->stats.packet_len[0]++;
		else if (l4_packet_len >= 64 && l4_packet_len < 128)
			workflow->stats.packet_len[1]++;
		else if (l4_packet_len >= 128 && l4_packet_len < 256)
			workflow->stats.packet_len[2]++;
		else if (l4_packet_len >= 256 && l4_packet_len < 1024)
			workflow->stats.packet_len[3]++;
		else if (l4_packet_len >= 1024 && l4_packet_len < 1500)
			workflow->stats.packet_len[4]++;
		else if (l4_packet_len >= 1500)
			workflow->stats.packet_len[5]++;

		if (l4_packet_len > workflow->stats.max_packet_len)
			workflow->stats.max_packet_len = l4_packet_len;
	}

	*proto = iph->protocol;
	l4 = ((const uint8_t *)l3 + l4_offset);

	if (iph->protocol == IPPROTO_TCP && l4_packet_len >= 20) {
		u_int tcp_len;
		workflow->stats.tcp_count++;
		*tcph = (const struct dpi_tcphdr *)l4;
		*sport = ntohs((*tcph)->source);
		*dport = ntohs((*tcph)->dest);
		tcp_len = DPI_MIN(4 * (*tcph)->doff, l4_packet_len);
		*payload = &l4[tcp_len];
		*payload_len = DPI_MAX(0, l4_packet_len - 4 * (*tcph)->doff);
	} else if(iph->protocol == IPPROTO_UDP && l4_packet_len >= 8) {
		workflow->stats.udp_count++;
		*udph = (const struct dpi_udphdr *)l4;
		*sport = ntohs((*udph)->source);
		*dport = ntohs((*udph)->dest);
		*payload = &l4[sizeof(struct dpi_udphdr)];
		*payload_len = l4_packet_len > sizeof(struct dpi_udphdr) ? l4_packet_len - sizeof(struct dpi_udphdr) : 0;
	} else {
		*payload_len = 0;
		*sport = *dport = 0;
		return NULL;
	}

	return _find_create_flow_ipv4(workflow, outer, iph, htons(*sport), htons(*dport), 0, src_to_dst_direction);

}

static struct flow_info *dpi_find_create_flow_ipv6(struct work_process_data * workflow,
				const struct five_tuple *outer,
				const struct dpi_ipv6hdr *iph6,
				uint16_t ipsize,
				const struct dpi_tcphdr **tcph,
				const struct dpi_udphdr **udph,
				uint16_t *sport, uint16_t *dport,
				uint8_t *proto,
				const uint8_t **payload,
				uint16_t *payload_len,
				uint8_t *src_to_dst_direction)
{

	const uint8_t *data_start;
	uint16_t offset = 0;
	uint16_t l4_packet_len;
	const uint8_t *l4;

	if (ipsize < sizeof(const struct dpi_ipv6hdr) || ipsize < ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen))
		return NULL;

	data_start = (const u_int8_t*)iph6 + sizeof(const struct dpi_ipv6hdr);
	*proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;

	if (*proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
		*proto = data_start[0];
		offset += 8 * (data_start[1] + 1);
	}

	if (*proto != IPPROTO_TCP && *proto != IPPROTO_UDP)
		return NULL;
	/*
	while (*proto != IPPROTO_TCP && *proto != IPPROTO_UDP && offset < ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen))
	{
		*proto = get_uint8_t(data_start, offset);
		uint16_t opt_len = get_uint8_t(data_start, offset + 1);
		offset += opt_len + 2;
	}

	if (offset >= ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen))
		return NULL;
	*/
	l4 = data_start + offset;
	l4_packet_len = ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - offset;

	if (outer == NULL) {
		if(l4_packet_len < 64)
			workflow->stats.packet_len[0]++;
		else if(l4_packet_len >= 64 && l4_packet_len < 128)
			workflow->stats.packet_len[1]++;
		else if(l4_packet_len >= 128 && l4_packet_len < 256)
			workflow->stats.packet_len[2]++;
		else if(l4_packet_len >= 256 && l4_packet_len < 1024)
			workflow->stats.packet_len[3]++;
		else if(l4_packet_len >= 1024 && l4_packet_len < 1500)
			workflow->stats.packet_len[4]++;
		else if(l4_packet_len >= 1500)
			workflow->stats.packet_len[5]++;

		if(l4_packet_len > workflow->stats.max_packet_len)
			workflow->stats.max_packet_len = l4_packet_len;
	}

	if(*proto == IPPROTO_TCP && l4_packet_len >= 20) {
		u_int tcp_len;
		workflow->stats.tcp_count++;
		*tcph = (const struct dpi_tcphdr *)l4;
		*sport = ntohs((*tcph)->source);
		*dport = ntohs((*tcph)->dest);
		tcp_len = DPI_MIN(4 * (*tcph)->doff, l4_packet_len);
		*payload = &l4[tcp_len];
		*payload_len = l4_packet_len > (4 * (*tcph)->doff) ? l4_packet_len - (4 * (*tcph)->doff) : 0;
	} else if(*proto == IPPROTO_UDP && l4_packet_len >= 8) {
		workflow->stats.udp_count++;
		*udph = (const struct dpi_udphdr *)l4;
		*sport = ntohs((*udph)->source);
		*dport = ntohs((*udph)->dest);
		*payload = &l4[sizeof(struct dpi_udphdr)];
		*payload_len = l4_packet_len > sizeof(struct dpi_udphdr) ? l4_packet_len - sizeof(struct dpi_udphdr) : 0;
	} else {
		*payload_len = 0;
		*sport = *dport = 0;
		return NULL;
	}

	struct flow_key key;
	memset(&key, 0, sizeof(key));

	if (outer) {
		if (outer->ip_version == 4)
			SET_IPV4_FLOW_TUPLE(key.outer, outer->proto, outer->ip_src.ip4, outer->ip_dst.ip4, outer->port_src, outer->port_dst, 0)
		else
			SET_IPV6_FLOW_TUPLE(key.outer, outer->proto, outer->ip_src.ip6, outer->ip_dst.ip6, outer->port_src, outer->port_dst, 0)
	}
	SET_IPV6_FLOW_TUPLE(key.inner, *proto, iph6->ip6_src, iph6->ip6_dst, htons(*sport), htons(*dport), 0);

	struct flow_info *flow_find;
	int pos = rte_hash_lookup_data(workflow->hash, &key, (void **)&flow_find);

	if(pos < 0) {
		struct flow_info *newflow;
		if (rte_mempool_get(flow_mempool, (void **)&newflow) < 0) {
			DPI_LOG(DPI_LOG_WARNING, "not enough memory");
			return (NULL);
		}

		workflow->num_allocated_flows++;
		memset(newflow, 0, sizeof(struct flow_info));
		INIT_LIST_HEAD(&newflow->node_timeout);
		INIT_LIST_HEAD(&newflow->reassemble_src2dst_head);
		INIT_LIST_HEAD(&newflow->reassemble_dst2src_head);
		INIT_LIST_HEAD(&newflow->rsm_head[FLOW_DIR_SRC2DST]);
		INIT_LIST_HEAD(&newflow->rsm_head[FLOW_DIR_DST2SRC]);

		if (outer) {
			if (outer->ip_version == 4) {
				SET_IPV4_FLOW_TUPLE(newflow->tuple.outer, outer->proto, outer->ip_src.ip4, outer->ip_dst.ip4, outer->port_src, outer->port_dst, 0)
				SET_IPV4_FLOW_TUPLE(newflow->tuple_reverse.outer, outer->proto, outer->ip_dst.ip4, outer->ip_src.ip4, outer->port_dst, outer->port_src, 0)
			} else {
				SET_IPV6_FLOW_TUPLE(newflow->tuple.outer, outer->proto, outer->ip_src.ip6, outer->ip_dst.ip6, outer->port_src, outer->port_dst, 0)
				SET_IPV6_FLOW_TUPLE(newflow->tuple_reverse.outer, outer->proto, outer->ip_dst.ip6, outer->ip_src.ip6, outer->port_dst, outer->port_src, 0)
			}
		}
		SET_IPV6_FLOW_TUPLE(newflow->tuple.inner, *proto, iph6->ip6_src, iph6->ip6_dst, htons(*sport), htons(*dport), 0)
		SET_IPV6_FLOW_TUPLE(newflow->tuple_reverse.inner, *proto, iph6->ip6_dst, iph6->ip6_src, htons(*dport), htons(*sport), 0)

		newflow->ip_version = 6;
		newflow->ttl = iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;
    newflow->thread_id = workflow->thread_id;
		newflow->pkt_rx_timestamp_first	= workflow->timestamp;
		if (*sport < *dport)
			newflow->direction = FLOW_DIR_DST2SRC;
		else
			newflow->direction = FLOW_DIR_SRC2DST;

		*src_to_dst_direction = FLOW_DIR_SRC2DST;

		int retval = rte_hash_add_key_data(workflow->hash, &newflow->tuple, newflow);

		if (retval < 0) {
			DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
			rte_mempool_put(flow_mempool, (void *)newflow);
			newflow = NULL;
		} else {
			retval = rte_hash_add_key_data(workflow->hash, &newflow->tuple_reverse, newflow);
			if (retval < 0) {
				DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
				retval = rte_hash_del_key(workflow->hash, &newflow->tuple);
				if (retval < 0)
					DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple to hash");
				rte_mempool_put(flow_mempool, (void *)newflow);
				newflow = NULL;
			} else {
				workflow->stats.flow_count++;
				workflow->stats.flow_stats[PROTOCOL_UNKNOWN]++;
				workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]++;
			}
		}
		workflow->stats.inc_flow_num++;
		return newflow;
	} else {
		if (memcmp(flow_find->tuple.inner.ip_src.ip6, iph6->ip6_src, sizeof(iph6->ip6_src)) == 0) {
			*src_to_dst_direction = FLOW_DIR_SRC2DST;
		} else {
			*src_to_dst_direction = FLOW_DIR_DST2SRC;
		}
		flow_find->ttl = iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim;
		return flow_find;
	}
}

/*ipv4的主处理函数*/
int dpi_packet_processing_ipv4(struct work_process_data *workflow,
								const uint64_t time,
								const struct five_tuple *outer,
								const PacketInfo  *pkt_info,
								const struct dpi_iphdr *iph,
								uint16_t ipsize, uint16_t rawsize)
{
	int ret = PKT_OK;
	struct flow_info *flow = NULL;
	uint8_t proto;
	const struct dpi_tcphdr *tcph = NULL;
	const struct dpi_udphdr *udph = NULL;
	uint16_t sport, dport, payload_len;
	const uint8_t *payload;
	uint8_t src_to_dst_direction = FLOW_DIR_SRC2DST;
	const uint8_t *eth_trialer = NULL;
	uint16_t eth_trialer_len = 0;

	if (iph && iph->protocol == IPPROTO_SCTP) {
		dpi_dissect_sctp(workflow, time, iph, ipsize, rawsize);
		return PKT_OK;
	}



	if (iph)
		flow = dpi_find_create_flow_ipv4(workflow, outer, iph,
				ipsize,
				ntohs(iph->tot_len) - (iph->ihl * 4),
				&tcph, &udph, &sport, &dport,
				&proto,
				&payload, &payload_len, &src_to_dst_direction);

	if (!flow) {
		return PKT_OK;
	}

  flow->timestamp = g_config.g_now_time_usec;
  flow->pkt_rx_timestamp = workflow->timestamp;
	if (flow && iph && ipsize > ntohs(iph->tot_len)) {
		eth_trialer = (const uint8_t *)iph + ntohs(iph->tot_len);
		eth_trialer_len = ipsize - ntohs(iph->tot_len);
		parser_eth_trailer(flow, eth_trialer, eth_trialer_len, workflow->ethhdr);
	}

	flow->pkt_info = pkt_info;

	if(flow != NULL) {

        // 在流结构中，直接记录当前报文的ip,port
        if(IPPROTO_TCP == proto)
        {
            flow->pkt.ip_ver       = 4;
            flow->pkt.src_port     = tcph->source;
            flow->pkt.dst_port     = tcph->dest;
            flow->pkt.src_ip.ipv4  = iph->saddr;
            flow->pkt.dst_ip.ipv4  = iph->daddr;
        }
        else
        if(IPPROTO_UDP == proto)
        {
            flow->pkt.ip_ver       = 4;
            flow->pkt.src_port     = udph->source;
            flow->pkt.dst_port     = udph->dest;
            flow->pkt.src_ip.ipv4  = iph->saddr;
            flow->pkt.dst_ip.ipv4  = iph->daddr;
        }

		workflow->stats.ip_packet_count++;
		workflow->stats.total_ip_bytes += rawsize;

		if(src_to_dst_direction == FLOW_DIR_SRC2DST) {
			flow->src2dst_packets++;
			flow->src2dst_bytes += rawsize;
		} else {
			flow->dst2src_packets++;
			flow->dst2src_bytes += rawsize;
		}
		workflow->stats.flow_stats_total_pkts[flow->real_protocol_id]++;
		workflow->stats.flow_stats_total_bytes[flow->real_protocol_id] += rawsize;
		flow->last_seen = time;

		uint16_t index = dpi_get_flow_timeout_index(proto);
		if (list_empty(&flow->node_timeout)) {
			list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
		} else {
			list_del(&flow->node_timeout);
			list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
		}

		flow->pkt_first_line.has_search = 0;
		flow->pkt_first_line.linelen = 0;

		//tcp keep alive packet
		if (tcph) {
			uint32_t seq = ntohl(tcph->seq);
			if ((payload_len == 0 || payload_len == 1)
					&& seq == (flow->next_seq[src_to_dst_direction] - 1)
					&& ((tcph->fin | tcph->rst | tcph->syn) == 0)) {
				return PKT_OK;
			}
			if (seq + payload_len >= flow->next_seq[src_to_dst_direction]) {
				flow->next_seq[src_to_dst_direction] = seq + payload_len;
			}
			if (tcph->fin == 1 || tcph->syn == 1) {
				flow->next_seq[src_to_dst_direction]++;
			}

		}
	} else {
		workflow->stats.total_discarded_pkts++;
		return PKT_OK;
	}

	if (flow->real_protocol_id == PROTOCOL_UNKNOWN && flow->src2dst_packets + flow->dst2src_packets == 1) {
		struct conversation_tuple tuple;
		memset(&tuple, 0, sizeof(tuple));
		tuple.port_src = flow->tuple.inner.port_src;
		tuple.port_dst = flow->tuple.inner.port_dst;
		memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
		memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
		tuple.proto = flow->tuple.inner.proto;

		struct conversation_value *conv = find_conversation(&tuple, 0);
		if (conv) {
			flow->real_protocol_id = conv->protocol;
			workflow->stats.flow_stats[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats[flow->real_protocol_id]++;
			workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats_total[flow->real_protocol_id]++;
		}

	}

	if (flow->real_protocol_id == PROTOCOL_UNKNOWN
			&& ((proto == IPPROTO_UDP && flow->src2dst_packets + flow->dst2src_packets <= g_config.udp_identify_pkt_num)
				|| (proto == IPPROTO_TCP && flow->src2dst_packets + flow->dst2src_packets <= g_config.tcp_identify_pkt_num))) {
		/* guess protocol */
//		flow->guessed_protocol_id = guess_protocol_id(flow->tuple.inner.proto, ntohs(flow->tuple.inner.port_src), ntohs(flow->tuple.inner.port_dst));
		check_dpi_flow_func(flow, payload, payload_len);
		if (flow->real_protocol_id != PROTOCOL_UNKNOWN) {
			workflow->stats.flow_stats[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats[flow->real_protocol_id]++;
			workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats_total[flow->real_protocol_id]++;

			workflow->stats.flow_stats_total_pkts[flow->real_protocol_id] += flow->src2dst_packets + flow->dst2src_packets;
			workflow->stats.flow_stats_total_bytes[flow->real_protocol_id] += flow->src2dst_bytes + flow->dst2src_bytes;
			workflow->stats.flow_stats_total_pkts[PROTOCOL_UNKNOWN] -= flow->src2dst_packets + flow->dst2src_packets;
			workflow->stats.flow_stats_total_bytes[PROTOCOL_UNKNOWN] -= flow->src2dst_bytes + flow->dst2src_bytes;
		}
	}

	if (flow->real_protocol_id != PROTOCOL_UNKNOWN && payload_len > 0) {
		if (proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			ret = tcp_detection_array[flow->real_protocol_id].dissect_func(flow, src_to_dst_direction, ntohl(tcph->seq), payload, payload_len, DISSECT_PKT_ORIGINAL);
		else if (proto == IPPROTO_UDP && udp_detection_array[flow->real_protocol_id].dissect_func)
			ret = udp_detection_array[flow->real_protocol_id].dissect_func(flow, src_to_dst_direction, 0, payload, payload_len, DISSECT_PKT_ORIGINAL);
		flow->pkt_info = NULL;
	}

	if (flow && tcph && tcph->fin == 1) {
		flow->fin_flag[src_to_dst_direction] = 1;
	}

    /*rst和fin包结束tcp会话，正反两个方向的fin都收到才结束会话*/
    if (flow && tcph && ((flow->fin_flag[FLOW_DIR_SRC2DST] && flow->fin_flag[FLOW_DIR_DST2SRC]) || tcph->rst == 1))
    {
    }
	return ret;
}


/*
*ipv6的主处理函数
*/
int dpi_packet_processing_ipv6(struct work_process_data *workflow,
								const uint64_t time,
								const struct five_tuple *outer,
								const PacketInfo  *pkt_info,
								const struct dpi_ipv6hdr *iph6,
								uint16_t ipsize, uint16_t rawsize)
{
	int ret = PKT_OK;
	struct flow_info *flow = NULL;
	uint8_t proto;
	const struct dpi_tcphdr *tcph = NULL;
	const struct dpi_udphdr *udph = NULL;
	uint16_t sport, dport, payload_len;
	const uint8_t *payload;
	uint8_t src_to_dst_direction = FLOW_DIR_SRC2DST;
	const uint8_t *data_start;
	uint16_t offset = 0;
	uint16_t l4_packet_len;
	const uint8_t *l4;
	const uint8_t *eth_trialer = NULL;
	uint16_t eth_trialer_len = 0;

	if (ipsize < sizeof(const struct dpi_ipv6hdr) || ipsize < ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen))
		return 0;
	data_start = (const u_int8_t*)iph6 + sizeof(const struct dpi_ipv6hdr);
	proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;

	if (proto == IPPROTO_DSTOPTS) {
		proto = data_start[0];
		offset += 8 * (data_start[1] + 1);
	}
	l4 = data_start + offset;
	l4_packet_len = ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - offset;



	if (iph6)
		flow = dpi_find_create_flow_ipv6(workflow, outer, iph6,
				ipsize,
				&tcph, &udph, &sport, &dport,
				&proto,
				&payload, &payload_len, &src_to_dst_direction);

	if (flow != NULL) {
		flow->pkt_info = pkt_info;
	} else {
		return PKT_OK;
	}
  flow->pkt_rx_timestamp = workflow->timestamp;
	if (flow && iph6 && ipsize > sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen)) {
		eth_trialer = (const uint8_t *)iph6 + sizeof(struct dpi_ipv6hdr) + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
		eth_trialer_len = ipsize - sizeof(struct dpi_ipv6hdr) - ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
		parser_eth_trailer(flow, eth_trialer, eth_trialer_len, workflow->ethhdr);
	}

	if(flow != NULL) {

        // 在流结构中，直接记录当前报文的ip,port
        if(IPPROTO_TCP == proto)
        {
            flow->pkt.ip_ver   = 6;
            flow->pkt.src_port = tcph->source;
            flow->pkt.dst_port = tcph->dest;
            memcpy(flow->pkt.src_ip.ipv6, iph6->ip6_src, 16);
            memcpy(flow->pkt.dst_ip.ipv6, iph6->ip6_dst, 16);
        }
        else
        if(IPPROTO_UDP == proto)
        {
            flow->pkt.ip_ver   = 6;
            flow->pkt.src_port = udph->source;
            flow->pkt.dst_port = udph->dest;
            memcpy(flow->pkt.src_ip.ipv6, iph6->ip6_src, 16);
            memcpy(flow->pkt.dst_ip.ipv6, iph6->ip6_dst, 16);
        }

		workflow->stats.ip_packet_count++;
		workflow->stats.total_ip_bytes += rawsize;

		if(src_to_dst_direction == FLOW_DIR_SRC2DST) {
			flow->src2dst_packets++;
			flow->src2dst_bytes += rawsize;
		} else {
			flow->dst2src_packets++;
			flow->dst2src_bytes += rawsize;
		}
		flow->last_seen = time;

		uint16_t index = dpi_get_flow_timeout_index(proto);
		if (list_empty(&flow->node_timeout)) {
			list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
		} else {
			list_del(&flow->node_timeout);
			list_add_tail(&flow->node_timeout, &workflow->timeout_head[index]);
		}
		flow->pkt_first_line.has_search = 0;
		flow->pkt_first_line.linelen = 0;

		//tcp keep alive packet
		if (tcph) {
			uint32_t seq = ntohl(tcph->seq);
			if ((payload_len == 0 || payload_len == 1)
					&& seq == (flow->next_seq[src_to_dst_direction] - 1)
					&& ((tcph->fin | tcph->rst | tcph->syn) == 0)) {
				return PKT_OK;
			}
			if (seq + payload_len >= flow->next_seq[src_to_dst_direction]) {
				flow->next_seq[src_to_dst_direction] = seq + payload_len;
			}
			if (tcph->fin == 1 || tcph->syn == 1) {
				flow->next_seq[src_to_dst_direction]++;
			}

		}
	} else {
		workflow->stats.total_discarded_pkts++;
		return PKT_OK;
	}

	if (flow->real_protocol_id == PROTOCOL_UNKNOWN
			&& ((proto == IPPROTO_UDP && flow->src2dst_packets + flow->dst2src_packets <= g_config.udp_identify_pkt_num)
				|| (proto == IPPROTO_TCP && flow->src2dst_packets + flow->dst2src_packets <= g_config.tcp_identify_pkt_num))) {
		/* guess protocol */
		//flow->guessed_protocol_id = guess_protocol_id(flow->tuple.inner.proto, ntohs(flow->tuple.inner.port_src), ntohs(flow->tuple.inner.port_dst));
		check_dpi_flow_func(flow, payload, payload_len);
		if (flow->real_protocol_id != PROTOCOL_UNKNOWN) {
			workflow->stats.flow_stats[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats[flow->real_protocol_id]++;
			workflow->stats.flow_stats_total[PROTOCOL_UNKNOWN]--;
			workflow->stats.flow_stats_total[flow->real_protocol_id]++;
		}
	}

	if (flow->real_protocol_id != PROTOCOL_UNKNOWN && payload_len > 0) {
		if (proto == IPPROTO_TCP && tcp_detection_array[flow->real_protocol_id].dissect_func)
			ret = tcp_detection_array[flow->real_protocol_id].dissect_func(flow, src_to_dst_direction, ntohl(tcph->seq), payload, payload_len, DISSECT_PKT_ORIGINAL);
		else if (proto == IPPROTO_UDP && udp_detection_array[flow->real_protocol_id].dissect_func)
			ret = udp_detection_array[flow->real_protocol_id].dissect_func(flow, src_to_dst_direction, 0, payload, payload_len, DISSECT_PKT_ORIGINAL);
	}

	if (flow && tcph && tcph->fin == 1) {
		flow->fin_flag[src_to_dst_direction] = 1;
	}

    if (flow && tcph && ((flow->fin_flag[FLOW_DIR_SRC2DST] && flow->fin_flag[FLOW_DIR_DST2SRC]) || tcph->rst == 1))
    {
    }

	return ret;
}


int workflow_process_packet2 (struct work_process_data * workflow,
 const struct rte_mbuf *mbuf,
					u_int64_t p_usec,
					const u_char *packet,
					uint32_t pkt_len) {
	/*
	* Declare pointers to packet headers
	*/
	const struct dpi_ethhdr *ethhdr;
	const struct dpi_iphdr *iph = NULL;
	const struct dpi_ipv6hdr *iph6 = NULL;

	uint16_t eth_offset = 0;
	uint16_t ip_offset = 0;
	uint16_t ip_len = 0;
	uint16_t type;
	uint8_t proto = 0;

	int check;

	PacketInfo pkt_info;
	memset(&pkt_info, 0, sizeof(PacketInfo));

	workflow->stats.total_wire_bytes += pkt_len;
	workflow->stats.raw_packet_count++;
	workflow->last_time = p_usec > workflow->last_time ? p_usec : workflow->last_time;
	pkt_info.pkt_len = pkt_len;

	/*每10msec都要去超时一次会话*/
	do_idle_flow_free(workflow->thread_id, p_usec, 0);
  get_eth_info(packet, pkt_len, &(workflow->legacy), g_config.trailertype); /* 获取以太层相关信息 */

	ethhdr = (const struct dpi_ethhdr *) &packet[eth_offset];
	ip_offset = sizeof(struct dpi_ethhdr) + eth_offset;
    workflow->ethhdr = ethhdr;
	pkt_info.ethhdr = ethhdr;

	check = ntohs(ethhdr->h_proto);

	if (check >= 0x0600)
		type = check;
	else
		type = 0;

	switch(type) {
		case VLAN:
			type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
			ip_offset += 4;
			// double tagging for 802.1Q
			if(type == 0x8100) {
				type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
				ip_offset += 4;
			}
			break;
		case MPLS_UNI:
		case MPLS_MULTI:
			type = ETH_P_IP;
			ip_offset += 4;
			break;
		case PPPoE:
			type = ETH_P_IP;
			ip_offset += 8;
			break;
		default:
			break;
	}

	if (type != ETH_P_IP && type != ETH_P_IPV6) {
		return PKT_DROP;
	}

	iph = (const struct dpi_iphdr *) &packet[ip_offset];
	pkt_info.ipversion = iph->version;

	if (iph->version == 4) {
		ip_len = ((uint16_t)iph->ihl * 4);
		proto = iph->protocol;
		iph6 = NULL;
		pkt_info.iph = iph;
	} else if (iph->version == 6) {
		iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
		proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
		ip_len = sizeof(struct dpi_ipv6hdr);
		iph = NULL;
		if(proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
			const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
			proto = options[0];
			ip_len += 8 * (options[1] + 1);
		}
		pkt_info.iph6 = iph6;
	}

	if (proto == IPPROTO_UDP) {
    const struct dpi_udphdr *udp = (const struct dpi_udphdr *)&packet[ip_offset + ip_len];
#if 1
		uint16_t sport = ntohs(udp->source);
		uint16_t dport = ntohs(udp->dest);

		if (((sport == GTP_U_V1_PORT) || (dport == GTP_U_V1_PORT))
				&& (ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8 + 20 <= pkt_len)) {
			u_int offset = ip_offset + ip_len + sizeof(struct dpi_udphdr);
			uint8_t flags = packet[offset];
			uint8_t message_type = packet[offset+1];

			if ((((flags & 0xE0) >> 5) == 1 /* GTPv1 */)
					&& (message_type == 0xFF /* T-PDU */)) {

				ip_offset = ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8; /* GTPv1 header len */
				if (flags & 0x04) ip_offset += 1; /* next_ext_header is present */
				if (flags & 0x02) ip_offset += 4; /* sequence_number is present (it also includes next_ext_header and pdu_number) */
				if (flags & 0x01) ip_offset += 1; /* pdu_number is present */

				iph = (const struct dpi_iphdr *) &packet[ip_offset];
	      pkt_info.ipversion = iph->version;
        if (iph->version == 4) {
          ip_len = ((uint16_t)iph->ihl * 4);
          proto = iph->protocol;
          iph6 = NULL;
          pkt_info.iph = iph;
        } else if (iph->version == 6) {
          iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
          proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
          ip_len = sizeof(struct dpi_ipv6hdr);
          iph = NULL;
          if(proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
            const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
            proto = options[0];
            ip_len += 8 * (options[1] + 1);
          }
          pkt_info.iph6 = iph6;
        } else {
          return PKT_DROP;
        }

				// if (iph->version == 6) {
				// 	iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];

				// 	iph = NULL;
				// 	pkt_info.iph6 = iph6;
				// } else if (iph->version == 4) {
				// 	pkt_info.iph = iph;

				// }  else if (iph->version != 4) {
				// 	return PKT_DROP;
				// }
			}
		} else {
		  pkt_info.udph = udp;
    }
#endif
	}
	pkt_info.proto = proto;

	if(proto == IPPROTO_TCP){
        const struct dpi_tcphdr *tcp = (const struct dpi_tcphdr *)&packet[ip_offset + ip_len];
        pkt_info.tcph = tcp;
    }

	if (iph) {
		return dpi_packet_processing_ipv4(workflow, p_usec, NULL, &pkt_info, iph, pkt_len - ip_offset, pkt_len);
//		return(packet_processing_ipv4(workflow, p_usec, iph,
//				ip_offset, pkt_len - ip_offset, pkt_len));
	} else if(iph6) {
		return dpi_packet_processing_ipv6(workflow, p_usec, NULL, &pkt_info, iph6, pkt_len - ip_offset, pkt_len);
//		return(packet_processing_ipv6(workflow, p_usec, iph6,
//				ip_offset, pkt_len - ip_offset, pkt_len));
	} else {
		workflow->stats.total_discarded_pkts++;
		return PKT_DROP;
	}
}


/*
*根据传输层协议和当前会话的超时下标，得到会话应该放入的超时链表的下标
*/
uint16_t dpi_get_flow_timeout_index(uint8_t proto)
{
	uint64_t index;

	switch (proto) {

		case IPPROTO_TCP :

			index = (g_config.timeout_index + g_config.tcp_flow_timeout - 1) % TIMEOUT_MAX;
			break;

		case IPPROTO_UDP :

			index = (g_config.timeout_index + g_config.udp_flow_timeout - 1) % TIMEOUT_MAX;
			break;

		case IPPROTO_SCTP :

			index = (g_config.timeout_index + g_config.sctp_flow_timeout - 1) % TIMEOUT_MAX;
			break;

		default :

			index = (g_config.timeout_index + g_config.tcp_flow_timeout - 1) % TIMEOUT_MAX;
			break;
	}

	return index;
}

/*
*会话超时的主要函数
*/
void flow_timeout(struct     work_process_data *process, int timeout_index, int max_del_num)
{
	int del_num = 0;
	int retval;
	struct flow_info *pos;
	struct flow_info *n;
	struct list_head *head;

	/*当前应该超时的会话链表*/
	head = &process->timeout_head[timeout_index];

	list_for_each_entry_safe(pos, n, head, node_timeout) {
		del_num++;

	//设置超时标记
	pos->is_timeout = 1;

		// 调用 每个flow 的 fini 接口
        if(tcp_detection_array[pos->real_protocol_id].flow_timeout)
        {
            tcp_detection_array[pos->real_protocol_id].flow_timeout(pos);
        }

		if (udp_detection_array[pos->real_protocol_id].flow_timeout) {
			udp_detection_array[pos->real_protocol_id].flow_timeout(pos);
		}

		if (pos->tuple.inner.proto == IPPROTO_TCP)
			tcp_reassemble_do_final(pos);

		list_del(&pos->node_timeout);

		retval = rte_hash_del_key(process->hash, &pos->tuple_reverse);
		if (retval < 0)
			DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple_reverse to hash");
		retval = rte_hash_del_key(process->hash, &pos->tuple);
		if (retval < 0)
			DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple to hash");

		process->stats.flow_stats[pos->real_protocol_id]--;
		process->stats.flow_count--;

        flow_session_free(pos);
		rte_mempool_put(flow_mempool, (void *)pos);

		/*每次最多超时的会话个数*/
		if (max_del_num > 0 && del_num >= max_del_num)
			break;
	}

}

void do_idle_flow_free(uint16_t thread_id, uint64_t now_time, int flag)
{
	struct work_process_data *process = &flow_thread_info[thread_id];

	if (process->stats.flow_count == 0)
		return;

	if (flag || process->last_idle_scan_time + g_config.idle_scan_period < now_time) {
		flow_timeout(process, g_config.timeout_index, 1000);

		process->last_idle_scan_time = now_time;
	}

	return;
}


void do_all_flow_free(uint16_t thread_id)
{
	int i;
	struct work_process_data *process = &flow_thread_info[thread_id];

	if (process->stats.flow_count == 0)
		return;

	for (i = 0; i < TIMEOUT_MAX; i++) {
		flow_timeout(process, i, -1);
	}
	return;
}

int
get_ip_port(struct packet *pkt, struct sonwden *sonwden, int *C2S)
{

    sonwden->ip_ver = pkt->ip_ver;
    if(ntohs(pkt->src_port) > ntohs(pkt->dst_port))
    {
        memcpy(sonwden->client_ip.ipv6, pkt->src_ip.ipv6, 16);
        memcpy(sonwden->server_ip.ipv6, pkt->dst_ip.ipv6, 16);
        sonwden->client_port = ntohs(pkt->src_port);
        sonwden->server_port = ntohs(pkt->dst_port);
        *C2S                 = CLIENT_TO_SERVER;
    }
    else
    {
        memcpy(sonwden->client_ip.ipv6, pkt->dst_ip.ipv6, 16);
        memcpy(sonwden->server_ip.ipv6, pkt->src_ip.ipv6, 16);
        sonwden->client_port = ntohs(pkt->dst_port);
        sonwden->server_port = ntohs(pkt->src_port);
        *C2S                 = SERVER_TO_CLIENT;
    }
    return 0;
}

int
get_ip_port_v4(struct flow_info *flow, int *client_ip, int *server_ip, short *client_port, short *server_port, int *C2S)
{
    if(ntohs(flow->pkt.src_port) > ntohs(flow->pkt.dst_port))
    {
        *client_ip   = flow->pkt.src_ip.ipv4;
        *server_ip   = flow->pkt.dst_ip.ipv4;
        *client_port = ntohs(flow->pkt.src_port);
        *server_port = ntohs(flow->pkt.dst_port);
        *C2S         = CLIENT_TO_SERVER;
    }
    else
    {
        *client_ip   = flow->pkt.dst_ip.ipv4;
        *server_ip   = flow->pkt.src_ip.ipv4;
        *client_port = ntohs(flow->pkt.dst_port);
        *server_port = ntohs(flow->pkt.src_port);
        *C2S         = SERVER_TO_CLIENT;
    }
    return 0;
}

