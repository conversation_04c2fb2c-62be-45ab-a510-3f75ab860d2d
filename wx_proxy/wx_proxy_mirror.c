/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <event2/listener.h>

#include <glib.h>

#include <event2/util.h>

#include "wx_proxy_conf.h"
#include "wx_proxy_log.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_common.h"

#define  MSG_BUFF  8192

/*****************************************************************
*Function    : mirror_create
*Description : create mirror
*Input       : tcp server num, ssl server num
*Output      : none
*Return      : new mirror
*Others      : none
*****************************************************************/
struct mirror_t*
mirror_create(struct context *ctx)
{
    return palloc(sizeof(struct mirror_t));
}

/*****************************************************************
*Function    : connection_malloc
*Description : connection memory malloc
*Input       : none
*Output      : none
*Return      : new connection
*Others      : none
*****************************************************************/
struct connection_t *
connection_malloc()
{
    struct connection_t *connection = malloc(sizeof(struct connection_t));
    memset(connection, 0, sizeof(struct connection_t));
    return connection;
}

/*****************************************************************
*Function    : connection_free
*Description : free memory
*Input       : none
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void connection_free(struct connection_t *connection)
{
    struct bufferevent *bev = connection->bev;

    log_write(DEBUG, "free connection:%p", connection);
    bufferevent_free(bev);
    pfree(connection);
}

/*****************************************************************
*Function    : mirror_free
*Description : free memory space of mirror
*Input       : none
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
mirror_free(struct mirror_t *mirror)
{
    if(mirror)
    {
        log_write(DEBUG, "mirror free: %p", mirror);
        free(mirror);
    }
}

