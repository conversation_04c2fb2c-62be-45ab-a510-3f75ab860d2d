/****************************************************************************************
 * 文 件 名 : wxa_tbl_writer.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-01-05
* 编    码 : root      '2019-01-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <time.h>
#include <errno.h>
#include <unistd.h>
#include <string.h>

#include <iconv.h>

#include "wxcs_tbl_writer.h"
#include "wxcs_logger.h"
#include "wxcs_utils.h"

wxcsTblWriter::wxcsTblWriter(const std::string &strTblFileDir,
                           const std::string &strProtoName,
                           int lRecordCntPerFile)
    : strTblFileDir_(strTblFileDir + "/" + strProtoName)
    , strProtoName_(strProtoName)
    , lRecordCntPerFile_(lRecordCntPerFile)
{
    LOG_DEF->info("{} write tbl to dir {}", strProtoName_, strTblFileDir_);
    maxFileCode_ = CFG->GetValueOf<int>("TBL_FILE_NAME_MAX_FORMAT_CODE", 1);
    tblFileMaxWritingTime = (unsigned int)CFG->GetValueOf<int>("WRITE_TBL_MAXTIME", 60);
    TblFileInfo tmpTblInfo;
    tmpTblInfo.tblFilePtr = nullptr;
    tmpTblInfo.tblWritenCnts = 0;
    tmpTblInfo.tblFileLastWriteTime = 0;
    tmpTblInfo.tblFileFullPathWriteCompleted.clear();
    tmpTblInfo.tblFileFullPathWriting.clear();
    tblFileInfoVec_.resize(maxFileCode_, tmpTblInfo);
#ifdef DPI_WXA_WEB

    sockfd_ = ::socket(AF_INET, SOCK_DGRAM | SOCK_NONBLOCK | SOCK_CLOEXEC, IPPROTO_UDP);
    if (sockfd_ < 0) {
      LOG_DEF->info("::socket");
    }

    memset(&addr_, 0, sizeof addr_);
    addr_.sin_family = AF_INET;
    int port = CFG->GetValueOf<int>("TBL_SEND_UDP_PORT", 9999);
    addr_.sin_port = htobe16(port);
    std::string ip = CFG->GetValueOf<CSTR>("TBL_SEND_UDP_IP", "127.0.0.1");

    if (::inet_pton(AF_INET, ip.c_str(), &addr_.sin_addr) <= 0) {
      LOG_DEF->info("sockets::fromIpPort");
    }

    int ret = connect(sockfd_, (const sockaddr*)&addr_, sizeof(addr_));
    if (ret < 0) {
      LOG_DEF->info("::connect");
    }
    LOG_DEF->info("{} :: {} connect",ip,port);
#endif
}

void wxcsTblWriter::writeCurrentFileDone(int index)
{
    if(tblFileInfoVec_[index].tblFilePtr)
    {
        fclose(tblFileInfoVec_[index].tblFilePtr);
        tblFileInfoVec_[index].tblFilePtr = nullptr;
    }
    rename(tblFileInfoVec_[index].tblFileFullPathWriting.c_str(), tblFileInfoVec_[index].tblFileFullPathWriteCompleted.c_str());
}

void wxcsTblWriter::generateFileName(int index)
{
    // done:    20180226173528_45038_rtxdr_014.tbl
    // writing: 20180226173528_45038_rtxdr_014.tbl.writing
    char buf[PATH_MAX] = {0};
    time_t unixTime = time(0);
    tm t = *localtime((time_t *)&unixTime);

    // 补全目录
    int ret = 0;
    int len = PATH_MAX;
    ret += snprintf(buf+ret, len-ret, "%s/", strTblFileDir_.c_str());

    // date string
    ret += strftime(buf+ret, len-ret, "%Y%m%d%H%M%S", &t);

    // etc
    ret += snprintf(buf+ret, len-ret, "_%05d_%s_%s.tbl",
                          hundredThousandthSecond(),
                          strProtoName_.c_str(),
                          getFileFormatCode(index).c_str());
    tblFileInfoVec_[index].tblFileFullPathWriteCompleted = buf;
    tblFileInfoVec_[index].tblFileFullPathWriting = tblFileInfoVec_[index].tblFileFullPathWriteCompleted + TBL_WRITING_SUFFIX;
}

int wxcsTblWriter::createTblFile(int index)
{
    // 创建 xdr 目录
    if (access(strTblFileDir_.c_str(),R_OK) != 0)
    {
        makeDir(strTblFileDir_.c_str());
    }
    /*ADD_S by yangna 2020-09-17 */
    /*新生成一个文件，文件相关属性要重置 */
    tblFileInfoVec_[index].tblFilePtr = nullptr;
    tblFileInfoVec_[index].tblWritenCnts = 0;
    tblFileInfoVec_[index].tblFileLastWriteTime = 0;
    tblFileInfoVec_[index].tblFileFullPathWriteCompleted.clear();
    tblFileInfoVec_[index].tblFileFullPathWriting.clear();
    /*ADD_E by yangna 2020-09-17 */
    // 生成文件名
    generateFileName(index);
    // 创建文件
    tblFileInfoVec_[index].tblFilePtr = fopen( tblFileInfoVec_[index].tblFileFullPathWriting.c_str(), "w");
    return 0;
}

static int isUTF8(const char *pData, int len)
{
    int loop = len;
    const char *p = pData;

    if(NULL == pData || len <=0)
    {
        return 0;
    }

    while(loop > 0 )
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            p++;
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }
        else if(loop>=2 &&  (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0)))
        {
            p = p + 2;
            loop = loop - 2;
            continue;
        }
        else if(loop>=3 &&  (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) )
        {
            p = p + 3;
            loop = loop - 3;
            continue;
        }
        else if(loop>=4 &&  (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))  && (0X80 == (p[3] & 0XC0)) )
        {
            p = p + 4;
            loop = loop - 4;
            continue;
        }
        return 0; /* 这不是 UTF-8 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

std::string ReplaceCRLF(std::string &strRecordLine)
{
    // 如果有 换行符, 则替换成 空格
    std::string buffer;
    for(size_t i = 0; i < strRecordLine.length(); i++)
    {
        if(strRecordLine[i] == '\r')
        {
            buffer.push_back(' ');
        }
        else
        if(strRecordLine[i] == '\n')
        {
            buffer.push_back(' ');
        }
        else
        {
            buffer.push_back(strRecordLine[i]);
        }
    }
    return buffer;
}

static int hasCRLF(const char*p, int len)
{
    if(NULL == p || len < 0)
    {
        return -1;
    }

    for(int i=0; i< len; i++)
    {
        if(p[i] == '\r')
        {
            return 1;
        }
        else
        if(p[i] == '\n')
        {
            return 1;
        }
    }
    return 0;
}

int wxcsTblWriter::writeToFileReal(int index, std::string &strRecordLine)
{
    if(strRecordLine.length() < 1)
    {
        return 0;
    }

    std::string StringOrg = strRecordLine;

    // 消灭回车换行符
    strRecordLine = ReplaceCRLF(StringOrg);

    // 再检查一遍有没有非 UTF8编码
    if(strRecordLine.length() != isUTF8(strRecordLine.c_str(), strRecordLine.length()))
    {
        LOG_DEF->warn("TBLNotUtf8,len={},str={}", strRecordLine.length(), strRecordLine.c_str());
        return 0;
    }

    // 再检查一遍有没有 回车换行符
    if(1 == hasCRLF(strRecordLine.c_str(), strRecordLine.length()))
    {
        LOG_DEF->warn("TBLHasCRLF,len={},str={}", strRecordLine.length(), strRecordLine.c_str());
        return 0;
    }

    if (tblFileInfoVec_[index].tblFilePtr)
    {
        fprintf(tblFileInfoVec_[index].tblFilePtr, "%s\n", strRecordLine.c_str());
        fflush(tblFileInfoVec_[index].tblFilePtr);
        /*这里每次写文件获取当前时间很耗性能，后期需要优化 */
        tblFileInfoVec_[index].tblFileLastWriteTime = time(NULL);
    }

    // 计数还未达到上限
    if (++tblFileInfoVec_[index].tblWritenCnts <= lRecordCntPerFile_)
    {
        return 0;
    }
    writeCurrentFileDone(index);
    return 0;
}

int wxcsTblWriter::writeColumnsFile(const std::string &strFieldsDir, const std::string &strFieldsList)
{
    char strFieldsFilePath[PATH_MAX] = { 0 };

    // 创建 fields  目录
    if (access(strFieldsDir.c_str(), R_OK) != 0)
    {
        makeDir(strFieldsDir.c_str());
    }

    // 目录 + filename
    sprintf(strFieldsFilePath, "%s/%s",
            strFieldsDir.c_str(),
            (strProtoName_ + "_f.txt").c_str());

    FILE *pField = fopen(strFieldsFilePath, "w");
    CHECK_NOR_EXIT(NULL == pField, -1, strerror(errno));

    fprintf(pField, "%s", strFieldsList.c_str());

    fclose(pField);

    return 0;
}
