#ifndef DPI_PRECORD_WRITER__H
#define DPI_PRECORD_WRITER__H

#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>

class ProtoRecordWriterKeeper;

/*
 * 某协议的 record writer
 * 需求:
 * 1) 当没有调用任何 writeRecord 时，并不在磁盘上产生大小为零的空文件;
 * 2) 当一个文件超过 10s 没有发生任何写入时，将其关闭;
 * 3) 当一个文件行数超过 n 时，将其关闭;
 * 4) 支持进程存在时将任何输出文件删除后的文件重建，即文件可能随时需要重新创建;
 */
class ProtoRecordWriter
{
public:
    ProtoRecordWriter(ProtoRecordWriterKeeper *keeper, const std::string &strProto, const std::string &strOutputRootPath,const std::string &strOutputFormat);

    int writeRecord(precord_t *record);

    int onIdleCheck(uint64_t timeNow);

    int onProcessGoingToTerminate();

    int setRecordCountPerFile(uint32_t recordCount);

private: // 生成文件路径
    std::string generateOutputParentDirPath();

    std::string generateOutputFileName();

    std::string generateOutputFilePath();

private: // 创建与关闭文件
    bool currentFileShouldRotate(uint64_t time_now);

    int openNewOutputFile();

    int closeCurrentFile();

private: // 写入记录
    int writeOneRecord_in_json(precord_t *record);
    int writeOneRecord_in_tbl(precord_t *record);
private:
    ProtoRecordWriterKeeper *keeper_ = NULL;;
    std::string protoName_;
    std::string outputRootPath_;
    std::string outputParentDirPath_;
    std::string outputFormat_;
    uint32_t    recordCountsMaxPerFile_ = 5000;
    uint32_t    writingTimeout_inSeconds_ = 10; // 超过该时间没有进行写入该文件将关闭
    uint64_t    lastWriteInTime_ = 0;
private:
    std::string currentFileNameWriting_;
    std::string currentFileName_;
    std::string currentFilePath_;
    FILE *      currentFileHandle_ = NULL;
    uint64_t    currentFileCreateTime_;
    uint32_t    currentFileWriteRecordCounts_;
};

class ProtoRecordWriterKeeper
{
public:
    ProtoRecordWriterKeeper(const std::string &strOutputRootPath, const std::string &strOutputFormat);

    ProtoRecordWriter* getProtoRecordWriterOf(const std::string &strProtoName);

    uint64_t getNewFileSerialNumber();

    int setExtraProperty(const std::string &name, const std::string &value);

    std::string getExtraProperty(const std::string &name);

    int foreachRecordWriter(std::function<int(ProtoRecordWriter *)> func);

public:
    static int                      createSomeProtoRecordWriterKeeper(uint32_t count, const std::string &strOutputRootPath, const std::string &strOutputFormat);
    static ProtoRecordWriterKeeper* getProtoRecordWriterKeeperByIndex(uint32_t index);

private:
    std::string outputRootPath_;
    std::string outputFormat_;
    uint64_t    protoFileSerialNumber_ = 0;

private:
    std::map<std::string, std::unique_ptr<ProtoRecordWriter>> mapping_protoName2RecordWriter_;
    std::map<std::string, std::string> extraPropertyMap_;

private:
    static std::vector<std::unique_ptr<ProtoRecordWriterKeeper>> cs_perThreadProtoRecordWriterKeeper;
};

#endif /* DPI_PRECORD_WRITER_H */
