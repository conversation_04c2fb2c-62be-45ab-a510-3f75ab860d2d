/****************************************************************************************
 * 文 件 名 : wxcs_client_test.c
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019.01.03
* 编    码 : zhengsw      '2019.01.03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <wxcs_def.h>

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <stddef.h>
#include <string.h>

typedef union UN_sevenBytes
{
    uint64_t num;
    uint8_t  bytes[8];
} UN_sevenBytes;

uint64_t hostToNetwork64(uint64_t data)
{
    uint64_t res;
    int i = 0;
    uint8_t *p = (uint8_t *)&res;

    for (i = 0; i<8; i++)
    {
        p[i] = ((uint8_t *)&data)[7-i];
    }

    return res;
}

int randomProto(ST_wxAudioSessionAlive *pProto, int len)
{
    // random offset
    int randomOffset = random() % 25103;

    // random content
    FILE* fp = fopen("/dev/random", "rb");
    fseek(fp, randomOffset, SEEK_SET);
    fread(pProto, 1, len, fp);
    fclose(fp);

    // some field must be reasonable
#if 1
    UN_sevenBytes sv;
    sv.num = hostToNetwork64(8618019268982L);
    memcpy(pProto->lteInfo + 16, &sv.bytes[1], 7); // 仅使用高7字节
#endif

    return 0;
}

int main()
{
    char        buff[1024] = { 0 };
    wxc_handle handle      = 0;
    int        i           = 0;

    srandom(time(NULL));

    wxc_init(&handle, "127.0.0.1", 1024);

    // client network event thread
    ST_wxAudioSessionAlive    *pMsg    = (ST_wxAudioSessionAlive *)(buff);

    for (i = 0; i < 10; i++)
    {
        sleep(3);

        /* random msg */
        randomProto(pMsg, sizeof *pMsg);

        /*  */
        wxc_sendMsg(handle, (char*)pMsg, sizeof(*pMsg), 0);

        printf("send msg out.\n");
    }

    wxc_fini(handle);
}
