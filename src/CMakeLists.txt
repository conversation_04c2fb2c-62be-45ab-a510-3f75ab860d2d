# set(CMAKE_VERBOSE_MAKEFILE on)
set(warn_flags " -Wall -Wextra                  \
                -Wno-missing-field-initializers \
                -Wno-unused-parameter           \
                -Wno-unused-variable            \
                -Wno-unused-function            \
                -Wno-unused-but-set-variable    \
                -Wno-cast-qual")                # 暂不使用 -Waddress-of-packed-member 打包成员地址

if (GCC_VERSION VERSION_GREATER 7)
	  # gcc 版本大于 7.0 的编译参数
	  set(warn_flags "-Wno-format-truncation      \
                    -Wno-address-of-packed-member")
else()
	  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
endif()


set(CMAKE_C_FLAGS "-D_GNU_SOURCE -std=c11 -m64  ${CMAKE_C_FLAGS}  ${warn_flags} -fdiagnostics-color=always")
set(CMAKE_CXX_FLAGS "-D_GNU_SOURCE -m64 ${CMAKE_CXX_FLAGS}  ${warn_flags}")
# set(CMAKE_LINKER_FLAGS "${CMAKE_LINKER_FLAGS}  -fsanitize=address")
# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fsanitize=leak")
# set(ASAN_FLAGS "-fno-omit-frame-pointer -fsanitize=address")
# set(CMAKE_EXE_LINKER_FLAGS_ASAN "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libasan")

set(CMAKE_CXX_STANDARD 11)
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")

find_package(PkgConfig REQUIRED)

pkg_check_modules(YA_PROTO_RECORD REQUIRED IMPORTED_TARGET libyaProtoRecord)
list(APPEND pkg_libaries
  ${YA_PROTO_RECORD_STATIC_LDFLAGS}
)

list(APPEND pkg_include
  ${yaSdxWatch_LIBRARIES}
)

find_library(dpdk_libaries dpdk)

include_directories(
  /usr/local/include/dpdk
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/src/framework
  ${CMAKE_SOURCE_DIR}/src/utils
  ${CMAKE_SOURCE_DIR}/src/input
  ${CMAKE_SOURCE_DIR}/src/codec
  ${CMAKE_SOURCE_DIR}/src/trailer
  ${CMAKE_SOURCE_DIR}/src/proto
  ${CMAKE_SOURCE_DIR}/src/proto/enginew
  ${CMAKE_SOURCE_DIR}/src/output
  ${CMAKE_SOURCE_DIR}/src/sdx
  /usr/include/glib-2.0
  /usr/include
  /usr/lib64/glib-2.0/include
)

add_compile_options("${dpdk_cflags}")

add_subdirectory(framework)
add_subdirectory(utils)
add_subdirectory(proto)
add_subdirectory(output)

add_executable(${AppName}
  dpi_main.c
  $<TARGET_OBJECTS:framework>
  $<TARGET_OBJECTS:utils>
  $<TARGET_OBJECTS:output>
  $<TARGET_OBJECTS:proto>
)

target_link_directories(${AppName} PRIVATE
  ${CMAKE_SOURCE_DIR}/lib
)
option(USE_LUAJIT "use luajit" off)

target_link_libraries(${AppName}
  -Wl,--whole-archive,${dpdk_libaries},--no-whole-archive
  ${pkg_libaries}
  wxc
  -pthread
  pthread
  iniparser
  glib-2.0
  ssl
  crypto
  z
  cjson
  dl
  numa
  pcap
  rt
  protobuf
  yaBasicUtils
)
if(USE_LUAJIT)
  pkg_check_modules(LUA REQUIRED IMPORTED_TARGET luajit)
else()
  pkg_check_modules(LUA REQUIRED IMPORTED_TARGET lua)
endif(USE_LUAJIT)
set_target_properties(${AppName} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)

# 为可执行文件生成软链接
add_custom_command(TARGET ${AppName} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E create_symlink $<TARGET_FILE:${AppName}> ${CMAKE_SOURCE_DIR}/run/${ProName}
)

add_custom_target(vtysh
  COMMAND
    make -C ${CMAKE_SOURCE_DIR}/vtysh
)

message("begin dpi_files = ${dpi_files}")
message("begin dpi_dirs = ${dpi_dirs}")

list(APPEND dpi_files
  ${CMAKE_SOURCE_DIR}/etc/config.ini
  ${CMAKE_SOURCE_DIR}/etc/http_host_uri_qq_list.txt
  ${CMAKE_SOURCE_DIR}/etc/http_host_uri_wx_list.txt
  ${CMAKE_SOURCE_DIR}/etc/http_host_uri_wx_msg_list.txt
  ${CMAKE_SOURCE_DIR}/etc/http_host_wxpay.txt
  ${CMAKE_SOURCE_DIR}/run/ip2region.db
  ${CMAKE_SOURCE_DIR}/changelog.txt
)

list(APPEND dpi_bins
  ${CMAKE_SOURCE_DIR}/run/start.sh
  ${CMAKE_SOURCE_DIR}/run/vtysh
)

# list(APPEND dpi_dirs
#   ${CMAKE_SOURCE_DIR}/run/lua_adapt
# )

file(GLOB LUA_FILES "${CMAKE_SOURCE_DIR}/etc/lua_adapt/*.yalua")

install (
  FILES
    ${LUA_FILES}
  DESTINATION
    ${INSTALL_ROOTDIR}/lua_adapt
)
include(${CMAKE_SOURCE_DIR}/cmake/Install.cmake)
include(${CMAKE_SOURCE_DIR}/cmake/CPack.cmake)
