/****************************************************************************************
 * 文 件 名 : wxcs_content_writer.h
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2019-11-05
* 编    码 : root      '2019-01-05
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#ifndef _WXCS_CONTENT_WRITER_H_
#define _WXCS_CONTENT_WRITER_H_

#include <string>

class WxcsContentWriter
{
public:
    enum WxcsContentType {
        WXCS_CONTENT_JPG,
    };

    std::string getSuffix(WxcsContentType fileType);

    bool writeFile(const char* fileDir, const char* fileBaseName, WxcsContentType fileType, const uint8_t* data, uint32_t len);
};

#endif