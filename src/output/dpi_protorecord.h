#ifndef DPI_PROTORECORD_H
#define DPI_PROTORECORD_H
#include <yaProtoRecord/precord.h>
#include "dpi_common.h"
#include "dpi_log.h"
#include "dpi_tbl_record_log.h"
#include <yaFtypes/fvalue.h>

typedef struct dpi_proto {
  const char* name;
  void (*func)(void);
  struct dpi_proto* next;
} DpiProto;
void dpi_distory_schemaDB(void);
void ya_record_init(void);
int ya_record_register_proto(const char* template, const char* proto_name, const char* proto_full_name);
void dpi_proto_register(const char* proto_name, void (*proto_register)());
int dpi_pschema_register_proto_field(
    const char* proto_name, dpi_record_field_table* field_table, int field_count, int with_common_schema);
int dpi_pschema_get_common_field(dpi_record_field_table* field_table_array[]);

pschema_t* ya_record_get_proto(const char* proto_name);
precord_t* ya_create_record(const char* proto_name);
void ya_destroy_record(precord_t* record);
pfield_t* dpi_precord_fvalue_put_by_name(precord_t* record, const char* field_name, ya_fvalue_t* field_value);

pfield_t* dpi_precord_fvalue_put_by_index(precord_t* record, int fieldIndex, ya_fvalue_t* field_value);
pfield_t* dpi_precord_fvalue_put_by_index_increment(precord_t* record, int* fieldIndex, ya_fvalue_t* field_value);
void printf_record(precord_t *record);
pschema_t * dpi_pschema_get_proto(const char * proto_name);
#endif