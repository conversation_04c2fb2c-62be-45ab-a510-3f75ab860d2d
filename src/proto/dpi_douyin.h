/****************************************************************************************
 * 文 件 名 : dpi_douyin.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 编码: chenzq          2022/10/24
 修改: chenzq          2022/10/24
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2020 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *****************************************************************************************/
#ifndef DPI_DOUYIN_H 
#define DPI_DOUYIN_H

#include <stdint.h>

#include "dpi_detect.h"
#include "dpi_tbl_log.h"


#define   DY_RTP_FLAG_0           0x90
#define   DY_RTP_FLAG_1           0x100000
#define   DY_RTP_FLAG_2           0x0502
#define   DY_RTP_FLAG_3           0x3a0200
#define   DY_RTP_PAYLOAD_LEN_MIN  32            // 最小负载长度
#define   DY_RTP_MORE_PORT_0      80
#define   DY_RTP_MORE_PORT_1      50000
// #define DY_PORT_0 
// #define DY_PORT_1 
// #define DY_PORT_2 
// #define DY_PORT_3 
// #define DY_PORT_4 

#define   DY_STR_NUM_MAX                  128
#define   DY_JSON_MIN_LEN                 64


#define   DY_PORT_0                       9000
#define   DY_FLAG_0_0               0x51007f00            // 3 字节
#define   DY_FLAG_0_1               0x51008000            // 4 字节
#define   DY_FLAG_1                 0x000001000000        // 6 字节


enum dy_index_em{
    EM_DY_DATA,
    EM_DY_MAX
};


// 一帧中提取的 utf8 字符串信息
typedef struct DyUtf8Info
{
  uint8_t num;                        // 提取计数
  uint32_t offset[DY_STR_NUM_MAX];    // 每个 utf8 字符串的起始便宜位置
  uint32_t len[DY_STR_NUM_MAX];       // offset 对应的字符串长度
}DyUtf8Info;


typedef struct DataUtf8
{
  uint8_t *data;
  uint32_t data_len;
}DataUtf8;


typedef struct DyInfo
{
  // char data[10240];
  char data[20000];
  uint32_t data_len;
}DyInfo;

/**
 * @brief 
 *   从 rtp 协议中解析抖音内容
 * 
 * @param flow
 *  flow 流结构体指针，存储流信息
 * @param direction
 * @param seq
 * @param payload
 *   负载内容指针
 * @param payload_len
 *   负载长度
 * @param flag
 * 
 * @return 函数执行结果
 * -  
 */
int dissect_dy_from_rtp(struct flow_info *flow, 
                        int direction, uint32_t seq, 
                        const uint8_t *payload, 
                        const uint32_t payload_len, 
                        uint8_t flag);


/**
 * @brief 
 *   从 rtp 协议中解析抖音内容
 * 
 * @param flow
 *  flow 流结构体指针，存储流信息
 * @param direction
 * @param seq
 * @param payload
 *   负载内容指针
 * @param payload_len
 *   负载长度
 * @param flag
 * 
 * @return 函数执行结果
 * -  
 */
int dissect_dy_from_rtmp(struct flow_info *flow, 
                        int direction, uint32_t seq, 
                        const uint8_t *payload, 
                        const uint32_t payload_len, 
                        uint8_t flag);
/**
 * @brief 
 *   从 rtp 协议中解析抖音内容
 * 
 * @param flow
 *  flow 流结构体指针，存储流信息
 * @param direction
 * @param payload
 *   负载内容指针
 * @param payload_len
 *   负载长度
 * 
 * @return 函数执行结果
 * -  
 */
int dissect_dy_from_http(struct flow_info *flow, int direction, 
                        const uint8_t *payload,  const uint32_t payload_len);

#endif