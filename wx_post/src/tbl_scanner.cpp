#include "tbl_scanner.h"
#include "tbl_utils.h"
#include "tbl_reader.h"
#include "tbl_config.h"
#include "tbl_logger.h"
#include "tbl_statistics.h"

#include <utility>

int TblScanner::init()
{
    tblDirPath_   = CFG.getCurrentConfigValue( {"read_conf", "input_path"    }, std::string("/tmp/tbls") );
    processEnd_   = CFG.getCurrentConfigValue( {"read_conf", "file_suffix"   }, std::string(".tbl")      );
    deleteTbl_    = CFG.getCurrentConfigValue( {"read_conf", "delete_tbl"    }, false                    );
    afterReadEnd_ = CFG.getCurrentConfigValue( {"read_conf", "update_suffix" }, std::string(".done")     );

    return 0;
}


int TblScanner::scan()
{
   
    LOG_DEF->info("work mode:{}",CFG.getWorkMode());
    while (CFG.getWorkMode() > 0)
    {
        for(const auto& it:CFG.protoRecord_)   //只处理配置文件中声明的路径
        {
            //LOG_DEF->info("scan thread work!");
            int rst = forDirEntry((tblDirPath_+it).c_str(), it.c_str(), processEnd_.c_str(), std::bind(&TblScanner::shouldProcessTbl, this, std::placeholders::_1, std::placeholders::_2));
            if(rst < 0)
            {
                LOG_DEF->error("TblScanner scan dir is not exist!");
                sleep(1);
            }
        }
    }
    return 0;
}

int TblScanner::shouldProcessTbl(const char * tbl_name, const char * proto_name)
{
    if(nullptr == proto_name)
    {
        return 0;
    }
    if(nullptr == tbl_name)
    {
        return 0;
    }

    if(!strEndwith(tbl_name, processEnd_))
    {
        return 0;
    }

    std::lock_guard<std::mutex> lock(mtx_);

    FILE * fp = nullptr;

    LOG_DEF->debug("{} is need open.", tbl_name);

    if(tblRecorder_.find(tbl_name) == tblRecorder_.end())
    {
        fp = fopen(tbl_name, "r");

        tblRecorder_.emplace(tbl_name, std::make_pair(tbl_name, fp));
    }
    else
    {
        LOG_DEF->debug("{} has been read.", tbl_name);
        return -1;
    }
    LOG_DEF->error("proto_name {}, tbl_name {}", proto_name, tbl_name);
    
    if(fp != nullptr)
    {
        TblReader::addTblName(proto_name, tbl_name, fp);          //收包队列就一个
        
        LOG_ST.addScan(proto_name);
        
        return 0;
    }
    else
    {
        perror("tbl file open");
        return -1;
    }
}

int TblScanner::shouldDeleteTbl(const char * file_name)
{
    if(nullptr == file_name)
    {
        return -1;
    }
    //std::lock_guard<std::mutex> lock(mtx_);
    

    if(tblRecorder_.find(file_name)!= tblRecorder_.end())
    {
        fclose(tblRecorder_[file_name].second);

        tblRecorder_[file_name].second = nullptr;
        LOG_DEF->debug("{} will erase {}",__FUNCTION__, file_name);
        tblRecorder_.erase(file_name);
    }

    if(deleteTbl_)
    {
        remove(file_name);
    }
    else
    {
        rename(file_name, (file_name+afterReadEnd_).c_str());
    }
    
    return 0;
}

int TblScanner::work()
{
    if(tblScan_t_.joinable())
    {
        LOG_DEF->info(" tbl state:{}", tblRecorder_.size());
        tblScan_t_.join();
    }
    
    return 0;
}