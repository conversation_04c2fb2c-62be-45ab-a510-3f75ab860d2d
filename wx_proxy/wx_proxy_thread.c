/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#define _GNU_SOURCE             /* See feature_test_macros(7) */
#include <sys/sysinfo.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/syscall.h>   /* For SYS_xxx definitions */

#include <sys/types.h>
#include <unistd.h>
#include <pthread.h>

#include <unistd.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>

#include <event2/bufferevent.h>
#include <event2/buffer.h>
#include <event2/listener.h>
#include <glib.h>

#include "wx_proxy_log.h"
#include "wx_proxy_common.h"
#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_thread.h"
#include "wx_proxy_timer.h"

/*****************************************************************
*Function    : accept_push
*Description : push a accept fd to worker thread queue, run in master thread
*Input       : none
*Output      : none
*Return      : [node:OK, NULL:ERROR]
*Others      : none
*****************************************************************/
struct new_client*
accept_push(struct thread_info* thread)
{
    if((thread->queuq_to_read + thread->queuq_size) == thread->queuq_to_write)
    {
        return NULL; // queue is full
    }

    ATOMIC_ADD_FETCH(&thread->queuq_to_write);
    return  &thread->queue[thread->queuq_to_write % thread->queuq_size];
}

/*****************************************************************
*Function    : accept_pop
*Description : pop a accept fd from worker thread queue, run in worker thread
*Input       : none
*Output      : none
*Return      : [node:OK, NULL:ERROR]
*Others      : none
*****************************************************************/
struct new_client*
accept_pop(struct thread_info* thread)
{
    if(thread->queuq_to_write == thread->queuq_to_read)
    {
        return NULL; // queue is empty
    }

    ATOMIC_ADD_FETCH(&thread->queuq_to_read);
    return &thread->queue[thread->queuq_to_read % thread->queuq_size];
}

/*****************************************************************
*Function    : wakeup
*Description : wake worker thread, run in master thread
*Input       : none
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
int
wakeup(struct thread_info* thread)
{
    int wake = 1;
    return write(thread->pair[SOCKETPAIR_MASTER], &wake, sizeof(wake));
}

/*****************************************************************
*Function    : proxy_thread_get_least_conn
*Description : choice a thread from thread pool, run in master thread
*Input       : none
*Output      : none
*Return      : [node:OK, NULL:ERROR]
*Others      : none
*****************************************************************/
struct thread_info*
proxy_thread_get_least_conn(struct context *ctx)
{
    int                   ref;
    int                   min;
    int                   index;
    struct thread_info   *thread;

    ref    = 0;
    min    = 0;
    index  = 0;

    if(ctx->thread_manager->len <= 0)
    {
        return NULL;
    }

    for(int i = 0; i < ctx->thread_manager->len; i++)
    {
        thread  = ((struct thread_info*) ctx->thread_manager->data) + i;
        ref     = thread->ref;

        if(0 == i)
        {
            min = ref;
        }

        if(ref < min)
        {
            min   = ref;
            index = i;
        }
    }

    thread = ((struct thread_info*) ctx->thread_manager->data) + index;
    ATOMIC_ADD_FETCH(&thread->ref);
    return thread;
}

/*****************************************************************
*Function    : proxy_thread_init
*Description : create worker thread, run in master thread
*Input       : thread num, proxy ctx
*Output      : none
*Return      : [node:OK, NULL:ERROR]
*Others      : none
*****************************************************************/
int
proxy_thread_init(int num, struct context *ctx)
{
    GArray               *thread_manager;
    struct thread_info    t;
    struct thread_info   *thread;
    int                   ret;

    thread = &t;

    /**** Get Cores ****/
    ctx->cpus = get_nprocs();

    /**** Create a array  for thread pool ****/
    thread_manager = g_array_new(FALSE, TRUE, sizeof(struct thread_info));
    what(NULL == thread_manager, pexit(-1),  "ERROR init thread_manager");
    ctx->thread_manager = thread_manager;

    /**** Create & init a thread info ****/
    for(int i = 0; i < num; i++)
    {
        memset(thread, 0, sizeof(struct thread_info));

        thread->ctx        = ctx;
        thread->index      = i;
        thread->ctx        = ctx;

        thread->queuq_size = CLIENT_QUEUE_SIZE;
        thread->wakeup     = wakeup;

        /**** init socket pair: master <----> work ****/
        ret           = evutil_socketpair(AF_LOCAL, SOCK_STREAM, 0, thread->pair);
        what(-1 == ret, pexit(-1),  "ERROR init socketpair");

        ret = evutil_make_socket_nonblocking(thread->pair[SOCKETPAIR_MASTER]);
        what(-1 == ret, pexit(-1),  "ERROR init socketpair master");

        ret = evutil_make_socket_nonblocking(thread->pair[SOCKETPAIR_WORKER]);
        what(-1 == ret, pexit(-1),  "ERROR init socketpair work");

        /**** Add this thread to  thread_manager ****/
        g_array_append_vals(thread_manager, thread, 1); // copy value, not pointer!
    }

    // run thread
    for(int i = 0; i < ctx->thread_manager->len; i++)
    {
        thread =  ((struct thread_info*) ctx->thread_manager->data) + i;
        new(&thread->thread, NULL, ctx->create, thread);
        names(thread->thread, "worker_thread");
    }

    return 0;
}

/*****************************************************************
*Function    : proxy_thread_loopexit
*Description : break out event loop
*Input       : proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
proxy_thread_loopexit(struct context *ctx)
{
    struct thread_info   *thread;

    for(int i = 0; i < ctx->thread_manager->len; i++)
    {
        thread =  ((struct thread_info*) ctx->thread_manager->data) + i;
        event_base_loopexit(thread->base, NULL);
    }
}

/*****************************************************************
*Function    : proxy_thread_loopexit
*Description : break out event loop
*Input       : proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
proxy_thread_wait(struct context *ctx)
{
    struct thread_info   *thread;

    for(int i = 0; i < ctx->thread_manager->len; i++)
    {
        thread =  ((struct thread_info*) ctx->thread_manager->data) + i;
        pthread_join(thread->thread, NULL);
    }
    g_array_free(ctx->thread_manager, TRUE);
}

/*****************************************************************
*Function    : wake_readcb
*Description : do accept in worker thread
*Input       : thread
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
static void
wake_readcb(struct bufferevent *bev, void *p)
{
    struct new_client*   client = NULL;
    struct thread_info*  pts    = NULL;

    pts = p;

    while((client = accept_pop(pts)))
    {
        client->accept(client->listener, client->client_fd, client->sa, client->salen, pts);
    }
}

/*****************************************************************
*Function    : wake_eventcb
*Description : do  nothing
*Input       : none
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
static void
wake_eventcb(struct bufferevent *bev, short what, void *p)
{
    log_write(ERROR, "pair panic");
}

/*****************************************************************
*Function    : proxy_thread_entry
*Description : worker thread run
*Input       : thread indo
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void*
proxy_thread_entry(void*  thread_info)
{
    int                     ret;
    struct thread_info     *pts;
    struct bufferevent     *bev_wake;

    pts                   = thread_info;

    log_write(DEBUG, "thread [%d] tid [%zd]", pts->index, syscall(__NR_gettid));

    /**** Every thread need a arrar for session ****/
    pts->session_manager = g_ptr_array_new();
    what(NULL == pts->session_manager, pexit(-1), "ERROR init session_manager");

    /**** Every thrad need a Event_base ****/
    pts->base = event_base_new();
    what(NULL == pts->session_manager, pexit(-1), "ERROR create Event Base");

    /**** add socket pair event ****/
    bev_wake = bufferevent_socket_new(pts->base, pts->pair[SOCKETPAIR_WORKER], BEV_OPT_CLOSE_ON_FREE|BEV_OPT_DEFER_CALLBACKS);
    what(NULL == bev_wake, pexit(-1), "ERROR set wakeup pair");

    bufferevent_setcb  (bev_wake, wake_readcb, NULL, wake_eventcb, pts);
    bufferevent_enable (bev_wake, EV_READ|EV_CLOSED|EV_PERSIST);
    bufferevent_disable(bev_wake, EV_WRITE);
    bufferevent_setwatermark(bev_wake, EV_READ, sizeof(int), 0);  // tigger wakeup

    /**** Thread timer event ****/
    pts->thread_timer = evtimer_new(pts->base, do_thread_timer, pts);
    what(NULL == pts->thread_timer, pexit(-1), "ERROR evtimer_new");

    /**** Thread timer add ****/
    pts->thread_tv.tv_sec   = 5;
    pts->thread_tv.tv_usec  = 0;
    ret = evtimer_add(pts->thread_timer, &pts->thread_tv);
    what(ret < 0, pexit(-1), "ERROR event_add");

    /**** Thread loop ****/
    event_base_dispatch(pts->base);

    evtimer_del(pts->thread_timer);
    event_free (pts->thread_timer);
    event_base_free(pts->base);
    g_ptr_array_unref(pts->session_manager);

    evutil_closesocket(pts->pair[SOCKETPAIR_MASTER]);
    evutil_closesocket(pts->pair[SOCKETPAIR_WORKER]);
    log_write(DEBUG, "fini   thread [%d]", pts->index);
    return 0;
}
