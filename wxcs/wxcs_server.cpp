/****************************************************************************************
 * 文 件 名 : rmap_server.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhengsw      '2019.01.03
* 编    码 : zhengsw      '2019.01.03
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_server.h"
#include "wxcs_utils.h"
#include "wxcs_config.h"
#include "wxcs_dpi_node.h"

#include <signal.h>

#include <muduo/net/EventLoop.h>
#include <muduo/net/EventLoopThread.h>
#include <openssl/md5.h>

#define SESSION_CHECK_INTERVAL_IN_SECONDS   (20)
#define SESSION_ACTIVE_TIME_DIFF_MAX  (90)
#define WXA_HASH_TABLE_SIZE           (5 * 1024 * 1024) // 5M
#define SKYPE_HASH_TABLE_SIZE         (1024 * 1024)
#define WXA_PER_TBL_LINE              (500)
#define WXP_PER_TBL_LINE              (5)
#define QQAV_PER_TBL_LINE             (20)
#define SKYPE_PER_TBL_LINE            (10)
#define MIN_QQ_NUM                    (10000)
#define MAX_QQ_NUM                    (100000000000)

using namespace muduo;
using namespace muduo::net;
using namespace std::placeholders;

std::set<WxcsServer *>         WxcsServer::cs_serverSet;

/*audioSessionKeeper_(CFG->GetValueOf<int>("WXA_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                          CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , */
WxcsServer::WxcsServer(muduo::net::EventLoop *pLoop, uint16_t port, const std::string &strTblDir)
    : wxaTblWriter_(strTblDir, "wxa", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))

    , zoomSessionKeeper_(CFG->GetValueOf<int>("ZOOM_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                              CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , zoomTblWriter_(strTblDir, "zoom", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))

    , groupHeadSessionKeeper_(CFG->GetValueOf<int>("WXGH_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                              CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , wxghTblWriter_(strTblDir, "wxgh", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))

    , QQFileSessionKeeper_(CFG->GetValueOf<int>("QQF_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                              CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , QQFileTblWriter_(strTblDir, "qqfile", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))

    , positionSessionKeeper_(CFG->GetValueOf<int>("WXP_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                             CFG->GetValueOf<int>("WXP_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , wxpTblWriter_(strTblDir, "wxp", CFG->GetValueOf<int>("WXP_TBL_FILE_LINE", WXP_PER_TBL_LINE))

    , qqGroupSessionKeeper_(CFG->GetValueOf<int>("QQAV_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                             CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , qqSingleSessionKeeper_(CFG->GetValueOf<int>("QQAV_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                             CFG->GetValueOf<int>("WXA_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , qqavTblWriter_(strTblDir, "qqav", CFG->GetValueOf<int>("TBL_FILE_LINE_QQ", QQAV_PER_TBL_LINE))

    , skypeSessionKeeper_(CFG->GetValueOf<int>("SKYPE_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                          CFG->GetValueOf<int>("SKYPE_HASH_TABLE_SIZE", SKYPE_HASH_TABLE_SIZE))
    , skypeTblWriter_(strTblDir, "skype", CFG->GetValueOf<int>("SKYPE_TBL_FILE_LINE", SKYPE_PER_TBL_LINE))
    , locSharingTblWrite_(strTblDir, "wxls", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))

    , tencentMeetingSessionKeeper_(CFG->GetValueOf<int>("TENCENT_MEETING_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
                                   CFG->GetValueOf<int>("TENCENT_MEETING_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
    , tencentMeetingTblWriter_(strTblDir, "tencent_meeting", CFG->GetValueOf<int>("TENCENT_MEETING_TBL_FILE_LINE", WXA_PER_TBL_LINE))
    , addr_(port)
    , server_(pLoop, addr_, "wxcserver")
    , pProtoCodec_(NULL)
{
    // protoCodec
    pProtoCodec_ = new WxcsProtoCodec(NULL);
    pProtoCodec_->setMessageCallback(std::bind(&WxcsServer::onMessage, this, _1, _2, _3));

    // set callbacks
    server_.setThreadNum(CFG->GetValueOf<int>("THREAD_COUNT_IN_THREAD_POOL", 4));
    server_.setConnectionCallback(std::bind(&WxcsServer::onConnection, this, _1));
    server_.setMessageCallback(std::bind(&WxcsProtoCodec::onMessage, pProtoCodec_, _1, _2, _3));

    // 加入 server set
    cs_serverSet.insert(this);

    // 把配置文件中的 MSISDN到 监视的 MSISDN
    for (auto msisdn : CFG->getInterestingMsisdn())
    {
        interestingMsisdnSet_.emplace(msisdn);
    }

    // 把配置文件中的 IMSI到 监视的 IMSI
    for (auto imsi : CFG->getInterestingImsi())
    {
        interestingImsiSet_.emplace(imsi);
    }

    // 把配置文件中的 IMEI到 监视的 IMEI
    for (auto imei : CFG->getInterestingImei())
    {
        interestingImeiSet_.emplace(imei);
    }


    // tbl fields file
    std::string field_dir = CFG->GetValueOf<CSTR>("TBL_FIELD_DIR", "/root/program/field/");
    wxaTblWriter_.writeColumnsFile (field_dir, WxcsSession<WxcsAudioPerson>::getColumnList('\n'));
    zoomTblWriter_.writeColumnsFile (field_dir, WxcsSession<WxcsZoomPerson>::getColumnList('\n'));
    wxghTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsGroupHeadPerson>::getColumnList('\n'));
    wxpTblWriter_.writeColumnsFile (field_dir, WxcsSession<WxcsPositionPerson>::getColumnList('\n'));
    qqavTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsQQGroupPerson>::getColumnList('\n'));
    QQFileTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsQQFilePerson>::getColumnList('\n'));
    skypeTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsSkypePerson>::getColumnList('\n'));
    locSharingTblWrite_.writeColumnsFile(field_dir, WxcsSession<WxcsLocSharingPerson>::getColumnList('\n'));

    LOG_DEF->info("I'm listening on port {}" , addr_.toIpPort());
}

WxcsServer::~WxcsServer()
{
    std::lock_guard<std::mutex> lock(connMutex_);

    // auto & audioSessionKeeper_ = WxcsAudioSessionKeeper::GetInstance();

    // 从 set 中移除
    cs_serverSet.erase(this);

    // protoCodec
    delete pProtoCodec_;

    delete timerTh_;

    WXAKS->removeAllSessions(std::bind(&WxcsServer::onRemveAudioSession, this, _1, _2));
    zoomSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemveZoomSession,     this, _1, _2));
    groupHeadSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemveGroupHeadSession, this, _1, _2));
    positionSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemvePersonPosition,   this, _1, _2));
    qqGroupSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemveQQGroupSession,   this, _1, _2));
    qqSingleSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemveQQSingleSession,   this, _1, _2));
    skypeSessionKeeper_.removeAllSessions(std::bind(&WxcsServer::onRemveSkypeChatSession, this, _1, _2));

    LOG_DEF->info("WxcsServer destruct.");
}

int WxcsServer::start()
{
    server_.start();

    // 启动定时器
    timerTh_ = new EventLoopThread(NULL, "timer");
    timerLoop_ = timerTh_->startLoop();
    timerLoop_->runEvery(CFG->GetValueOf<int>("SESSION_CHECK_INTERVAL_IN_SECONDS", SESSION_CHECK_INTERVAL_IN_SECONDS),
                        std::bind(&WxcsServer::checkWxcsTimeout, this));

    return 0;
}

void WxcsServer::onConnection(const TcpConnectionPtr& conn)
{
  std::lock_guard<std::mutex> lock(connMutex_);

  if (conn->connected())
  {
    conn->setTcpNoDelay(true);

    // 加入集合
    connSet_.insert(conn);
    // LOG_DEF->info("new conn of : {}", conn->peerAddress().toIpPort());
  }
  else if (conn->disconnected())
  {
      if (connSet_.empty())
      {
          return ;
      }

      //LOG_DEF->info("disconnected from me : {}", conn->peerAddress().toIpPort());
      connSet_.erase(conn);
  }
}

/* 微信群图像 */
void WxcsServer::ProcessGroupHead(const unsigned char*pdata, int len)
{
    if(sizeof(ST_wxGroupHead) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }
    ST_wxGroupHead* pGroupHead = (ST_wxGroupHead*)pdata;
    PersonPtr<WxcsGroupHeadPerson> aGroupHeadPerson(new WxcsGroupHeadPerson(pGroupHead));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true)
            && aGroupHeadPerson->wasUncrediableId())
    {
        LOG_DEF->debug("drop uncredible person record {}", aGroupHeadPerson->getPrintablePersonMobileID());
        return ;
    }

    bool bInterestingPerson = interestingMsisdnSet_.find(aGroupHeadPerson->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
                        aGroupHeadPerson->getMsisdn(),
                        aGroupHeadPerson->getPrintableSessionID());
    }


    /* 根据  URL HASH_KEY 查找节点 */
    auto pGroup = groupHeadSessionKeeper_.getSessionOf((uint8_t*)pGroupHead->PersonURL, strlen(pGroupHead->PersonURL));
    if (!pGroup) // 没有找到这个微信群
    {
        // 创建微信群
        SessionPtr<WxcsGroupHeadPerson> newSession(new WxcsSession<WxcsGroupHeadPerson>(pGroupHead->PersonURL, strlen(pGroupHead->PersonURL),
                                                         1, pGroupHead->PersonLastActiveTime));
        // 使用群头像名称生成Code
        newSession->generateSessionCode(pGroupHead->GroupHeadPictureName);

        // 将当前这个人 添加到 微信群
        newSession->addNewPerson(aGroupHeadPerson, bInterestingPerson);

        // 将当前的微信群 加入会话管理
        groupHeadSessionKeeper_.addNewSession(newSession);
    }
    else // 找到这个微信群了
    {
        // 那么 当前这个人 是否在所指示的 微信群里呢?
        if (!pGroup->findPerson(aGroupHeadPerson))
        {
            // 该用户不存在，添加新用户
            pGroup->addNewPerson(aGroupHeadPerson, bInterestingPerson);
        }
        else
        {
            // 用户已经存在，更新信息
            pGroup->updatePersonInfo(aGroupHeadPerson, bInterestingPerson);
        }
    }

}


/* ZOOM 会议 */
void WxcsServer::ProcessZoomChat(const unsigned char*pdata, int len)
{
    if(sizeof(ST_ZOOM_person) != len)
    {
        return;
    }

    ST_ZOOM_person *p = (ST_ZOOM_person *)pdata;
    PersonPtr<WxcsZoomPerson> personInfo(new WxcsZoomPerson(p));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        LOG_DEF->debug("drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(),
                        personInfo->getPrintableSessionID());
    }

    // 找会话
    auto s = zoomSessionKeeper_.getSessionOf(p->SID, sizeof(p->SID));
    if (!s)
    {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsZoomPerson> newSession(new WxcsSession<WxcsZoomPerson>((char *)p->SID, sizeof(p->SID)
                    , 0 /* no session type */
                    , p->first));

        newSession->addNewPerson(personInfo, bInterestingPerson);
        zoomSessionKeeper_.addNewSession(newSession);
    }
    else
    //找人
    {
        // session 存在，检查是否存在该用户
         auto pPersonIn = s->findPerson(personInfo);
         if (!pPersonIn)
         {
             // 该用户不存在，添加新用户
             s->addNewPerson(personInfo, bInterestingPerson);
         }
         else
         {
             // 用户已经存在，更新信息
             s->updatePersonInfo(personInfo, bInterestingPerson);
         }
     }
}


/* 微信话单 */
void WxcsServer::ProcessMediaChat(const unsigned char*pdata, int len)
{
    if(sizeof(ST_wxAudioSessionAlive) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }

    ST_wxAudioSessionAlive *pMsgAlive = (ST_wxAudioSessionAlive *)pdata;
    PersonPtr<WxcsAudioPerson> personInfo(new WxcsAudioPerson(pMsgAlive));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        LOG_DEF->debug("drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(),
                        personInfo->getPrintableSessionID());
    }


    // 找会话
    auto pAudioSession = WXAKS->getSessionOf(pMsgAlive->SessionID, sizeof pMsgAlive->SessionID);
    if (!pAudioSession)
    {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsAudioPerson> newSession(new WxcsSession<WxcsAudioPerson>((char *)pMsgAlive->SessionID
                                                , sizeof pMsgAlive->SessionID, pMsgAlive->SessionType
                                                , pMsgAlive->PersonFirstActiveTime));

        newSession->addNewPerson(personInfo, bInterestingPerson);
        WXAKS->addNewSession(newSession);
    }
    else {  // session 存在，检查是否存在该用户
        auto pPersonIn = pAudioSession->findPerson(personInfo);
        if (!pPersonIn)
        {   // 该用户不存在，添加新用户
            pAudioSession->addNewPerson(personInfo, bInterestingPerson);
        } else
        {   // 用户已经存在，更新信息
            pAudioSession->updatePersonInfo(personInfo, bInterestingPerson);
        }

        if (pAudioSession->GetSessionType() != pMsgAlive->SessionType) {
            pAudioSession->SetSessionType(pMsgAlive->SessionType);
        }
    }
}

/* 微信用户分享位置 */
void WxcsServer::ProcessPersonPosition(const unsigned char*pdata, int len)
{
    if (sizeof(ST_wxPosition) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }
    ST_wxPosition* pPosition = (ST_wxPosition*)pdata;
    PersonPtr<WxcsPositionPerson> aPositionPerson(new WxcsPositionPerson(pPosition));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true)
        && aPositionPerson->wasUncrediableId())
    {
        LOG_DEF->debug("drop uncredible person record {}", aPositionPerson->getPrintablePersonMobileID());
        return;
    }

    bool bInterestingPerson = interestingMsisdnSet_.find(aPositionPerson->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
            aPositionPerson->getMsisdn(),
            aPositionPerson->getPrintableSessionID());
    }

    std::string coordinate = aPositionPerson->getLongitude() + aPositionPerson->getLatitude() + aPositionPerson->getZoom();

    /* 根据  位置 HASH_KEY 查找节点 */
    auto pGroup = positionSessionKeeper_.getSessionOf(coordinate);
    if (!pGroup) // 没有找到这个微信会话
    {
        // 创建微信会话
        SessionPtr<WxcsPositionPerson> newSession(new WxcsSession<WxcsPositionPerson>(const_cast<char*>(coordinate.c_str()), (int)coordinate.length()
                                                    , 1, pPosition->PersonLastActiveTime));

        // 将当前这个人 添加到 微信群
        newSession->addNewPerson(aPositionPerson, bInterestingPerson);

        // 将当前的微信群 加入会话管理
        positionSessionKeeper_.addNewSession(newSession);
    }
    else // 找到这个微信群了
    {
        // 那么 当前这个人 是否在所指示的 微信群里呢?
        if (!pGroup->findPerson(aPositionPerson))
        {
            // 该用户不存在，添加新用户
            pGroup->addNewPerson(aPositionPerson, bInterestingPerson);
        }
        else
        {
            // 用户已经存在，更新信息
            pGroup->updatePersonInfo(aPositionPerson, bInterestingPerson);
        }
    }

}

void WxcsServer::ProcessQQGroupChat(const unsigned char *pdata, int len)
{
    if(sizeof(ST_qqGroup) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }

    static uint64_t allowMinQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MIN_NUM", MIN_QQ_NUM);
    static uint64_t allowMaxQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MAX_NUM", MAX_QQ_NUM);

    ST_qqGroup *pQQgroup = (ST_qqGroup *)pdata;
    PersonPtr<WxcsQQGroupPerson> personInfo(new WxcsQQGroupPerson(pQQgroup));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        LOG_DEF->debug("qq group: drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("qq group: found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(),
                        personInfo->getPrintableSessionID());
    }

    // 过滤qq号较小的
    if (pQQgroup->selfQQNum < allowMinQQ || pQQgroup->selfQQNum > allowMaxQQ)
        return;

    // 找会话
    auto pQQGroupSession = qqGroupSessionKeeper_.getSessionOf((uint8_t*)&pQQgroup->groupId, sizeof pQQgroup->groupId);
    if (!pQQGroupSession)
    {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsQQGroupPerson> newSession(new WxcsSession<WxcsQQGroupPerson>((char *)&pQQgroup->groupId, sizeof pQQgroup->groupId
                                                , WXCS_SESSION_NONE, pQQgroup->firstActiveTime));

        newSession->addNewPerson(personInfo, bInterestingPerson);
        qqGroupSessionKeeper_.addNewSession(newSession);
        pQQGroupSession = newSession;
    }
    else
    //找人
     {  // session 存在，检查是否存在该用户
         auto pPersonIn = pQQGroupSession->findPerson(personInfo);
         if (!pPersonIn)
         {   // 该用户不存在，添加新用户
            pQQGroupSession->addNewPerson(personInfo, bInterestingPerson);
         }
         else
         {   // 用户已经存在，更新信息
            if (pPersonIn->selfQQ_ != 0) {
                personInfo->selfQQ_  = pPersonIn->selfQQ_; // personInfo的qq可能为0，这里确保更新时没有把本来已经确定的qq号覆盖
            }
            pQQGroupSession->updatePersonInfo(personInfo, bInterestingPerson);
         }
    }

    // 处理额外的qq
    pQQGroupSession->handleExtraQQList(pQQgroup->selfQQNum, pQQgroup->otherQQNums, (sizeof pQQgroup->otherQQNums/sizeof pQQgroup->otherQQNums[0]) );
}

void WxcsServer::ProcessQQSingleChat(const unsigned char *pdata, int len)
{
    if(sizeof(ST_qqSingle) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }

    static uint64_t allowMinQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MIN_NUM", MIN_QQ_NUM);
    static uint64_t allowMaxQQ = (uint64_t)CFG->GetValueOf<long>("QQ_MAX_NUM", MAX_QQ_NUM);

    ST_qqSingle *pQQSingle = (ST_qqSingle *)pdata;
    PersonPtr<WxcsQQSinglePerson> personInfo(new WxcsQQSinglePerson(pQQSingle));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        LOG_DEF->debug("qq single: drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("qq single: found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(),
                        personInfo->getPrintableSessionID());
    }

    if ((pQQSingle->selfQQNum < allowMinQQ || pQQSingle->otherQQNum < allowMinQQ || pQQSingle->selfQQNum > allowMaxQQ ||
            pQQSingle->otherQQNum > allowMaxQQ) &&
        (personInfo->qqSession_[0] == 0 && personInfo->qqSession_[1] == 0)) {
        return;
    }

    if (pQQSingle->selfQQNum == 0 || pQQSingle->otherQQNum == 0
        || pQQSingle->selfQQNum == pQQSingle->otherQQNum)  // 此时无法确定session id
    {
        /**
         * 判断该person是否已经属于某个session
         *      1 是：更新session中该person的信息
         *      2 否：添加到未确定会话的容器中
         */
        auto pSession = qqSingleSessionKeeper_.findSession(personInfo);
        if (pSession) {
            auto pPerson = pSession->findPerson(personInfo);
            // selfQQNum可能不存在，使用上一次的记录填充，避免operator=()把原来的覆盖
            if (pPerson && pQQSingle->selfQQNum == 0)
                personInfo->selfQQ_ = pPerson->selfQQ_;
            personInfo->setSessionId(pSession->getSessionId());
            pSession->updatePersonInfo(personInfo, bInterestingPerson);
        }
        else {
            // 将未确定会话的人缓存下来
            qqSingleSessionKeeper_.addUnsessionPerson(personInfo);
        }
        if(personInfo->qqSession_[0]!=0 && personInfo->qqSession_[1]!=0)
        {
            // qq1-qq2
            std::string sessionId;
            sessionId.reserve(48);
            if (pQQSingle->qqSession[0] > pQQSingle->qqSession[1]) {
                sessionId += std::to_string(pQQSingle->qqSession[0]);
                sessionId += "-";
                sessionId += std::to_string(pQQSingle->qqSession[1]);
            } else {
                sessionId += std::to_string(pQQSingle->qqSession[1]);
                sessionId += "-";
                sessionId += std::to_string(pQQSingle->qqSession[0]);
            }

            personInfo->setSessionId(sessionId);
            // 找会话
            auto pQQSingleSession = qqSingleSessionKeeper_.getSessionOf(sessionId);

            if (personInfo->isTimeout_) {
                // 会话存在，立即超时
                if (pQQSingleSession) {
                  qqSingleSessionKeeper_.removeOneSession(pQQSingleSession);
                  timerLoop_->runInLoop([pQQSingleSession, this]() { this->onRemveQQSingleSession(0, pQQSingleSession); });
                }

                // 会话不存在时不在处理，因为1对1通话存在2个人，若上个人的isTimeout_为真
                // 则会话已经超时过了，第二个人再超时则不必再处理

                return;
            }

            if (!pQQSingleSession) {  // 添加新 session,向该 session 中添加用户
                SessionPtr<WxcsQQSinglePerson> newSession(new WxcsSession<WxcsQQSinglePerson>(
                    (char *)sessionId.data(), sessionId.size(), pQQSingle->sessionType, pQQSingle->firstActiveTime));
                newSession->setOtherQQNum(pQQSingle->qqSession[1]);
                newSession->addNewPerson(personInfo, bInterestingPerson);
                qqSingleSessionKeeper_.addAuxiliaryPerson(personInfo, newSession);
                pQQSingleSession = newSession;
                qqSingleSessionKeeper_.addNewSession(pQQSingleSession);
            } else
            //找人
            {  // session 存在，检查是否存在该用户
                auto pPersonIn = pQQSingleSession->findPerson(personInfo);
                if (!pPersonIn) {  // 该用户不存在，添加新用户
                  pQQSingleSession->addNewPerson(personInfo, bInterestingPerson);
                  qqSingleSessionKeeper_.addAuxiliaryPerson(personInfo, pQQSingleSession);
                } else {  // 用户已经存在，更新信息
                  pQQSingleSession->updatePersonInfo(personInfo, bInterestingPerson);
                }
            }
            pQQSingleSession->setonly48(1);
            if (pQQSingleSession->getPersonCount() < 2) {
                // 从缓存的人中找到属于该会话的并添加到该会话中
                PersonPtr<WxcsQQSinglePerson> otherPerson = qqSingleSessionKeeper_.getUnsessionPerson(pQQSingle->qqSession[1]);
                if (otherPerson) {
                  otherPerson->setPublicIP(pQQSingle->pubSrcIP);
                  pQQSingleSession->updateStartTime(otherPerson->startTime_);
                  pQQSingleSession->addNewPerson(otherPerson, false);
                  qqSingleSessionKeeper_.addAuxiliaryPerson(otherPerson, pQQSingleSession);
                }
            }
        }
    }
    else {
        // qq1-qq2
        std::string sessionId;
        sessionId.reserve(48);
        if (pQQSingle->selfQQNum > pQQSingle->otherQQNum) {
            sessionId += std::to_string(pQQSingle->selfQQNum);
            sessionId += "-";
            sessionId += std::to_string(pQQSingle->otherQQNum);
        }
        else {
            sessionId += std::to_string(pQQSingle->otherQQNum);
            sessionId += "-";
            sessionId += std::to_string(pQQSingle->selfQQNum);
        }

        personInfo->setSessionId(sessionId);

        // 找会话
        auto pQQSingleSession = qqSingleSessionKeeper_.getSessionOf(sessionId);

        // 对于1对1通话，该字段严格只表示当前这个人所处的上一次通话已经超时，
        // 即这个人重新打了第二通电话
        if (personInfo->isTimeout_) {
            // 会话存在，立即超时
            if (pQQSingleSession) {
                qqSingleSessionKeeper_.removeOneSession(pQQSingleSession);
                timerLoop_->runInLoop([pQQSingleSession, this]() {
                    this->onRemveQQSingleSession(0, pQQSingleSession);
                });
            }

            // 会话不存在时不在处理，因为1对1通话存在2个人，若上个人的isTimeout_为真
            // 则会话已经超时过了，第二个人再超时则不必再处理

            return;
        }

        if (!pQQSingleSession)
        { // 添加新 session,向该 session 中添加用户
            SessionPtr<WxcsQQSinglePerson> newSession(new WxcsSession<WxcsQQSinglePerson>((char*)sessionId.data(), sessionId.size()
                                                      , WXCS_SESSION_NONE, pQQSingle->firstActiveTime));
            newSession->setOtherQQNum(pQQSingle->otherQQNum);
            newSession->setOtherQQPubIP(pQQSingle->pubSrcIP);
            newSession->addNewPerson(personInfo, bInterestingPerson);
            qqSingleSessionKeeper_.addAuxiliaryPerson(personInfo, newSession);
            pQQSingleSession = newSession;
            qqSingleSessionKeeper_.addNewSession(pQQSingleSession);
        }
        else
        //找人
        { // session 存在，检查是否存在该用户
            auto pPersonIn = pQQSingleSession->findPerson(personInfo);
            if (!pPersonIn)
            { // 该用户不存在，添加新用户
                pQQSingleSession->addNewPerson(personInfo, bInterestingPerson);
                qqSingleSessionKeeper_.addAuxiliaryPerson(personInfo, pQQSingleSession);
            }
            else
            { // 用户已经存在，更新信息
                pQQSingleSession->updatePersonInfo(personInfo, bInterestingPerson);
            }
        }
        pQQSingleSession->setonly48(0);

        for (auto kv : pQQSingleSession->personMap_) {
            if (kv.second->selfQQ_ == pQQSingle->otherQQNum) {
                kv.second->setPublicIP(pQQSingle->pubSrcIP);
            }
        }

        if (pQQSingleSession->getPersonCount() < 2) {
            // 从缓存的人中找到属于该会话的并添加到该会话中
            PersonPtr<WxcsQQSinglePerson> otherPerson = qqSingleSessionKeeper_.getUnsessionPerson(pQQSingle->otherQQNum);
            if (otherPerson) {
                otherPerson->setPublicIP(pQQSingle->pubSrcIP);
                pQQSingleSession->updateStartTime(otherPerson->startTime_);
                pQQSingleSession->addNewPerson(otherPerson, false);
                qqSingleSessionKeeper_.addAuxiliaryPerson(otherPerson, pQQSingleSession);
            }
        }
    }
}

void WxcsServer::ProcessQQFile(const unsigned char*pdata, int len)
{
    if(NULL == pdata || sizeof(ST_QQ_File_MSG) != len)
    {
        return;
    }

    ST_QQ_File_MSG *p = (ST_QQ_File_MSG *)pdata;

    size_t QQNum = 0;
    unsigned int weight = 0;
    unsigned int lastTime = 0;
    size_t msisdn = 0;
    if (p->trailer.MSISDN <= 0)
    {
        msisdn = p->trailer.IMSI;
    }
    else
    {
        msisdn = p->trailer.MSISDN;
    }

    PersonPtr<WxcsQQFilePerson> personInfo(new WxcsQQFilePerson(p));
    /*ADD_S by yangna 2020-08-05 */
    if (personInfo->getQQNum() <= 0)
    {
        int ret = qqEventMap_.getQQByMsisdnWithWeight(msisdn, QQNum, weight, lastTime);
        if (ret != 0)
        {
            // LOG_INTST->debug("getQQByMsisdnWithWeight failed");
            personInfo->setQQNumFrom("EmptyInQQEventProtocol");
        }
        else
        {
            if (QQNum <= 0)
            {
                personInfo->setQQNumFrom("EmptyInQQEventProtocol");
            }
            else
            {
                personInfo->setQQNum(QQNum);
                personInfo->setQQNumFrom("QQEventProtocol");
            }
        }
    }
    else
    {
            personInfo->setQQNumFrom("QQFileProtocol ");
    }
    if (personInfo->get_tblPushFlg() == 0)
    {
        /*推送TBL数据的话保留字段为空，不让用户看到QQ号码来源 */
        personInfo->setQQNumFrom("");
    }
    /*ADD_E by yangna 2020-08-05 */

    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        return ;
    }

    bool b = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    auto Session = QQFileSessionKeeper_.getSessionOf((uint8_t*)p->FileEncode, strlen(p->FileEncode));
    if (!Session)
    {
        SessionPtr<WxcsQQFilePerson> newSession(new WxcsSession<WxcsQQFilePerson>(
                    (char *)p->FileEncode, strlen(p->FileEncode)
                    , p->isGroup
                    , p->LastActiveTime) );
        newSession->addNewPerson(personInfo, b);

        newSession->set_ispictupe(personInfo->get_ispicture());
        newSession->set_pictupe_w(personInfo->get_picture_w());
        newSession->set_pictupe_h(personInfo->get_picture_h());
        QQFileSessionKeeper_.addNewSession(newSession);
    }
    else
    {
        auto person = Session->findPerson(personInfo);
        if (!person)
        {
            Session->addNewPerson(personInfo, b);
        }
        else
        {
            Session->updatePersonInfo(personInfo, b);
        }
        //缩略图 变 大图
        Session->set_ispictupe(personInfo->get_ispicture());
        Session->set_pictupe_w(personInfo->get_picture_w());
        Session->set_pictupe_h(personInfo->get_picture_h());
    }

}

void WxcsServer::ProcessWXGroupHeadData(const unsigned char*pdata, int len)
{
    if(sizeof(ST_wxGroupHeadPicData) >= len) {
       return;
    }

    ST_wxGroupHeadPicData* pHeadData = (ST_wxGroupHeadPicData*)pdata;
    if (strlen(pHeadData->SessionUrl) == 0 || strlen(pHeadData->GroupHeadPictureName) == 0) {
        LOG_DEF->warn("bad group head picture data");
        return;
    }

    auto pGroup = groupHeadSessionKeeper_.getSessionOf((uint8_t*)pHeadData->SessionUrl, strlen(pHeadData->SessionUrl));
    if (!pGroup) {
        LOG_DEF->debug("no group head session, sessionId={}", pHeadData->SessionUrl);
        return;
    }

    static std::string imgDir = CFG->GetValueOf<const char*>("WX_GROUP_HEAD_IMG_DIR", "");
    if (imgDir.empty()) {
        LOG_DEF->warn("group head img dir is not config");
        return;
    }

    // 设置群头像
    std::string groupPicBaseName = std::string(pHeadData->GroupHeadPictureName) + "_" + wxghTblWriter_.getFileFormatCode(pGroup->getSessionCode());
    pGroup->setGroupPictureName(imgDir + "/" + groupPicBaseName + contentWriter_.getSuffix(WxcsContentWriter::WXCS_CONTENT_JPG));

    // 计算MD5
    if (pHeadData->PictureDataLen > 0 && (pHeadData->PictureDataLen + sizeof(ST_wxGroupHeadPicData) <= len)) {
        // 设置群头像的md5值
        if (!pGroup->hasMd5()) {
            uint8_t digest[MD5_DIGEST_LENGTH] = {0};
            MD5(pHeadData->PictureData, pHeadData->PictureDataLen, digest);
            char md5Str[MD5_DIGEST_LENGTH*2+1] = {0};
            for(int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
                sprintf(&md5Str[i*2], "%02x", digest[i]);
            }
            pGroup->setGroupPictureNameMd5(md5Str);
        }

        // 将群头像写入文件
        bool ret = contentWriter_.writeFile(imgDir.c_str(), groupPicBaseName.c_str(), WxcsContentWriter::WXCS_CONTENT_JPG
                                            , pHeadData->PictureData, pHeadData->PictureDataLen);
        if (!ret) {
            LOG_DEF->debug("failed to write group head picture file");
        }
    }
    else {
        LOG_DEF->debug("group head picture data is invalid");
    }
}

void WxcsServer::ProcessSkypeMediaChat(const unsigned char*pdata, int len)
{
    if(sizeof(ST_SkypeMediaSessionAlive) != len)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }

    ST_SkypeMediaSessionAlive *pMsgAlive = (ST_SkypeMediaSessionAlive *)pdata;
    PersonPtr<WxcsSkypePerson> personInfo(new WxcsSkypePerson(pMsgAlive));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId())
    {
        LOG_DEF->debug("drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(personInfo->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(),
                        personInfo->getPrintableSessionID());
    }


    // 找会话
    auto pSkypeSession = skypeSessionKeeper_.getSessionOf(pMsgAlive->SessionID, sizeof pMsgAlive->SessionID);
    if (!pSkypeSession)
    {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsSkypePerson> newSession(new WxcsSession<WxcsSkypePerson>((char *)pMsgAlive->SessionID
                                                , sizeof pMsgAlive->SessionID, pMsgAlive->SessionType
                                                , pMsgAlive->PersonFirstActiveTime));

        newSession->addNewPerson(personInfo, bInterestingPerson);
        skypeSessionKeeper_.addNewSession(newSession);
    }
    else
    //找人
    {  // session 存在，检查是否存在该用户
        auto pPersonIn = pSkypeSession->findPerson(personInfo);
        if (!pPersonIn)
        {   // 该用户不存在，添加新用户
            pSkypeSession->addNewPerson(personInfo, bInterestingPerson);
        }
        else
        {   // 用户已经存在，更新信息
            pSkypeSession->updatePersonInfo(personInfo, bInterestingPerson);
        }
    }
}

void WxcsServer::ProcessWXRelation(const unsigned char*pdata, int len _U_)
{
    ST_WXRELA *wx_rela = (ST_WXRELA *)pdata;

    WxrelaPerInfo info;
    memset(&info, 0, sizeof(info));

    info.timestamp = wx_rela->trailer.TS;
    memcpy(info.wxid, wx_rela->wxid, WXRELA_STR_LEN);
    memcpy(info.uin, wx_rela->uin, WXRELA_STR_LEN);

    char clientIP_[64] = {0};
    int af = wx_rela->ip_version == 4 ? AF_INET : AF_INET6;
    inet_ntop(af, wx_rela->client_ip.ipv6, clientIP_, 64);
    std::string clientIP = clientIP_;

    wx_ios_rela_.orderInsert(clientIP, info);

}

void WxcsServer::ProcessQQEvent(const unsigned char*pdata, int len)
{

    if(sizeof(ST_QQEventAlive) != len
        || NULL == pdata)
    {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }
    ST_QQEventAlive *pQQEvent = (ST_QQEventAlive *)pdata;

    /*当手机号码为空时就用client_ip作为map中的KEY*/
    if (pQQEvent->trailer.MSISDN <= 0)
    {
        pQQEvent->trailer.MSISDN = pQQEvent->trailer.IMSI;
    }

    qqEventMap_.inserQQEventData(pQQEvent);
    return;
}

void WxcsServer::ProcessTencentMeeting(const unsigned char*pdata, int len)
{
    if (len != sizeof(ST_TecentMeeting))
    {
        LOG_INTST->error("ProcessTencentMeeting len error, len={}, sizeof(ST_TecentMeeting)={}", len, sizeof(ST_TecentMeeting));
        return;
    }

    ST_TecentMeeting *pMeetingMsg = (ST_TecentMeeting *)pdata;

    // 创建腾讯会议Person对象
    PersonPtr<WxcsTencentMeetingPerson> pPerson = std::make_shared<WxcsTencentMeetingPerson>(pMeetingMsg);

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && pPerson->wasUncrediableId())
    {
        LOG_DEF->debug("tencent meeting: drop uncredible person record {}", pPerson->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestingMsisdnSet_.find(pPerson->getMsisdn()) != interestingMsisdnSet_.end();
    if (bInterestingPerson)
    {
        LOG_INTST->warn("tencent meeting: found interesting person msg : {} of session {}",
                        pPerson->getMsisdn(),
                        pPerson->getPrintableSessionID());
    }

    // 查找或创建session（使用sessionId作为session标识）
    auto pTencentMeetingSession = tencentMeetingSessionKeeper_.getSessionOf((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId));
    if (!pTencentMeetingSession)
    {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsTencentMeetingPerson> newSession(new WxcsSession<WxcsTencentMeetingPerson>((char *)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId)
                                                , WXCS_SESSION_NONE, pMeetingMsg->firstActiveTime));

        newSession->addNewPerson(pPerson, bInterestingPerson);
        tencentMeetingSessionKeeper_.addNewSession(newSession);
        pTencentMeetingSession = newSession;
    }
    else
    {   // session 存在，检查是否存在该用户
        auto pPersonIn = pTencentMeetingSession->findPerson(pPerson);
        if (!pPersonIn)
        {   // 该用户不存在，添加新用户
            pTencentMeetingSession->addNewPerson(pPerson, bInterestingPerson);
        }
        else
        {   // 该用户存在，更新用户信息
            pTencentMeetingSession->updatePersonInfo(pPerson, bInterestingPerson);
        }
    }

    LOG_INTST->debug("ProcessTencentMeeting: sessionId={}, selfMeetingNum={}, selfLoginNum={}, msisdn={}",
                     pMeetingMsg->sessionId, pMeetingMsg->selfMeetingNum, pMeetingMsg->selfLoginNum,
                     pMeetingMsg->trailer.MSISDN);
}

/* 消息类型 分发器  */
void WxcsServer::onMessage(const TcpConnectionPtr& conn _U_,
                           const std::string& message,
                           Timestamp)
{
    const ST_wxcsProtoHdr  *pProtoHdr = reinterpret_cast<const ST_wxcsProtoHdr *>(message.data());
    const unsigned char* pdata = pProtoHdr->payload;
    int len = pProtoHdr->msgLen - sizeof(ST_wxcsProtoHdr);

    ST_trailer *trailer = (ST_trailer *)pdata;
    set_node_timestamp(trailer->DPI_Node_id, pProtoHdr->msgType, trailer->TS);
    set_node_name(trailer->DPI_Node_id, trailer->Area, trailer->DevName);

    switch(pProtoHdr->msgType)
    {
        case WXCS_MEDIA_CHAT:
            ProcessMediaChat(pdata, len);
            break;

        case WXCS_ZOOM_CHAT:
            ProcessZoomChat(pdata, len);
            break;

        case WXCS_GROUP_HEAD:
            ProcessGroupHead(pdata, len);
            break;

        case WXCS_POSITION:
            ProcessPersonPosition(pdata, len);
            break;

        case WXCS_QQ_GROUP:
            ProcessQQGroupChat(pdata, len);
            break;

        case WXCS_QQ_SINGLE:
            ProcessQQSingleChat(pdata, len);
            break;

        case WXCS_QQ_FILE:
            ProcessQQFile(pdata, len);
            break;

        case WXCS_WX_GROUP_HEAD_DATA:
            ProcessWXGroupHeadData(pdata, len);
            break;

        case WXCS_SKYPE_MEDIA_CHAT:
            ProcessSkypeMediaChat(pdata, len);
            break;

        case WXCS_WX_RELATION:
            ProcessWXRelation(pdata, len);
            break;

        case WXCS_QQ_EVENT:
            ProcessQQEvent(pdata, len);
            break;

        case WXCS_WX_PEERS:
            // LOG_DEF->debug("===========> get a3 message!!!");
            // ProcessWXPeer(pdata, len);
            WXAKS->ProcessWXPeer(pdata, len);
            break;

        case WXCS_WX_LOC_SHARING:
            WXLSKS->ProcessWXLS(pdata, len);
            break;

        case WXCS_TENCENT_MEETING:
            ProcessTencentMeeting(pdata, len);
            break;

        default:
            return;
    }
}

/*ADD_S by yangna 2020-09-16 */
/*TBL写文件超时检测 */
void WxcsServer::checkTblWriteTimeout()
{
    /*轮询每个消息的每个写TBL线程，超过每个TBL文件条数上限或者TBL最新写文件时间到当前时间间隔超过写时间关闭文件 */
    timerLoop_->runAfter(0, std::bind(&WxcsServer::WxaTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::ZoomTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::GroupHeadTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::QQFileTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::PositionTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::QQVoipTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::SkypeTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::LocSharingTblWriteTimeout, this));
    timerLoop_->runAfter(0, std::bind(&WxcsServer::TencentMeetingTblWriteTimeout, this));
}


/*微信话单写TBL文件超时检测 */
void WxcsServer::WxaTblWriteTimeout()
{
    tblWriteTimeout(zoomTblWriter_);
}
/*ZOOM会议写TBL文件超时检测 */
void WxcsServer::ZoomTblWriteTimeout()
{
    tblWriteTimeout(wxaTblWriter_);
}
/*微信群图像写TBL文件超时检测 */
void WxcsServer::GroupHeadTblWriteTimeout()
{
    tblWriteTimeout(wxghTblWriter_);
}
/*QQ文件写TBL文件超时检测 */
void WxcsServer::QQFileTblWriteTimeout()
{
    tblWriteTimeout(QQFileTblWriter_);
}
/*微信分享位置写TBL文件超时检测 */
void WxcsServer::PositionTblWriteTimeout()
{
    tblWriteTimeout(wxpTblWriter_);
}
/*QQ话单写TBL文件超时检测 */
void WxcsServer::QQVoipTblWriteTimeout()
{
    tblWriteTimeout(qqavTblWriter_);
}
/*SKYPE话单写TBL文件超时检测 */
void WxcsServer::SkypeTblWriteTimeout()
{
    tblWriteTimeout(skypeTblWriter_);
}
void WxcsServer::LocSharingTblWriteTimeout()
{
    tblWriteTimeout(locSharingTblWrite_);
}
/*腾讯会议写TBL文件超时检测 */
void WxcsServer::TencentMeetingTblWriteTimeout()
{
    tblWriteTimeout(tencentMeetingTblWriter_);
}
/*写TBL超时检测通用方法 */
void WxcsServer::tblWriteTimeout(wxcsTblWriter& tblWriter)
{
    /*超过每个文件写条数上限或者超过最大写时间，关闭文件 */
    for (size_t i = 0; i < tblWriter.tblFileInfoVec_.size(); i++)
    {
        if (tblWriter.tblFileInfoVec_[i].tblFilePtr
            && tblWriter.tblFileInfoVec_[i].tblWritenCnts >= tblWriter.lRecordCntPerFile_ )
        {
            tblWriter.writeCurrentFileDone(i);
        }
        else if (tblWriter.tblFileInfoVec_[i].tblFilePtr
                && time(NULL) - tblWriter.tblFileInfoVec_[i].tblFileLastWriteTime >= tblWriter.tblFileMaxWritingTime)
        {
            tblWriter.writeCurrentFileDone(i);
        }
    }
}
/*wxcs整体超时检查 */
void WxcsServer::checkWxcsTimeout()
{
    /*会话超时检查 */
    this->checkSessionTimeout();
    /*写tbl超时检查 */
    this->checkTblWriteTimeout();
}
/*ADD_E by yangna 2020-09-16 */


// 超时检测
void WxcsServer::checkSessionTimeout()
{
    if (!timerTh_ || !timerLoop_)
        return;

    const int checkInterval = 2; // 2s
    timerLoop_->runAfter(0 * checkInterval, std::bind(&WxcsServer::checkWXASessionTimeout, this));
    timerLoop_->runAfter(0 * checkInterval, std::bind(&WxcsServer::checkZOOMSessionTimeout, this));
    timerLoop_->runAfter(1 * checkInterval, std::bind(&WxcsServer::checkWXGHSessionTimeout, this));
    timerLoop_->runAfter(2 * checkInterval, std::bind(&WxcsServer::checkWXPSessionTimeout, this));
    timerLoop_->runAfter(3 * checkInterval, std::bind(&WxcsServer::checkQQGSessionTimeout, this));
    timerLoop_->runAfter(4 * checkInterval, std::bind(&WxcsServer::checkQQSSessionTimeout, this));
    timerLoop_->runAfter(5 * checkInterval, std::bind(&WxcsServer::checkQQFileSessionTimeout, this));
    timerLoop_->runAfter(6 * checkInterval, std::bind(&WxcsServer::checkSkypeChatSessionTimeout, this));
    timerLoop_->runAfter(0 * checkInterval, std::bind(&WxcsServer::CheckLocSharingSessionTimeout, this));
    timerLoop_->runAfter(7 * checkInterval, std::bind(&WxcsServer::checkTencentMeetingSessionTimeout, this));
}

void WxcsServer:: checkWXASessionTimeout()
{
    WXAKS->removeDeadSessions(std::bind(&WxcsServer::onRemveAudioSession,this, _1, _2));
}
void WxcsServer:: checkZOOMSessionTimeout()
{
    zoomSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemveZoomSession,this, _1, _2));
}
void WxcsServer:: checkWXGHSessionTimeout()
{
    groupHeadSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemveGroupHeadSession,this, _1, _2));
}
void WxcsServer:: checkWXPSessionTimeout()
{
    positionSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemvePersonPosition,this, _1, _2));
}
void WxcsServer:: checkQQGSessionTimeout()
{
    qqGroupSessionKeeper_.removeDeadSessions (std::bind(&WxcsServer::onRemveQQGroupSession,   this, _1, _2));
}
void WxcsServer:: checkQQSSessionTimeout()
{
    qqSingleSessionKeeper_.removeDeadSessions (std::bind(&WxcsServer::onRemveQQSingleSession,   this, _1, _2));
}
void WxcsServer:: checkQQFileSessionTimeout()
{
    QQFileSessionKeeper_.removeDeadSessions (std::bind(&WxcsServer::onRemveQQFileSession,   this, _1, _2));
}

void WxcsServer:: checkSkypeChatSessionTimeout()
{
    skypeSessionKeeper_.removeDeadSessions (std::bind(&WxcsServer::onRemveSkypeChatSession, this, _1, _2));
}

void WxcsServer::CheckLocSharingSessionTimeout()
{
    WXLSKS->removeDeadSessions(std::bind(&WxcsServer::onRemveLocSharingSession, this, _1, _2));
}

void WxcsServer::checkTencentMeetingSessionTimeout()
{
    tencentMeetingSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemveTencentMeetingSession, this, _1, _2));
}

int WxcsServer::onRemveGroupHeadSession(uint32_t time _U_, const SessionPtr<WxcsGroupHeadPerson> & session)
{
    wxghTblWriter_.writeToFile(session);
    return 0;
}

int WxcsServer::onRemvePersonPosition(uint32_t time _U_, const SessionPtr<WxcsPositionPerson>& session)
{
    wxpTblWriter_.writeToFile(session);
    return 0;
}

int WxcsServer::onRemveQQGroupSession(uint32_t time _U_, const SessionPtr<WxcsQQGroupPerson> &session)
{
    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N个报文)
    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
    {
        //判断报文个数
        static uint32_t minCnt = CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20);
        if(it->second->c2sPackCount_ < minCnt ||
           it->second->s2cPackCount_ < minCnt )
        {
            LOG_DEF->debug("qq group session: {}, invalid packet count, c2s: {}, s2c: {}", session->getSessionId(),
                    it->second->c2sPackCount_, it->second->s2cPackCount_);
            it = const_cast<SessionPtr<WxcsQQGroupPerson> &>(session)->personMap_.erase(it);
        }
        else // 这是一个正常的报文
        {
            it++;
        }
    }

    // 有人吗 ?
    if(session->personMap_.empty())
    {
        return 0;
    }

    //LOG_DEF->debug("WxcsServer::onRemveQQGroupSession(), sessionId={}", session.getPrintableSessionId());
    qqavTblWriter_.writeToFile(session);
    return 0;
}

int WxcsServer::onRemveQQSingleSession(uint32_t time _U_, const SessionPtr<WxcsQQSinglePerson> &session)
{
    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N个报文)
    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
    {
        // 删除辅助数据
        qqSingleSessionKeeper_.removeAuxiliaryPerson(it->second);

        //判断报文个数
        static uint32_t minCnt = CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20);
        if(it->second->c2sPackCount_ < minCnt ||
           it->second->s2cPackCount_ < minCnt )
        {
            LOG_DEF->debug("qq single session: {}, invalid packet count, c2s: {}, s2c: {}", session->getSessionId(),
                    it->second->c2sPackCount_, it->second->s2cPackCount_);
            it = const_cast<SessionPtr<WxcsQQSinglePerson> &>(session)->personMap_.erase(it);
        }
        else // 这是一个正常的报文
        {
            it++;
        }
    }

    // 有人吗 ?
    if(session->personMap_.empty())
    {
        return 0;
    }

    //LOG_DEF->debug("WxcsServer::onRemveQQSingleSession(), sessionId={}", session.getSessionId());
    qqavTblWriter_.writeToFile(session);
    return 0;
}


// ZOOM 超时检测
int WxcsServer::onRemveZoomSession(uint32_t time _U_, SessionPtr<WxcsZoomPerson> & session)
{
    // 剩下的都是 正常的Person 写入tbl
    zoomTblWriter_.writeToFile(session);
    return 0;
}


// 话单超时检测
int WxcsServer::onRemveAudioSession(uint32_t time _U_, SessionPtr<WxcsAudioPerson> & session)
{
    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N 个报文)
    for (auto it = session->personMap_.begin(); it != session->personMap_.end(); ++it)
    {
        /* 更新 uin wxid字段 仅操作 一对一情况*/
        {
            if (session->GetSessionType()  == 0) {
                std::string clientIP = it->second->getClientIP();
                uint32_t timeStamp = it->second->getLastActiveTime();
                auto findPtr = wx_ios_rela_.findNearestPer(clientIP, timeStamp);
                if (findPtr != nullptr) {
                    it->second->SetWxId(findPtr->wxid);
                    it->second->SetWxUin(findPtr->uin);
                }
            }
        }
    }

    // 有人吗 ?
    if(session->personMap_.empty())
    {
        return 0;
    }

    session->interestingmsisdn_     = interestingMsisdnSet_;
    session->interestingimsi_       = interestingImsiSet_;
    session->interestingimei_       = interestingImeiSet_;

    session->OnRemove();

    if (session->CheckOutput()) {
        // 剩下的都是 正常的Person 写入tbl
        wxaTblWriter_.writeToFile(session);
    }

    return 0;
}

int WxcsServer::onRemveQQFileSession(uint32_t time _U_, const SessionPtr<WxcsQQFilePerson> & session)
{
    QQFileTblWriter_.writeToFile(session);
    return 0;
}

int WxcsServer::onRemveSkypeChatSession(uint32_t time _U_, SessionPtr<WxcsSkypePerson> & session)
{
    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N 个报文)
    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
    {
        //判断报文个数
        if(it->second->getC2STransPackets() < CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20) ||
           it->second->getS2CTransPackets() < CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20) )
        {
            //日志记录死亡原因
            bool PersonMSISDN = interestingMsisdnSet_.find(it->second->getMsisdn()) != interestingMsisdnSet_.end();
            bool PersonIMSI   = interestingImsiSet_.find(  it->second->getImsi())   != interestingImsiSet_.end();
            bool PersonIMEI   = interestingImeiSet_.find(  it->second->getImei())   != interestingImeiSet_.end();

            if (PersonMSISDN) // 日志优先级第1, 显示MSISDN
            {
                LOG_INTST->warn("[tbl_DROP_MSISDN:C2S{},S2C{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                        it->second->getC2STransPackets(),
                        it->second->getS2CTransPackets(),
                        it->second->getMsisdn(),
                        it->second->getImsi(),
                        it->second->getImei(),
                        it->second->getDevName(),
                        it->second->getOperator(),
                        it->second->getPrintableSessionID());
            }
            else
            if(PersonIMSI) // 日志优先级第2, 显示IMSI
            {
                LOG_INTST->warn("[tbl_DROP_IMSI:C2S{},S2C{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                        it->second->getC2STransPackets(),
                        it->second->getS2CTransPackets(),
                        it->second->getMsisdn(),
                        it->second->getImsi(),
                        it->second->getImei(),
                        it->second->getDevName(),
                        it->second->getOperator(),
                        it->second->getPrintableSessionID());
            }
            else
            if(PersonIMEI) // 日志优先级第3, 显示IMEI
            {
                LOG_INTST->warn("[tbl_DROP_IMEI:C2S{},S2C{}] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                        it->second->getC2STransPackets(),
                        it->second->getS2CTransPackets(),
                        it->second->getMsisdn(),
                        it->second->getImsi(),
                        it->second->getImei(),
                        it->second->getDevName(),
                        it->second->getOperator(),
                        it->second->getPrintableSessionID());
            }
            //遗言讲完了, 安心的离开
            it = session->personMap_.erase(it);
        }
        else // 这是一个正常的报文
        {
            it++;
        }
    }

    // 有人吗 ?
    if(session->personMap_.empty())
    {
        return 0;
    }


    // 剩下的都是 正常的Person 写入tbl
    skypeTblWriter_.writeToFile(session);

    //return 0; 本来到这里已经结束就没什么事儿了, 下面的代码都是只是统计功能


    /**************************************************************************/
    // 写TBL文件时, 输出监视的手机号相关信息
    // 遍历Session, 拎出每一个Person, 比对MSISDN, IMSI, IMEI
    for (auto kv : session->personMap_)
    {
        //LOG_INTST->warn("[Found] MSISDN:{}, IMSI:{}, IMEI:{}, SessionID:{}",
        //        kv.second.getMsisdn(),
        //        kv.second.getImsi(),
        //        kv.second.getImei(),
        //        kv.second.getPrintableSessionID());

        bool PersonMSISDN = interestingMsisdnSet_.find(kv.second->getMsisdn()) != interestingMsisdnSet_.end();
        bool PersonIMSI   = interestingImsiSet_.find(kv.second->getImsi())     != interestingImsiSet_.end();
        bool PersonIMEI   = interestingImeiSet_.find(kv.second->getImei())     != interestingImeiSet_.end();

        if (PersonMSISDN) // 优先级第1, 显示MSISDN
        {
            LOG_INTST->warn("[tbl_found_MSISDN] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                    kv.second->getMsisdn(),
                    kv.second->getImsi(),
                    kv.second->getImei(),
                    kv.second->getDevName(),
                    kv.second->getOperator(),
                    kv.second->getPrintableSessionID());
        }
        else
        if(PersonIMSI)  // 优先级第2, 显示IMSI
        {
            LOG_INTST->warn("[tbl_found_IMSI] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                    kv.second->getMsisdn(),
                    kv.second->getImsi(),
                    kv.second->getImei(),
                    kv.second->getDevName(),
                    kv.second->getOperator(),
                    kv.second->getPrintableSessionID());
        }
        else
        if(PersonIMEI)   // 优先级第3, 显示IMEI
        {
            LOG_INTST->warn("[tbl_found_IMEI] MSISDN:{}, IMSI:{}, IMEI:{}, 解析板:{}, 运营商:{}, SessionID:{}",
                    kv.second->getMsisdn(),
                    kv.second->getImsi(),
                    kv.second->getImei(),
                    kv.second->getDevName(),
                    kv.second->getOperator(),
                    kv.second->getPrintableSessionID());
        }
    }
    /**************************************************************************/

    return 0;
}


// wxcs location sharing 超时检测
int WxcsServer::onRemveLocSharingSession(uint32_t time _U_, SessionPtr<WxcsLocSharingPerson> & session)
{
    // 剩下的都是 正常的Person 写入tbl
    locSharingTblWrite_.writeToFile(session);
    return 0;
}

// 腾讯会议超时检测
int WxcsServer::onRemveTencentMeetingSession(uint32_t time _U_, SessionPtr<WxcsTencentMeetingPerson> & session)
{
    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N个报文)
    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
    {
        //判断报文个数
        static uint32_t minCnt = CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20);
        if(it->second->c2sPackCount < minCnt ||
           it->second->s2cPackCount < minCnt )
        {
            LOG_DEF->debug("tencent meeting session: {}, invalid packet count, c2s: {}, s2c: {}", session->getSessionId(),
                    it->second->c2sPackCount, it->second->s2cPackCount);
            it = const_cast<SessionPtr<WxcsTencentMeetingPerson> &>(session)->personMap_.erase(it);
        }
        else // 这是一个正常的报文
        {
            it++;
        }
    }

    // 有人吗 ?
    if(session->personMap_.empty())
    {
        return 0;
    }

    // 剩下的都是 正常的Person 写入tbl
    tencentMeetingTblWriter_.writeToFile(session);
    return 0;
}

void WxcsServer::handleSignal(int signal)
{
	// printf("[%s][%d]receive signal: %d \n", __FILE__, __LINE__, signal);
    for (auto pServer : cs_serverSet)
    {
        pServer->onSignal(signal);
    }
    /*ADD_S by yangna 2020-08-20 */
    /*这里应该释放各种资源，回收掉各种线程后优雅退出 ，由于项目业务，线程复杂，最好由业务的相关人员来释放各自的资源和线程*/
    /*这里exit是因为ctrl+c 无法结束进程，只好粗暴退出 */
    exit(0);
    /*ADD_E by yangna 2020-08-20 */
}

void WxcsServer::onSignal(int signal)
{
  std::lock_guard<std::mutex> lock(connMutex_);
    switch (signal)
    {
    case SIGINT:
		connSet_.clear();
        server_.getLoop()->quit();
        break;

    default:
        break;
    }
}




WxcsProtoCodec::WxcsProtoCodec(const StringMessageCallback& cb)
    : messageCallback_(cb)
{

}

void WxcsProtoCodec::setMessageCallback(const WxcsProtoCodec::StringMessageCallback &cb)
{
    messageCallback_ = cb;
}

void WxcsProtoCodec::onMessage(const TcpConnectionPtr& conn,
                               Buffer* buf,
                               muduo::Timestamp receiveTime)
{
    while (buf->readableBytes() >= kHeaderLen)
    {
        const char       *data   = buf->peek();
        ST_wxcsProtoHdr  *hdr    = (ST_wxcsProtoHdr*)data;
        uint16_t          uLen   = hdr->msgLen;
        if (0 == uLen)
        {
            LOG_DEF->error("Invalid length {}", uLen);
            conn->shutdown();
            break;
        }
        else
        if(!(WXCS_PROTO_MAGIC_A == hdr->magic[0] && WXCS_PROTO_MAGIC_B == hdr->magic[1]))
        {
            LOG_DEF->error("Invalid Magic");
            conn->shutdown();
            break;
        }
        else
        if (buf->readableBytes() >= uLen)
        {
            //LOG_DEF->debug("receved new msg: {},  should read {}", buf->readableBytes(), uLen);
            std::string message(buf->peek(), uLen);
            messageCallback_(conn, message, receiveTime);
            buf->retrieve(uLen);
        }
        else
        {
            LOG_DEF->debug("data in buff not enough: {} < {}", buf->readableBytes(),  uLen);
            break;
        }
    }
}
