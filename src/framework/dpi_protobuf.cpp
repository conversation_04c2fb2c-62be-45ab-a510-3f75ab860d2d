/***
 * @Author: wuby <EMAIL>
 * @Date: 2023-02-24 15:27:25
 * @LastEditors: wuby <EMAIL>
 * @LastEditTime: 2023-03-02 18:09:42
 * @FilePath: /encode-protoc/encode_protobuf.cpp
 * @Description:
 * @
 * @Copyright (c) 2023 YView     Corporation All Rights Reserved.
 */
#include "google/protobuf/descriptor.h"
#include "google/protobuf/descriptor.pb.h"
#include "google/protobuf/dynamic_message.h"
#include "google/protobuf/io/zero_copy_stream_impl.h"
#include "google/protobuf/message.h"
#include "google/protobuf/text_format.h"

using namespace google::protobuf;
#ifdef __cplusplus
extern "C" {
#endif
/**
 * @brief 解码二进制protobuf流变成json格式流
 *
 * @param input
 * @param input_size
 * @param output
 * @return true 解码成功，out的内存需要释放
 * @return false 解码失败
 */
uint8_t decode_protobuf(char *input, int input_size, char **output);

#ifdef __cplusplus
}
#endif

uint8_t decode_protobuf(char *input, int input_size, char **output) {
  DescriptorPool pool;
  FileDescriptorProto proto_file;
  std::string context(input, input_size);
  proto_file.set_name("empty_message.proto");
  proto_file.add_message_type()->set_name("EmptyMessage");
  GOOGLE_CHECK(pool.BuildFile(proto_file) != nullptr);
  std::string codec_type_ = "EmptyMessage";
  const Descriptor *type = pool.FindMessageTypeByName(codec_type_);
  // Decode
  DynamicMessageFactory dynamic_factory(&pool);
  std::unique_ptr<Message> message(dynamic_factory.GetPrototype(type)->New());
  // 设置解析器的输入输出

  // io::ArrayOutputStream out(output, *output_size);
  // Input is binary.
  if (!message->ParsePartialFromString(context)) {
    std::cerr << "Failed to parse input." << std::endl;
    return 0;
  }
  if (!message->IsInitialized()) {
    std::cerr << "warning:  Input message is missing required fields:  "
              << message->InitializationErrorString() << std::endl;
  }
  if (message->DebugJsonString().c_str() == NULL) {
    return 0;
  }
  size_t malloc_len = message->DebugJsonString().length() +1;
  *output = (char*)malloc(malloc_len);
  if (!(*output)) {
    return 0;
  }
  snprintf(*output,malloc_len,"%s", message->DebugJsonString().c_str());
  return 1;
}
