/****************************************************************************************
 * 文 件 名 : dpi_tbl_record_log.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TBL_RECORD_LOG_H_
#define _DPI_TBL_RECORD_LOG_H_

#include "dpi_detect.h"
#include "dpi_lua_adapt.h"

#define TBL_RING_MAX_NUM 4
#define TBL_LOG_MAX_LEN (2048 * 10)
#define TBL_LOG_CONTENT_4K (1024 * 4)
#define TBL_LOG_CONTENT_4K_NUM (4096 * 16)
#define TBL_LOG_CONTENT_256K (1024 * 256)
#define TBL_LOG_CONTENT_256K_NUM 4096

#define TBL_FIELDS_MAX_LEN (2048 * 1)

#define KV53_KV99_LEN ((99 - 53 + 1) * (2))

#define TBL_PATH_LENGTH 256

#define DPI_RECORD_FIELD_D(index, type, field_name) \
  { (index), (type), (field_name) }
extern const char *empty_data;

typedef struct ProtoRecord precord_t;

typedef struct dpi_field_table_t {
	uint16_t        index;
	uint8_t         type;
	const char      *field_name;
}dpi_record_field_table;


struct tbl_log_record {
  int thread_id;
  precord_t *record;
};

void *write_tbl_log_to_file_func_(void *arg);

int tbl_record_log_enqueue(struct tbl_log_record *log);

void write_tbl_log_common_by_record(
    struct flow_info *flow, int direction,precord_t *record);


/************************初始化****************************/
int write_proto_record_field_tab(dpi_record_field_table *proto_array, int field_count, const char *proto_name);
int dpi_pschema_dump_proto_schemas(const char *pschema_output_dir);
int init_tbl_record_log(void);

#endif