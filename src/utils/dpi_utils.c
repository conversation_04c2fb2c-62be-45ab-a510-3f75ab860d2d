#include "dpi_utils.h"

#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "dpi_common.h"

const char *dpi_utils_show_bytes(uint64_t bytes, char out[], int len)
{
    if (bytes > 1024 * 1024 * 1024)
        snprintf(out, len, "%.2lf GB", (double)bytes / (1024 * 1024 * 1024));
    else if (bytes > 1024 * 1024)
        snprintf(out, len, "%.2lf MB", (double)bytes / (1024 * 1024));
    else if (bytes > 1024)
        snprintf(out, len, "%.2lf KB", (double)bytes / (1024));
    else
        snprintf(out, len, "%lu B", bytes);

    return out;
}


const char *dpi_utils_show_pps(uint64_t pps, char out[], int len)
{
    if (pps > 1000 * 1000 * 1000)
        snprintf(out, len, "%.2lf Gbps", (double)pps / (1000 * 1000 * 1000));
    else if (pps > 1000 * 1000)
        snprintf(out, len, "%.2lf Mbps", (double)pps / (1000 * 1000));
    else if (pps > 1000)
        snprintf(out, len, "%.2lf Kbps", (double)pps / (1000));
    else
        snprintf(out, len, "%lu bps", pps);

    return out;
}


static const char s_chars[] = {0x0a, 0x0d, 0x20, 0x00};

/**
 * 去除字符串左边的指定字符
 * @param chars
 *      指定删除的字符串, 以'\0'结束。
 *      ==NULL 时默认空白符。
 *
 * @return
 *     自左向右偏移后的位置, 原始内容不变
*/
char *dpi_utils_lstrip(char *str, int max_len, const char *chars)
{
    int i, chars_len;

    if(chars == NULL)
        chars = s_chars;

    for(i = 0; i < max_len; i++)
    {
        if(find_special_char((const uint8_t *)chars, strlen(chars), str[i]) < 0)
            break;
    }

    return &str[i];
}

/**
 * 去除字符串右边的指定字符,
 * @param chars
 *      指定删除的字符串, 以'\0'结束。
 *      ==NULL 时默认空白符。
 *
 * @return
 *      原始str指针, 最右边的目标字符替换为'\0'
*/
char *dpi_utils_rstrip(char *str, int max_len, const char *chars)
{
    int i;

    if(chars == NULL)
        chars = s_chars;

    for(i = max_len -1; i >= 0; i--)
    {
        if(find_special_char((const uint8_t *)chars, strlen(chars), str[i]) < 0)
            break;

        str[i] = '\0';
    }

    return str;
}

/**
 * 同时执行 dpi_utils_lstrip 和 dpi_utils_rstrip
*/
char *dpi_utils_strip(char *str, int max_len, const char *chars)
{
    char *p = dpi_utils_lstrip(str, max_len, chars);
    return dpi_utils_rstrip(p, max_len - (p - str), chars);
}



// char * dpi_strstr_kmp(const char * haystack, const char * needle)
// {
//   int len_haystack = strlen(haystack);
//   int len_needle = strlen(needle);

//   if (len_needle > len_haystack) {  
//     return NULL;
//   }

//   int i = 0, j = -1;
//   int next[len_needle];
//   next[0] = -1;

//   while (i < len_needle - 1) {
//     if (j == -1 || needle[i] == needle[j]) {
//       ++i;
//       ++j;
//       next[i] = (needle[i] != needle[j]) ? j : next[j];
//     } else {
//       j = next[j];
//     }
//   }

//   i = j = 0;
//   while (i < len_haystack && j < len_needle) {
//     if (j == -1 || haystack[i] == needle[j]) {
//       ++i;
//       ++j;
//     } else {
//       j = next[j];
//     }
//   }

//   if (j == len_needle) {
//     return (char *)(haystack + i - j);
//   } else {
//     return NULL;
//   }
// }

// char * dpi_strstr(const char * haystack, int len_haystack, 
//                    const char * needle, int len_needle)
// {
//   if (len_needle > len_haystack) {  
//     return NULL;
//   }

//   int i = 0, j = -1;
//   int next[len_needle];
//   next[0] = -1;

//   while (i < len_needle - 1) {
//     if (j == -1 || needle[i] == needle[j]) {
//       ++i;
//       ++j;
//       next[i] = (needle[i] != needle[j]) ? j : next[j];
//     } else {
//       j = next[j];
//     }
//   }

//   i = j = 0;
//   while (i < len_haystack && j < len_needle) {
//     if (j == -1 || haystack[i] == needle[j]) {
//       ++i;
//       ++j;
//     } else {
//       j = next[j];
//     }
//   }

//   if (j == len_needle) {
//     return (char *)(haystack + i - j);
//   } else {
//     return NULL;
//   }
// }

int dpi_utils_strftime(char buff[], int buff_size, const char *format)
{

    struct timeval tv;
    gettimeofday(&tv, NULL);
    struct tm tmp  ;
    localtime_r(&tv.tv_sec, &tmp);
    strftime(buff, buff_size, "%Y%m%d%H%M%S", &tmp);
    int tmp_len= strlen(buff);
    snprintf(buff+tmp_len,sizeof(buff),"_%06ld",tv.tv_usec);
    return 0;
}

char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name)
{
    struct ifaddrs *ifap, *ifa;
    struct sockaddr_in *sa;
    char *ip_address = NULL;

    if (getifaddrs(&ifap) == -1)
    {
        return NULL;
    }

    for (ifa = ifap; ifa; ifa = ifa->ifa_next)
    {
        if (ifa->ifa_addr->sa_family == AF_INET && strcmp(ifa->ifa_name, interface_name) == 0)
        {
            sa = (struct sockaddr_in *) ifa->ifa_addr;
            ip_address = inet_ntoa(sa->sin_addr);
            break;
        }
    }

    freeifaddrs(ifap);

    return ip_address;
}
