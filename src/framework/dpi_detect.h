/****************************************************************************************
 * 文 件 名 : dpi_detect.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_DETECT_H_
#define _DPI_DETECT_H_

#include <regex.h>
#include <sys/types.h>

#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_atomic.h>
#include <rte_malloc.h>
#include <rte_mbuf.h>

#include "list.h"
#include "dpi_proto_ids.h"
#include "dpi_typedefs.h"
#include "dpi_skype_media.h"
#include "dpi_common.h"

#include <glib.h>

#define TIMEOUT_MAX 60
#define RCV_CORE_MAX_NUM 128

#define PACK         __attribute__((packed))

#ifndef ETH_P_IP
#define ETH_P_IP               0x0800 	/* IPv4 */
#endif

#ifndef ETH_P_IPv6
#define ETH_P_IPV6	       0x86dd	/* IPv6 */
#endif

#define SLARP                  0x8035   /* Cisco Slarp */
#define CISCO_D_PROTO          0x2000	/* Cisco Discovery Protocol */

#define VLAN                   0x8100
#define MPLS_UNI               0x8847
#define MPLS_MULTI             0x8848
#define PPPoE                  0x8864
#define SNAP                   0xaa
#define BSTP                   0x42     /* Bridge Spanning Tree Protocol */

#define GTP_U_V1_PORT                   2152
#define TZSP_PORT                      37008

#define IDLE_SCAN_BUDGET 10240
#define IDLE_SCAN_MAX_NUM (10240 * 10)
//#define IDLE_SCAN_PERIOD           (10 * 1000) /* msec (use TICK_RESOLUTION = 1000) */
#define MAX_IDLE_TIME           (30 * 1000 * 1000)
#define MAX_FLOW_THREAD_NUM 32

#define DEV_MAX_NUM 10
#define TRAFFIC_NUM 10
#define REASSEMBLE_LEN_MAX 655350

#define SERVER_TO_CLIENT      0
#define CLIENT_TO_SERVER      1

#define DPI_MAX(a,b)   ((a) > (b) ? (a) : (b))
#define DPI_MIN(a,b)   ((a) < (b) ? (a) : (b))

typedef uint32_t dpi_ndpi_mask;

#define DPI_NUM_BITS              256

#define DPI_BITS /* 32 */ (sizeof(dpi_ndpi_mask) * 8 /* number of bits in a byte */)        /* bits per mask */
#define howmanybits(x, y)   (((x)+((y)-1))/(y))

#define DPI_SET(p, n)    ((p)->fds_bits[(n)/DPI_BITS] |=  (1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_CLR(p, n)    ((p)->fds_bits[(n)/DPI_BITS] &= ~(1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_ISSET(p, n)  ((p)->fds_bits[(n)/DPI_BITS] &   (1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_ZERO(p)      memset((char *)(p), 0, sizeof(*(p)))
#define DPI_ONE(p)       memset((char *)(p), 0xFF, sizeof(*(p)))

#define DPI_NUM_FDS_BITS     howmanybits(DPI_NUM_BITS, DPI_BITS)

typedef struct dpi_protocol_bitmask_struct
{
	dpi_ndpi_mask fds_bits[DPI_NUM_FDS_BITS];
}dpi_protocol_bitmask_struct_t;

#define DPI_PROTOCOL_BITMASK dpi_protocol_bitmask_struct_t

#define DPI_BITMASK_ADD(a,b)     DPI_SET(&a,b)
#define DPI_BITMASK_DEL(a,b)     DPI_CLR(&a,b)
#define DPI_BITMASK_RESET(a)     DPI_ZERO(&a)
#define DPI_BITMASK_SET_ALL(a)   DPI_ONE(&a)
#define DPI_BITMASK_SET(a, b)    { memcpy(&a, &b, sizeof(DPI_PROTOCOL_BITMASK)); }

#define DPI_ADD_PROTOCOL_TO_BITMASK(bmask,value)     DPI_SET(&bmask,value)
#define DPI_DEL_PROTOCOL_FROM_BITMASK(bmask,value)   DPI_CLR(&bmask,value)
#define DPI_COMPARE_PROTOCOL_TO_BITMASK(bmask,value) DPI_ISSET(&bmask,value)
#define DPI_SAVE_AS_BITMASK(bmask,value)  { DPI_ZERO(&bmask) ; DPI_ADD_PROTOCOL_TO_BITMASK(bmask, value); }

enum pkt_status {
	PKT_OK,
	PKT_STOLEN,
	PKT_REASSEMBLE,
	PKT_DROP
};

enum dissect_pkt_status {
	DISSECT_PKT_ORIGINAL,
	DISSECT_PKT_REASSEMBLE,
	DISSECT_PKT_FIANL
};

enum flow_dir {
	FLOW_DIR_SRC2DST,
	FLOW_DIR_DST2SRC,
	FLOW_DIR_MAX
};

enum RSS_USE_TYPE {
	RSS_USE_CONF = 0,
	RSS_USE_FILTER_CTRL,
	RSS_USE_CUSTOM,
};

struct five_tuple
{
	uint8_t proto;
	uint8_t ip_version;
	uint16_t port_src;
	uint16_t port_dst;
	uint16_t sctp_id;
	union {
		uint32_t ip4;
		uint8_t ip6[16];
	} ip_src;
	union {
		uint32_t ip4;
		uint8_t ip6[16];
	} ip_dst;
};

struct flow_key {
	struct five_tuple outer;
	struct five_tuple inner;
};

struct traffic_stats {
	uint64_t imissed;
	uint64_t ierrors;
	uint64_t pkts;
	uint64_t bytes;
};

struct pkt_line_end {
	uint8_t has_search;
	int linelen;
};

// 用于存储 报文级别 Src IP port, Dst IP port
struct packet
{
    uint8_t   ip_ver;
    union     {uint32_t ipv4;char ipv6[16];}src_ip;// IP报文 公网IP
    union     {uint32_t ipv4;char ipv6[16];}dst_ip;// IP报文 公网IP
    uint16_t  src_port;                       // IP报文 公网 Port
    uint16_t  dst_port;                       // IP报文 公网 Port
};

// 用于存储 会话级别 服务器IP port, 客户端IP port
typedef struct sonwden
{
    uint8_t   ip_ver;
    union     {uint32_t ipv4;char ipv6[16];}client_ip;// IP报文 公网IP
    union     {uint32_t ipv4;char ipv6[16];}server_ip;// IP报文 公网IP
    uint16_t  client_port;                       // IP报文 公网 Port
    uint16_t  server_port;                       // IP报文 公网 Port
}Snowden;

typedef struct pkt_info_t {
    const struct dpi_ethhdr *ethhdr;
    const struct dpi_iphdr *iph;
    const struct dpi_ipv6hdr *iph6;
    const struct dpi_tcphdr *tcph;
    const struct dpi_udphdr *udph;
    uint8_t proto;
    uint8_t ipversion;
    int pkt_len;
}PacketInfo;


//会话的数据结构
typedef struct flow_info {
	struct flow_key tuple;
	struct flow_key tuple_reverse;
    struct packet   pkt;      // 存储最新的 5元组
	struct list_head node_timeout;

	uint8_t in_idle_flows;

	uint8_t thread_id;
  uint64_t  timestamp;
	uint64_t  pkt_rx_timestamp_first;	// 框架收包的时间戳，weixin重组时取第一帧的这个时戳
  uint64_t  pkt_rx_timestamp; 			// 当前帧的时间戳，循环更新
    struct dpi_ethhdr  *ethhdr;
	uint8_t             has_trailer;
	uint8_t            *trailer;
	int                 trailerlen;

	uint8_t ip_version;
	uint8_t ttl;
	uint8_t ip_flag[7];
	uint16_t guessed_protocol_id;
	uint16_t real_protocol_id;
	uint64_t flow_id;

	struct pkt_line_end pkt_first_line;

	uint32_t src2dst_packets;
	uint32_t dst2src_packets;
	uint64_t src2dst_bytes;
	uint64_t dst2src_bytes;
	uint64_t last_seen;

	DPI_PROTOCOL_BITMASK excluded_protocol_bitmask;

	uint8_t direction;

	uint32_t reassemble_pkt_src2dst_num;
	uint32_t reassemble_pkt_dst2src_num;
	uint32_t rsm_src2dst_len;
	uint32_t rsm_dst2src_len;
	struct list_head reassemble_src2dst_head;
	struct list_head reassemble_dst2src_head;


    uint32_t          next_seq    [FLOW_DIR_MAX];
    uint32_t          rsm_len     [FLOW_DIR_MAX];
    uint32_t          rsm_pkt_num [FLOW_DIR_MAX];
    struct list_head  rsm_head    [FLOW_DIR_MAX];
    int               reassemble_enable;
    int               reassemble_direction;
    int               reassemble_expect_len[FLOW_DIR_MAX];;

	uint8_t fin_flag[FLOW_DIR_MAX];

  uint8_t pkt_cnt;     // 包计数，单条流需要多帧识别使用此变量
  char user_data[16];   // 如果 需要存储少量用户数据，可以不用 app_session  使用user_data
	void *app_session;
	int is_timeout;  //会话是否超时
  void (*flow_EOF)(struct flow_info *flow, void *app_session);

	void* rsm;                     // 新版 TCP 重组接口
	const PacketInfo  *pkt_info;
	uint16_t   port_src;
    uint16_t   port_dst;
    uint16_t   drt_port_src[2];
    uint16_t   drt_port_dst[2];

	char *err_pcap;                   // 解析失败-报文转储
    int   err_pcap_hold;              // 解析失败-报文转储
    int   err_pcap_dump;              // 解析失败-报文转储
} dpi_flow_info_t;


/* add by liugh 统计还原率占比*/
enum content_protocol{
    EM_CONTENT_WXGH,
    EM_CONTENT_MAX
};

struct stat_content{
    rte_atomic32_t total_number;
    rte_atomic32_t actual_number;
    rte_atomic32_t percent_100_number;
    rte_atomic32_t percent_90_number;
    rte_atomic32_t percent_80_number;
    rte_atomic32_t percent_other_number;
    rte_atomic32_t repeat_number;
    rte_atomic32_t s_wxcs_number;
};


// flow statistics info
typedef struct process_stats {
	uint16_t max_packet_len;
	uint32_t flow_count;
	uint32_t guessed_flow_protocols;
	uint64_t raw_packet_count;
	uint64_t ip_packet_count;
	uint64_t total_wire_bytes;
	uint64_t total_ip_bytes;
	uint64_t total_discarded_pkts;

	uint64_t inc_flow_num;
	uint64_t tcp_count;
	uint64_t udp_count;
	uint64_t sctp_count;
	uint64_t mpls_count, pppoe_count, vlan_count, fragmented_count;
	uint64_t packet_len[6];

	uint32_t flow_stats[PROTOCOL_MAX];
	uint64_t flow_stats_total[PROTOCOL_MAX];
	uint64_t flow_stats_total_pkts[PROTOCOL_MAX];
	uint64_t flow_stats_total_bytes[PROTOCOL_MAX];

	struct traffic_stats traffic[TRAFFIC_NUM];
} process_stats;

// workflow main structure
typedef struct work_process_data {
	pthread_t                 pthread;
	uint32_t                  thread_id;
	uint32_t                  num_allocated_flows;
	uint64_t                  last_time;
	uint64_t                  last_idle_scan_time;

    const
    struct dpi_ethhdr        *ethhdr;
	struct rte_hash          *hash;
	struct process_stats      stats;
	struct list_head          timeout_head[TIMEOUT_MAX];

	uint64_t  timestamp;
    char*     path;


    const uint8_t *l3;    // add by liugh
    uint16_t      ip_len; // add by liugh

    //add by xuxn
    uint16_t  payload_len;
    uint8_t   ip_version;
    uint8_t   proto_type;
    uint16_t  dst_port;
    uint8_t   handshake;
    uint16_t  check;
    uint8_t   ip_link_type;
    uint8_t   routers_flag;
    uint32_t  max_interval;
    uint32_t  min_interval;

    uint8_t  is_mpls;       //mpls flag
    uint8_t  vlan_flag;     //vlan flag
    union eth_data legacy;    //ether info

    char      pcap_file_name[COMMON_FILE_PATH];

    char      file_tbl_dir[COMMON_FILE_PATH];
    char      file_tbl_name[COMMON_FILE_PATH];
    FILE      *tbl_fp;
    FILE      *ip_position_fp;
    uint32_t  file_count;
    uint32_t  timeout_sec;
} dpi_workflow_t;


struct st_tcp_reassemble_obj
{
    size_t expect;
    size_t success;
    size_t error;
};

struct st_tcp_reassemble
{
    struct st_tcp_reassemble_obj obj[2];
    size_t weixin_total;
    size_t weixin_size;
    size_t weixin_start;

    struct stat_content  content_statics[EM_CONTENT_MAX];
};


struct http_host_uri
{
    char  host[64];
    char  uri [1024];
} PACK;


struct config_http
{
    int         error_pcap_dump        ; //转储Error  PCAP 开关
    int         tcp_out_of_order       ; //TCP 抖动容忍
    int         tcp_padding_len        ; //TCP 缺包小于N时 允许padding

    int         http_strip_cache_size  ; //http 切割缓存大小
    uint8_t     http_switch_store_file ; //http 是否存储文件开关
    uint8_t     http_switch_response_file;  // http 下行数据是否要写文件
    int         http_file_size         ; //http 存储文件最大值，要小于cache szie

    char        tcp_padding_str[512]   ; //TCP PADDING 字符

    char        http_content_filter[512];
    char        *filter_split[COMMON_FILTER_NUM];
    regex_t     reg[COMMON_FILTER_NUM];
    int         num;
    uint8_t     drop_no_content_type;
    uint8_t     http_tax_switch;
};

struct global_config {
	time_t g_start_time;	     //程序启动时间，秒
	time_t g_now_time;           //时间戳，秒
	uint64_t g_now_time_usec;    //时间戳，微秒
	unsigned int nb_rxq;         //每个网卡的收报队列数，收包线程数；可启动时配置
	unsigned int dissector_thread_num; 	//解析线程个数； 可启动时配置

	unsigned int pkt_rcv_core_id[RCV_CORE_MAX_NUM];
	unsigned int pkt_rcv_queue_id[RCV_CORE_MAX_NUM];
	unsigned int pkt_process_core_id[MAX_FLOW_THREAD_NUM];
	unsigned int log_core_id[4];

	unsigned int mempool_cache_size;
	unsigned int packet_ring_size;		//解析线程的缓冲队列大小； 可启动时配置

	uint16_t mbuf_size;
	uint16_t max_pkt_len;
	uint32_t nb_mbuf;

    uint8_t drop_filter[16];

    uint32_t max_hash_node_per_thread;
    uint32_t max_flow_num;
    uint32_t tcp_reassemble_mempool_num;
    uint32_t max_conversation_hash_node;
    uint32_t tbl_ring_size;
    uint32_t tbl_log_content_256k;
	uint32_t tbl_log_content_256k_num;


	uint16_t write_tbl_maxtime;            //写一个tbl文件，从开始到现在用时最大不能超过的时间（单位：秒）
	uint16_t tcp_flow_timeout;             //tcp 会话的超时时间  可动态配置
	uint16_t udp_flow_timeout;             //udp 会话的超时时间  可动态配置
	uint16_t sctp_flow_timeout;            //sctp 会话的超时时间  可动态配置
	uint16_t tcp_identify_pkt_num;         //tcp 会话的最大报文识别个数  可动态配置
	uint16_t udp_identify_pkt_num;         //udp 会话的最大报文识别个数  可动态配置
	uint16_t sctp_identify_pkt_num;        //sctp 会话的最大报文识别个数  可动态配置
    uint16_t per_fields_len;               // 每个字段最大长度

	uint16_t tcp_resseamble_max_num;       //tcp会话重组的最大数据包个数   可动态配置
	uint32_t log_max_num;                  //每个日志文件的最大条目数 可动态配置
	uint8_t protocol_switch[PROTOCOL_MAX]; //每个协议的开关 可动态配置
	uint8_t protocol_switch_wxinfo_mini;
	uint8_t conversation_switch;           //关联协议识别开关
	uint32_t idle_scan_period;             //usec
	int      socketid;                //查看使能网卡所在socketid
	int log_output_level;                  //日志输出级别
	char log_output_path[256];             //日志输出路径
	FILE *fp_log;                          //日志文件handle

	char devArea[32];                      //地域
	char devname[64];                      //计算板 板号

	uint8_t show_task_id;             //是否任务id
	char      task_id[32];
	char operator_name[64];                //运营商名字
	int  trailertype;                      //trailer type
	char tbl_out_dir[256];                 //tbl日志输出目录
	char dpi_field_dir[256];               //字段表输出目录 add by liugh
	char RT_model[256];                    //戎腾设备型号

	uint32_t wx_group_head_timeout;		   //微信群头像上传超时时间

	uint8_t stop_rcv_pkts;                 //停止收包
	uint8_t exit;                          //退出
	int timeout_index;                     //会话链表超时下标

	// weixin
	uint8_t wxf_filter;                    // 进一步提取微信有用数据开关
	char    wxf_filter_config[512];        // 决定由哪些字段作为有效信息默认"filemd5" "fileid" "url"
	uint8_t wxf_filter_repeat;             // 过滤多流传数据时，保留rangeend==totalsize的一条数据
	uint8_t wxf_check_http_times;          // 当大文件传输数据，一条流多个http头时，决定在解析时解析前面的多个http头
	uint8_t wxf_fileid_part1_byte;
	uint8_t wxf_fileid_part2_byte;
    char    wxf_pyq_data_dir[1024];
	uint8_t wx_filedata_bytes;
	uint8_t wxf_time_precise_flag;			//是否开启精确度

	// gquic  being
	uint8_t gquic_wxf_filter;
	char 		gquic_wxf_filter_str[512];
	// gquic end

  uint8_t   pcap_port_id;
		uint8_t data_source;
    char     wx_group_ip[16];              //微信群头像    聚合程序 ip
    uint16_t wx_group_port;                //微信群头像    聚合程序 port

    char     wx_pos_ip[16];                //微信位置分享  聚合程序 ip
    uint16_t wx_pos_port;                  //微信位置分享  聚合程序 port

    char     qq_voip_ip[16];               //QQ话单        聚合程序 ip
    uint16_t qq_voip_port;                 //QQ话单        聚合程序 port

    char     wx_voice_ip[16];              //微信话单      聚合程序 ip
    uint16_t wx_voice_port;                //微信话单      聚合程序 port

    char     QQ_File_ip[16];               // QQ 传文件
    uint16_t QQ_File_port;                 // QQ 传文件

    char     skype_ip[16];                 // skype 聚合程序ip
    uint16_t skype_port;                   // skype 聚合程序port

	// 微信 ios 1对1 通联关系通信端口ip
	char 	 wx_rela_ip[16];
	uint16_t wx_rela_port;

    char     wx_phone_watch[4096];         //微信/QQ话单   监视手机号码 列表
    uint32_t wx_session_timeout;           //微信/QQ话单   不活跃的会话超时删除
    uint32_t wx_session_timeloop;          //微信/QQ话单   Hash表定时推送
    uint32_t wx_session_drop;              //微信/QQ话单   SessionID前4字节为0 检测
    uint32_t wx_session_pkt_size;          //微信/QQ话单   有效UDP报文最小值
    uint32_t wx_session_flag_pkt;          //微信话单      D5数据报文检测最小数
    uint32_t wx_session_seq_jitter;        //微信话单      Sequence值 有效抖动值
    uint32_t wx_session_ring_size;         //微信话单      正常的音视频_响铃结束包大小
		uint8_t  wxa_recognition;              // wxa 识别模式
	  uint8_t  wx_session_filter;				// 微信话单 16:1 过滤开关
    uint32_t protocol_tbl_http;            //是否输出 TBL 文件
    uint8_t tbl_out_quote;
    uint32_t http_rsm;            //是否开启http重组

	uint32_t qq_session_seq_jitter;        // QQ话单      Sequence值 有效抖动值

	uint8_t wx_red_packet_precise;			//微信红包  jingque
	uint8_t wx_red_packet_switch;			//微信红包 开关

	/* wx info  */
	uint8_t wx_info_incomplete;				// 微信信息是否输出不完整信息
	uint8_t wx_info_export_illegal;			// 是否过滤非法字段

	// ---------- wxmisc
	uint8_t wx_misc_payload_save;

    // 调试参数
    int debug_weixin_voice  ;
    int debug_zoom_conference;
    int debug_qq_voip       ;
    int debug_qq_file       ;
    int debug_group_head    ;
    int debug_position_share;
	int debug_skype_media;
    int debug_qq_event  ;

	uint8_t ring_rss_mode;
	uint8_t rss_use_type[RTE_MAX_ETHPORTS]; // enum RSS_USE_TYPE, 表明不同的rss使用方

    // TCP数据重组状态
    struct st_tcp_reassemble reassemble_info;


    struct http_host_uri  http_qq[1024];
    struct http_host_uri  http_wx[1024];       //个人头像
	  uint8_t 			  http_wx_host;
    struct http_host_uri  http_wx_msg[1024];   //微信文字消息
    GHashTable           *wxpay_hash;

	struct  config_http  http;


	// ------  日志相关 ------
	uint8_t  log_thread_num;

	// ------  日志相关 ------


  char ip_interface[64];
  char tbl_out_format[64];
  uint8_t stop_write_tbl;
  char lua_adapt_dir[64];
  char lua_adapt_script_type[64];
  int dpdk_stop_receive;
  time_t  stop_time;      //设置程序停止时间
  uint8_t tcp_rsm_report_err;
};


#define PORT_PROTO_NUM_MAX 4

struct guess_proto_data{
	uint16_t proto[PORT_PROTO_NUM_MAX];
};

struct check_proto_data{
	uint16_t proto;
	DPI_PROTOCOL_BITMASK excluded_protocol_bitmask;
	void (*identify_func)(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);
	int (*dissect_func)(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
	int  (*flow_timeout)(struct flow_info *flow);             //在 旧版TCP超时前
};

extern struct guess_proto_data tcp_port_proto[65536];
extern struct guess_proto_data udp_port_proto[65536];

extern struct check_proto_data tcp_detection_array[PROTOCOL_MAX];
extern struct check_proto_data udp_detection_array[PROTOCOL_MAX];

int workflow_node_cmp(const void *a, const void *b);
int workflow_process_packet (struct work_process_data * workflow, uint64_t p_msec, const u_char *packet, uint32_t pkt_len);
int workflow_process_packet2 (struct work_process_data * workflow, const struct rte_mbuf *mbuf, uint64_t p_msec, const u_char *packet, uint32_t pkt_len);
void do_idle_flow_free(uint16_t thread_id, uint64_t now_time, int flag);

int dpi_packet_processing_ipv4(struct work_process_data *workflow, const uint64_t time, const struct five_tuple *outer,
							const PacketInfo  *pkt_info, const struct dpi_iphdr *iph, uint16_t ipsize, uint16_t rawsize);
int dpi_packet_processing_ipv6(struct work_process_data *workflow, const uint64_t time, const struct five_tuple *outer,
							const PacketInfo  *pkt_info, const struct dpi_ipv6hdr *iph, uint16_t ipsize, uint16_t rawsize);

uint16_t dpi_get_flow_timeout_index(uint8_t proto);
void flow_timeout(struct     work_process_data *process, int timeout_index, int max_del_num);
void do_all_flow_free(uint16_t thread_id);
void port_add_proto_head(uint8_t tcp_or_udp, uint16_t port, uint16_t protocol);

int  init_dissector_wx_sesion_thread(void);
int  init_dissector_qq_sesion_thread(void);
int  init_weixin(void);
void init_wx_http_sub_handle(void);
int init_wx_tm_data(void);
int  get_ip_port(struct packet *pkt, struct sonwden *sonwden, int *C2S); // 新的接口
int  get_ip_port_v4(struct flow_info *flow, int *client_ip, int *server_ip, short *client_port, short *server_port, int *C2S); // 老的接口

/*初始化QQ活动事件解析线程 */
int init_dissector_qq_event_thread(void);

/*结束QQ活动事件解析线程 */
void fini_dissector_qq_event_thread(void);


#endif
