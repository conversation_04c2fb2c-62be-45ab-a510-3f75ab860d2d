/****************************************************************************************
 * 文 件 名 : wxcs_voice_sub.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-05-10
* 编    码 : chenzq         '2021-05-10
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_voice_peers.h"

void PeersInfoConcat(std::unordered_map<std::string, WxpeerInfo> & _peersinfo, PersonPeersInfo & _info, TimeRange & _range)
{
    if (_peersinfo.empty()) {
        return;
    }

    // _info.ipcount = _peersinfo.size();
    for (auto & peerinfo : _peersinfo) {
        auto & peer = peerinfo.second;
        std::string _str;
        if (_info.peerlist.find(peer.peerip) != std::string::npos) {
            continue;
        }
        if (peer.location.empty()) {
            _str = peer.peerip;
        } else {
            _str = peer.peerip + "(" + peer.location + ")";
        }
        WxcsStrConcat(_info.peerlist, _str, ';');

        uint8_t portflag = 0;
        _str = "(";
        for (auto & port : peer.peerports) {
            auto &attr = port.second;
            if ((_range.begin <= attr.begin && _range.end >= attr.begin) ||
              (_range.begin <= attr.end && _range.end >= attr.end)) {
                if (portflag == 0) {
                    _str = _str + std::to_string(port.first);
                    portflag = 1;
                } else {
                    _str = _str + "," + std::to_string(port.first);
                    portflag++;
                }
            }

        }
        _str = peer.peerip + _str + ")";
        WxcsStrConcat(_info.ipport, _str, ';');


        WxcsStrConcat(_info.country, peer.country, ';');
        WxcsStrConcat(_info.province, peer.province, ';');

        // iptype 可能为0， 所以不能用公共字符串拼接接口
        _str = std::to_string(peer.ip_type);
        if (_info.iptype.empty()) {
            _info.iptype = _str;
        }
        if (_info.iptype.find(_str, 0) == std::string::npos) {
            _info.iptype = _info.iptype + ';' + _str;
        }

        switch (peer.ip_type) {
        case 0:
            WxcsStrConcat(_info.publicip, peer.peerip, ';');
            break;
        case 1:
            WxcsStrConcat(_info.privateip, peer.peerip, ';');
            break;
        case 2:
            WxcsStrConcat(_info.ipfor4G, peer.peerip, ';');
            break;
        default:
            break;
        }

        WxcsStrConcat(_info.ttl, peer.ttl, ';');

        _info.a3totoal += peer.a3count;
        _str = peer.peerip + "=" + std::to_string(peer.a3count);
        WxcsStrConcat(_info.a3list, _str, ';');

        _info.ipcount++;

    }

}


std::shared_ptr<WxpeerInfo> WXPeers::getPeerInfo(std::string _clientIP, std::string _peerIp)
{
    auto findPeer = clientList_.find(_clientIP);
    if (findPeer == clientList_.end()) {
        return nullptr;
    }

    auto findPeerInfo = clientList_[_clientIP].find(_peerIp);
    if (findPeerInfo == clientList_[_clientIP].end()) {
        return nullptr;
    }

    return std::make_shared<WxpeerInfo>(clientList_[_clientIP][_peerIp]);
}


bool WXPeers::GetPeersInfo(std::string & _clientip, uint64_t _msisdn, TimeRange & _range,  PersonPeersInfo & _peersinfo)
{
    //find

    std::unordered_map<std::string, WxpeerInfo> legalpeers;
    bool ip_delflag = false;
    bool msisdn_delflag = false;
    auto peers = clientList_.find(_clientip);

    {// 首先查找 ip
        std::lock_guard<std::mutex> lock(mtx_);
        if ( peers != clientList_.end()) {
            for (auto & peer : peers->second) {
                auto & info = peer.second;
                std::string peerip = peer.first;
                // 只要 a3 第一次出现的时间在话单时间范围内即可
                if ((_range.begin <= info.begin_time && _range.end >= info.begin_time) ||
                (_range.begin <= info.end_time && _range.end >= info.end_time)) {
                    legalpeers[peerip] = info;
                    ip_delflag = true;
                    msisdn_delflag = true;
                }
            }
        } else {  // ip 未找到再查找手机号
            auto msisdn_peers = msisdnlist_.find(_msisdn);
            if (msisdn_peers == msisdnlist_.end()) {
                return false;
            }
            for (auto & peer : msisdn_peers->second){
                auto & info = peer.second;
                std::string peerip = peer.first;
                // 只要 a3 第一次出现的时间在话单时间范围内即可
                if ((_range.begin <= info.begin_time && _range.end >= info.begin_time) ||
                (_range.begin <= info.end_time && _range.end >= info.end_time)) {
                    legalpeers[peerip] = info;
                    msisdn_delflag = true;
                }
            }
        }
    }

    // 合并字符串
    PeersInfoConcat(legalpeers, _peersinfo, _range);

    {
        std::lock_guard<std::mutex> lock(mtx_);
        // 直接删除 client
        if (ip_delflag) {
            clientList_.erase(_clientip);
        }

        if (msisdn_delflag) {
            msisdnlist_.erase(_msisdn);
        }
    }


    return true;

}


void WXPeers::DealPeerInfo(ST_WXPEERS * _wxpeers)
{
    std::string client_ip = _wxpeers->clientipstr;
    std::string peer_ip = _wxpeers->peeripstr;

    FindType cflag = FindClientPeer(client_ip, peer_ip);
    switch (cflag)
    {
    case FindType::client_peer:
        UpdateClient(_wxpeers);
        break;
    case FindType::client_no_peer:
    case FindType::no_client:
        InsertClient(_wxpeers);
        break;
    default:
        break;
    }

    // 判断是否打上标签
    if (_wxpeers->trailer.MSISDN == 0) {
        return;
    }

    FindType mflag = FindMsisdnPeer(_wxpeers->trailer.MSISDN, peer_ip);
    switch (mflag)
    {
    case FindType::msidn_peer:
        UpdateMsisdn(_wxpeers);
        break;
    case FindType::msidn_no_peer:
    case FindType::no_msisdn:
        InsertMsisdn(_wxpeers);
        break;
    default:
        break;
    }
}


FindType  WXPeers::FindClientPeer(std::string _client, std::string _peer)
{
    std::lock_guard<std::mutex> lock(mtx_);
    auto clientit = clientList_.find(_client);
    if (clientit == clientList_.end()) {
        return FindType::no_client;
    } else {
        if (clientit->second.find(_peer) == clientit->second.end()) {
            return FindType::client_no_peer;
        } else {
            return FindType::client_peer;
        }
    }

    return FindType::undefine;
}


FindType  WXPeers::FindMsisdnPeer(uint64_t _msisdn, std::string _peer)
{
    std::lock_guard<std::mutex> lock(mtx_);
    auto msisdnit = msisdnlist_.find(_msisdn);
    if (msisdnit == msisdnlist_.end()) {
        return FindType::no_msisdn;
    } else {
        if (msisdnit->second.find(_peer) == msisdnit->second.end()) {
            return FindType::msidn_no_peer;
        } else {
            return FindType::msidn_peer;
        }
    }

    return FindType::undefine;
}


bool WXPeers::InsertClient(ST_WXPEERS * _wxpeers){
    WxpeerInfo peerInfo;
    PortAttr portattr;
    peerInfo.location   = std::string(_wxpeers->location);
    peerInfo.country    = std::string(_wxpeers->country);
    peerInfo.province   = std::string(_wxpeers->province);
    peerInfo.begin_time = _wxpeers->trailer.TS;
    peerInfo.end_time   = _wxpeers->trailer.TS;
    peerInfo.ip_type    = _wxpeers->ip_type;
    peerInfo.clientip   = _wxpeers->clientipstr;
    peerInfo.peerip     = _wxpeers->peeripstr;
    peerInfo.msisdn     = _wxpeers->trailer.MSISDN;


    portattr.begin      =  _wxpeers->trailer.TS;
    portattr.end        =  _wxpeers->trailer.TS;
    peerInfo.peerports[_wxpeers->peerport] = portattr;  // a3 多流，多端口

    std::pair<std::string, WxpeerInfo> info(peerInfo.peerip, peerInfo);
    std::lock_guard<std::mutex> lock(mtx_);
    clientList_[_wxpeers->clientipstr].insert(info);

    return true;
}


bool WXPeers::UpdateClient(ST_WXPEERS * _wxpeers){
    std::lock_guard<std::mutex> lock(mtx_);

    std::string clientip(_wxpeers->clientipstr);
    std::string peerip(_wxpeers->peeripstr);
    auto & peerinfo = clientList_[clientip][peerip];
    peerinfo.end_time = _wxpeers->trailer.TS;

    if (peerinfo.peerports.find(_wxpeers->peerport) != peerinfo.peerports.end()) {
         peerinfo.peerports[_wxpeers->peerport].end = _wxpeers->trailer.TS;
    } else {
        PortAttr portattr;
        portattr.begin      =  _wxpeers->trailer.TS;
        portattr.end        =  _wxpeers->trailer.TS;
        peerinfo.peerports[_wxpeers->peerport] = portattr;
    }

    if (_wxpeers->is_timeout == 1) {
        peerinfo.a3count = peerinfo.a3count + _wxpeers->a3_count;
    }

    return true;
}


bool WXPeers::InsertMsisdn(ST_WXPEERS * _wxpeers){
    WxpeerInfo peerInfo;
    PortAttr portattr;

    peerInfo.location   = std::string(_wxpeers->location);
    peerInfo.country    = std::string(_wxpeers->country);
    peerInfo.province   = std::string(_wxpeers->province);
    peerInfo.begin_time = _wxpeers->trailer.TS;
    peerInfo.end_time   = _wxpeers->trailer.TS;
    peerInfo.ip_type    = _wxpeers->ip_type;
    peerInfo.clientip   = _wxpeers->clientipstr;
    peerInfo.peerip     = _wxpeers->peeripstr;
    peerInfo.msisdn     = _wxpeers->trailer.MSISDN;
    peerInfo.ttl        = _wxpeers->ttlstr;


    portattr.begin      =  _wxpeers->trailer.TS;
    portattr.end        =  _wxpeers->trailer.TS;
    peerInfo.peerports[_wxpeers->peerport] = portattr;  // a3 多流，多端口

    std::pair<std::string, WxpeerInfo> info(peerInfo.peerip, peerInfo);
    std::lock_guard<std::mutex> lock(mtx_);
    msisdnlist_[peerInfo.msisdn].insert(info);

    return true;
}


bool WXPeers::UpdateMsisdn(ST_WXPEERS * _wxpeers){
    std::lock_guard<std::mutex> lock(mtx_);
    uint64_t msisdn = _wxpeers->trailer.MSISDN;
    std::string peerip(_wxpeers->peeripstr);
    auto & peerinfo = msisdnlist_[msisdn][peerip];
    peerinfo.end_time = _wxpeers->trailer.TS;
    if (_wxpeers->is_timeout == 1) {
        peerinfo.a3count = peerinfo.a3count + _wxpeers->a3_count;
    }

    if (peerinfo.peerports.find(_wxpeers->peerport) != peerinfo.peerports.end()) {
         peerinfo.peerports[_wxpeers->peerport].end = _wxpeers->trailer.TS;
    } else {
        PortAttr portattr;
        portattr.begin      =  _wxpeers->trailer.TS;
        portattr.end        =  _wxpeers->trailer.TS;
        peerinfo.peerports[_wxpeers->peerport] = portattr;
    }

    return true;
}