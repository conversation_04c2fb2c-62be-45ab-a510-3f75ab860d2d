/****************************************************************************************
 * 文 件 名 : wxcs_loc_sharing.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : chenzq         '2021-12-09
* 编    码 : chenzq         '2021-12-09
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include "wxcs_loc_sharing.h"

#include <unordered_map>
#include <map>

#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep

/* 1 template specialization for wxcs location sharing */
template<>
std::string WxcsSessionBase<WxcsLocSharingPerson>::getColumnList(char sep)
{
    std::string strColumns;

    WX_COLUMN_TEXT(strColumns, "DevNo",         sep);
    WX_COLUMN_TEXT(strColumns, "LineNo",        sep);
    WX_COLUMN_TEXT(strColumns, "CapDate",       sep);

    // if(CFG->GetValueOf<int>("SESSION_ID_WXA_ENABLE", 0))
    // {
    //     WX_COLUMN_TEXT(strColumns, "SessionID"        , sep);
    // }

    WX_COLUMN_TEXT(strColumns, "isGroup",       sep);
    WX_COLUMN_TEXT(strColumns, "BeginTime",     sep);
    WX_COLUMN_TEXT(strColumns, "Endtime",       sep);
    WX_COLUMN_TEXT(strColumns, "Duration",      sep);
    WX_COLUMN_TEXT(strColumns, "PersonCount",   sep);
    WX_COLUMN_TEXT(strColumns, "PersonList",    sep);
    WX_COLUMN_TEXT(strColumns, "resv00",        sep);

    for (int i = 0; i<SESSION_LS_PERSON_COUNT_MAX; i++)
    {
        strColumns += WriteTrailerCommon(i, sep);
        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);

        WX_COLUMN_TEXT(strColumns, "PersonIPVersion_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonC2SPackets_"          + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonS2CPackets_"          + index, sep);
        // WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"       + index, sep);
        // WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"       + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"           + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonEndTime_"             + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv00_"              + index, sep);
        WX_COLUMN_TEXT(strColumns, "PersonResv01_"              + index, sep);
    }

    return strColumns;
}


std::string WxcsLocSharingPerson::toStrRecord(char sep) const
{
    // 22 fields
    std::string strRecord;
    strRecord += getStrTrailer(sep);  // 建联信息

    // statistics
    CONS_RECORD_FIELD_NUM(strRecord,    ip_version,     sep);
    CONS_RECORD_FIELD_NUM(strRecord,    c2spkts_,       sep);
    CONS_RECORD_FIELD_NUM(strRecord,    s2cpkts_,       sep);
    // date
    CONS_RECORD_FIELD_TIME(strRecord,   begintime_,     sep);
    CONS_RECORD_FIELD_TIME(strRecord,   endtime_,       sep);
    //保留字段
    CONS_RECORD_FIELD_TEXT(strRecord,   resv00_,        sep);
    CONS_RECORD_FIELD_TEXT(strRecord,   resv01_,        sep);


    return strRecord;
}

// 人数不够时, 写入空的数据, 注意每个人的字段数
std::string WxcsLocSharingPerson::toStrBlankRecord(char sep)
{
    std::string strRecord;
    for (int i = 0; i < 31; i++) {
        strRecord += std::string("\"\"") + sep;
    }

    return strRecord;
}


// 初始化列表, 参数过长, 不优雅.(宁愿舍弃这点性能)
WxcsLocSharingPerson::WxcsLocSharingPerson(ST_WXLocSharing *p)
    : WxcsPerson(p->sessionid, sizeof(p->sessionid), 0,
                 p->client_port, p->server_port, p->client_ip.ipv4, p->server_ip.ipv4,
                 p->end_time, &p->trailer)
{
    c2spkts_        = p->c2s_pkts;
    s2cpkts_        = p->s2c_pkts;
    begintime_      = p->begin_time;
    endtime_        = p->end_time;

    if(6 == p->ip_version) {
        ip_version = 6;
        memcpy(client_ipv6, p->client_ip.ipv6, 16);
        memcpy(server_ipv6, p->server_ip.ipv6, 16);
    }
}



void WxcsLocSharingKeeper::ProcessWXLS(const unsigned char*pdata, int len)
{
    if(sizeof(ST_WXLocSharing) != len) {
        // LOG_ERR 需要无IO阻塞 LOG API
        return;
    }

    ST_WXLocSharing *pMsgAlive = (ST_WXLocSharing *)pdata;
    PersonPtr<WxcsLocSharingPerson> personInfo(new WxcsLocSharingPerson(pMsgAlive));

    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && personInfo->wasUncrediableId()) {
        LOG_DEF->debug("drop uncredible person record {}", personInfo->getPrintablePersonMobileID());
        return ;
    }

    // 是不是监视的 手机号 ?
    bool bInterestingPerson = interestmsisdn_.find(personInfo->getMsisdn()) != interestmsisdn_.end();
    if (bInterestingPerson) {
        LOG_INTST->warn("found interesting person msg : {} of session {}",
                        personInfo->getMsisdn(), personInfo->getPrintableSessionID());
    }


    // 找会话
    auto pAudioSession = WXLSKS->getSessionOf(pMsgAlive->sessionid, sizeof pMsgAlive->sessionid);
    if (!pAudioSession) {   // 添加新 session,向该 session 中添加用户
        SessionPtr<WxcsLocSharingPerson> newSession(new WxcsSession<WxcsLocSharingPerson>((char *)pMsgAlive->sessionid
                                                , sizeof pMsgAlive->sessionid, 0
                                                , pMsgAlive->begin_time));

        newSession->addNewPerson(personInfo, bInterestingPerson);
        WXLSKS->addNewSession(newSession);
    } else {
            //找人
          // session 存在，检查是否存在该用户
        auto pPersonIn = pAudioSession->findPerson(personInfo);
        if (!pPersonIn) {   // 该用户不存在，添加新用户
            pAudioSession->addNewPerson(personInfo, bInterestingPerson);
        } else {   // 用户已经存在，更新信息
            pAudioSession->updatePersonInfo(personInfo, bInterestingPerson);
        }
     }
}


// template<>
std::string WxcsSession<WxcsLocSharingPerson>::toStrRecordLine(char sep) const
{
    uint32_t tmptime;
    personcnt_ = personMap_.size();
    for (auto & iter : personMap_) {
        auto & person = iter.second;
        tmptime = person->GetStartTime();
        if (begintime_ == 0) begintime_ = tmptime;
        begintime_ = tmptime > begintime_ ? begintime_ : tmptime;
        tmptime = person->GetEndTime();
        endtime_ = tmptime > endtime_ ? tmptime : endtime_;
        std::string prefix = personlist_.empty() ? "" : ",";
        personlist_ += prefix + person->getPrintablePersonID();
    }

    duration_ = endtime_ - begintime_;

    std::string strLine;
    // 写入头部信息
    CONS_RECORD_FIELD_TEXT(strLine, "wxls",      sep); // DevNO
    CONS_RECORD_FIELD_TEXT(strLine, "31",        sep); // LineNO
    CONS_RECORD_FIELD_TIME(strLine, time(NULL),  sep); // CapDate
    CONS_RECORD_FIELD_NUM (strLine, isgroup_,    sep); // IsGroup
    CONS_RECORD_FIELD_TIME(strLine, begintime_,  sep); // SessionBegin
    CONS_RECORD_FIELD_TIME(strLine, endtime_,    sep); // SessionEnd
    CONS_RECORD_FIELD_NUM (strLine, duration_,   sep); // IsAnswered
    CONS_RECORD_FIELD_NUM (strLine, personcnt_ , sep); // PersonCount
    CONS_RECORD_FIELD     (strLine, personlist_, sep); // PersonList
    CONS_RECORD_FIELD     (strLine, resv00_,     sep); //

    ////写入每个人的数据
    int iChecked = 0;
    for (auto iter = personMap_.begin();
         iter != personMap_.end() && iChecked < SESSION_LS_PERSON_COUNT_MAX;
         iter++, iChecked++) {
        strLine += iter->second->toStrRecord(sep);
    }

    // 接着写入空白行
    for (; iChecked < SESSION_LS_PERSON_COUNT_MAX; iChecked++) {
        strLine += WxcsLocSharingPerson::toStrBlankRecord(sep);
    }

    return strLine;
}

