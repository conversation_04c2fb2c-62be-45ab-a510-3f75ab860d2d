#include <stdint.h>
#include <stdlib.h>
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include <string.h>

#include "glib.h"

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tbl_record_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_weixin.h"
#include "openssl/md5.h"

#define  WEIXIN_TCP_PAYLOAD_MAX 1024*1024*50 //  最大50M
#define  REASSEMBLE_DEFAULT   0
#define  REASSEMBLE_ENABLE    1
#define  REASSEMBLE_DISABLE  -1

struct rte_mempool *weixin_parse_mempool;


#define COPY_VALUE(a,ptr,len,release)             \
do                                                \
{                                                 \
    if((a).free && (a).value_len > 0)             \
    {                                             \
        break;                                    \
    }                                             \
    if((a).value==NULL){                          \
        (a).value     = memdup(ptr, len);         \
    }                                             \
    (a).value_len = len;                          \
    if ((len) > 0) {                              \
        (a).free      = release;                  \
    } else {                                      \
        (a).free = NULL;                          \
    }                                             \
}while(0);

/* 专门用于更新rangeend的值 */
#define UPDATE_VALUE(a,ptr,len,release)                                \
do                                                                     \
{                                                                      \
    if((a).value==NULL){                                               \
        (a).value     = memdup(ptr, len);                              \
    }else{                                                             \
        (a).free((a).value);                                           \
        (a).value     = memdup(ptr, len);                              \
    }                                                                  \
    (a).value_len = len;                                               \
    (a).free      = (ptr) ? release : NULL;                            \
}while(0);


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_record_mempool;
static uint16_t   wxf_filter_array[WEIXIN_FILTER_MAX_NUM]={0};

    const char * key_1 = "test_key";

dpi_field_table weixin_array_f[] = {
    DPI_FIELD_D(EM_WX_FIRST_CAPTATE,                  EM_F_TYPE_UINT64,                 "capdate_start"),
    DPI_FIELD_D(EM_WX_LAST_CAPTATE,                   EM_F_TYPE_UINT64,                 "capdate_last"),
    DPI_FIELD_D(EM_WX_WXF_TOTALLEN,                   EM_F_TYPE_UINT32,                 "WXF_totalLen"),
    DPI_FIELD_D(EM_WX_WXF_RESV1,                      EM_F_TYPE_HEX,                    "WXF_resv1"),
    DPI_FIELD_D(EM_WX_WXF_RESV2,                      EM_F_TYPE_HEX,                    "WXF_resv2"),
    DPI_FIELD_D(EM_WX_WXF_RESV3,                      EM_F_TYPE_HEX,                    "WXF_resv3"),
    DPI_FIELD_D(EM_WX_WXF_RESV4,                      EM_F_TYPE_HEX,                    "WXF_resv4"),
    DPI_FIELD_D(EM_WX_WXF_MSGLEN,                     EM_F_TYPE_UINT32,                 "WXF_msgLen"),
    DPI_FIELD_D(EM_WX_WXF_UNKNOWN,                    EM_F_TYPE_STRING,                 "WXF_unknown"),
    DPI_FIELD_D(EM_WX_HTTP_METHOD,                    EM_F_TYPE_STRING,                 "Http-Method"),
    DPI_FIELD_D(EM_WX_HTTP_URI,                       EM_F_TYPE_STRING,                 "Http-Uri"),
    DPI_FIELD_D(EM_WX_HTTP_VERSION,                   EM_F_TYPE_STRING,                 "Http-Version"),
    DPI_FIELD_D(EM_WX_HTTP_CONTENTLENGTH,             EM_F_TYPE_STRING,                 "Http-ContentLength"),
    DPI_FIELD_D(EM_WX_VER,                            EM_F_TYPE_STRING,                 "ver"),
    DPI_FIELD_D(EM_WX_WEIXINNUM,                      EM_F_TYPE_STRING,                 "weixinnum"),
    DPI_FIELD_D(EM_WX_SEQ,                            EM_F_TYPE_STRING,                 "seq"),
    DPI_FIELD_D(EM_WX_CLIENTVERSION,                  EM_F_TYPE_STRING,                 "clientversion"),
    DPI_FIELD_D(EM_WX_CLIENTOSTYPE,                   EM_F_TYPE_STRING,                 "clientostype"),
    DPI_FIELD_D(EM_WX_TOUSER,                         EM_F_TYPE_STRING,                 "touser"),
    DPI_FIELD_D(EM_WX_WXCHATTYPE,                     EM_F_TYPE_STRING,                 "wxchattype"),
    DPI_FIELD_D(EM_WX_NETTYPE,                        EM_F_TYPE_STRING,                 "nettype"),
    DPI_FIELD_D(EM_WX_APPTYPE,                        EM_F_TYPE_STRING,                 "apptype"),
    DPI_FIELD_D(EM_WX_LASTIP,                         EM_F_TYPE_STRING,                 "lastip"),
    DPI_FIELD_D(EM_WX_WXMSGFLAG,                      EM_F_TYPE_STRING,                 "wxmsgflag"),
    DPI_FIELD_D(EM_WX_LOCALNAME,                      EM_F_TYPE_STRING,                 "localname"),
    DPI_FIELD_D(EM_WX_SCENE,                          EM_F_TYPE_STRING,                 "scene"),
    DPI_FIELD_D(EM_WX_URL,                            EM_F_TYPE_STRING,                 "url"),
    DPI_FIELD_D(EM_WX_FILEID,                         EM_F_TYPE_STRING,                 "fileid"),
    DPI_FIELD_D(EM_WX_OLD_FILEID,                     EM_F_TYPE_STRING,                 "old_fileid"),
    DPI_FIELD_D(EM_WX_FILEMD5,                        EM_F_TYPE_STRING,                 "filemd5"),
    DPI_FIELD_D(EM_WX_FILEKEY,                        EM_F_TYPE_STRING,                 "filekey"),
    DPI_FIELD_D(EM_WX_FILETYPE,                       EM_F_TYPE_STRING,                 "filetype"),
    DPI_FIELD_D(EM_WX_FILECRC,                        EM_F_TYPE_STRING,                 "filecrc"),
    DPI_FIELD_D(EM_WX_FILEBITMAP,                     EM_F_TYPE_HEX,                    "filebitmap"),
    DPI_FIELD_D(EM_WX_OFFSET,                         EM_F_TYPE_STRING,                 "offset"),
    DPI_FIELD_D(EM_WX_TOTALSIZE,                      EM_F_TYPE_STRING,                 "totalsize"),
    DPI_FIELD_D(EM_WX_RAWTOTALSIZE,                   EM_F_TYPE_STRING,                 "rawtotalsize"),
    DPI_FIELD_D(EM_WX_BLOCKMD5,                       EM_F_TYPE_STRING,                 "blockmd5"),
    DPI_FIELD_D(EM_WX_RAWFILEMD5,                     EM_F_TYPE_STRING,                 "rawfilemd5"),
    DPI_FIELD_D(EM_WX_FILEDATAMD5,                    EM_F_TYPE_STRING,                 "filedatamd5"),
    DPI_FIELD_D(EM_WX_DATACHECKMD5,                   EM_F_TYPE_STRING,                 "datacheckmd5"),
    DPI_FIELD_D(EM_WX_DATACHECKSUM,                   EM_F_TYPE_STRING,                 "datachecksum"),
    DPI_FIELD_D(EM_WX_FILEURL,                        EM_F_TYPE_STRING,                 "fileurl"),
    DPI_FIELD_D(EM_WX_REFERER_URL,                    EM_F_TYPE_STRING,                 "refererurl"),
    DPI_FIELD_D(EM_WX_COMPRESSTYPE,                   EM_F_TYPE_STRING,                 "compresstype"),
    DPI_FIELD_D(EM_WX_RECVLEN,                        EM_F_TYPE_STRING,                 "recvlen"),
    DPI_FIELD_D(EM_WX_SRCSIZE,                        EM_F_TYPE_STRING,                 "srcsize"),
    DPI_FIELD_D(EM_WX_HASTHUMB,                       EM_F_TYPE_STRING,                 "hasthumb"),
    DPI_FIELD_D(EM_WX_NEEDTHUMBFLAG,                  EM_F_TYPE_STRING,                 "needthumbflag"),
    DPI_FIELD_D(EM_WX_THUMBFLAG,                      EM_F_TYPE_STRING,                 "thumbflag"),
    DPI_FIELD_D(EM_WX_THUMBTOTALSIZE,                 EM_F_TYPE_STRING,                 "thumbtotalsize"),
    DPI_FIELD_D(EM_WX_THUMBCRC,                       EM_F_TYPE_STRING,                 "thumbcrc"),
    DPI_FIELD_D(EM_WX_THUMBDATA,                      EM_F_TYPE_HEX,                    "thumbdata"),
    DPI_FIELD_D(EM_WX_THUMBURL,                       EM_F_TYPE_STRING,                 "thumburl"),
    DPI_FIELD_D(EM_WX_RAWTHUMBSIZE,                   EM_F_TYPE_STRING,                 "rawthumbsize"),
    DPI_FIELD_D(EM_WX_RAWTHUMBMD5,                    EM_F_TYPE_STRING,                 "rawthumbmd5"),
    DPI_FIELD_D(EM_WX_ENCTHUMBCRC,                    EM_F_TYPE_STRING,                 "encthumbcrc"),
    DPI_FIELD_D(EM_WX_HITTYPE,                        EM_F_TYPE_STRING,                 "hittype"),
    DPI_FIELD_D(EM_WX_EXISTFLAG,                      EM_F_TYPE_STRING,                 "existflag"),
    DPI_FIELD_D(EM_WX_ENABLEHIT,                      EM_F_TYPE_STRING,                 "enablehit"),
    DPI_FIELD_D(EM_WX_EXISTANCECHECK,                 EM_F_TYPE_STRING,                 "existancecheck"),
    DPI_FIELD_D(EM_WX_RANGESTART,                     EM_F_TYPE_STRING,                 "rangestart"),
    DPI_FIELD_D(EM_WX_RANGEEND,                       EM_F_TYPE_STRING,                 "rangeend"),
    DPI_FIELD_D(EM_WX_IMGHEIGHT,                      EM_F_TYPE_STRING,                 "imgheight"),
    DPI_FIELD_D(EM_WX_IMGWIDTH,                       EM_F_TYPE_STRING,                 "imgwidth"),
    DPI_FIELD_D(EM_WX_JPEGSCANCOUNT,                  EM_F_TYPE_STRING,                 "jpegscancount"),
    DPI_FIELD_D(EM_WX_JPEGSCANLIST,                   EM_F_TYPE_STRING,                 "jpegscanlist"),
    DPI_FIELD_D(EM_WX_JPEGCRCLIST,                    EM_F_TYPE_STRING,                 "jpegcrclist"),
    DPI_FIELD_D(EM_WX_MIDIMGLEN,                      EM_F_TYPE_STRING,                 "midimglen"),
    DPI_FIELD_D(EM_WX_MIDIMGRAWSIZE,                  EM_F_TYPE_STRING,                 "midimgrawsize"),
    DPI_FIELD_D(EM_WX_MIDIMGTOTALSIZE,                EM_F_TYPE_STRING,                 "midimgtotalsize"),
    DPI_FIELD_D(EM_WX_MIDIMGCHECKSUM,                 EM_F_TYPE_STRING,                 "midimgchecksum"),
    DPI_FIELD_D(EM_WX_MIDIMGMD5,                      EM_F_TYPE_STRING,                 "midimgmd5"),
    DPI_FIELD_D(EM_WX_MIDIMGDATA,                     EM_F_TYPE_STRING,                 "midimgdata"),
    DPI_FIELD_D(EM_WX_RSPPICFORMAT,                   EM_F_TYPE_STRING,                 "rsppicformat"),
    DPI_FIELD_D(EM_WX_SETOFPICFORMAT,                 EM_F_TYPE_STRING,                 "setofpicformat"),
    DPI_FIELD_D(EM_WX_LARGESVIDEO,                    EM_F_TYPE_STRING,                 "largesvideo"),
    DPI_FIELD_D(EM_WX_VIDEOFORMAT,                    EM_F_TYPE_STRING,                 "videoformat"),
    DPI_FIELD_D(EM_WX_SMALLVIDEOFLAG,                 EM_F_TYPE_STRING,                 "smallvideoflag"),
    DPI_FIELD_D(EM_WX_ADVIDEOFLAG,                    EM_F_TYPE_STRING,                 "advideoflag"),
    DPI_FIELD_D(EM_WX_MP4IDENTIFY,                    EM_F_TYPE_STRING,                 "mp4identify"),
    DPI_FIELD_D(EM_WX_DROPRATEFLAG,                   EM_F_TYPE_STRING,                 "droprateflag"),
    DPI_FIELD_D(EM_WX_SOURCEFLAG,                     EM_F_TYPE_STRING,                 "sourceflag"),
    DPI_FIELD_D(EM_WX_AUTHKEY,                        EM_F_TYPE_HEX,                    "authkey"),
    DPI_FIELD_D(EM_WX_SAFEPROTO,                      EM_F_TYPE_STRING,                 "safeproto"),
    DPI_FIELD_D(EM_WX_RSAVER,                         EM_F_TYPE_STRING,                 "rsaver"),
    DPI_FIELD_D(EM_WX_RSAVALUE,                       EM_F_TYPE_HEX,                    "rsavalue"),
    DPI_FIELD_D(EM_WX_NOCHECKAESKEY,                  EM_F_TYPE_STRING,                 "nocheckaeskey"),
    DPI_FIELD_D(EM_WX_CLIENTRSAVER,                   EM_F_TYPE_STRING,                 "clientrsaver"),
    DPI_FIELD_D(EM_WX_CLIENTRSAVAL,                   EM_F_TYPE_HEX,                    "clientrsaval"),
    DPI_FIELD_D(EM_WX_BFRSAVER,                       EM_F_TYPE_HEX,                    "bfrsaver"),
    DPI_FIELD_D(EM_WX_BFRSAVALUE,                     EM_F_TYPE_HEX,                    "bfrsavalue"),
    DPI_FIELD_D(EM_WX_SKEYRESP,                       EM_F_TYPE_STRING,                 "skeyresp"),
    DPI_FIELD_D(EM_WX_SKEYBUF,                        EM_F_TYPE_HEX,                    "skeybuf"),
    DPI_FIELD_D(EM_WX_AESKEY,                         EM_F_TYPE_HEX,                    "aeskey"),
    DPI_FIELD_D(EM_WX_SESSIONBUF,                     EM_F_TYPE_HEX,                    "sessionbuf"),
    DPI_FIELD_D(EM_WX_ISOVERLOAD,                     EM_F_TYPE_STRING,                 "isoverload"),
    DPI_FIELD_D(EM_WX_ISGETCDN,                       EM_F_TYPE_STRING,                 "isgetcdn"),
    DPI_FIELD_D(EM_WX_ISSTOREVIDEO,                   EM_F_TYPE_STRING,                 "isstorevideo"),
    DPI_FIELD_D(EM_WX_ISRETRY,                        EM_F_TYPE_STRING,                 "isretry"),
    DPI_FIELD_D(EM_WX_RETRYCNT,                       EM_F_TYPE_STRING,                 "retrycnt"),
    DPI_FIELD_D(EM_WX_RETRYSEC,                       EM_F_TYPE_STRING,                 "retrysec"),
    DPI_FIELD_D(EM_WX_X_CLIENTIP,                     EM_F_TYPE_STRING,                 "x-ClientIp"),
    DPI_FIELD_D(EM_WX_X_SNSVIDEOFLAG,                 EM_F_TYPE_STRING,                 "X-snsvideoflag"),
    DPI_FIELD_D(EM_WX_X_ENCFLAG,                      EM_F_TYPE_STRING,                 "X-encflag"),
    DPI_FIELD_D(EM_WX_X_ENCLEN,                       EM_F_TYPE_STRING,                 "X-enclen"),
    DPI_FIELD_D(EM_WX_IPSEQ,                          EM_F_TYPE_STRING,                 "ipseq"),
    DPI_FIELD_D(EM_WX_ACCEPTDUPACK,                   EM_F_TYPE_STRING,                 "acceptdupack"),
    DPI_FIELD_D(EM_WX_WXAUTOSTART,                    EM_F_TYPE_STRING,                 "wxautostart"),
    DPI_FIELD_D(EM_WX_SIGNAL,                         EM_F_TYPE_STRING,                 "signal"),
    DPI_FIELD_D(EM_WX_REDIRECT,                       EM_F_TYPE_STRING,                 "redirect"),
    DPI_FIELD_D(EM_WX_REDIRECTFAIL,                   EM_F_TYPE_STRING,                 "redirectfail"),
    DPI_FIELD_D(EM_WX_LASTRETCODE,                    EM_F_TYPE_STRING,                 "lastretcode"),
    DPI_FIELD_D(EM_WX_RETCODE,                        EM_F_TYPE_STRING,                 "retcode"),
    DPI_FIELD_D(EM_WX_RETMSG,                         EM_F_TYPE_STRING,                 "retmsg"),
    DPI_FIELD_D(EM_WX_WXMSGSIGNATURE,                 EM_F_TYPE_STRING,                 "wxmsgsignature"),
    DPI_FIELD_D(EM_WX_ELST,                           EM_F_TYPE_HEX,                    "elst"),
    DPI_FIELD_D(EM_WX_ETL,                            EM_F_TYPE_STRING,                 "etl"),
    DPI_FIELD_D(EM_WX_DYNAMICETL,                     EM_F_TYPE_STRING,                 "dynamicetl"),
    DPI_FIELD_D(EM_WX_SUBSTITUTEFTYPE,                EM_F_TYPE_STRING,                 "substituteftype"),
    DPI_FIELD_D(EM_WX_VIEW,                           EM_F_TYPE_HEX,                    "view"),
    DPI_FIELD_D(EM_WX_FILEDATA,                       EM_F_TYPE_BYTES,                 "filedata"),

    DPI_FIELD_D(EM_WX_FILEID_1,                       EM_F_TYPE_BYTES,                 "fileid_1"),
    DPI_FIELD_D(EM_WX_FILEID_2,                       EM_F_TYPE_BYTES,                 "fileid_2"),
    DPI_FIELD_D(EM_WX_FILEID_3,                       EM_F_TYPE_BYTES,                 "fileid_3"),
    DPI_FIELD_D(EM_WX_FILEID_4,                       EM_F_TYPE_BYTES,                 "fileid_4"),

    DPI_FIELD_D(EM_WX_PATH_MP4,                       EM_F_TYPE_STRING,                 "mp4_path"),
    DPI_FIELD_D(EM_WX_PATH_JPG,                       EM_F_TYPE_STRING,                 "jpg_path"),
    DPI_FIELD_D(EM_WX_MD5_MP4,                        EM_F_TYPE_STRING,                 "mp4_md5"),
    DPI_FIELD_D(EM_WX_MD5_JPG,                        EM_F_TYPE_STRING,                 "jpg_md5"),

    DPI_FIELD_D(EM_WX_PYQ_MUL_URL,                    EM_F_TYPE_STRING,                 "mul_url"),
    DPI_FIELD_D(EM_WX_PYQ_UIN,                        EM_F_TYPE_STRING,                 "uin"),
    DPI_FIELD_D(EM_WX_REFERER_VERSION,                EM_F_TYPE_STRING,                "referer_version"),

    DPI_FIELD_D(EM_WX_U_PROVINCE,                     EM_F_TYPE_STRING,                 "u_province"),
    DPI_FIELD_D(EM_WX_U_CITY,                         EM_F_TYPE_STRING,                 "u_city"),
    DPI_FIELD_D(EM_WX_U_ISP,                          EM_F_TYPE_STRING,                 "u_isp"),
    DPI_FIELD_D(EM_WX_TASKID,                         EM_F_TYPE_STRING,                 "taskid"),
    DPI_FIELD_D(EM_WX_CLI_QUIC_FLAG,                  EM_F_TYPE_STRING,                 "cli-quic-flag"),
    DPI_FIELD_D(EM_WX_DOWNPICFORMAT,                  EM_F_TYPE_STRING,                 "downpicformat"),
    DPI_FIELD_D(EM_WX_VIDEOCDNMSG,                    EM_F_TYPE_STRING,                 "videocdnmsg"),
    DPI_FIELD_D(EM_WX_ISPCODE,                        EM_F_TYPE_STRING,                 "ispcode"),

    DPI_FIELD_D(EM_WX_TP,                             EM_F_TYPE_STRING,                 "tp"),
    DPI_FIELD_D(EM_WX_TOKEN,                          EM_F_TYPE_STRING,                 "token"),
    DPI_FIELD_D(EM_WX_IDX,                            EM_F_TYPE_STRING,                 "idx"),
    DPI_FIELD_D(EM_WX_LENGTH,                         EM_F_TYPE_STRING,                 "length"),
    DPI_FIELD_D(EM_WX_WIDTH,                          EM_F_TYPE_STRING,                 "width"),

    DPI_FIELD_D(EM_WX_USERAGENT,                      EM_F_TYPE_STRING,                 "useragent"),
    DPI_FIELD_D(EM_WX_UNKNOWN_VAL,                    EM_F_TYPE_STRING,                 "unknown_val"),

    DPI_FIELD_D(EM_WX_VALUE_ORIGIN,                   EM_F_TYPE_STRING,                 "data_origin"),
    DPI_FIELD_D(EM_WX_DATA,                           EM_F_TYPE_STRING,                 "data"),
    DPI_FIELD_D(EM_WX_RESV1,                          EM_F_TYPE_STRING,                  "resv_1"),
    DPI_FIELD_D(EM_WX_RESV2,                          EM_F_TYPE_STRING,                 "resv_2"),
    DPI_FIELD_D(EM_WX_RESV3,                          EM_F_TYPE_STRING,                 "resv_3"),
    DPI_FIELD_D(EM_WX_RESV4,                          EM_F_TYPE_UINT32,                 "resv_4"),
    DPI_FIELD_D(EM_WX_RESV5,                          EM_F_TYPE_STRING,                 "resv_5"),
    DPI_FIELD_D(EM_WX_RESV6,                          EM_F_TYPE_STRING,                 "resv_6"),
};

static struct rte_hash_parameters wx_hash = {
    .name = "weixin_field",
    .entries = WX_HASH_SIZE,
    .key_len = MAX_FIELD_LEN,
    .hash_func = rte_jhash,
    .hash_func_init_val = 0,
    .socket_id = 0,
};

struct rte_hash *wx_handle;

static int write_weixin_log(struct flow_info *flow, int direction,  weixin_info_t  *line_info)
{
    return 0;//使用protorecord输出tbl
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;

    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }

    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_WX_MAX;i++)
    {
        switch(weixin_array_f[i].index)
        {
            case EM_WX_LOCALNAME:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;

            case EM_WX_FILEID:
                if(0 == line_info[i].value_len || NULL == line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0)
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;
            case EM_WX_THUMBDATA:
			case EM_WX_MIDIMGDATA:
            case EM_WX_FILEDATA:
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_PYQ_MUL_URL:
                if(line_info[i].value_len>7){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_WX_PYQ_UIN:
                if(line_info[i].value_len>4){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            default:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, weixin_array_f[i].type, line_info[i].value, line_info[i].value_len);
                break;
        }

        // if(line_info[i].free && line_info[i].value)
        // {
        //     line_info[i].free((void*)line_info[i].value);
        //     line_info[i].value = NULL;
        //     line_info[i].free  = NULL;
        // }

    }

    log_ptr->type         = TBL_LOG_WEIXIN;
    log_ptr->len          = idx;
    log_ptr->tid          = flow->thread_id;

    if(tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}


void free_info_ref(weixin_info_t *line_info)
{
    for(int i=0; i<EM_WX_MAX; i++)
    {
        if(line_info[i].free && line_info[i].value)
        {
        free((void*)line_info[i].value);
            line_info[i].value = NULL;
            line_info[i].free  = NULL;
        }
    }
}


static
void _copy_filedata_cut(const uint8_t * data, const uint32_t data_len, weixin_info_t *line_info)
{
    if (data == NULL || line_info == NULL) return;
    uint8_t cut_len     = g_config.wx_filedata_bytes;
    cut_len = cut_len > data_len ? data_len : cut_len;
    char    val[256] = { 0 };
    // strncpy(val, data, cut_len);
    memcpy(val, data, cut_len);
    weixin_info_t elem;
    elem.value = malloc(2 * cut_len + 1);
    bintohex(val, cut_len, (char *)elem.value, 2 * cut_len + 1);
    elem.value_len = cut_len * 2;
    *((char *)(elem.value) + elem.value_len) = 0;
    UPDATE_VALUE(line_info[EM_WX_RESV1], elem.value, elem.value_len, free);
    free(elem.value);
    elem.free = free;
}

uint32_t parse_weixin_packet_info(struct flow_info *flow, int C2S, const uint8_t *wx_payload, const uint32_t payload_len,
        weixin_info_t  *line_info)
{
    if((payload_len == 0) || (wx_payload == NULL))
        return WX_ERROR;

    int32_t  wx_payload_len=0;
    uint32_t field_len=0;
    uint32_t value_len=0;

    uint32_t offset = 0;
    weixin_field_t *wx_field=NULL;
    char     tmp_buff[MAX_FIELD_LEN]={0};
    ST_weixin_file_session *session;

    session        = (ST_weixin_file_session*)flow->app_session;

    wx_payload_len  = payload_len;
    while (wx_payload_len>0) {
        if( offset + WEIXIN_COMMON_LENGTH > payload_len){
            goto end;
        }
        field_len = ntohl(get_uint32_t(wx_payload, offset));
        //if(field_len>MAX_FIELD_LEN-1 || field_len==0){
        if(field_len>256 || field_len>payload_len-offset){
            goto end;
        }

        wx_payload_len -= WEIXIN_COMMON_LENGTH;
        wx_payload_len -= field_len;
        offset += WEIXIN_COMMON_LENGTH;

        if(0==field_len){
            goto end;
        }
        /*strncpy会在尾部填充"\0"*/
        strncpy(tmp_buff,(char*)(wx_payload+offset),sizeof(tmp_buff));

        int pos = rte_hash_lookup_data(wx_handle, tmp_buff, (void **)&wx_field);
        if(pos>0){
            offset += field_len;
            if( offset + WEIXIN_COMMON_LENGTH > payload_len || offset > payload_len){
                goto end;
            }

            value_len =  ntohl(get_uint32_t(wx_payload, offset));

            if (wx_field->index == EM_WX_FILEDATA) {
              _copy_filedata_cut(wx_payload + offset, value_len, line_info);
            }
            offset += WEIXIN_COMMON_LENGTH;
            if(value_len>payload_len-offset){
                goto end;
            }

            //首次记录fileid, 不区分上下行
            if((EM_WX_FILEID == wx_field->index) && (NULL == line_info[EM_WX_FILEID].value))
            {
                COPY_VALUE(line_info[wx_field->index], wx_payload + offset, value_len, free);
                session->fileid_direction  = C2S; // 记录当前fileid的方向
            }
            else
            //文件被转发，记录服务器返回的新fileid, 将原先的fileid置为old_fileid
            //情景限定: 必须已经存在 C2S的fileid,且当前为S2C的fileid
            if((EM_WX_FILEID == wx_field->index)                 // 当前 字段类型必须为 EM_WX_FILEID
                && NULL != line_info[EM_WX_FILEID].value         // session 解析已存在  EM_WX_FILEID
                && NULL != line_info[EM_WX_FILEKEY].value        // session 解析已存在  EM_WX_FILEKEY
                && SERVER_TO_CLIENT == C2S                       // 当前报文的方向必须为服务器的返回
                && CLIENT_TO_SERVER == session->fileid_direction // session 已存在 client to server 的 EM_WX_FILEID
                && 0 == session->fileid_server_done
                )
            {
                session->fileid_server_done= 1;              // 标记已经接受到服务器的fileid, 避免fileid翻转
                COPY_VALUE(line_info[EM_WX_OLD_FILEID], wx_payload + offset, value_len, free);

                //交换 EM_WX_FILEID  EM_WX_OLD_FILEID， EM_WX_OLD_FILEID内容作为真正的fileid
                uint32_t      value_len                    = 0;
                uint8_t       *value                       = NULL;
                void          *release                     = NULL;

                value                                      = line_info[EM_WX_OLD_FILEID].value;
                value_len                                  = line_info[EM_WX_OLD_FILEID].value_len;
                release                                    = line_info[EM_WX_OLD_FILEID].free;

                line_info[EM_WX_OLD_FILEID].value          = line_info[EM_WX_FILEID].value;
                line_info[EM_WX_OLD_FILEID].value_len      = line_info[EM_WX_FILEID].value_len;
                line_info[EM_WX_OLD_FILEID].free           = line_info[EM_WX_FILEID].free;

                line_info[EM_WX_FILEID].value              = value;
                line_info[EM_WX_FILEID].value_len          = value_len;
                line_info[EM_WX_FILEID].free               = release;

            }
            else
            // 不是 EM_WX_FILEID， 默认处理
            if(EM_WX_RANGEEND == wx_field->index )
            {
                if(value_len>WEIFIN_RANGEEND_MAX_LEN){
                    goto end;
                }
                if(line_info[wx_field->index].value!=NULL){
                    char tmp_buff[64]={0};
                    uint32_t safe_len=value_len+1;
                    if(value_len+1>64){
                        safe_len=64;
                    }
                    strncpy(tmp_buff, (const char *)wx_payload + offset,safe_len);
                    int iold=atoi((char *)line_info[wx_field->index].value);
                    int inew=atoi(tmp_buff);
                    //printf("[=====]iold=%d,inew=%d\n",iold,inew);
                    if(inew>iold){
                        UPDATE_VALUE(line_info[wx_field->index], wx_payload + offset, value_len, free);
                    }
                }else{
                    UPDATE_VALUE(line_info[wx_field->index], wx_payload + offset, value_len, free);
                }
            }
            else
            {
                if( wx_field->index!=EM_WX_THUMBDATA && wx_field->index!=EM_WX_FILEDATA && wx_field->index!=EM_WX_MIDIMGDATA){
                    COPY_VALUE(line_info[wx_field->index], wx_payload + offset, value_len, free);
                }
            }

            offset += value_len;
            if(offset>payload_len){
                goto end;
            }
        }
        else
        {
            offset += field_len;
            if( offset + WEIXIN_COMMON_LENGTH > payload_len){
                goto end;
            }
            value_len =  ntohl(get_uint32_t(wx_payload, offset));
            offset += WEIXIN_COMMON_LENGTH;
            if(value_len>payload_len-offset){
                goto end;
            }
            offset += value_len;
            if(offset>payload_len){
                goto end;
            }

        }
        wx_payload_len -= WEIXIN_COMMON_LENGTH;
        wx_payload_len -= value_len;

        value_len=0;
        field_len=0;
    }

end:
    return offset;
}


int write_weixin_log_pyq(struct flow_info *flow, int direction,  weixin_info_t  *line_info)
{
    return 0;//使用protorecord输出tbl
    int idx = 0;
    struct tbl_log *log_ptr;
    int i,j;

    if(rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return PKT_OK;
    }



    write_tbl_log_common(flow, direction, log_ptr->log_content, &idx, TBL_LOG_MAX_LEN);

    for(i=0;i<EM_WX_MAX;i++)
    {
        switch(weixin_array_f[i].index)
        {

            case EM_WX_LOCALNAME:
                if(0==line_info[i].value_len || NULL==line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0 )
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;

            case EM_WX_FILEID:
                if(0==line_info[i].value_len || NULL==line_info[i].value)
                {
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                    break;
                }

                if(isUTF8((const char *)line_info[i].value, line_info[i].value_len)>0 )
                {
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }
                else
                {
                    write_multi_num_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, line_info[i].value, line_info[i].value_len);
                }
                break;
            case EM_WX_THUMBDATA:
            case EM_WX_MIDIMGDATA:
            case EM_WX_FILEDATA:
                write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                break;
            case EM_WX_PYQ_MUL_URL:
                if(line_info[i].value_len>7){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_WX_PYQ_UIN:
                if(line_info[i].value_len>4){
                    write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, (const char *)line_info[i].value, line_info[i].value_len);
                }else{
                    write_n_empty_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_WX_VALUE_ORIGIN:
                write_one_str_reconds(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, "wxf", strlen("wxf"));
                break;
            default:
                write_coupler_log(log_ptr->log_content, &idx, TBL_LOG_MAX_LEN, weixin_array_f[i].type, line_info[i].value, line_info[i].value_len);
                break;

        }
        if(line_info[i].free && line_info[i].value)
        {
            line_info[i].free((void*)line_info[i].value);
            line_info[i].value = NULL;
            line_info[i].free  = NULL;
        }

    }

    log_ptr->type     = TBL_LOG_WEIXIN_PYQ;
    log_ptr->len      = idx;
    log_ptr->tid      = flow->thread_id;

    if (tbl_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 0;
}



static int extract_weixin_pyq_uin(weixin_info_t  *line_info)
{
    if(line_info[EM_WX_PYQ_UIN].value!=NULL){
        return 0;
    }
    if(line_info[EM_WX_REFERER_URL].value_len<=0 || NULL==line_info[EM_WX_REFERER_URL].value){
        return 0;
    }

    const uint8_t *p=NULL;
    const uint8_t *q=NULL;
    p=memmem(line_info[EM_WX_REFERER_URL].value,line_info[EM_WX_REFERER_URL].value_len,"uin=",strlen("uin="));
    if(p){
        int left_len=p-line_info[EM_WX_REFERER_URL].value;
        q=memmem(p,left_len,"&",1);
        if(q && q-p>4 && q-p<15){
            COPY_VALUE(line_info[EM_WX_PYQ_UIN], p+4, q-p-4, free);
        }
    }

    return 1;
}

const char *dissect_wx_url_key(const char *data, int len,const char *key, int *value_len)
{
    if(NULL==data || len<=0 || NULL==key){
        *value_len=0;
        return NULL;
    }

    const char *value_start = NULL;
    const char *value_end   = NULL;
    const char *key_start   = NULL;

    int  key_offset   = 0;

    key_start=strncasestr(data,key, len);
    if(NULL==key_start){
        *value_len=0;
        return NULL;
    }

    key_offset = key_start - data;
    int left_len=len-key_offset-strlen(key);
    if(left_len<=0){
        *value_len=0;
        return NULL;
    }
    value_start=&data[key_offset+strlen(key)];
    value_end=memmem(value_start,left_len,"&",1);
    if(NULL==value_end){
        *value_len=left_len;
    }else{
        *value_len=value_end-value_start;
    }

    return value_start;
}


int dissect_referer_key_value(weixin_info_t  *line_info)
{

    if(line_info[EM_WX_REFERER_URL].value_len<=0 || NULL==line_info[EM_WX_REFERER_URL].value){
        return 0;
    }

    /* 提取version */
    const char *value=NULL;
    int  value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_REFERER_URL].value,
                             (int)line_info[EM_WX_REFERER_URL].value_len,
                             "version=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_REFERER_VERSION], value, value_len, free);
    }

    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_REFERER_URL].value,
                             (int)line_info[EM_WX_REFERER_URL].value_len,
                             "uin=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_PYQ_UIN], value, value_len, free);
    }

    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_REFERER_URL].value,
                             (int)line_info[EM_WX_REFERER_URL].value_len,
                             "nettype=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_NETTYPE], value, value_len, free);
    }

    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_REFERER_URL].value,
                             (int)line_info[EM_WX_REFERER_URL].value_len,
                             "signal=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_SIGNAL], value, value_len, free);
    }

    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_REFERER_URL].value,
                             (int)line_info[EM_WX_REFERER_URL].value_len,
                             "scene=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_SCENE], value, value_len, free);
    }

    return 1;
}

int dissect_fileurl_key_value(weixin_info_t  *line_info)
{
    if(line_info[EM_WX_FILEURL].value_len<=0 || NULL==line_info[EM_WX_FILEURL].value){
        return 0;
    }

    /* 提取tp */
    const char *value=NULL;
    int  value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "tp=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_TP], value, value_len, free);
    }

    /* 提取token */
    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "token=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_TOKEN], value, value_len, free);
    }

    /* 提取idx */
    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "idx=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_IDX], value, value_len, free);
    }

    /* 提取length */
    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "length=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_LENGTH], value, value_len, free);
    }

    /* 提取width*/
    value=NULL;
    value_len=0;
    value=dissect_wx_url_key((const char*)line_info[EM_WX_FILEURL].value,
                             (int)line_info[EM_WX_FILEURL].value_len,
                             "width=",&value_len);
    if(value){
        COPY_VALUE(line_info[EM_WX_WIDTH], value, value_len, free);
    }


    return 0;
}



static int parse_weixin_pyq_packet_info(const uint8_t *wx_payload, const uint16_t payload_len, weixin_info_t  *line_info)
{
    if ((payload_len == 0) || payload_len <10 || payload_len>1500  || (wx_payload == NULL))
    {
        return WX_ERROR;
    }

    uint16_t len_tmp=0,spare_len=0;
    uint16_t url_1=0,url_2=0,version_start=0;
    char buff[1500]={0};
    char  *p=NULL,*q=NULL;
    strncpy(buff,(const char*)wx_payload,sizeof(buff));
    p=strcasestr(buff,"http");
    if(p)
    {
        url_1=p-&buff[0];
        q=strcasestr(p+1,"http");
        if(q)
        {
            if(q-p-2>0){
                COPY_VALUE(line_info[EM_WX_FILEURL], wx_payload+url_1, q-p-2, free);
            }

            url_2=q-&buff[0];
            if(url_2<=1){
                return WX_OK;
            }
            uint8_t referer_len=get_uint8_t(wx_payload+url_2-1, 0);
            if(referer_len>8 && referer_len<=payload_len-url_2){
                COPY_VALUE(line_info[EM_WX_REFERER_URL], wx_payload+url_2, referer_len, free);
            }else{
                return WX_OK;
            }
            len_tmp=url_2+line_info[EM_WX_REFERER_URL].value_len;
            if(payload_len-len_tmp>4)
            {
                if(get_uint8_t(wx_payload+len_tmp+3,0)<=payload_len-len_tmp-4){
                    COPY_VALUE(line_info[EM_WX_CLIENTOSTYPE], wx_payload+len_tmp+4, get_uint8_t(wx_payload+len_tmp+3,0), free);
                }else{
                    return WX_OK;
                }

                spare_len=payload_len-len_tmp-4-line_info[EM_WX_CLIENTOSTYPE].value_len;
                if(spare_len>2 && (get_uint8_t(wx_payload+version_start-1,0)<=payload_len-version_start))
                {
                    version_start=payload_len-spare_len+2;
                    if(version_start>0){
                        COPY_VALUE(line_info[EM_WX_WXF_UNKNOWN], wx_payload+version_start, get_uint8_t(wx_payload+version_start-1,0), free);
                    }
                }
            }
        }
        else
        {
            q=strstr(p,"\"");
            if(q)
            {
                if(q-p-1>0){
                    COPY_VALUE(line_info[EM_WX_FILEURL], wx_payload+url_1, q-p-1, free);
                }
                len_tmp=q-&buff[0];
                if(payload_len-len_tmp>4)
                {
                    if(get_uint8_t(wx_payload+len_tmp+3,0)<=payload_len-len_tmp-4){
                        COPY_VALUE(line_info[EM_WX_CLIENTOSTYPE],wx_payload+len_tmp+4, get_uint8_t(wx_payload+len_tmp+3,0), free);
                    }else{
                        return WX_OK;
                    }
                    spare_len=payload_len-len_tmp-4-line_info[EM_WX_CLIENTOSTYPE].value_len;
                    if(spare_len>2 && (get_uint8_t(wx_payload+version_start-1,0)<=payload_len-version_start))
                    {
                        version_start=payload_len-spare_len+2;
                        if(version_start>0){
                            COPY_VALUE(line_info[EM_WX_WXF_UNKNOWN], wx_payload+version_start, get_uint8_t(wx_payload+version_start-1,0), free);
                        }
                    }
                }
            }
        }
    }
    return WX_OK;
}


static int liu_parse_weixin_pyq_packet_info(const uint8_t *wx_payload, const uint16_t payload_len, weixin_info_t  *line_info)
{
    if (payload_len <48 || wx_payload == NULL)
    {
        return WX_ERROR;
    }

    const char  *p=NULL;
    int offset=0;
    p=memmem((const char *)wx_payload,payload_len,"http", 4);
    if(NULL==p){
        return WX_ERROR;
    }

    /********************************************************
     *                   开始提取fileurl
     ********************************************************/
    /* 由于头长度不确定，只能通过关键词，找到url开始问题 */
    int head_len=0;
    head_len=p-(const char *)wx_payload;
    if(head_len<3){
        return WX_ERROR;
    }

    if(wx_payload[head_len-3]!=0x12){/* fileurl类型标识*/
        return WX_ERROR;
    }
    offset+=head_len;

    /* 获取url长度 */
    int url_len=0;
    url_len=wx_payload[head_len-2] + 0x80*(wx_payload[head_len-1]-0x01);
    offset+=url_len;
    if(url_len==0 || offset>payload_len){
        return WX_ERROR;
    }
    /* 获取url字符串值 */
    COPY_VALUE(line_info[EM_WX_FILEURL], &wx_payload[head_len], url_len, free);



    /********************************************************
     *                   开始提取referer_url
     ********************************************************/
    /*  接下来应该是referer，判断剩余长度是否够referer，
     *  1byte(是否为referer类型)+1byte(长度)+8bytes("https://"referer头)=10bytes
     */
    if(offset+10>=payload_len){
        return WX_ERROR;
    }

    /* 0x1a标识是referer类型 */
    if(wx_payload[offset]!=0x1a){
        return WX_ERROR;
    }
    offset+=1;

    int referer_len=0;
    referer_len=get_uint8_t(wx_payload, offset);
    offset+=1;
    if(offset+referer_len>payload_len){
        return WX_ERROR;
    }
    if(strncasecmp((const char *)&wx_payload[offset],"http",4)!=0){
        return WX_ERROR;
    }
    COPY_VALUE(line_info[EM_WX_REFERER_URL], &wx_payload[offset], referer_len, free);
    offset+=referer_len;

    /********************************************************
     *                   开始提取user-agent
     ********************************************************/
    if(offset+5>payload_len){
        return WX_ERROR;
    }

    if( 0x22!=wx_payload[offset]   ||
        0x00!=wx_payload[offset+1] ||
        0x2a!=wx_payload[offset+2]){
        return WX_ERROR;
    }

    offset+=3;

    int user_agent_len=0;
    user_agent_len=get_uint8_t(wx_payload, offset);
    if(user_agent_len==0||offset+user_agent_len>payload_len){
        return WX_ERROR;
    }
    offset+=1;
    COPY_VALUE(line_info[EM_WX_CLIENTOSTYPE], &wx_payload[offset], user_agent_len, free);
    offset+=user_agent_len;

    /********************************************************
     *                   开始提取最后的unknown数字串
     ********************************************************/
    if(offset+2>=payload_len){
        return WX_ERROR;
    }
    offset+=1;
    int last_len=0;
    last_len=get_uint8_t(wx_payload, offset);
    offset+=1;
    if(offset+last_len>payload_len){
        return WX_ERROR;
    }
    COPY_VALUE(line_info[EM_WX_WXF_UNKNOWN], &wx_payload[offset], last_len, free);

    return WX_OK;
}

static int parse_weixin_pyq_mulurl_info(const uint8_t *wx_payload, const uint16_t payload_len, weixin_info_t  *line_info)
{
    if ((payload_len == 0) || payload_len <10 || (wx_payload == NULL))
    {
        return WX_ERROR;
    }

    uint16_t len_tmp=0,spare_len=0;
    uint16_t url_1=0,url_2=0,version_start=0;
    const uint8_t  *p=NULL,*q=NULL;
    uint16_t  check_len=payload_len;
    if(check_len>1500){
        check_len=1500;
    }

    uint16_t ref_start=0;
    uint16_t mult_start=0;
    p=memmem(wx_payload,check_len,"http",strlen("http"));
    if(p)
    {
        ref_start=p-wx_payload;
        if(ref_start<=1){
            return WX_OK;
        }
        uint8_t referer_len=get_uint8_t(wx_payload+ref_start-1, 0);
        if(referer_len>8 && referer_len<=payload_len-ref_start){
            COPY_VALUE(line_info[EM_WX_REFERER_URL], wx_payload+ref_start, referer_len, free);
        }else{
            return WX_OK;
        }
        extract_weixin_pyq_uin(line_info);
        //dissect_referer_key_value(line_info);
        len_tmp=ref_start+line_info[EM_WX_REFERER_URL].value_len;
        if(payload_len>(len_tmp+4))
        {
            if(get_uint8_t(wx_payload+len_tmp+3,0)<=payload_len-len_tmp-4){
                COPY_VALUE(line_info[EM_WX_CLIENTOSTYPE], wx_payload+len_tmp+4, get_uint8_t(wx_payload+len_tmp+3,0), free);
            }else{
                return WX_OK;
            }

            spare_len=payload_len-len_tmp-4-line_info[EM_WX_CLIENTOSTYPE].value_len;
            if(spare_len>2)
            {
                version_start=payload_len-spare_len+2;
                if(version_start>0 && (get_uint8_t(wx_payload+version_start-1,0)<=payload_len-version_start)){
                    COPY_VALUE(line_info[EM_WX_WXF_UNKNOWN], wx_payload+version_start, get_uint8_t(wx_payload+version_start-1,0), free);
                }
            }
        }

        uint16_t offset=0;
        offset=len_tmp;
        uint8_t first_flag=0;
        while(payload_len>offset){
            q=memmem(wx_payload+offset,payload_len-offset,"http",strlen("http"));
            if(q!=NULL){
                if(0==first_flag){
                    first_flag=1;
                    mult_start=q-wx_payload;
                    if(0==mult_start || mult_start>=payload_len-offset){
                        break;
                    }
                    offset=mult_start;
                }
                uint16_t http_len=get_uint8_t(q-2, 0);
                if(http_len==0 || http_len>= payload_len-offset){
                    break;
                }
                offset+=http_len;
                q=NULL;
            }else{
                int flag=0;
                COPY_VALUE(line_info[EM_WX_PYQ_MUL_URL], wx_payload+mult_start, offset-mult_start, free);
                if(line_info[EM_WX_PYQ_MUL_URL].value!=NULL){
                    uint32_t i=0;
                    for(i=0;i<line_info[EM_WX_PYQ_MUL_URL].value_len;i++){
                        if(0==flag && line_info[EM_WX_PYQ_MUL_URL].value[i]==0x10){
                            flag++;
                        }
                        if(flag>0 && flag<8){
                            line_info[EM_WX_PYQ_MUL_URL].value[i]=' ';
                            flag++;
                        }
                        if(flag==8){
                            flag=0;
                        }
                    }
                }
                break;
            }
        }
    }

    return WX_OK;
}


static void identify_weixin(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_WEIXIN] == 0)
    {
        return;
    }

    const uint8_t *p=NULL;
    char  buff[CHECK_HTTP_HEADER_LEN]={0};
    uint16_t index=0;
    uint32_t first_len=0;

    /* identify ab + head lklv*/
    if(payload[0]==0xab && payload_len > WEIXIN_NOTHTTP_HEADER_LEN*2)
    {
        index = WEIXIN_NOTHTTP_HEADER_LEN;
        first_len=get_uint32_ntohl(payload+index, 0);
        if(first_len==3)
        {
            if(0 == memcmp(payload+index+4,"ver",3) || 0 == memcmp(payload+index+4,"seq",3))
            {
                flow->real_protocol_id = PROTOCOL_WEIXIN;
                return;
            }
        }
    }


    /* identify http +lklv */
    if(payload_len>CHECK_HTTP_HEADER_LEN)
    {
        first_len=get_uint32_ntohl(payload, 0);
        if(3 == first_len)
        {
            if(0 == memcmp(payload+4,"ver",3) || 0 == memcmp(payload+4,"seq",3))
            {
                flow->real_protocol_id = PROTOCOL_WEIXIN;
                return;
            }
        }

        /* identify lklv */
        snprintf(buff,CHECK_HTTP_HEADER_LEN,"%s",payload);
        if(NULL != strcasestr(buff,"HTTP"))
        {
            int check_len=1500;
            if(check_len>payload_len){
                check_len=payload_len;
            }
            p=memmem(payload,check_len,"\r\n\r\n",4);
            if(p==NULL)
            {
                return;
            }
            index=p-payload+8;  /* \r\n\r\n + l 8bytes */
            if(payload_len>index+3 && get_uint32_ntohl(p+4, 0)==3){
                if(memcmp(payload+index,"ver",3) == 0){
                    flow->real_protocol_id = PROTOCOL_WEIXIN;
                    return;
                }
            }
        }
    }

    /* identify ab + pyq */
    if(payload[0]==0xab && payload_len > WEIXIN_NOTHTTP_HEADER_LEN*2 && payload[25]==0x08 && payload[26]==0x01 && payload[27]==0x10){
        flow->real_protocol_id = PROTOCOL_WEIXIN;
    }

    return;
}


int weixin_process_fileid(weixin_info_t *line_info)
{
    int fileid_1_len = g_config.wxf_fileid_part1_byte;
    int fileid_2_len = g_config.wxf_fileid_part2_byte;

    const uint8_t *phead;
    const uint8_t *ptail;
    uint8_t       *decode=NULL;
    const uint8_t *pend;
    const uint8_t *realeed;
    const uint8_t *fileid_3_p;
    int            fileid_3_l;
    int            len;

    const char    *split;
    const char    *split_list[] =
    {
        "0204",
        "0202",
        "0201",
        NULL  ,
    };
    const char    *split_lis_last_tlv[] =
    {
        "0400",
        "0405",
        NULL  ,
    };

    if(NULL == line_info || NULL==line_info[EM_WX_FILEID].value)
    {
        return -1;
    }

    if(line_info[EM_WX_FILEID].value_len <= 100 || line_info[EM_WX_FILEID].value_len >= 400)
    {
        return -1;
    }

    if(1 != isHEX((const char*)line_info[EM_WX_FILEID].value, line_info[EM_WX_FILEID].value_len))
    {
        return -1;
    }

    /**** part1 fileid type ? ****/
    // COPY_VALUE(line_info[EM_WX_FILEID_1], line_info[EM_WX_FILEID].value, fileid_1_len,  free);


    /**** part2 fileid session ? ****/
    COPY_VALUE(line_info[EM_WX_FILEID_2], line_info[EM_WX_FILEID].value + fileid_1_len, fileid_2_len,  free);

    /**** find the split ****/
    fileid_3_p = line_info[EM_WX_FILEID].value + fileid_1_len+fileid_2_len;                 // set head
    phead      = fileid_3_p;                                                              // for exceptional
    for(int i  = 0; NULL != split_list[i]; i++)
    {
        int find   = 0;                                                                   // set flag
        split      = split_list[i];                                                       // set split
        phead      = fileid_3_p;                                                          // reset tmp_head
        len        = line_info[EM_WX_FILEID].value_len - fileid_1_len - fileid_2_len;     // reset tmp_headlen

        if(len > 0)
        {
            ptail = memmem(phead + 1, len - 1, split, strlen(split));
            if(NULL == ptail)
            {
                continue;
            }
            phead = ptail;
            find  = 1;
        }

        if(1 == find)
        {
            break;
        }
    }
    fileid_3_l = phead - fileid_3_p;

    /**** part3 file key ****/
    decode = malloc(1024);
    if(NULL == decode){return -1;}
    memset(decode, 0, DPI_COMMON_BUFF_LEN);

    len = hextobin((const char*)fileid_3_p, fileid_3_l, (char *)decode, 1024);
    if(len > 0)
    {
        COPY_VALUE(line_info[EM_WX_FILEID_3], decode, len, free);
    }
    free(decode);
    decode = NULL;

    /**** part4 fileid end ****/
    len = line_info[EM_WX_FILEID].value_len - fileid_1_len - fileid_2_len - fileid_3_l;
    if (len > 0) {
        COPY_VALUE(line_info[EM_WX_FILEID_4], phead, len, free);
    }
    //从fileid session中取得IP
    char ip_str[128] = {0};
    char str[9];
    strncpy(str, (const char *)(fileid_3_p - 24), 8);
    uint32_t num = strtoul(str, NULL, 16);
    uint8_t *bytes = (uint8_t *)&num;
    snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d", bytes[0], bytes[1],
        bytes[2], bytes[3]);

    COPY_VALUE(line_info[EM_WX_FILEID_1], ip_str, strlen(ip_str), free);
    //从fileid session中取得时间戳
    const uint8_t *pTimestamp = NULL;
    pTimestamp = fileid_3_p - 12;
    char timestamp_hex[9] = {0};
    strncpy(timestamp_hex, (const char *)fileid_3_p - 12, 8);
    char timestamp_str[11] = {0};
    sprintf(
        timestamp_str, "%lu", strtoul((const char *)timestamp_hex, NULL, 16));
    COPY_VALUE(
        line_info[EM_WX_RESV5], timestamp_str, strlen(timestamp_str), free);

    /**   从part4中取得场景描述**/
    if (memmem(phead, 4, split_list[0], strlen(split_list[0])) || len > 12) {
        uint8_t str[128] = {0};
        strcat((char *)str, "0x");
        strncat((char *)str, (const char *)(phead + 4), 8);
        strcat((char *)str, ";");
        // 根据fileid最后一个TLV的情况可以区分图片缩略图和APP分享
        for (int i = 0; NULL != split_lis_last_tlv[i]; i++) {
            split = split_lis_last_tlv[i];  // set split
            ptail = memmem(phead, len, split, strlen(split));
            if (NULL == ptail) {
                continue;
            }
            phead = ptail;
            break;
        }
        strcat((char *)str, "0x");
        strncat((char *)str, (const char *)phead, 4);
        COPY_VALUE(line_info[EM_WX_RESV6], str, strlen((char*)str), free);
    }
    return 0;
}

static int
is_weixin_pyq_mp4_upload(const uint8_t *payload, const uint16_t payload_len, weixin_info_t* line_info)
{
    int         weight   = 0;
    const char *str_type = "20202";

    if(payload_len >= line_info[EM_WX_WXF_MSGLEN].value_len)
    {
        return 0; // 一个IP报文是不可能直接承载一个视频
    }

    if(line_info[EM_WX_FILETYPE].value_len < (long)strlen(str_type))
    {
        return 0; // 为FILETYP为20202 铺垫
    }

    if(memmem(line_info[EM_WX_FILETYPE].value, line_info[EM_WX_FILETYPE].value_len, str_type, strlen(str_type))) // 20202 是视频
    {
        weight++;
    }

    if(line_info[EM_WX_LOCALNAME].value_len > 3 && line_info[EM_WX_LOCALNAME].value_len < 1024)
    {
        weight++; // 存在 localname长度大于3小于1024
    }

    if(weight >= 2)
    {
        return 1; // 确实是 朋友圈正在上传视频的动作
    }

    return 0;  // 这不是 朋友圈正在上传视频的动作
}


static int
is_weixin_pyq_jpg_upload(const uint8_t *payload, const uint16_t payload_len, weixin_info_t* line_info)
{
    int         weight   = 0;
    const char *str_type = "20201";
    const char * str_type_2 = "20302";
    if(payload_len >= line_info[EM_WX_WXF_MSGLEN].value_len)
    {
        return 0; // 一个IP报文是不可能直接承载一个视频
    }

    if(line_info[EM_WX_FILETYPE].value_len < (long)strlen(str_type))
    {
        return 0; // 为FILETYP为20202 铺垫
    }

    if(memmem(line_info[EM_WX_FILETYPE].value, line_info[EM_WX_FILETYPE].value_len, str_type, strlen(str_type)) ||
      memmem(line_info[EM_WX_FILETYPE].value, line_info[EM_WX_FILETYPE].value_len, str_type_2, strlen(str_type_2))) // 20202 是视频
    {
        weight++;
    }

    if(line_info[EM_WX_LOCALNAME].value_len > 3 && line_info[EM_WX_LOCALNAME].value_len < 1024)
    {
        weight++; // 存在 localname长度大于3小于1024
    }

    if(weight >= 2)
    {
        return 1; // 确实是 朋友圈正在上传图片的动作
    }

    return 0;  // 这不是 朋友圈正在上传视频的动作
}


static int
set_mp4_info(weixin_info_t *line_info, ST_weixin_file_session* session)
{
    struct timeval tv;
    char filename[1024];
    char buff    [128];

    if(NULL == line_info || NULL == session)
    {
        return -1;
    }

    gettimeofday(&tv, NULL);

    snprintf(session->jpg.flag, sizeof(session->jpg.flag), "thumbdata");
    snprintf(session->jpg.path, sizeof(session->jpg.path), "%s_%06ld_%s_%03d", dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, "weixin_pyq", session->tid);

    snprintf(session->mp4.flag, sizeof(session->mp4.flag), "filedata");
    snprintf(session->mp4.path, sizeof(session->mp4.path), "%s_%06ld_%s_%03d", dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, "weixin_pyq", session->tid);

    return 0;
}

static int
set_jpg_info(weixin_info_t *line_info, ST_weixin_file_session* session)
{
    struct timeval tv;
    char filename[1024];
    char buff    [128];

    if(NULL == line_info || NULL == session)
    {
        return -1;
    }

    gettimeofday(&tv, NULL);

    snprintf(session->jpg.flag, sizeof(session->jpg.flag), "filedata");
    snprintf(session->jpg.path, sizeof(session->jpg.path), "%s_%06ld_%s_%03d", dpi_now(&tv.tv_sec, buff, sizeof(buff)), tv.tv_usec, "weixin_pyq", session->tid);
    return 0;
}

// wx data
static
int is_weixin_data(struct flow_info *flow, int C2S, const uint8_t *payload, const uint32_t payload_len, weixin_info_t * line_info)
{

    // 过滤上行流量
    if (C2S == 1) {
        return -1;
    }
    uint16_t idenity = get_uint16_ntohs(payload, 5);
    if (idenity != WX_DATA_IDENTIFY) {
        return -1;
    }

    if (line_info[EM_WX_RANGESTART].value == NULL
      || line_info[EM_WX_RANGEEND].value == NULL
      || line_info[EM_WX_TOTALSIZE].value == NULL ) {
        return -1;
    }

    if (line_info[EM_WX_X_ENCFLAG].value == NULL) {
        return -1;
    }
    int8_t encflag = atoi((char *)line_info[EM_WX_X_ENCFLAG].value);
    if (encflag != 0) {
        return -1;
    }

    return 1;
}


// 拷贝待创建文件属性
static
void wxf_copy_attr(WxDataInfo * value, weixin_info_t * info)
{
  // value->cur_rangestart = atoi((char *)line_info[EM_WX_RANGESTART].value);
  // value->cur_rangeend = atoi((char *)line_info[EM_WX_RANGEEND].value);
}


static uint32_t wxpyq_parse_packet(struct flow_info *flow, int C2S, const uint8_t *wx_payload, const uint32_t payload_len,
        weixin_info_t  *line_info)
{
    if((payload_len == 0) || (wx_payload == NULL))
        return WX_ERROR;

    int32_t  wx_payload_len=0;
    uint32_t field_len=0;
    uint32_t value_len=0;

    uint32_t offset = 0;
    weixin_field_t *wx_field=NULL;
    char     tmp_buff[MAX_FIELD_LEN]={0};
    ST_weixin_file_session *session;

    session        = (ST_weixin_file_session*)flow->app_session;

    wx_payload_len  = payload_len;
    while (wx_payload_len>0) {
        if( offset + WEIXIN_COMMON_LENGTH > payload_len){
            goto end;
        }
        field_len = ntohl(get_uint32_t(wx_payload, offset));
        //if(field_len>MAX_FIELD_LEN-1 || field_len==0){
        if(field_len>256 || field_len>payload_len-offset){
            goto end;
        }

        wx_payload_len -= WEIXIN_COMMON_LENGTH;
        wx_payload_len -= field_len;
        offset += WEIXIN_COMMON_LENGTH;

        if(0==field_len){
            goto end;
        }

        /*strncpy会在尾部填充"\0"*/
        strncpy(tmp_buff,(char*)(wx_payload+offset),sizeof(tmp_buff));
        int pos = rte_hash_lookup_data(wx_handle, tmp_buff, (void **)&wx_field);
        if(pos>0){
            // 解析到 filedata 之后就可以退出当前字段解析
            if (wx_field->index == EM_WX_FILEDATA) goto end;

            offset += field_len;
            if( offset + WEIXIN_COMMON_LENGTH > payload_len){
                goto end;
            }

            value_len =  ntohl(get_uint32_t(wx_payload, offset));
            offset += WEIXIN_COMMON_LENGTH;
            if(value_len>payload_len-offset){
                goto end;
            }

            //首次记录fileid, 不区分上下行
            if(NULL == line_info[EM_WX_FILEID].value)
            {
                COPY_VALUE(line_info[wx_field->index], wx_payload + offset, value_len, free);
                // session->fileid_direction  = C2S; // 记录当前fileid的方向
            }
            offset += value_len;
            if(offset>payload_len){
                goto end;
            }
        }
        else
        {
            offset += field_len;
            if( offset + WEIXIN_COMMON_LENGTH > payload_len){
                goto end;
            }
        }
        wx_payload_len -= WEIXIN_COMMON_LENGTH;
        wx_payload_len -= value_len;

        value_len=0;
        field_len=0;
    }

end:
    return offset;
}

// 无论操作成功失败，都要删除，并释放 value 内存
gboolean val_hash_remove(gpointer key, gpointer value, gpointer user_data)
{
  // 不操作 直接删除
  if (value == NULL || user_data == NULL) return TRUE;

  char write_name[256] = { 0 };
  char fin_name[256] = { 0 };
  unsigned char data[1024];
  WxDataInfo * info = (WxDataInfo *)value;
  ST_weixin_file_session * session = (ST_weixin_file_session*)user_data;



  if (info->is_finish != 1) {
    // 还原失败，删除文件
    fclose(info->fp);
    unlink(info->filepath);
    return TRUE;
  }

  // 计算 md5
  unsigned char md5[MD5_DIGEST_LENGTH];
  unsigned char md5hex[MD5_DIGEST_LENGTH * 2 + 2];
  MD5_CTX context;
  int bytes;
  MD5_Init(&context);
  while ((bytes = fread(data, 1, 1024, info->fp)) != 0) {
    MD5_Update(&context, data, bytes);
  }
  MD5_Final(md5, &context);
  bintohex((const char *)md5, MD5_DIGEST_LENGTH, (char *)md5hex, sizeof(md5hex)-1);

  fclose(info->fp);
  // 修改文件名
  snprintf(write_name, sizeof(write_name), "%s/%s", g_config.wxf_pyq_data_dir, info->filename);
  // 删除 writing 后缀 填充 md5 值
  info->filename[strlen(info->filename) - 8] = '\0';
  if (info->suffix[0] != '\0') {
    snprintf(info->filename + strlen(info->filename), sizeof(info->filename), "_%s.%s", md5hex, info->suffix);
  } else {
    snprintf(info->filename + strlen(info->filename), sizeof(info->filename), "_%s.bin", md5hex);
  }

  snprintf(fin_name, sizeof(fin_name), "%s/%s", g_config.wxf_pyq_data_dir, info->filename);
  rename(write_name, fin_name);
  // 保存 文件名 md5
  snprintf(session->file_info + strlen(session->file_info), sizeof(session->file_info), "%s,%s;", info->filename, md5hex);

  // printf("file name = %s\n", session->file_info);
  return TRUE;
}


static
int wxf_dump_file(struct flow_info *flow, const uint8_t * payload, const uint32_t payload_len, GHashTable *hash, weixin_info_t * line_info)
{
  struct timeval tv;

  const char find_flag[] = "filedata";
  // total size 必须大于 0
  int total_size = atoi((char *)line_info[EM_WX_TOTALSIZE].value);
  if (total_size <= 0) return -1;
  WxDataInfo * value = NULL;
  ST_weixin_file_session * session = NULL;

  session = (ST_weixin_file_session*)flow->app_session;
  gettimeofday(&tv, NULL);
  value = (WxDataInfo *)g_hash_table_lookup(hash, GINT_TO_POINTER(total_size));
  if (value == NULL) {
    value = g_malloc0(sizeof(WxDataInfo));

    wxf_copy_attr(value, line_info);
    // 生成临时文件名

    snprintf(value->filename, sizeof(value->filepath),
            "%s_%06ld_%s_%03d.%s",
            time_to_datetime(tv.tv_sec),
            tv.tv_usec,
            "weixin_pyq",
            session->tid,
            "writing"
            );
    snprintf(value->filepath, sizeof(value->filepath),
            "%s/%s", g_config.wxf_pyq_data_dir, value->filename
            );

    if(0 != access(g_config.wxf_pyq_data_dir, F_OK|R_OK|W_OK)) {
      mkdirs(g_config.wxf_pyq_data_dir);
    }
    value->fp = fopen(value->filepath, "wb+");

    // 首次创建，获取文件长度
    value->totalsize = total_size;

    g_hash_table_insert(hash, GINT_TO_POINTER(total_size), (void *)value);
  } else {

  }

  // 复制文件起止位置
  value->cur_rangestart = atoi((char *)line_info[EM_WX_RANGESTART].value);
  value->cur_rangeend = atoi((char *)line_info[EM_WX_RANGEEND].value);

  int len = 0;
  long curlen = 0;
  int empty_len = 0;

  const char * data = memmem(payload, payload_len, find_flag, strlen(find_flag));
  if (data == NULL) return -1;
  data += strlen(find_flag);
  len = ntohl(*(int *)(size_t)data);
  data += sizeof(int);

  if ((data - (const char *)payload) + len > payload_len) return -1;

  // 获取当前文件长度
  curlen = ftell(value->fp);
  // 如果本次写入文件的起始位置大于当前文件长度，中间的间隔补空
  if (value->cur_rangestart > curlen) {
    empty_len = value->cur_rangestart - curlen;
    fseek(value->fp, curlen, SEEK_SET);
    int write_len = 0;
    for(int i = 0; i < empty_len ; i += write_len){
    if(empty_len - write_len <1024){
    fwrite("", sizeof(char), empty_len - write_len, value->fp);
    break;
    }
    fwrite("", sizeof(char), 1024, value->fp);
    write_len += 1024;
    }
  }

  if (value->cur_rangestart == 0) {
    const char * tmpstr = filetype(data, len);
    if (tmpstr != NULL) {
      snprintf(value->suffix, sizeof(value->suffix), "%s", tmpstr);
    }
  }

  fseek(value->fp, value->cur_rangestart, SEEK_SET);
  fwrite(data, sizeof(char), len, value->fp);
  fflush(value->fp);
  // printf("total size = %ld, cur size = %d, ftell_c = %d, ftell_e = %d, cur write size = %d, range_start = %d, range_end = %d\n",
  //         value->totalsize, value->currsize, curlen, tmplen, len, value->cur_rangestart, value->cur_rangeend);
  value->currsize += len;

  if (value->currsize >= value->totalsize) {
    value->is_finish = 1;
    // fclose(value->fp);
  }

  return 0;
}


static
int dissect_wxf_data(struct flow_info *flow, int C2S, const uint8_t *reassemble_result, const uint32_t reassemble_result_len)
{
  int ret = 0;
  const uint8_t  *wxf_start  = reassemble_result;
  uint32_t wxf_max_len = reassemble_result_len;
  uint32_t wxf_len=0;
  int      count=0;
  int      index=0;
  uint32_t filedata_len = 0;
  GHashTable * hash = g_hash_table_new_full(g_direct_hash, g_direct_equal, NULL, g_free);
  weixin_info_t   line_info[EM_WX_MAX];
  memset(&line_info,0,sizeof(line_info));
  int i;

  if (wxf_max_len == 0) {
    return -1;
  }

  while (wxf_max_len>0) {
    index = WEIXIN_NOTHTTP_HEADER_LEN;
    // index = _find_three_zero_position(wxf_start,wxf_max_len);
    if(index<0){
        break;
    }
    if(index<(int)wxf_max_len){
        wxf_len=wxf_max_len-index;
    }else{
        break;
    }
    ret = wxpyq_parse_packet(flow, C2S, &wxf_start[index], (const uint32_t)wxf_len, line_info);
    if (index+ret< (int)wxf_max_len){
        wxf_start += index;
        wxf_start += ret;
        wxf_max_len -= index;
        wxf_max_len -= ret;
    } else {
      break;
    }

    // 判定每段解析是否有效
    if (line_info[EM_WX_TOTALSIZE].value == NULL ||
        line_info[EM_WX_RANGEEND].value == NULL ||
        line_info[EM_WX_RANGESTART].value == NULL) {
      continue;
    }

    if (strncmp("filedata", (const char *)wxf_start, strlen("filedata")) == 0) {
      filedata_len = get_uint32_ntohl(wxf_start, 8);
      // wxf_start += 8;
    }

    wxf_dump_file(flow, wxf_start, wxf_len, hash, line_info);
    // filedata offset
    wxf_start += 8;                     // field len
    wxf_start += WEIXIN_COMMON_LENGTH;  // common length
    wxf_start += filedata_len;
    wxf_max_len = wxf_max_len - 8 - WEIXIN_COMMON_LENGTH - filedata_len;
    count++;
    if (count>g_config.wxf_check_http_times){
      break;
    }
  }

  int begin = g_hash_table_size(hash);
  // printf("begin remove hash size = %d\n", begin);
  // fclose file fd
  g_hash_table_foreach_remove(hash, val_hash_remove, flow->app_session);
  // free line info
  int end = g_hash_table_size(hash);
  // printf("end remove hash size = %d\n", end);
  free_info_ref(line_info);

  return 0;
}

// * 返回大于 0 代表还原数据长度
// * 返回小于 0 代表转储出错
// * 返回等于 0 代表没有找到, 不代表出现了错误
static int
dissect_weixin_data(const uint8_t *payload, const uint32_t len,  const char *find, char *prefix, const char *suffix, char *md5sum)
{
    const char *p    = NULL;
    int         l    = 0;
    int         fd   = 0;
    int         ret  = 0;
    char        name_writing[1024];
    char        name_done[1024];
    char        md5HEX[48];
    const char *writing = "writing";

    if(0 == find[0])
    {
        return 0;  // 这不是出错
    }

    p = memmem(payload, len, find, strlen(find));
    if(NULL == p)
    {
        return 0; // 这不是出错
    }

    p += strlen(find);
    l  = ntohl(*(int*)(size_t)p);
    p += sizeof(int);

    // 越界检测
    if((p - (const char*)payload) + l > len)
    {
        return -1; // 这不是转储出错, 是还原出错了
    }

    MD5((const unsigned char *)p, l, (unsigned char *)md5sum);
    memset(md5HEX, 0, sizeof(md5HEX));
    bintohex(md5sum, MD5_DIGEST_LENGTH, md5HEX, sizeof(md5HEX)-1);
    snprintf(name_done,    sizeof(name_done),    "%s/%s_%s%s",    g_config.wxf_pyq_data_dir, prefix, md5HEX, suffix);
    snprintf(name_writing, sizeof(name_writing), "%s/%s_%s%s.%s", g_config.wxf_pyq_data_dir, prefix, md5HEX, suffix, writing);

    // 完善显示在TBL记录中的文件名
    strcat(prefix, "_");
    strcat(prefix, md5HEX);
    strcat(prefix, suffix);

    if(0 != access(g_config.wxf_pyq_data_dir, F_OK|R_OK|W_OK))
    {
        mkdirs(g_config.wxf_pyq_data_dir);
    }

    // 数据转储, 无论成功与否, 都要关闭.  暂无通知机制(简单.粗暴.快捷.有效)
    fd = creat(name_writing, 0644);
    if(fd < 0)
    {
        return -1; // 这是系统出错
    }

    ret = write(fd, p, l);
    close(fd);
    if(ret == l)
    {
        rename(name_writing, name_done);  // OK
    }
    else
    {
        unlink(name_writing);  // failer
        ret = -1; // 转储出错
    }

    return ret; // 返回长度
}


static int
weixin_get_filter_result(weixin_info_t          *line_info)
{
    if(line_info==NULL){
        return 0;
    }
    int i=0;

    for(i=0;i<WEIXIN_FILTER_MAX_NUM;i++){
        if(wxf_filter_array[i]>0 && wxf_filter_array[i]<EM_WX_MAX){
            if(line_info[wxf_filter_array[i]].value!=NULL){
                return 1;
            }
        }else{
            break;
        }
    }

    return 0;
}


static
void weixin_get_wxnum_from_field2(struct flow_info *flow, void *ptr)
{
  weixin_info_t          *line_info = NULL;
  ST_weixin_file_session *session = NULL;
  int i = 0;
  gchar       **elems = NULL;
  uint8_t       elem_cnt = 0;
  const char   *delim = "0203";
  const char   *delim_sub = "0204";
  gchar        *wxnum_str = NULL;
  char          wxnum[16] = { 0 };
  uint8_t       wxnum_len = 0;


  session   = ( ST_weixin_file_session *)ptr;
  line_info = session->line_info;

  if (line_info[EM_WX_FILEID_2].value == NULL) return;
  elems = g_strsplit((const char *)line_info[EM_WX_FILEID_2].value, delim, 0);
  for (i = 0; elems[i]; ++i) {
    elem_cnt++;
  }

  if (elem_cnt == 2) {
      char * tmpstr = (char *)elems[0];
      wxnum_str = strstr(tmpstr, delim_sub);
      if  (wxnum_str != NULL) {
          wxnum_str = wxnum_str + strlen(delim_sub);
      }
  } else if (elem_cnt == 3) {
      wxnum_str = elems[1];
  }


  if (wxnum_str == NULL) return;
  if (isHEX(wxnum_str, strlen(wxnum_str)) != 1) return;

  if (wxnum == NULL) return;
  wxnum_len = strlen(wxnum_str);
  if (wxnum_len == 6) {
      snprintf(wxnum, sizeof(wxnum), "ff%s", wxnum_str);
  } else if (wxnum_len == 8) {
      snprintf(wxnum, sizeof(wxnum), "%s", wxnum_str);
  } else {
      return;
  }

  long long num = hex_to_decimal(wxnum, strlen(wxnum));
  char dec_str[64] = { 0 };
  snprintf(dec_str, sizeof(dec_str), "%lld", num);

  char *encode_out;
  encode_out = malloc(BASE64_ENCODE_OUT_SIZE(strlen(dec_str)));
  base64_encode((const unsigned char *)dec_str, strlen(dec_str), encode_out);


  COPY_VALUE(line_info[EM_WX_RESV2], encode_out, strlen(encode_out), free);

  free(encode_out);
  g_strfreev(elems);
}

static void weixin_put_record_value_by_type(precord_t *recorder ,weixin_info_t * line_info,  const char * input_name,uint8_t type)
{
  if (line_info  == NULL){
    return;
  }

  if (-1 == line_info->value_len && line_info->value == NULL)
    {
      return;
    }

  ya_fvalue_t *new_value = NULL;

  if (type == EM_F_TYPE_STRING) {
      new_value = ya_fvalue_new_stringn(YA_FT_STRING, (const char *)line_info->value, line_info->value_len);
  } else if (type == EM_F_TYPE_UINT64){
      new_value = ya_fvalue_new_uinteger64(YA_FT_UINT64, line_info->value_len);
  } else if (type == EM_F_TYPE_HEX) {
      char tmp[2048] = { 0 };
      bintohex((const char *)line_info->value,line_info->value_len,tmp,sizeof(tmp)-1);
      new_value = ya_fvalue_new_stringn(YA_FT_STRING, tmp,strlen(tmp));
  }else if(EM_F_TYPE_BYTES)
  {
      new_value = ya_fvalue_new_bytes(YA_FT_BYTES, (uint8_t *)line_info->value, line_info->value_len);
  }
  else {
      new_value = ya_fvalue_new_uinteger(YA_FT_UINT32, line_info->value_len);
  }
  dpi_precord_fvalue_put_by_name(recorder, input_name, new_value);
}

static void weixin_record_put_value(struct flow_info*flow,int direction,weixin_info_t *line_info){
    ST_weixin_file_session *session = NULL;
    session   = ( ST_weixin_file_session *)flow->app_session;

    for(int i = 0 ; i< EM_WX_MAX;i++){
      weixin_put_record_value_by_type(session->record,&line_info[i],weixin_array_f[i].field_name,weixin_array_f[i].type);
    }

    return;
}

int write_weixin_pyq_log_by_recorder(struct flow_info *flow, int direction,precord_t *record) {
    int idx = 0, i;
    struct tbl_log_record *log_ptr;
    const char *str = NULL;

    if (rte_mempool_get(tbl_log_record_mempool, (void **)&log_ptr) < 0) {
      DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_record_mempool");
      return PKT_OK;
    }
    write_tbl_log_common_by_record(flow,direction,record);
    log_ptr->record             = record;
    log_ptr->thread_id          = flow->thread_id;
    precord_layer_move_cursor(record, "weixin_pyq");

    if(tbl_record_log_enqueue(log_ptr) != 1) {
        ya_destroy_record(record);
        rte_mempool_put(tbl_log_record_mempool, (void *)log_ptr);
    }
    record = NULL;
    struct rte_mempool *pool = NULL;
    uint32_t pool_len = 0;


    return 0;
}
static void weixin_record_put_pyq_value(struct flow_info*flow,int direction,weixin_info_t *line_info){
    if (g_config.protocol_switch[PROTOCOL_WEIXIN_PYQ] == 0)
    {
        return;
    }
    ST_weixin_file_session *session = NULL;
    session   = ( ST_weixin_file_session *)flow->app_session;
    precord_t *weixin_pyq_record = ya_create_record("weixin_pyq");
    for(int i = 0 ; i< EM_WX_MAX;i++){
      weixin_put_record_value_by_type(weixin_pyq_record,&line_info[i],weixin_array_f[i].field_name,weixin_array_f[i].type);
    }
    ya_fvalue_t *new_value = NULL;
    new_value = ya_fvalue_new_stringn(YA_FT_STRING, "wxf", strlen("wxf"));
    dpi_precord_fvalue_put_by_name(weixin_pyq_record, "data_origin", new_value);
    write_weixin_pyq_log_by_recorder(flow,direction,weixin_pyq_record);
    return;
}
static int write_weixin_log_by_recorder(struct flow_info *flow, int direction) {
    int idx = 0, i;
    struct tbl_log_record *log_ptr;
    ST_weixin_file_session *session = (ST_weixin_file_session *)flow->app_session;
    const char *str = NULL;

    if (rte_mempool_get(tbl_log_record_mempool, (void **)&log_ptr) < 0) {
      DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_record_mempool");
      return PKT_OK;
    }
    write_tbl_log_common_by_record(flow,direction,session->record);
    log_ptr->record             = session->record;
    log_ptr->thread_id          = flow->thread_id;
    precord_layer_move_cursor(log_ptr->record, "weixin");

    if(tbl_record_log_enqueue(log_ptr) != 1) {
        rte_mempool_put(tbl_log_record_mempool, (void *)log_ptr);
        return 0;
    }
    session->record = NULL;
    struct rte_mempool *pool = NULL;
    uint32_t pool_len = 0;


    return 0;
}

static void
weixin_timeout_write_tbl(struct flow_info *flow, void *ptr)
{
    weixin_info_t          *line_info = NULL;
    ST_weixin_file_session *session = NULL;
    char                    md5HEX[33];
    char                   *pmd5 = md5HEX;
    int                    ivalue = 0;
    int                    itotal=0;

    session   = ( ST_weixin_file_session *)ptr;
    line_info = session->line_info;

    /**** 微信朋友圈 fileid 分割 S ****/
    weixin_process_fileid(line_info);
    /**** 微信朋友圈 fileid 分割 E ****/

    /* 微信朋友圈查看图片或视频行为
     *  url  含有特殊字符串，比如videodownload  视为朋友圈数据
     */
    if(line_info[EM_WX_URL].value_len>0 && line_info[EM_WX_URL].value!=NULL)
    {
        int  url_len      = 512;
        if(url_len > (int)line_info[EM_WX_URL].value_len)
        {
            url_len=line_info[EM_WX_URL].value_len;
        }

        if (memmem(line_info[EM_WX_URL].value,url_len,"videodownload", strlen("videodownload")) ||
            memmem(line_info[EM_WX_URL].value,url_len,"snscosdownload", strlen("snscosdownload")) ||
            memmem(line_info[EM_WX_URL].value,url_len,"stodownload", strlen("stodownload")) ||
            memmem(line_info[EM_WX_URL].value,url_len,"snssvpdownload", strlen("snssvpdownload")) ||
            memmem(line_info[EM_WX_URL].value,url_len,"mmsns", strlen("mmsns")) )
        {
          session->wxf_type = EM_WX_WXF_WEIXIN_PYQ;
        }
    }



    /* 微信朋友圈发送图片或视频回应数据
     * 规则：
     *     1.ios  filekey 开头为"[TEMP]"
     *     2.android filekey无"_"
     */
    if (line_info[EM_WX_FILEKEY].value_len >= 6 && line_info[EM_WX_FILEKEY].value!=NULL)
    {
        if( strncasecmp((const char *)line_info[EM_WX_FILEKEY].value, "[TEMP]", 6) == 0 ||  // IOS
            (!memmem((const char *)line_info[EM_WX_FILEKEY].value,line_info[EM_WX_FILEKEY].value_len, "_",1) &&
               line_info[EM_WX_TOUSER].value_len <=0)// Android
        )
        {
            session->wxf_type = EM_WX_WXF_WEIXIN_PYQ;
        }
    }

    /* 微信朋友圈发送图片或视频发送数据
     * 规则：
     *     filetype为20201,20202,20204,20205,20250
     *     filetype有值且不等于以上值，则判断为wxf数据
     */
    if(line_info[EM_WX_FILETYPE].value_len > 0 && line_info[EM_WX_FILETYPE].value!=NULL)
    {
        if(line_info[EM_WX_FILETYPE].value_len<5){
            session->wxf_type = EM_WX_WXF_WEIXIN;
        }else{
            uint32_t type_id = atoi((char *)line_info[EM_WX_FILETYPE].value);
            if(20201==type_id || 20202==type_id ||
               20204==type_id || 20205==type_id ||
               20250==type_id || 20302 == type_id)
            {
                session->wxf_type = EM_WX_WXF_WEIXIN_PYQ;
            }else{
                session->wxf_type = EM_WX_WXF_WEIXIN;
            }
        }
    }

    // reset filename
    COPY_VALUE(line_info[EM_WX_PATH_JPG], session->jpg.path, strlen(session->jpg.path), free);
    COPY_VALUE(line_info[EM_WX_PATH_MP4], session->mp4.path, strlen(session->mp4.path), free);

    if(session->mp4.size <= 0)
    {
        if(line_info[EM_WX_PATH_MP4].value)
        {
            line_info[EM_WX_PATH_MP4].value[0]  = '\0';
            line_info[EM_WX_PATH_MP4].value_len = 0;
        }
        if(line_info[EM_WX_MD5_MP4].value)
        {
            line_info[EM_WX_MD5_MP4].value[0]  = '\0';
            line_info[EM_WX_MD5_MP4].value_len = 0;
        }
    }
    if(session->jpg.size <= 0)
    {
        if(line_info[EM_WX_PATH_JPG].value)
        {
            line_info[EM_WX_PATH_JPG].value[0]  = '\0';
            line_info[EM_WX_PATH_JPG].value_len = 0;
        }
        if(line_info[EM_WX_MD5_JPG].value)
        {
            line_info[EM_WX_MD5_JPG].value[0]  = '\0';
            line_info[EM_WX_MD5_JPG].value_len = 0;
        }
    }

    if(NULL == line_info[EM_WX_MD5_JPG].value && 1 != is_zero((unsigned char*)session->jpg.md5, sizeof(session->jpg.md5)))
    {
        memset(md5HEX, 0, sizeof(md5HEX));
        bintohex(session->jpg.md5, sizeof(session->jpg.md5), md5HEX, sizeof(md5HEX)-1);
        COPY_VALUE(line_info[EM_WX_MD5_JPG], pmd5, strlen(pmd5), free);
    }

    if(NULL == line_info[EM_WX_MD5_MP4].value && 1 != is_zero((unsigned char*)session->mp4.md5, sizeof(session->mp4.md5)))
    {
        memset(md5HEX, 0, sizeof(md5HEX));
        bintohex(session->mp4.md5, sizeof(session->mp4.md5), md5HEX, sizeof(md5HEX)-1);
        COPY_VALUE(line_info[EM_WX_MD5_MP4], pmd5, strlen(pmd5), free);
    }
    line_info[EM_WX_FIRST_CAPTATE].value_len = session->frist_pkt_timestamp;
    line_info[EM_WX_LAST_CAPTATE].value_len  = session->time_last;



    // 输出为TBL记录, 并释放内存
    if(session->wxf_type == EM_WX_WXF_WEIXIN_PYQ)
    {
      if (session->file_info[0] != '\0' && session->file_info[strlen(session->file_info) -1] == ';') {
        session->file_info[strlen(session->file_info) -1] = '\0';
        COPY_VALUE(line_info[EM_WX_DATA], session->file_info, strlen(session->file_info), free);
      }

      if ((line_info[EM_WX_URL].value != NULL ||
          line_info[EM_WX_FILEURL].value != NULL ||
          line_info[EM_WX_VIDEOCDNMSG].value != NULL) &&
          (line_info[EM_WX_WEIXINNUM].value != NULL || line_info[EM_WX_PYQ_UIN].value != NULL)) {
        dissect_weixin_relation(flow, line_info);
      }
      dissect_referer_key_value(line_info);
      dissect_fileurl_key_value(line_info);
      weixin_record_put_pyq_value(flow,FLOW_DIR_SRC2DST,line_info);
      write_weixin_log_pyq(flow, FLOW_DIR_SRC2DST,  line_info);
    }

    if(session->wxf_type == EM_WX_WXF_WEIXIN)
    {
        /* 如果开启了过滤[filemd5][url][fileid], 则不输出 */
        if(g_config.wxf_filter>0 &&
           weixin_get_filter_result(line_info)!=1)
        {
            goto end;
        }

        /* 如果发现有rangeend,totalsize,并且rangeend+1!=totalsize 则过滤掉         */
        if(g_config.wxf_filter_repeat>0 &&
           line_info[EM_WX_RANGEEND].value!=NULL &&
           line_info[EM_WX_TOTALSIZE].value!=NULL )
        {
            ivalue=atoi((char *)line_info[EM_WX_RANGEEND].value);
            itotal=atoi((char *)line_info[EM_WX_TOTALSIZE].value);
            //printf("[###### debug liugh ######]ivalue=%d, itotal=%d\n",ivalue, itotal);
            if(ivalue+1!=itotal){
              goto end;
            }
        }

        //
        if (line_info[EM_WX_FILEID_2].value != NULL) {
          weixin_get_wxnum_from_field2(flow, ptr);
        }

        weixin_record_put_value(flow,FLOW_DIR_SRC2DST,line_info);
        write_weixin_log_by_recorder(flow, FLOW_DIR_SRC2DST);
        write_weixin_log(flow, FLOW_DIR_SRC2DST,  line_info);
    }
end:
    free_info_ref(line_info);
    if(session->record != NULL){
        ya_destroy_record(session->record);
    }
}


static int dissect_weixin_file_fields(struct flow_info *flow, int  C2S, ST_weixin_file_session *session, int direction,
        uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{

    uint8_t                         ret=0;
    uint32_t                        index= 0;
    const char                      *p   = NULL;
    const char                      *p1  = NULL;
    const char                      *q   = NULL;
    char                            buff[CHECK_HTTP_HEADER_LEN]={0};
    uint32_t                        first_len;
    weixin_info_t                   *line_info = NULL;
    int                             i    = 0;

    if(session==NULL){
        return PKT_DROP;
    }
    line_info = session->line_info;

    /***** 开始解析当前报文 *****/
    if(payload!=NULL && payload_len>CHECK_HTTP_HEADER_LEN)
    {
        strncpy(buff, (const char *)payload, CHECK_HTTP_HEADER_LEN);
    }
    else
    {
        return PKT_DROP;
    }

    p=strcasestr(buff,"HTTP");
    if( p!=NULL ) /* http + lklv*/
    {
        index=p-&buff[0];
        q=strstr(p,"\r\n");
        if(q!=NULL && q-p>0)
        {
            COPY_VALUE(line_info[EM_WX_HTTP_VERSION], payload+index, q-p, free);
        }

        /*get post*/
        p1=strcasestr(buff,"post");
        if(p1!=NULL)
        {
            int len;
            index=p1-buff;
            len  = p-p1-6;
            COPY_VALUE(line_info[EM_WX_HTTP_METHOD], payload+index,   4,      free);

            // 防范
            if(len < (int)payload_len && len>0 && index+5<payload_len)
            {
                COPY_VALUE(line_info[EM_WX_HTTP_URI],    payload+index+5, len, free);
            }
        }

        char http_buff[MAX_HTTP_HEADER_LEN]={0};
        strncpy(http_buff, (const char *)payload, MAX_HTTP_HEADER_LEN);

        p=strcasestr(http_buff,"content-length");
        if(p!=NULL)
        {
            index=p-http_buff+16;
            q=strstr(p,"\r\n");
            if(q!=NULL && q-p-16>0)
            {
                COPY_VALUE(line_info[EM_WX_HTTP_CONTENTLENGTH], payload+index, q-p-16, free);
            }
        }

        p=strstr(http_buff,"\r\n\r\n");
        if(p==NULL)
        {
            return PKT_DROP;
        }
        index=p-&http_buff[0]+4;
    }
    else
    if(payload[0]==0xab)    /* TCP: ab + head + lklv*/
    {
        index=WEIXIN_NOTHTTP_HEADER_LEN;
        if(index+7>payload_len)
        {
            return PKT_DROP;
        }

        // ver
        first_len=get_uint32_ntohl(payload, index);
        if(first_len==3)
        {
            index=1;
            COPY_VALUE(line_info[EM_WX_WXF_TOTALLEN], NULL, ntohl(get_uint32_t(payload, index)), NULL);
            index+=WEIXIN_COMMON_LENGTH;

            COPY_VALUE(line_info[EM_WX_WXF_RESV1], payload+index, WEIXIN_COMMON_LENGTH, free);
            index+=WEIXIN_COMMON_LENGTH;

            COPY_VALUE(line_info[EM_WX_WXF_RESV2], payload+index, WEIXIN_COMMON_LENGTH, free);
            index+=WEIXIN_COMMON_LENGTH;

            COPY_VALUE(line_info[EM_WX_WXF_RESV3], payload+index, WEIXIN_COMMON_LENGTH, free);
            index+=WEIXIN_COMMON_LENGTH;

            COPY_VALUE(line_info[EM_WX_WXF_RESV4], payload+index, WEIXIN_COMMON_LENGTH, free);
            index+=WEIXIN_COMMON_LENGTH;

            COPY_VALUE(line_info[EM_WX_WXF_MSGLEN], NULL, ntohl(get_uint32_t(payload, index)), NULL);
            index+=WEIXIN_COMMON_LENGTH;
        }
        else
        if(payload[25]==0x08 && payload[26]==0x01 && payload[27]==0x10)  /*微信查看朋友圈数据*/
        {
            #if 1
            for(i=0;i<CHECK_HTTP_HEADER_LEN-4;i++){
                if(strncasecmp((const char*)&payload[i], "http", 4) == 0){
                    index=i;
                    break;
                }
            }
            parse_weixin_pyq_packet_info(payload+index, payload_len-index-1, line_info);
            #else
            liu_parse_weixin_pyq_packet_info(payload, payload_len, line_info);
            #endif
            session->wxf_type = EM_WX_WXF_WEIXIN_PYQ;
            return PKT_OK; // 解析完成

        }
        else
        {
            for(i=0;i<(int)payload_len-2;i++){
                if(payload[i]==0x00 && payload[i+1]==0x00 && payload[i+2]==0x00){
                    index=i;
                    break;
                }
            }
            if(index==0){
                return PKT_DROP;
            }
        }
    }
    else    /*lklv*/
    {
        first_len=get_uint32_ntohl(payload, 0);
        if(3==first_len)
        {
            if(strncasecmp((const char *)payload+4, "ver", 3) != 0 && strncasecmp((const char *)payload+4, "seq", 3) != 0)
            {
                return PKT_DROP;
            }
            index=0;
        }else{
            for(i=0;i<(int)payload_len-2;i++){
                if(payload[i]==0x00 && payload[i+1]==0x00 && payload[i+2]==0x00){
                    index=i;
                    break;
                }
            }
        }
    }
    ret = parse_weixin_packet_info(flow, C2S, payload+index, payload_len - index, line_info);
    if(WX_ERROR==ret)
    {
        return PKT_DROP;
    }
    /***** 报文解析完毕 *****/


    return PKT_OK;
}


static int dissect_weixin_pyq_data(ST_weixin_file_session *s,  const uint8_t *reassemble_result, const uint32_t reassemble_result_len)
{

   if(NULL==s){
        return PKT_DROP;
   }

   /***** S [组报标记开始] 如果是 微信朋友圈上传视频行为, 则重组报文 *****/
    s->jpg.size = dissect_weixin_data(reassemble_result, reassemble_result_len, s->jpg.flag, s->jpg.path, ".jpg", s->jpg.md5);
    s->mp4.size = dissect_weixin_data(reassemble_result, reassemble_result_len, s->mp4.flag, s->mp4.path, ".mp4", s->mp4.md5);
    if(s->jpg.size < 0 || s->mp4.size < 0 || 0 == reassemble_result_len)
    {
        ATOMIC_ADD_FETCH(&g_config.reassemble_info.obj[s->reassemble_type].error); //文件转储失败, TCP丢包
    }
    else
    {
        ATOMIC_ADD_FETCH(&g_config.reassemble_info.obj[s->reassemble_type].success); //文件组报成功
        ATOMIC_ADD_NUM(&g_config.reassemble_info.weixin_total, s->jpg.size); //累计还原总量
        ATOMIC_ADD_NUM(&g_config.reassemble_info.weixin_total, s->mp4.size); //累计还原总量
        ATOMIC_ADD_NUM(&g_config.reassemble_info.weixin_size,  s->jpg.size); //累计还原总量
        ATOMIC_ADD_NUM(&g_config.reassemble_info.weixin_size,  s->mp4.size); //累计还原总量
    }

    if(time(NULL) - g_config.reassemble_info.weixin_start > 60*10)
    {
        ATOMIC_SET (&g_config.reassemble_info.weixin_start, time(NULL));
        ATOMIC_ZERO(&g_config.reassemble_info.weixin_size);
    }


    return PKT_OK;
}

static int dissect_reassemble_common_fields(struct flow_info *flow, int            C2S, uint8_t *reassemble_result ,
                                                         uint32_t reassemble_result_len , weixin_info_t   *line_info)
{
    int      ret = 0;
    uint8_t  *wxf_start  = reassemble_result;
    uint32_t wxf_max_len = reassemble_result_len;
    uint32_t wxf_len=0;
    int      count=0;
    int      index=0;

    while (wxf_max_len>0) {
       index = _find_three_zero_position(wxf_start,wxf_max_len);
       if(index<0){
            break;
       }
       if(index<(int)wxf_max_len){
            wxf_len=wxf_max_len-index;
       }else{
            break;
       }
       ret = parse_weixin_packet_info(flow, C2S, &wxf_start[index], (const uint32_t)wxf_len, line_info);
       if(index+ret< (int)wxf_max_len){
           wxf_start += index;
           wxf_start += ret;
           wxf_max_len -= index;
           wxf_max_len -= ret;
       }else{
            break;
       }
       count++;
       if(count>g_config.wxf_check_http_times){
            break;
       }
    }


    return 0;
}


static int dissect_reassemble_http_type_fields(struct flow_info *flow, int            C2S, uint8_t *reassemble_result ,
                                                         uint32_t reassemble_result_len , weixin_info_t   *line_info)
{
    int      ret = 0;
    uint8_t  *wxf_start  = reassemble_result;
    uint32_t wxf_max_len = reassemble_result_len;
    uint32_t wxf_len=0;
    int      count=0;
    int      index=0;

    while (wxf_max_len>0) {
       index = _find_empty_line(wxf_start,wxf_max_len);
       if(index<0){
            break;
       }
       if(index<(int)wxf_max_len){
            wxf_len=wxf_max_len-index;
       }else{
            break;
       }
       ret = parse_weixin_packet_info(flow, C2S, &wxf_start[index], (const uint32_t)wxf_len, line_info);
       if(index+ret< (int)wxf_max_len){
           wxf_start += index;
           wxf_start += ret;
           wxf_max_len -= index;
           wxf_max_len -= ret;
       }else{
            break;
       }
       count++;
       if(count>g_config.wxf_check_http_times){
            break;
       }
    }


    return 0;
}

static int dissect_reassemble_normal_type_fields(struct flow_info *flow, int            C2S, uint8_t *reassemble_result ,
                                                         uint32_t reassemble_result_len , weixin_info_t   *line_info)
{
    const char     *p            =  NULL;
    int            key_len       =  0;
    uint32_t       index         =  0;
    uint32_t       tmp_len       =  0;
    uint32_t       detect_len    =  0;
    uint32_t       check_key_len =  5000;
    uint8_t        thumbdata_exit=  0;
    uint32_t       ret           =  0;

    key_len = strlen("thumbtotalsize");
    if(check_key_len > reassemble_result_len){
        check_key_len = reassemble_result_len;
    }
    p = memmem(reassemble_result, check_key_len, "thumbtotalsize", key_len);
    if(NULL == p){
        return 0;
    }

    index=(const uint8_t *)p-reassemble_result;
    if(index+key_len>reassemble_result_len){
        return 0;
    }
    index+=key_len;
    if(index+WEIXIN_COMMON_LENGTH > reassemble_result_len){
        return 0;
    }

    tmp_len  =  get_uint32_ntohl(reassemble_result, index);
    index   +=  WEIXIN_COMMON_LENGTH;
    index   +=  tmp_len;

    if(index>reassemble_result_len){
        return 0;
    }

    detect_len=reassemble_result_len-index-1;

    if(detect_len>0 && detect_len<reassemble_result_len-1){
        /* 重组后，开始解析字段，不区分朋友圈和一对一或一对多*/
        ret = parse_weixin_packet_info(flow, C2S, &reassemble_result[index], (const uint32_t)detect_len, line_info);
    }

    return 0;
}


/*
PKT_OK,
PKT_STOLEN,
PKT_REASSEMBLE,
PKT_DROP
*/
static int dissect_weixin(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    int            client_ip     = 0;
    int            server_ip     = 0;
    short          client_port   = 0;
    short          server_port   = 0;
    int            C2S           = 0;
    uint32_t       index         = 0;
    const char     *p            = NULL;
    const char     *p1           = NULL;
    const char     *q            = NULL;
    uint32_t        ret          = 0;
    char            buff[CHECK_HTTP_HEADER_LEN]={0};
    uint32_t        first_len;
    weixin_info_t   *line_info = NULL;
    ST_weixin_file_session *session = NULL;
    struct timeval tv;

    /***** 异常处理 *****/
    if (g_config.protocol_switch[PROTOCOL_WEIXIN] == 0)
    {
        return 0;
    }

    // 获取当前报文的 上下行, 五元组
    get_ip_port_v4(flow, &client_ip, &server_ip, &client_port, &server_port, &C2S);
    gettimeofday(&tv, NULL);

    /***** 建立会话 数据结构 *****/
    /***** 建立会话 数据结构 *****/
    /***** 建立会话 数据结构 *****/
    /* ADD_BY_CHUNLI_S 为当前协议支持 session  */
    if(NULL == flow->app_session)
    {
        flow->app_session = malloc(sizeof(ST_weixin_file_session));
        if(NULL == flow->app_session)
        {
            DPI_LOG(DPI_LOG_ERROR, "WEIXIN SESSION Malloc ERROR");
            return PKT_DROP;
        }
        memset(flow->app_session, 0, sizeof(ST_weixin_file_session));
        flow->flow_EOF = weixin_timeout_write_tbl;
        session        = (ST_weixin_file_session*)flow->app_session;

        if (g_config.wxf_time_precise_flag == 1) {
            session->time_first = tv.tv_sec * 1000 + tv.tv_usec / 1000;        //微妙转毫秒
        } else {
            session->time_first = time(NULL);
        }

        session->tid        = flow->thread_id;
        /*初始化*/
        session->wxf_type = EM_WX_WXF_WEIXIN;
        int i;
        for(i=0;i<EM_WX_MAX;i++){
            session->line_info[i].free=NULL;
            session->line_info[i].value=NULL;
            session->line_info[i].value_len=-1;
        }
        if (g_config.wxf_time_precise_flag == 1) {
            session->frist_pkt_timestamp = flow->pkt_rx_timestamp_first;
        } else {
            session->frist_pkt_timestamp = flow->pkt_rx_timestamp_first / 1000000;
        }
        session->record = ya_create_record("weixin");
    }
    /* ADD_BY_CHUNLI_E 为当前协议支持 session  */

    /* 解析结果存放于 session中         */
    session   = (ST_weixin_file_session*)flow->app_session;
    line_info = session->line_info;

    /* time last 在重组之前更新 */
    if (flag == DISSECT_PKT_ORIGINAL) {
        /* 是否开启时间精确度 */
        if (g_config.wxf_time_precise_flag == 1) {
            session->time_last = flow->pkt_rx_timestamp;
        } else {
            session->time_last = flow->pkt_rx_timestamp / 1000000;
        }
    }

    /* 默认所有微信文件报文都进行重组 */
    if(DISSECT_PKT_ORIGINAL == flag && flow->rsm_pkt_num[direction]<g_config.tcp_resseamble_max_num){
        tcp_reassemble_add_item(&flow->rsm_head[direction], &flow->rsm_len[direction], seq, payload, payload_len);
        flow->rsm_pkt_num[direction]++;
    }

    /* TCP重组超时,在做解析或还原处理*/
    if(DISSECT_PKT_FIANL == flag && flow->rsm_len[direction]<WEIXIN_TCP_PAYLOAD_MAX){

        ST_weixin_file_session  *s      = (ST_weixin_file_session*)flow->app_session;
        uint32_t reassemble_result_len  = flow->rsm_len[direction];
        uint8_t *reassemble_result      = (uint8_t *)malloc(flow->rsm_len[direction]);
        if(NULL != reassemble_result){
            tcp_reassemble_do_surport_miss(&flow->rsm_head[direction], reassemble_result, &reassemble_result_len);

            /*
            if(line_info[EM_WX_HTTP_METHOD].value!=NULL){
                dissect_reassemble_http_type_fields(flow, C2S, reassemble_result ,
                                                    reassemble_result_len ,line_info);
            }else{
                dissect_reassemble_normal_type_fields(flow, C2S, reassemble_result ,
                                                    reassemble_result_len ,line_info);
            }
            */
            dissect_reassemble_common_fields(flow, C2S, reassemble_result ,
                                             reassemble_result_len ,line_info);

            if(session->wxf_type==EM_WX_WXF_WEIXIN_PYQ &&
               line_info[EM_WX_REFERER_URL].value==NULL){
                parse_weixin_pyq_mulurl_info(reassemble_result, reassemble_result_len ,line_info);
            }
            #if 0
            if(session->wxf_type != EM_WX_WXF_WEIXIN_PYQ){
                /*对重组后的报文查找thumbdata，从thumbdata的数据结束开始解析到往后延长1500字节报文*/
                int      key_len=0;
                uint32_t index=0;
                uint32_t tmp_len=0;
                uint32_t detect_len=0;
                uint32_t check_key_len=5000;
                uint8_t  thumbdata_exit=0;
                key_len = strlen("thumbtotalsize");
                if(check_key_len>reassemble_result_len){
                    check_key_len=reassemble_result_len;
                }
                p = memmem(reassemble_result, check_key_len, "thumbtotalsize", key_len);
                if(NULL == p){
                    goto free_data;
                }else{
                    thumbdata_exit=1;
                }

                index=(const uint8_t *)p-reassemble_result;
                if(index+key_len>reassemble_result_len){
                    goto free_data;
                }
                index+=key_len;
                if(index+WEIXIN_COMMON_LENGTH>reassemble_result_len){
                    goto free_data;
                }

                tmp_len  = get_uint32_ntohl(reassemble_result, index);
                index+=WEIXIN_COMMON_LENGTH;
                index+=tmp_len;

                if(index>reassemble_result_len){
                    goto free_data;
                }

                detect_len=reassemble_result_len-index-1;

                if(detect_len>0 && detect_len<reassemble_result_len-1){
                    /* 重组后，开始解析字段，不区分朋友圈和一对一或一对多*/
                    ret = parse_weixin_packet_info(flow, C2S, &reassemble_result[index], (const uint32_t)detect_len, line_info);
                }
            }
            #endif
           if( REASSEMBLE_ENABLE == flow->reassemble_enable && session!=NULL){
                /*如果是朋友上传视频或图片，则还原视频或图片数据，并落盘 */
              dissect_weixin_pyq_data(session,reassemble_result, reassemble_result_len);
            }

             // weixin 明文数据
            if (session->reassemble_type == WX_PYQ_DATA &&  session->wx_direction == direction) {

              dissect_wxf_data(flow, C2S, reassemble_result, reassemble_result_len);

            }

            free(reassemble_result);
            reassemble_result     = NULL;
            reassemble_result_len = 0;
        }

        tcp_reassemble_free(&flow->rsm_head[direction], &flow->rsm_len[direction]);
        flow->reassemble_enable     = REASSEMBLE_DISABLE; /* 设置标记. 不再重组 */
        flow->reassemble_direction  = 0;
        flow->reassemble_expect_len[direction] = 0;

        return PKT_OK;
    }


     /* 对于单个报文同样需要先解析字段 */
    dissect_weixin_file_fields(flow, C2S, session, direction, seq,payload, payload_len, flag);


    int dump_flag = memcmp(g_config.wxf_pyq_data_dir, "OFF", 3);
    /***** S [组报标记开始] 如果是 微信朋友圈上传视频行为, 则重组报文 *****/
    if((REASSEMBLE_DEFAULT == flow->reassemble_enable)  &&
        1 == is_weixin_pyq_mp4_upload(payload, payload_len, line_info) && (0 != memcmp(g_config.wxf_pyq_data_dir, "OFF", 3)))
    {
        flow->reassemble_enable     = REASSEMBLE_ENABLE;
        flow->reassemble_direction  = direction;
        flow->reassemble_expect_len[direction] = line_info[EM_WX_WXF_MSGLEN].value_len;
        set_mp4_info(line_info, (ST_weixin_file_session*)flow->app_session);
        ST_weixin_file_session  *s  = (ST_weixin_file_session*)flow->app_session;
        s->reassemble_type          = WX_PYQ_MP4;
        ATOMIC_ADD_FETCH(&g_config.reassemble_info.obj[s->reassemble_type].expect);
    }
    if((REASSEMBLE_DEFAULT == flow->reassemble_enable) &&
        1 == is_weixin_pyq_jpg_upload(payload, payload_len, line_info) && (0 != memcmp(g_config.wxf_pyq_data_dir, "OFF", 3)))
    {
        flow->reassemble_enable     = REASSEMBLE_ENABLE;
        flow->reassemble_direction  = direction;
        flow->reassemble_expect_len[direction] = line_info[EM_WX_WXF_MSGLEN].value_len;
        set_jpg_info(line_info, (ST_weixin_file_session*)flow->app_session);
        ST_weixin_file_session  *s  = (ST_weixin_file_session*)flow->app_session;
        s->reassemble_type          = WX_PYQ_JPG;
        ATOMIC_ADD_FETCH(&g_config.reassemble_info.obj[s->reassemble_type].expect);
    }
    /***** E [组报标记开始] 如果是 微信朋友圈上传视频行为, 则重组报文 *****/
    if (REASSEMBLE_DEFAULT == flow->reassemble_enable &&
      is_weixin_data(flow, C2S, payload, payload_len, line_info) == 1 &&
      dump_flag != 0) {
        flow->reassemble_enable     = REASSEMBLE_ENABLE;
        flow->reassemble_direction  = direction;
        flow->reassemble_expect_len[direction] = line_info[EM_WX_WXF_MSGLEN].value_len;
        // set_jpg_info(line_info, (ST_weixin_file_session*)flow->app_session);
        ST_weixin_file_session  *s  = (ST_weixin_file_session*)flow->app_session;
        s->reassemble_type          = WX_PYQ_DATA;
        s->wx_direction             = direction;
        session->wxf_type           = EM_WX_WXF_WEIXIN_PYQ;
        ATOMIC_ADD_FETCH(&g_config.reassemble_info.obj[s->reassemble_type].expect);
      }


    return PKT_OK; // 解析完成
}


static int write_weixn_field_txt(weixin_field_t *proto_array,const char *proto_name)
{
    if(proto_name==NULL || proto_array==NULL){return 0;}

    int i;
    char file_path[TBL_PATH_LENGTH ]={0};
    snprintf(file_path,TBL_PATH_LENGTH ,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);
    FILE *fp=fopen(file_path,"w+");
    if(fp)
    {
        /* common field */
        for(i=0;i<EM_COMMON_MAX;i++)
        {
            fwrite(dpi_common_field[i].field_name,strlen(dpi_common_field[i].field_name),1,fp);
            fwrite("\n",1,1,fp);
        }

        for(i=0;i<EM_WX_MAX;i++)
        {
            fwrite(proto_array[i].field, proto_array[i].field_len,1,fp);
            fwrite("\n",1,1,fp);
        }

        fclose(fp);
    }

    return 1;
}

void init_weixin_dissector(void)
{

    int       i = 0;
    int       j = 0;
    int       count = 0;
    char      tmp_buff[MAX_FIELD_LEN]={0};
    char      *tmp=NULL;

    write_proto_field_tab(weixin_array_f,EM_WX_MAX,"weixin2");
    write_proto_field_tab(weixin_array_f,EM_WX_MAX,"weixin_pyq2");


    init_wx_relation_dissector();

    port_add_proto_head(IPPROTO_TCP, WEIXIN_PORT_0, PROTOCOL_WEIXIN);
    port_add_proto_head(IPPROTO_TCP, WEIXIN_PORT_1, PROTOCOL_WEIXIN);
    port_add_proto_head(IPPROTO_TCP, WEIXIN_PORT_2, PROTOCOL_WEIXIN);

    tcp_detection_array[PROTOCOL_WEIXIN].proto         = PROTOCOL_WEIXIN;
    tcp_detection_array[PROTOCOL_WEIXIN].identify_func = identify_weixin;
    tcp_detection_array[PROTOCOL_WEIXIN].dissect_func  = dissect_weixin;

    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_WEIXIN].excluded_protocol_bitmask, PROTOCOL_WEIXIN);

    wx_handle = rte_hash_create(&wx_hash);
    if (wx_handle == NULL)
    {
        printf("create wx hash error!");
        return;
    }


    for(i=0;i<EM_WX_MAX;i++)
    {
        char tmp_field_name[MAX_FIELD_LEN] = {0};
        // snprintf(tmp_field_name, MAX_FIELD_LEN, "%s",weixin_array_f[i].field_name);
        strcpy(tmp_field_name,weixin_array_f[i].field_name);
        int retval = rte_hash_add_key_data(wx_handle, tmp_field_name, &weixin_array_f[i]);
        if(retval<0)
        {
            printf("weixin field %s index %d add to hash error!\n",weixin_array_f[i].field_name,i);
        }
    }


    for(i=0;i<WEIXIN_FILTER_MAX_NUM;i++){
        wxf_filter_array[i]=0;
    }

    dpi_field_table *wx_field=NULL;
    tmp = strtok(g_config.wxf_filter_config, ",");
    while (tmp) {

        /*strncpy会在尾部填充"\0"*/
        strncpy(tmp_buff,(char*)(tmp),sizeof(tmp_buff));

        count++;
        if(count>=WEIXIN_FILTER_MAX_NUM){
            break;
        }
        int pos = rte_hash_lookup_data(wx_handle, tmp_buff, (void **)&wx_field);
        if(pos>0){
            wxf_filter_array[j]=wx_field->index;
            j++;
        }
        tmp = strtok(NULL, ",");
    }


    return;
}


int init_weixin(void)
{

    init_wxpyq_relation();


    return 0;
}

static void init_weixin_protorecord(void){
    write_proto_record_field_tab((dpi_record_field_table *)weixin_array_f,EM_WX_MAX,"weixin");
    write_proto_record_field_tab((dpi_record_field_table *)weixin_array_f,EM_WX_MAX,"weixin_pyq");
}

static __attribute((constructor)) void     before_init_weixin(void){
    dpi_proto_register("weixin", init_weixin_protorecord);
}

/*
static __attribute((constructor)) void     before_init_weixin(void){
    register_tbl_array(TBL_LOG_WEIXIN, 0, "weixin", init_weixin_dissector);
}

static __attribute((constructor)) void     before_init_weixin_pyq(void){
    register_tbl_array(TBL_LOG_WEIXIN_PYQ, 0, "weixin_pyq", NULL);
}
*/
// 手段
// int fd = creat("/tmp/foo.data", 0644);
// write(fd, payload, payload_len);
// close(fd);
