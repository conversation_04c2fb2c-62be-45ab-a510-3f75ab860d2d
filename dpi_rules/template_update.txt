#*********************************************************
#Author  : jianghz
#Date    : 2021-09-16
#FileName: template_update.txt
#Describe: w业务规则模板文件
#Version : V2.4
#Modify  : 同步核查表规则内容(与核查表版本一致)
#*********************************************************
#微信文件过滤规则
[BASIC]
template_version = V2.4

#微信文件过滤规则
[wxf]
#特征描述: ipv4/ipv6, 80/8080/443端口, tcp协议, 载荷第一个字节为0xab, 上行锁单向流, 下行输出报文
r1 =  "rule RuleId ip-combination ipv4 dport 80/16   protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r2 =  "rule RuleId ip-combination ipv4 dport 8080/16 protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r3 =  "rule RuleId ip-combination ipv4 dport 443/16  protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r4 =  "rule RuleId ip-combination ipv4 sport 80/16   protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r5 =  "rule RuleId ip-combination ipv4 sport 8080/16 protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r6 =  "rule RuleId ip-combination ipv4 sport 443/16  protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r7 =  "rule RuleId ip-combination ipv6 dport 80/16   protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r8 =  "rule RuleId ip-combination ipv6 dport 8080/16 protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r9 =  "rule RuleId ip-combination ipv6 dport 443/16  protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId lock obverse"
r10 = "rule RuleId ip-combination ipv6 sport 80/16   protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r11 = "rule RuleId ip-combination ipv6 sport 8080/16 protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r12 = "rule RuleId ip-combination ipv6 sport 443/16  protocol 6/8 hex 0xab00 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"

#特征描述: ipv4/ipv6, 80/8080/443端口, tcp协议, 载荷前四个字节为0x00000003, 上行锁单向流, 下行输出报文
r13 = "rule RuleId ip-combination ipv4 dport 443/16  protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r14 = "rule RuleId ip-combination ipv4 dport 80/16   protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r15 = "rule RuleId ip-combination ipv4 dport 8080/16 protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r16 = "rule RuleId ip-combination ipv4 sport 443/16  protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"
r17 = "rule RuleId ip-combination ipv4 sport 80/16   protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"
r18 = "rule RuleId ip-combination ipv4 sport 8080/16 protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"
r19 = "rule RuleId ip-combination ipv6 dport 443/16  protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r20 = "rule RuleId ip-combination ipv6 dport 80/16   protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r21 = "rule RuleId ip-combination ipv6 dport 8080/16 protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId lock obverse"
r22 = "rule RuleId ip-combination ipv6 sport 443/16  protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"
r23 = "rule RuleId ip-combination ipv6 sport 80/16   protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"
r24 = "rule RuleId ip-combination ipv6 sport 8080/16 protocol 6/8 hex 0x00000003  operator OpeType forward oif-group GroupId"

#特征描述: ipv4/ipv6, 80/8080/443端口, tcp协议, 载荷前八个字节为POST /up, 输出双向流数据
r25 = "rule RuleId ip-combination ipv4 dport 443/16  protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"
r26 = "rule RuleId ip-combination ipv4 dport 80/16   protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"
r27 = "rule RuleId ip-combination ipv4 dport 8080/16 protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"
r28 = "rule RuleId ip-combination ipv6 dport 443/16  protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"
r29 = "rule RuleId ip-combination ipv6 dport 80/16   protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"
r30 = "rule RuleId ip-combination ipv6 dport 8080/16 protocol 6/8 hex 0x504F5354202F7570  operator OpeType forward oif-group GroupId lock stream"

#特征描述: ipv4/ipv6, 80/8080/443端口, tcp协议, 载荷前八个字节为POST /do, 输出上下行报文
r31 = "rule RuleId ip-combination ipv4 dport 443/16  protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r32 = "rule RuleId ip-combination ipv4 dport 80/16   protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r33 = "rule RuleId ip-combination ipv4 dport 8080/16 protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r34 = "rule RuleId ip-combination ipv4 sport 443/16  protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r35 = "rule RuleId ip-combination ipv4 sport 80/16   protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r36 = "rule RuleId ip-combination ipv4 sport 8080/16 protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r37 = "rule RuleId ip-combination ipv6 dport 443/16  protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r38 = "rule RuleId ip-combination ipv6 dport 80/16   protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r39 = "rule RuleId ip-combination ipv6 dport 8080/16 protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r40 = "rule RuleId ip-combination ipv6 sport 443/16  protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r41 = "rule RuleId ip-combination ipv6 sport 80/16   protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"
r42 = "rule RuleId ip-combination ipv6 sport 8080/16 protocol 6/8 hex 0x504F5354202F646F  operator OpeType forward oif-group GroupId"

#微信话单(非97)过滤规则
[wxa_not97]
r1  = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r2  = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r3  = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r4  = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r5  = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r6  = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r7  = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r8  = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r9  = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r10 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r11 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r12 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r13 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r14 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r15 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r16 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0xd500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"

r17 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r18 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId" 
r19 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r20 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r21 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r22 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"  
r23 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r24 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r25 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r26 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId" 
r27 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r28 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r29 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r30 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"  
r31 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r32 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xffffc0 operator OpeType forward oif-group GroupId"
r33 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r34 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"  
r35 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r36 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r37 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r38 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"  
r39 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r40 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r41 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r42 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"  
r43 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r44 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r45 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r46 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"  
r47 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"
r48 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x750000 keyword-mask 0xff000f operator OpeType forward oif-group GroupId"

r49 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r50 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r51 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r52 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r53 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r54 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r55 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r56 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r57 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r58 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r59 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r60 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r61 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r62 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r63 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r64 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r65 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r66 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r67 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r68 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r69 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r70 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r71 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r72 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r73 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r74 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r75 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r76 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r77 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r78 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r79 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r80 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x76030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"

r81 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r82 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r83 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r84 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r85 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r86 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r87 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r88 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r89 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r90 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r91 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r92 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r93 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r94 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"  
r95 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r96 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffffffc0 operator OpeType forward oif-group GroupId"
r97 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r98 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r99 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r100 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r101 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r102 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r103 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r104 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r105 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r106 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r107 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r108 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r109 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r110 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"  
r111 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"
r112 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x77030000 keyword-mask 0xffff000f operator OpeType forward oif-group GroupId"

r113 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r114 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r115 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r116 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r117 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r118 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r119 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r120 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r121 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r122 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r123 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r124 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r125 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r126 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r127 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r128 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x9500 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"

r129 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r130 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r131 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r132 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r133 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r134 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r135 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r136 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r137 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r138 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r139 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r140 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r141 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r142 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"  
r143 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r144 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0xd600 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"

r145 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r146 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r147 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r148 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r149 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r150 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r151 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r152 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r153 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r154 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r155 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r156 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r157 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r158 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"  
r159 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"
r160 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x9612 operator OpeType forward oif-group GroupId"

r161 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r162 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r163 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r164 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r165 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r166 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r167 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r168 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r169 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r170 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r171 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r172 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r173 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r174 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r175 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"
r176 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 6/8 hex 0xd800000000000800 keyword-mask 0xffffff0000ffffff operator OpeType forward oif-group GroupId"

r177 = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r178 = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r179 = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r180 = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r181 = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r182 = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r183 = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r184 = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r185 = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r186 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r187 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r188 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r189 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r190 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r191 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"
r192 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x9613 operator OpeType forward oif-group GroupId"

#微信群头像
[wxgh]
r1  = "rule RuleId ip-combination ipv4 dport 80/16    protocol 6/8 hex 0x474554202f6d6d63 operator OpeType forward oif-group GroupId lock stream"
r2  = "rule RuleId ip-combination ipv6 dport 80/16    protocol 6/8 hex 0x474554202f6d6d63 operator OpeType forward oif-group GroupId lock stream"

#微信位置
[wxp]
r1  = "rule RuleId ip-combination ipv4 dport 80/16    protocol 6/8 hex 0x474554202F617069 operator OpeType forward oif-group GroupId"
r2  = "rule RuleId ip-combination ipv6 dport 80/16    protocol 6/8 hex 0x474554202F617069 operator OpeType forward oif-group GroupId"

#微信话单(97)过滤规则
[wxa_97]
r1  = "rule RuleId ip-combination ipv4 dport 80/16    protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r2  = "rule RuleId ip-combination ipv4 dport 8000/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r3  = "rule RuleId ip-combination ipv4 dport 8080/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r4  = "rule RuleId ip-combination ipv4 dport 16285/16 protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r5  = "rule RuleId ip-combination ipv4 sport 80/16    protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r6  = "rule RuleId ip-combination ipv4 sport 8000/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r7  = "rule RuleId ip-combination ipv4 sport 8080/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r8  = "rule RuleId ip-combination ipv4 sport 16285/16 protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r9  = "rule RuleId ip-combination ipv6 dport 80/16    protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r10 = "rule RuleId ip-combination ipv6 dport 8000/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r11 = "rule RuleId ip-combination ipv6 dport 8080/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r12 = "rule RuleId ip-combination ipv6 dport 16285/16 protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r13 = "rule RuleId ip-combination ipv6 sport 80/16    protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r14 = "rule RuleId ip-combination ipv6 sport 8000/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r15 = "rule RuleId ip-combination ipv6 sport 8080/16  protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r16 = "rule RuleId ip-combination ipv6 sport 16285/16 protocol 17/8 hex 0x9700 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r17 = "rule RuleId keyword-exact-mask udp offset 12 hex 0x0010 keyword-mask 0xffc0 operator OpeType forward oif-group GroupId"
r18 = "rule RuleId keyword-exact-mask udp offset 12 hex 0x0010 keyword-mask 0x000f operator OpeType forward oif-group GroupId"

#微信话单(a3)过滤规则
[wxa_a3]
r1  = "rule RuleId acl-mask ipv4 * * * 80/16    17/8 operator OpeType drop"
r2  = "rule RuleId acl-mask ipv4 * * 80/16 *    17/8 operator OpeType drop"
r3  = "rule RuleId acl-mask ipv4 * * * 8080/16  17/8 operator OpeType drop"
r4  = "rule RuleId acl-mask ipv4 * * 8080/16 *  17/8 operator OpeType drop"
r5  = "rule RuleId acl-mask ipv4 * * * 8000/16  17/8 operator OpeType drop"
r6  = "rule RuleId acl-mask ipv4 * * 8000/16 *  17/8 operator OpeType drop"
r7  = "rule RuleId acl-mask ipv4 * * * 16285/16 17/8 operator OpeType drop"
r8  = "rule RuleId acl-mask ipv4 * * 16285/16 * 17/8 operator OpeType drop"
r9  = "rule RuleId acl-mask ipv6 * * * 80/16    17/8 operator OpeType drop"
r10 = "rule RuleId acl-mask ipv6 * * 80/16 *    17/8 operator OpeType drop"
r11 = "rule RuleId acl-mask ipv6 * * * 8080/16  17/8 operator OpeType drop"
r12 = "rule RuleId acl-mask ipv6 * * 8080/16 *  17/8 operator OpeType drop"
r13 = "rule RuleId acl-mask ipv6 * * * 8000/16  17/8 operator OpeType drop"
r14 = "rule RuleId acl-mask ipv6 * * 8000/16 *  17/8 operator OpeType drop"
r15 = "rule RuleId acl-mask ipv6 * * * 16285/16 17/8 operator OpeType drop"
r16 = "rule RuleId acl-mask ipv6 * * 16285/16 * 17/8 operator OpeType drop"
r17 = "rule RuleId ip-combination ipv4 protocol 17/8 hex 0xa300 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"
r18 = "rule RuleId ip-combination ipv6 protocol 17/8 hex 0xa300 keyword-mask 0xff00 operator OpeType forward oif-group GroupId"

[file_check]
template_update.txt = 2ba0f103ad6d9b54c0cf90bbe52b3ce7
