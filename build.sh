#!/bin/bash
# by zsw at 2018-11-23 22:46
#
#1.构建步骤：
# a.首次编译时执行以下命令，准备编译环境
#   ../10_第三方/01_rpm_for_build/install_deps.sh
# b.wx_dpi 基于 dpdk，首次编译或者 dpdk 被修改后使用以下命令构建 dpdk:
#   ./build dpdk
# c.构建 wx_dpi 与 vtysh, 输出位于 run 目录
#   ./build
#2.清理：
# a.清理 dpdk:
#   ./build dpdk clean
# b.清理 wx_dpi 与 vtysh:
#   ./build clean
#3.添加新协议需要重新生dpi_proto_ids.c dpi_proto_ids.h源码
#  内含对应枚举和宏，执行如下命令：
#  ./build add_proto

kill -9 $(pidof yaWxDpi)
cd $(dirname $0)

function step
{
    if [ $? == 0 ]
    then
        echo "-- excuting: $*"
        eval $*
    else
        echo "-- error: previous operation failed, exit"
        exit -1
    fi
}

function LOG_ERR() {
    echo -e "\033[1;31;40m"$*"\033[0m"
}

function LOG_WAR() {
    echo -e "\033[1;33;40m"$*"\033[0m"
}

function LOG_INFO() {
    echo -e "\033[1;32;40m"$*"\033[0m"
}

function LOG_DEBUG() {
    echo -e "\033[1;30;40m"Debug:=======\>\>$* "\033[0m"
}

usage() {
    LOG_WAR "Usage: $0 [-d] [-r|-b|-c] [clean|add_proto]"
    LOG_WAR " "
    LOG_WAR "options:"
    LOG_WAR " -d  whether or not to build debug"
    LOG_WAR "exclusive options:"
    LOG_WAR " -r build Release tar"
    LOG_WAR " -b build Beta tar"
    LOG_WAR " -c build RC tar"
    LOG_WAR "arguments:"
    LOG_WAR " clean \t\t clean all build"
    LOG_WAR " add_proto \t add new proto and build"

    exit 1
}

ver_type=""
while getopts ":drcb" opt;do
    case "${opt}" in
        d) export BUILD_DEBUG="yes"
            # debug=${OPTARG}
            # (( debug == 0 || debug == 1 )) || usage
            ;;
        r) [ -n "${ver_type}" ] && usage || ver_type="Release" ;;
        c) [ -n "${ver_type}" ] && usage || ver_type="RC" ;;
        b) [ -n "${ver_type}" ] && usage || ver_type="Beta" ;;
        *)  usage ;;
    esac
done
shift $((OPTIND-1))

export LC_ALL="en_US.UTF-8"
export RTE_SDK="${PWD}/dpdk/src"
export RTE_TARGET=x86_64-native-linuxapp-gcc
#export EXTRA_CFLAGS="-O0 -g"

echo "RTE_SDK=${RTE_SDK}"
CUR_DIR=$(pwd)
if [ ${1:-""} == "add_proto" ];then
    shift 1
    $CUR_DIR/src/write_protocol.sh
fi

TS=`date +"%Y%m%d"`
branch=$(git  rev-parse --abbrev-ref HEAD)
version=$(git log -1 --pretty=format:%h)
#tag=$(git describe --tag $(git rev-list --tags --max-count=1))
tag=$(git describe --abbrev=0 --tags)
date=$(date +%y%m%d)
########################################################
build_cmd="make -C src -j 4 $* && ln -srf run/yaWxDpi_${tag}  run/yaWxDpi"

build_vtysh="make -C vtysh $*"

build_wxcs="mkdir -p  wxcs/build; cd wxcs/build && cmake3 .. && make -j 8 && cd - \
           && ln -sfr  run/yaWxcs_${tag}  run/yaWxcs "
##########################################################

clean_cmd="rm -rf src/build/*                         \
           rm -rf wxcs/build/*                        \
           && make -C src $*                          \
           && make -C vtysh $*"

# check the mode
if [ ${1:-""} == "dpdk" ]    # use "" if there is not $1
then
   shift
   build_cmd="make -C ${RTE_SDK} defconfig           \
              && make -C ${RTE_SDK}                  \
                   T=${RTE_TARGET}                   \
                   prefix=${RTE_SDK}/${RTE_TARGET}   \
                   install $*"

   clean_cmd="rm -rf ${RTE_SDK}/${RTE_TARGET}/lib/* \
              && make -C ${RTE_SDK} clean"
fi

# do clean
if [ ${1:-""} == "clean" ]
   then
      step ${clean_cmd}
      exit
fi
function build_lua()
{
    echo "-------------------------"
    echo "do lua build"
    rm run/lua_adapt -rf
    cp etc/lua_adapt run/lua_adapt -r
    cd run/lua_adapt
    rm *.yalua -rf
    for filename in *.lua ; do
        if [ -f "$filename" ] ; then
            echo " yaluaGen $filename"
            ./yaluaGen.sh  $filename
        fi
    done
    echo "-------------------------"
    cd -
}
function release()
{
    # date=$(date +%Y_%m_%d)
    root_dir="/home/<USER>/release/02_wxdpi"
    [[ -d ${root_dir} ]] || mkdir -p ${root_dir}
    suffix=${tag}.${date}_${ver_type}
    pub_app_name=yaWxDpi_${suffix}
    # dir="yaWxDpi_${tag}_release"
    dir=${pub_app_name}
    dir_wxdpi="${dir}/yaWxDpi_${tag}"
    dpi_name=yaWxDpi_${suffix}

    rm -rf ${dir}*
    # wxDPi
    rm -rf ${dir_wxdpi}*
    mkdir -p ${dir_wxdpi}
    mkdir -p ${dir_wxdpi}/lua_adapt
    install -C -m 755 run/yaWxDpi_${tag} ${dir_wxdpi}/${dpi_name}
    install -C -m 755 run/start.sh       ${dir_wxdpi}
    install -C -m 644 run/ip2region.db      ${dir_wxdpi}
    install -C -m 644 dpi_rules/template_update.txt      ${dir_wxdpi}
    install -C -m 755 vtysh/vtysh        ${dir_wxdpi}/vtysh_${suffix}
    cp run/lua_adapt/*.yalua  -r     ${dir_wxdpi}/lua_adapt
    # 配置文件 只从提交区提取
    git show HEAD:./etc/config.ini                    > ${dir_wxdpi}/config.ini_${suffix}
    git show HEAD:./etc/http_host_uri_qq_list.txt     > ${dir_wxdpi}/http_host_uri_qq_list.txt
    git show HEAD:./etc/http_host_uri_wx_list.txt     > ${dir_wxdpi}/http_host_uri_wx_list.txt
    git show HEAD:./etc/http_host_uri_wx_msg_list.txt > ${dir_wxdpi}/http_host_uri_wx_msg_list.txt
    git show HEAD:./etc/http_host_wxpay.txt           > ${dir_wxdpi}/http_host_wxpay.txt
    ln -fsr                ${dir_wxdpi}/${dpi_name}    ${dir_wxdpi}/yaWxDpi
    ln -fsr                ${dir_wxdpi}/vtysh_${suffix}      ${dir_wxdpi}/vtysh
    ln -fsr                ${dir_wxdpi}/config.ini_${suffix} ${dir_wxdpi}/config.ini
    # ln -fsr                ${dir_wxdpi}/http_host_uri_qq_list.txt_${suffix}      ${dir_wxdpi}/http_host_uri_qq_list.txt
    # ln -fsr                ${dir_wxdpi}/http_host_uri_wx_list.txt_${suffix}      ${dir_wxdpi}/http_host_uri_wx_list.txt
    # ln -fsr                ${dir_wxdpi}/http_host_uri_wx_msg_list.txt_${suffix}  ${dir_wxdpi}/http_host_uri_wx_msg_list.txt
    # ln -fsr                ${dir_wxdpi}/http_host_wxpay.txt_${suffix}            ${dir_wxdpi}/http_host_wxpay.txt

    # wxcs
    dir_wxcs="${dir}/yaWxcs_${tag}"
    wxcs_name=yaWxcs_${suffix}
    rm -rf   ${dir_wxcs}*
    [[ ! -d ${dir_wxcs} ]] && mkdir -p ${dir_wxcs}
    install -C -m 755 run/yaWxcs_${tag}  ${dir_wxcs}/${wxcs_name}
    git show HEAD:./wxcs/etc/wxcs.conf                > ${dir_wxcs}/wxcs.conf_${suffix}
    ln -fsr                ${dir_wxcs}/yaWxcs_${suffix}     ${dir_wxcs}/yaWxcs
    ln -fsr                ${dir_wxcs}/wxcs.conf_${suffix}     ${dir_wxcs}/wxcs.conf

    #tar ball
    install -C -m 644 changelog                    ${dir}
    install -C -m 644 "doc/01_微信专项-安装文档.docx" ${dir}/01_install.docx
    install -C -m 644 "doc/02_微信专项_升级文档.docx" ${dir}/02_update.docx
    install -C -m 644 "doc/install.sh"             ${dir}/install.sh

    tar_name=${dir}.tar.gz
    tar -zcf ${tar_name} ${dir}
    rm ${dir} -rf
    mv ${tar_name} ${root_dir}
    LOG_INFO "mv ${tar_name} to ${root_dir}"
}

# do build
step ${build_wxcs}
step ${build_cmd}
build_lua
step ${build_vtysh}

rm -rf run/*.map
rm -rf run/.*.cmd

[ -n "${ver_type}" ] && release
