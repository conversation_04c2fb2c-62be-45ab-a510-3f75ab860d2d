/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <event2/bufferevent.h>
#include <event2/listener.h>
#include <glib.h>

#include "wx_proxy_conf.h"
#include "wx_proxy.h"
#include "wx_proxy_thread.h"
#include "wx_proxy_log.h"
#include "wx_proxy_mirror.h"
#include "wx_proxy_common.h"

/*****************************************************************
*Function    : session_timeout
*Description : free time out mirror session
*Input       : mirror session pool
*Output      : none
*Return      : no care
*Others      : none
*****************************************************************/
int
session_timeout(GPtrArray *session_manager)
{
//    struct mirror_t    *mirror_t = NULL;
//    struct thread_info *thread = NULL;
//
//    for(int i = 0; i < session_manager->len; i++)
//    {
//        mirror  = (struct session*)g_ptr_array_index(session_manager, i);
//        thread = mirror->thread;
//
//        if(SESSION_CONNECT_DONE != mirror->status || 1 != mirror->release)
//        {
//            continue;
//        }
//
//        log_write(DEBUG, "session timeout %p ...", mirror);
//        bev_free  (mirror->master, NULL);
//        bev_free  (mirror->master, NULL);
//        bev_free  (mirror->master, NULL);
//        mirror_free(ps);
//        g_ptr_array_remove(pts->session_manager, ps);
//        ATOMIC_SUB_FETCH(&thread->ref);
//        log_write(INFO, "remove session %p OK", ps);
//    }

    return 0;
}


/*****************************************************************
*Function    : show_session
*Description : printf event thread has mirror sessions
*Input       : proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
show_session(struct context *ctx)
{
    struct thread_info   *thread;
    GArray               *thread_manager;
    int                   sessions;
    int                   session_total;

    sessions        = 0;
    session_total   = 0;

    thread_manager = ctx->thread_manager;

    for(int i = 0; i < thread_manager->len; i++)
    {
        thread           = ((struct thread_info*)thread_manager->data) + i;
        sessions         = thread->session_manager->len; // sessions
        session_total   += sessions;
        log_write(DEBUG , "%3d sessions on thread %2d", sessions, thread->index);
    }
    log_write(DEBUG , "%3d sessions total", session_total);
}

/*****************************************************************
*Function    : do_master_timer
*Description : when master thread timeer is done, callback
*Input       : proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
do_master_timer(evutil_socket_t fd, short what, void *context)
{
    struct context *ctx = context;

    //show_session(ctx);
    //log_write(DEBUG , "");   // show empty line
    evtimer_add(ctx->master_timer, &ctx->master_tv); // timer again ...
}

/*****************************************************************
*Function    : do_thread_timer
*Description : when worker thread timeer is done, callback
*Input       : proxy ctx
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
do_thread_timer(evutil_socket_t fd, short what, void *thread)
{
    struct thread_info *pts = thread;

    pts              = thread;
    session_timeout(pts->session_manager);
    evtimer_add(pts->thread_timer, &pts->thread_tv);
}
