#pragma once
/****************************************************************************************
 * 文 件 名 : tbl_value_PersonULI.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : zhangsx      '2019-09-16
* 编    码 : zhangsx      '2019-09-16
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2019 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/
#include <map>
#include <unordered_map>
#include <set>
#include <string>

#include "tbl_value.h"

class TblValuePersonULI : public TblFieldValue
{
public:
    TblValuePersonULI();
    TblValuePersonULI(const std::string&);
    TblValuePersonULI(const std::string&, const std::string&);
    ~TblValuePersonULI() = default;

    void           setFieldAndProto(const std::string &, const std::string &)            override;
    std::string    getMatchFormat(const std::string &)                                   override;
    std::string    getRequestFieldValue(std::string &, std::string &, int)               override;
    void           setFieldTable(std::vector<std::pair<std::string, int>> &)             override;
    void           destoryItem()                                                         override;

private:
    const std::set<std::string>        sub_fields = {"eNodeBid", "CellId", "LAC" , "SAC"};              // 字段本身的格式(包含的子字段)

    std::map<std::string, std::string> proto_subfield_map;                                              // 协议名和子字段的映射

    std::string                        extra_field = "RT_BS";                                           // 解析此字段时需要额外配合的字段值

    std::map<uint32_t, uint32_t>       field_index_map;                                                 // 字段名与额外字段名解析所需的映射
};