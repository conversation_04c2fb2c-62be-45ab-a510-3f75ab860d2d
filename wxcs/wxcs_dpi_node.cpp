/****************************************************************************************
 * 文 件 名 : wxcs_dpi_node.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设    计 :       '2019-08-14
 * 编    码 :       '2019-08-14
 * 修    改 :
 ****************************************************************************************
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2019 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 ***************************************************************************************/
#include <map>
#include <thread>
#include <mutex>
#include "wxcs_dpi_node.h"

typedef struct dpi_node_s
{
    std::mutex    node_mutex;
    std::string   wxdpi_Name;
    int           wxdpi_Name_init;
    int           wxdpi_time;
    int           wxcs_time;
    int           sessions;
} dpi_node_t;

std::map<int, dpi_node_t> dpi_node_map;


void inc_node_sessions(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    dpi_node_map[node_id].sessions++;
}

void sub_node_sessions(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    dpi_node_map[node_id].sessions--;
}

int  get_node_sessions(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    return dpi_node_map[node_id].sessions;
}

int set_node_timestamp(int node_id, int type, int wxdpi_time)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    dpi_node_map[node_id].wxdpi_time = wxdpi_time;
    dpi_node_map[node_id].wxcs_time  = time(NULL);
    return wxdpi_time;
}

int set_node_name(int node_id, const char *Area, const char *Blade)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    if(1 == dpi_node_map[node_id].wxdpi_Name_init)
    {
        return 0;
    }

    dpi_node_map[node_id].wxdpi_Name += Area;
    dpi_node_map[node_id].wxdpi_Name += "_";
    dpi_node_map[node_id].wxdpi_Name += Blade;
    dpi_node_map[node_id].wxdpi_Name_init = 1;
    return 0;
}

std::string get_node_name(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    return dpi_node_map[node_id].wxdpi_Name;
}

int get_node_timestamp(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    return dpi_node_map[node_id].wxdpi_time;
}

int get_wxcs_timestamp(int node_id)
{
    std::lock_guard<std::mutex> lock(dpi_node_map[node_id].node_mutex);
    return dpi_node_map[node_id].wxcs_time;
}

