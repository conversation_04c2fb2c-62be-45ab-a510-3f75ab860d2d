/*
 * Copyright (C) 李春利
 * Copyright (C) 上海阅维科技股份有限公司
 */

#include <unistd.h>
#include <fcntl.h>
#include <paths.h>
#include <utmpx.h>

#include <stdio.h>
#include <string.h>
#include <stdlib.h>


#include <event2/bufferevent.h>
#include "wx_proxy_common.h"

static int ref;

/*****************************************************************
*Function    : format_sockaddr
*Description : formnat sockaddr to string with ip and port, example: 127.0.0.1:1080
*Input       : sockaddr
*Output      : string
*Return      : the buffer of input
*Others      : none
*****************************************************************/
const char *
format_sockaddr(const struct sockaddr *sa, char *out, size_t outlen)
{
    char b[128];
    const char *res=NULL;
    int port;
    if (sa->sa_family == AF_INET)
    {
        const struct sockaddr_in *sin = (const struct sockaddr_in*)sa;
        res = evutil_inet_ntop(AF_INET, &sin->sin_addr,b,sizeof(b));
        port = ntohs(sin->sin_port);
        if (res)
        {
            evutil_snprintf(out, outlen, "%s:%d", b, port);
            return out;
        }
    }
    else
    if (sa->sa_family == AF_INET6)
    {
        const struct sockaddr_in6 *sin6 = (const struct sockaddr_in6*)sa;
        res = evutil_inet_ntop(AF_INET6, &sin6->sin6_addr,b,sizeof(b));
        port = ntohs(sin6->sin6_port);
        if (res)
        {
            evutil_snprintf(out, outlen, "[%s]:%d", b, port);
            return out;
        }
    }

    evutil_snprintf(out, outlen, "<addr with socktype %d>", (int)sa->sa_family);
    return out;
}

/*****************************************************************
*Function    : palloc
*Description : instead of malloc, and set zero
*Input       : memory size
*Output      : none
*Return      : pointer of memory address
*Others      : none
*****************************************************************/
void*
palloc(int size)
{
    void *p = malloc(size);
    if(p)
    {
        memset(p, 0, size);
        ATOMIC_ADD_FETCH(&ref);
        return p;
    }
    return NULL;
}

/*****************************************************************
*Function    : pfree
*Description : instead of free
*Input       : The function frees the memory space pointed to by ptr
             : which must have been returned by a previous call to palloc
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
pfree(void*p)
{
    if(p)
    {
        free(p);
        ATOMIC_SUB_FETCH(&ref);
    }
}

/*****************************************************************
*Function    : get_counter
*Description : return malloc reference counter
*Input       : none
*Output      : none
*Return      : reference counter
*Others      : none
*****************************************************************/
int
get_counter()
{
    return ref;
}

/*****************************************************************
*Function    : pexit
*Description : instead exit
*Input       : exit code
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
pexit(int code)
{
    sleep(1);
    exit(code);
}


/*****************************************************************
*Function    : wall
*Description : notification to all login user
*Input       : Format of the string, parameters is option
*Output      : none
*Return      : none
*Others      : none
*****************************************************************/
void
wall(const char *format, ...)
{
    struct utmpx *utmpptr;
    va_list       args;
    char          tty    [1024];
    char          strbuff[1024];
    int           fd;
    int           len;

    if(NULL == format)
    {
        return;
    }

    va_start(args, format);
    vsnprintf(strbuff, sizeof(strbuff), format, args);
    va_end(args);
    len = strlen(strbuff);

    setutxent();
    while((utmpptr = getutxent()))
    {
        if (!utmpptr->ut_user[0])
        {
            continue;
        }

        if (utmpptr->ut_type != USER_PROCESS)
        {
            continue;
        }

        if (!*utmpptr->ut_line || *utmpptr->ut_line == ':')
        {
            continue;
        }

        snprintf(tty, sizeof(tty), "%s%s", _PATH_DEV, utmpptr->ut_line);
        if ((fd = open(tty, O_WRONLY|O_NONBLOCK, 0)) < 0)
        {
            return ;
        }

        write(fd, strbuff, len);
        close(fd);
    }
}

