/****************************************************************************************
 * 文 件 名 : dpi_skype_media.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设    计: 李春利            2019/11/15
 * 编    码: zhangsx           2019/11/15
 * 修    改: 
 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *        (C)Copyright 2019 YView    Corporation All Rights Reserved.
 * - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
****************************************************************************************/
#include <pthread.h>
#include <unistd.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <rte_rwlock.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_skype_media.h"
#include "dpi_dissector.h"
#include "jhash.h"
#include "wxcs_def.h"

typedef struct st_skype_chat
{
    uint32_t StunRequestsA2B;       // 记录流中stun bind request数
    uint32_t StunResponsesA2B;      // 记录流中stun success response数
    uint32_t StunRequestsB2A;       // 记录流中stun bind request数
    uint32_t StunResponsesB2A;      // 记录流中stun success response数
    uint32_t DataA2B;               // 记录流中带序号包上行数
    uint32_t DataB2A;               // 记录流中带序号包下行数
    uint32_t OtherA2B;              // 记录已识别的其他包上行数
    uint32_t OtherB2A;              // 记录已识别的其他包下行数
    uint32_t UnknownA2B;            // 记录流中其他包数上行
    uint32_t UnknownB2A;            // 记录流中其他包数下行

    uint64_t BytesA2B;              // 记录流上行总字节数
    uint64_t BytesB2A;              // 记录流下行总字节数

    uint16_t seqA2B;                // 记录上次会话正向包序号
    uint16_t seqB2A;                // 记录上次会话反向包序号
    int      sessionType;           // 会话类型 (目前仍不确定)
    int      lastDirection;         // 上帧方向 (可能对stun有用)

    uint32_t FirstActiveTime;       // 流开始时间
    uint32_t DataActiveTime;        // 数据开始时间
    uint32_t LastActiveTime;        // 流上次活跃时间

    uint8_t  sessionID_len;         // 流中sessionID的长度
    uint8_t  sessionID[9];          // 流中恒定不变的sessionID

} ST_SkypeChat;

/********************************************
 * 函 数 名 : isNeedStun
 * 功    能 : 检测复合要求的stun帧
 * 参    数 : const uint8_t *payload, const uint16_t payload_len
 * 返 回 值 : 1(帧是stun), 0(帧不是stun)
 ********************************************/
static uint8_t isNeedStun(const uint8_t *payload, const uint16_t payload_len)
{
    if (NULL == payload)
    {
        return 0;
    }

    if (payload_len < 18)
    {
        return 0;
    }

    if ( (get_uint16_ntohs(payload, 0) == 0x0101 || get_uint16_ntohs(payload, 0) == 0x0001)  /* skype stun:(bind request : 0x0001, bind success response : 0x0101)(识别type) */
      && (get_uint16_ntohs(payload, 2) == payload_len - 20)                                  /* 识别len , 与stun的attribute长度相等， */
      && (get_uint32_ntohl(payload, 4) == 0x2112a442) )                                      /* 识别magic cookie，这是classicstun与stun的报文特征的最大区别， stun cookie为固定值 0x2112a442 */
    {
        return 1;
    }
    return 0;
}

/********************************************
 * 函 数 名 : isSkypeUDP
 * 功    能 : 检测Skype帧
 * 参    数 : const uint8_t *payload
 * 返 回 值 : 1(帧是skype), 0(帧不是skype)
 ********************************************/
static uint8_t isSkypeUDP(const uint8_t *payload)
{
    if (NULL != payload)
    {
        if (payload[0] == 0x90 || payload[0] == 0x80 || payload[0] == 0x81)
        {
            return 1;
        }
    }
    
    return 0;
}

/********************************************
 * 函 数 名 : check_sequence_num
 * 功    能 : 检测流内序号
 * 参    数 : struct flow_info *flow , uint32_t seq , uint8_t direction
 * 返 回 值 : 0(正常)，-1(不正常) 
 ********************************************/
static int check_sequence_num(struct flow_info *flow, uint32_t seq, uint8_t direction)
{
    if (NULL == flow->app_session)
    {
        return -1;
    }

    ST_SkypeChat * pst_SkypeChat = (ST_SkypeChat *)flow->app_session;

    // Sequence 抖动检测
    if (FLOW_DIR_SRC2DST == direction)
    {
        // 第一次来，先赋值
        if (pst_SkypeChat->seqA2B == 0)
        {
            pst_SkypeChat->seqA2B = seq;
            return 0;
        }

        // 双字节的 Sequence, 是不是到达了轮回的边缘 ?
        if (pst_SkypeChat->seqA2B > 65500 && seq < pst_SkypeChat->seqA2B)
        {
            pst_SkypeChat->seqA2B = seq; // 新的轮回
        }

        // 抖动 检测
        uint32_t diff = seq > pst_SkypeChat->seqA2B ? seq - pst_SkypeChat->seqA2B : pst_SkypeChat->seqA2B - seq;
        if ( diff > g_config.wx_session_seq_jitter)
        {
            return -1; //这是一个错包, 拜拜
        }

        // 这是一个OK的包
        pst_SkypeChat->seqA2B = seq;
    }
    else
    {
        // 第一次来，先赋值
        if (pst_SkypeChat->seqB2A == 0)
        {
            pst_SkypeChat->seqB2A = seq;
            return 0;
        }
        // 双字节的 Sequence, 是不是到达了轮回的边缘 ?
        if (pst_SkypeChat->seqB2A > 65500 && seq < pst_SkypeChat->seqB2A)
        {
            pst_SkypeChat->seqB2A = seq; // 新的轮回
        }

        // 抖动 检测
        uint32_t diff = seq > pst_SkypeChat->seqB2A ? seq-pst_SkypeChat->seqB2A : pst_SkypeChat->seqB2A - seq;
        if ( diff > g_config.wx_session_seq_jitter)
        {
            return -1; //这是一个错包, 拜拜
        }

        // 这是一个OK的包
        pst_SkypeChat->seqB2A = seq;
    }

    return 0;
}


/********************************************
 * 函 数 名 : Skype_FlowMemInit
 * 功    能 : Skype流内存初始化
 * 参    数 : struct flow_info *flow
 * 返 回 值 : ST_SkypeChat * 
 ********************************************/
static ST_SkypeChat* Skype_FlowMemInit(struct flow_info *flow)
{
    if (NULL == flow)
    {
        return NULL;
    }

    if (NULL == flow->app_session)
    {
        ST_SkypeChat* pst_SkypeChat = (ST_SkypeChat*)calloc(1, sizeof(ST_SkypeChat));
        
        if (NULL == pst_SkypeChat)
        {
            DPI_LOG(DPI_LOG_ERROR, "error on malooc ST_SkypeChat");
            exit(-1);
        }

        flow->app_session = pst_SkypeChat;
        pst_SkypeChat->sessionType = WXCS_SESSION_CHAT_UNKNOWN;
    }

    return flow->app_session;
}

/********************************************
 * 函 数 名 : isSkypeFlowStart
 * 功    能 : 判断Skype单向流启动时间
 * 参    数 : struct flow_info *flow
 * 返 回 值 : 1(已开始)，0(未开始)  
 ********************************************/
static uint8_t isSkypeFlowStart(ST_SkypeChat* session)
{
    if (NULL != session)
    {
        if ( (session->StunRequestsA2B > 0 && session->StunResponsesA2B > 0 
          && session->StunRequestsB2A > 0 && session ->StunResponsesB2A > 0) )
        {
            return 1;
        }
    }
    return 0;
}

static int BinToHex(const unsigned char *inPut, unsigned int inPutLen, char *OutBuffer, unsigned int OutBufferSize)
{
    if (NULL == inPut || NULL == OutBuffer)
    {
        return -1;
    }

    if (OutBufferSize < inPutLen)
    return -1;

    char * pOrigin = OutBuffer;
    unsigned int i = 0;
    for(i = 0; i < inPutLen; i++)
    {
        snprintf(OutBuffer, OutBufferSize, "%02X", (unsigned char)inPut[i]);
        OutBuffer+=2;
    }
    return OutBuffer - pOrigin;
}

/********************************************
 * 函 数 名 : check_session_id
 * 功    能 : 检测双向流session id正确性，如果流上没有
 * 参    数 : struct flow_info *flow
 * 返 回 值 : 1(正确)，0(错误)  
 ********************************************/
static uint8_t check_session_id(const uint8_t* payload, const uint16_t payload_len, ST_SkypeChat* session)
{
    if (NULL == session || NULL == payload)
    {
        return 0;
    }

    if (payload[0] == 0x90)
    {
        if (session->sessionID_len == 0)
        {
            memcpy(session->sessionID, payload + 8, 9);

            session->sessionID_len = 9;
        }

        return memcmp(payload + 8, session->sessionID, 9) ? 0 : 1;
    }

    return 0;
}

static pthread_t skype_session_thread;

pthread_rwlock_t skype_rwlock = PTHREAD_RWLOCK_INITIALIZER; //定义和初始化读写锁

//static rte_rwlock_t skype_rwlock;

static void *skype_hash = NULL;
static wxc_handle skype_handle; //  仅供本协议使用

/* hash 表初始化 */
static void* skype_session_hash_init(void *hash)
{
    pthread_rwlock_wrlock(&skype_rwlock);     //写锁
    if (NULL == hash)
    {
        hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    }
    pthread_rwlock_unlock(&skype_rwlock);     //解锁

    return hash;
}

/* hash 添加节点 */
static int skype_session_hash_insert(void* hash, char *key, void* value)
{
    if (NULL == hash || NULL == key || NULL==value)
    {
        return -1;
    }
    pthread_rwlock_wrlock(&skype_rwlock);     //写锁
    int ret = g_hash_table_insert ((GHashTable *)hash, key, value);
    pthread_rwlock_unlock(&skype_rwlock);     //解锁
    return  ret;
}

/* hash 查找节点 */
static void* skype_session_hash_find(void* hash, char *pStr)
{
    if (NULL == hash || NULL == pStr)
    {
        return NULL;
    }

    pthread_rwlock_rdlock(&skype_rwlock);     //读锁
    void* ret = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&skype_rwlock);     //解锁
    return ret;
}
/* hash 节点个数 */
static size_t skype_session_hash_get_size(void* hash)
{
    if (NULL == hash)
    {
        return 0;
    }
    pthread_rwlock_rdlock(&skype_rwlock);     //读锁
    size_t ret = g_hash_table_size((GHashTable *)hash);
    pthread_rwlock_unlock(&skype_rwlock);     //解锁
    return ret;
}

/* hash 删除节点 */
static int skype_session_hash_delete(void* hash, char *pStr)
{
    if (NULL == hash || NULL == pStr)
    {
        return -1;
    }
    pthread_rwlock_wrlock(&skype_rwlock);     //写锁
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    pthread_rwlock_unlock(&skype_rwlock);     //解锁
    return 0;
}

/* hash 删除节点 (线程不安全)*/
static int skype_session_hash_delete_unthread_safe(void* hash, char *pStr)
{
    if (NULL == hash || NULL == pStr)
    {
        return -1;
    }
    int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
    return 0;
}

/* hash 销毁 */
static void skype_session_hash_destory(void* hash)
{
    if (NULL == hash)
    {
        return;
    }
    pthread_rwlock_wrlock(&skype_rwlock);     //写锁
    g_hash_table_destroy((GHashTable *)hash);
    pthread_rwlock_unlock(&skype_rwlock);     //解锁
}

static void* phone_watch_init(void)
{
    char  phone_number[16];
    int   safelen;
    void* hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if (NULL == hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }

    if (0 != memcmp(g_config.wx_phone_watch, "null", 4))
    {
        int    i = 0;
        char*p   = (char*)&g_config.wx_phone_watch;
        int size = sizeof(g_config.wx_phone_watch);

        char *pLeft  = p;
        char *pRight = pLeft;
        while('\0' != *p)
        {
            pRight = strchr(pLeft, ',');
            if (NULL == pRight)
            {
                // 探测是不是只有一个手机号，末尾没有逗号
                int len = strlen(pLeft);
                if (len >= 13 && len <15)//一个手机号就是13个字符
                {
                    char *pStr = pLeft;
                    g_hash_table_insert((GHashTable *)hash, g_strdup(pStr), g_strdup(pStr));
                }

                break;
            }
            safelen = sizeof(phone_number) < (unsigned)(pRight - pLeft) ? sizeof(phone_number) : (unsigned)(pRight - pLeft);
            memset(phone_number, 0, sizeof(phone_number));
            memcpy(phone_number, pLeft, safelen);
            g_hash_table_insert((GHashTable *)hash, g_strdup(phone_number), g_strdup(phone_number));
            pLeft = ++pRight;
        }
    }
    else
    {
        return NULL;
    }
    return hash;
}

static int phone_watch_find(void *hash, const char*pStr)
{
    if (NULL == hash)
    {
        return -1;
    }

    gpointer p = g_hash_table_lookup((GHashTable *)hash, (gconstpointer)pStr);
    if (NULL == p)
    {
        return -1;
    }
    return 1;
}

static void phone_watch_fini(void* hash)
{
    if (NULL == hash)
    {
        return;
    }
    g_hash_table_destroy((GHashTable *)hash);
}

static void print_session(ST_SkypeMediaSessionAlive* p)
{
    if (NULL == p)
    {
        return;
    }

    int  ret = 0;
    char buff1[64];

    ret = BinToHex(p->SessionID, 8, buff1, 64);
    buff1[ret] = 0;

    unsigned char* ip_client = (unsigned char *)(&p->client_ip.ipv4);
    unsigned char* ip_server = (unsigned char *)(&p->server_ip.ipv4);

    dpi_TrailerDump(&p->trailer);

    printf("sessionID         :%s\n",  buff1);
    printf("sessionType       :%d\n",  p->SessionType);
    printf("port_client       :%u\n",  p->client_port);
    printf("port_server       :%u\n",  p->server_port);

    printf("ip_client         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->client_ip)+0),
            *((uint8_t*)(&p->client_ip)+1),
            *((uint8_t*)(&p->client_ip)+2),
            *((uint8_t*)(&p->client_ip)+3));

    printf("ip_server         :%d.%d.%d.%d\n",
            *((uint8_t*)(&p->server_ip)+0),
            *((uint8_t*)(&p->server_ip)+1),
            *((uint8_t*)(&p->server_ip)+2),
            *((uint8_t*)(&p->server_ip)+3));

    printf("A2BTransPackets   :%d\n", p->PersonA2BTransPackets);
    printf("B2ATransPackets   :%d\n", p->PersonB2ATransPackets);
    printf("A2BTransBytes     :%d\n", p->PersonA2BTransBytes);
    printf("B2ATransBytes     :%d\n", p->PersonB2ATransBytes);
    printf("A2B_Stun_Pcakets  :%d\n", p->PersonA2B_Stun_Packets);
    printf("B2A_Stun_Pcakets  :%d\n", p->PersonB2A_Stun_Packets);
    printf("FirstActiveTime   :%d\n", p->PersonFirstActiveTime);
    printf("LastActiveTime    :%d\n", p->PersonLastActiveTime);
    printf("A2B Bytes/Pkts    :%d\n", p->PersonA2BTransPackets==0 ? 0:p->PersonA2BTransBytes/p->PersonA2BTransPackets);
    printf("B2A Bytes/Pkts    :%d\n", p->PersonB2ATransPackets==0 ? 0:p->PersonB2ATransBytes/p->PersonB2ATransPackets);
    printf("A2BUnknownPackets :%d\n", p->PersonA2BUnknown);
    printf("B2AUnknownPackets :%d\n", p->PersonB2AUnknown);
    printf("DurationTime      :%d\n", p->PersonLastActiveTime - p->PersonFirstActiveTime);

    printf("\n");
}

static void print_st(ST_SkypeMediaSessionAlive* p)
{
    if(0 == g_config.debug_skype_media)
    {
        return;
    }

    if(NULL == p)
    {
        return;
    }

    print_session(p);
}

// skype话单会话管理线程  入口
static void *skype_media_session_thread(void *arg)
{
#define MAX_TIME_OUT_NODE 1024*256

    // Hash init
    skype_hash = skype_session_hash_init(skype_hash);
    if (NULL == skype_hash)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create hash");
        exit(-1);
    }
    void* hash = skype_hash;

    // API init
    if (g_config.skype_port > 0 && g_config.skype_ip[0] > '0')                                     //如果skype有端口号
    {
        wxc_init(&skype_handle, g_config.skype_ip, g_config.skype_port);
    } 
    else
    {
        wxc_init(&skype_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
    }
    
    if (NULL == skype_handle)
    {
        return NULL;
    }
    wxc_handle handle = skype_handle;

    // 监控手机号 init
    void* hash_phone_num = phone_watch_init();

    // 命运的轮回...
    while(1) // should I exit ?
    {
        sleep(g_config.wx_session_timeloop);

        int   hash_count= 0;
        int   KeyTop    = 0;
        int   send      = 0;
        int   relation  = 0;
        void* Stack[MAX_TIME_OUT_NODE];// 用来存储超时的 hash node

        char* key = NULL;
        ST_SkypeMediaSessionAlive* value = NULL;

        // 遍历所有 session node
        pthread_rwlock_rdlock(&skype_rwlock);     //读锁.锁.锁.锁.锁.锁.锁.
        GHashTableIter iter;
        g_hash_table_iter_init(&iter, (GHashTable *) hash);//使用迭代器遍历哈希表
        while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&value))
        {
            // 有效性 检测
            if (NULL == handle || NULL == value)
            {
                continue;
            }

            // 超时判断
            if (time(NULL) - value->PersonLastActiveTime > g_config.wx_session_timeout)
            {
                if (KeyTop < MAX_TIME_OUT_NODE && NULL != key)
                {
                    value->isTimeout = 1;  // 设置超时标记
                    Stack[KeyTop++] = key; // 记录待删除的key
                }
            }

            // 统计建联人数
            ++ hash_count;
            if (value->trailer.MSISDN && value->trailer.IMSI && value->trailer.IMEI)
            {
                ++relation;
            }

            // 发送所有的 session node
            // stun包数必须大于 N 个才能进入聚合
            if (value->PersonA2B_Stun_Packets > g_config.wx_session_flag_pkt
                  && value->PersonB2A_Stun_Packets > g_config.wx_session_flag_pkt )
            {
                dpi_TrailerUpdateTS(&value->trailer);
                wxc_sendMsg(handle, (const unsigned char*)value, sizeof(ST_SkypeMediaSessionAlive), WXCS_SKYPE_MEDIA_CHAT);
                send++;
            }

            // 输出调试
            print_st(value);

            // 监控手机号 find
            char phone_num[20];
            snprintf(phone_num, 20, "%ld", value->trailer.MSISDN);
            if (1 == phone_watch_find(hash_phone_num, phone_num))
            {
                printf("find_phone        :%s\n", phone_num);
                // print_session(value);
            }
        } // End of foreach Hash ...
        pthread_rwlock_unlock(&skype_rwlock);     //解锁.锁.锁.锁.锁.锁.锁.

        // 打印 统计 信息
        char buffer[26];
        time_t timer;
        time(&timer);
        struct tm* tm_info = localtime(&timer);
        strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
        printf("SkypeOnline :[%s] 已发送/已超时/已建联/总人数=[%d/%d/%d/%d]\n", buffer, send, KeyTop, relation, hash_count);

        // 开始删除超时的 Hash node
        pthread_rwlock_wrlock(&skype_rwlock);     //读锁.锁.锁.锁.锁.锁.锁.
        while(KeyTop--)
        {
            skype_session_hash_delete_unthread_safe(hash, Stack[KeyTop]);
        }
        pthread_rwlock_unlock(&skype_rwlock);
    }

    // Fini 即使代码走不到这里, 我也要写一下.
    if (NULL != handle)
    {
        wxc_fini(handle);
        handle = NULL;
    }

    if (NULL != hash)
    {
        skype_session_hash_destory(hash);
        hash = NULL;
    }

    if (NULL != hash_phone_num)
    {
        phone_watch_fini(hash_phone_num);
        hash_phone_num = NULL;
    }

    return NULL;
}

int init_dissector_skype_sesion_thread(void)
{
    //如果微信话单 功能没有开启
    if (g_config.protocol_switch[PROTOCOL_SKYPE_MEDIA_CHAT] == 0)
    {
        return 0;
    }

    int status = pthread_create(&skype_session_thread, NULL, skype_media_session_thread, NULL);
    if (status != 0)
    {
        DPI_LOG(DPI_LOG_ERROR, "error on create skype_session thread");
        exit(-1);
    }

    return 0;// 返回到上一层
}

int wait_dissector_skype_sesion_thread_finish(void)
{
    return pthread_join(skype_session_thread, NULL);
}


static int dissect_skype_media_chat(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    /* 配置项加载 */
    if (g_config.protocol_switch[PROTOCOL_SKYPE_MEDIA_CHAT] == 0)
    {
        return 0;
    }

    if (flow->real_protocol_id != PROTOCOL_SKYPE_MEDIA_CHAT)
    {
        return -1;
    }


    while (NULL == skype_hash);
    void* hash = skype_hash;

    uint32_t ip_src    = 0;
    uint32_t ip_dst    = 0;
    uint16_t port_src  = 0;
    uint16_t port_dst  = 0;

    port_dst  = ntohs(flow->tuple.inner.port_dst);
    port_src  = ntohs(flow->tuple.inner.port_src);
    ip_src    =       flow->tuple.inner.ip_src.ip4;
    ip_dst    =       flow->tuple.inner.ip_dst.ip4;

    /* 流标识，识别流特征，更新流状态 */
    ST_SkypeChat * pflow_stat = NULL;

    if (NULL == flow->app_session)
    {
        pflow_stat = Skype_FlowMemInit(flow);
    }
    else
    {
        pflow_stat = flow->app_session;
    }

    if (NULL == pflow_stat)
    {
        return -1;
    }
    
    if (FLOW_DIR_SRC2DST == direction)
    {
        pflow_stat->BytesA2B += payload_len;
    }
    else if(FLOW_DIR_DST2SRC == direction)
    {
        pflow_stat->BytesB2A += payload_len;
    }

    if ( isNeedStun(payload, payload_len) )
    {
        if (get_uint16_ntohs(payload, 0) == 0x0001)
        {
            FLOW_DIR_SRC2DST == direction ? ++ pflow_stat->StunRequestsA2B : ++ pflow_stat->StunRequestsB2A;
        }
        else if (get_uint16_ntohs(payload, 0) == 0x0101)
        {
            FLOW_DIR_SRC2DST == direction ? ++ pflow_stat->StunResponsesA2B : ++ pflow_stat->StunResponsesB2A;
        }
        // 预计添加解析stun函数的位置
    }
    else if ( isSkypeUDP(payload) && isSkypeFlowStart(pflow_stat) )
    {
        if (payload[0] == 0x80 || payload[0] == 0x81)
        {
            FLOW_DIR_SRC2DST == direction ? ++ pflow_stat->OtherA2B : ++ pflow_stat->OtherB2A;
        }
        else if (payload[0] == 0x90)     /* 数据包到来 */
        {
            FLOW_DIR_SRC2DST == direction ? ++ pflow_stat->DataA2B : ++ pflow_stat->DataB2A;

            if (pflow_stat->DataActiveTime == 0 )
            {
                pflow_stat->DataActiveTime = time(NULL);
            }

            uint16_t data_seq = get_uint16_ntohs(payload, 2);

            // 开始序号检测以及更新
            if (check_sequence_num(flow, data_seq, (uint8_t)direction) < 0)
            {
                return -1;
            }

            if (FLOW_DIR_SRC2DST == direction)
            {
                pflow_stat->seqA2B = data_seq;
            }
            else if(FLOW_DIR_DST2SRC == direction)
            {
                pflow_stat->seqB2A = data_seq;
            }

            // sessionID一致性检测
            if (!check_session_id(payload, payload_len, pflow_stat))
            {   /* sessionID 不一致， 则说明识别错误，不是Skype的流 */
                return -1;
            }
            
        }
    }
    else
    {
        FLOW_DIR_SRC2DST == direction ? ++ pflow_stat->UnknownA2B : ++ pflow_stat->UnknownB2A;
    }

    if (pflow_stat->FirstActiveTime == 0)
    {
        pflow_stat->FirstActiveTime = time(NULL);
    }
    if (pflow_stat->FirstActiveTime > 0)
    {
        pflow_stat->LastActiveTime = time(NULL);
    }

    if ( !( isSkypeFlowStart(pflow_stat) && pflow_stat->DataActiveTime > 0 ) ) //流未开始，先等等
    {
        return 0;
    }
    else
    {
        pflow_stat->sessionType = WXCS_SESSION_CHAT_SINGLE_AUDIO;
    }
    
    /* hash表更新 */
    /* 1. 提取session_id, 根据session_id去生成hashkey */
    char Hash_Key[64];
    memset(Hash_Key, 0, sizeof(Hash_Key));

    // hash Key = [ 由2种元素组成,  { msisdn/client_ip } + session_id  ]  => Hash_Key

    int ret = 0;

    if (1 == flow->has_trailer) //如果有RTL标签
    {
        ST_trailer tr = {0};
        dpi_TrailerParser(&tr, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype);
        ret += snprintf(Hash_Key + ret, sizeof (uint64_t), "%lx", tr.MSISDN);  // 手机号 作为 hashkey的一部分
    }
    else
    {
        ret += snprintf(Hash_Key + ret, sizeof(ip_src), "%x", ip_src);  // 将ip_src和ip_dst一起作为HASH key 的一部分(毕竟不是真p2p)
        ret += snprintf(Hash_Key + ret, sizeof(ip_src), "%x", ip_dst);  // 将ip_src和ip_dst一起作为HASH key 的一部分(毕竟不是真p2p)
    }

    if (pflow_stat != NULL && pflow_stat->sessionID_len> 0) // 取出9字节的sessionID
    {
        ret += snprintf(Hash_Key + ret, pflow_stat->sessionID_len, "%lx%hhx", *(uint64_t *)pflow_stat->sessionID, pflow_stat->sessionID[pflow_stat->sessionID_len-1]);
    }

    Hash_Key[63] = '\0';

    ST_SkypeMediaSessionAlive* value = skype_session_hash_find(hash, Hash_Key);

    if (NULL == value)
    {
        // 创建
        value = malloc(sizeof(ST_SkypeMediaSessionAlive));
        if (NULL == value)
        {
            DPI_LOG(DPI_LOG_ERROR, "[skype Session] not enough memory");
            return -1;
        }
        memset(value, 0, sizeof(ST_SkypeMediaSessionAlive));

        //1 设置建联 相关 参数
        dpi_TrailerParser(&value->trailer, (const char*)flow->trailer, flow->trailerlen, g_config.trailertype); // 标签解析
        dpi_TrailerGetMAC(&value->trailer, (const char*)flow->ethhdr,  g_config.RT_model); // 解析戎腾MAC
        dpi_TrailerGetHWZZMAC(&value->trailer, (const char *)flow->ethhdr);
        dpi_TrailerSetDev(&value->trailer, g_config.devname);        // 解析板号
        dpi_TrailerSetOpt(&value->trailer, g_config.operator_name);  // 运营商
        dpi_TrailerSetArea(&value->trailer, g_config.devArea);       // 地域名

        value->ip_version = 4;
        // 1.2 设置 五元组
        if (FLOW_DIR_SRC2DST == direction)
        {
            value->client_ip.ipv4        = ip_src;
            value->server_ip.ipv4        = ip_dst;
            value->client_port           = port_src;
            value->server_port           = port_dst;
        }
        else
        {
            value->client_ip.ipv4        = ip_dst;
            value->server_ip.ipv4        = ip_src;
            value->client_port           = port_dst;
            value->server_port           = port_src;
        }

        //2  设置业务相关 参数
        value->Session_hash = jhash(pflow_stat->sessionID, pflow_stat->sessionID_len, 31);
        memcpy(&(value->SessionID), pflow_stat->sessionID, pflow_stat->sessionID_len);

        //3 存入 Hash表
        skype_session_hash_insert(hash, g_strdup(Hash_Key), (void*)value); // Hash_Key的内容需要全程维护
    }

    // 更新
    if (FLOW_DIR_SRC2DST == direction)
    {
        if(value->PersonA2BTransPackets == 0)
        {
            value->PersonA2BTransPackets = pflow_stat->UnknownA2B + pflow_stat->StunRequestsA2B + pflow_stat->StunResponsesA2B + pflow_stat->OtherA2B;
        }
        ++ value->PersonA2BTransPackets;
        value->PersonA2BTransBytes    = pflow_stat->BytesA2B;
        value->PersonA2BUnknown       = pflow_stat->UnknownA2B;
        value->PersonA2B_Stun_Packets = pflow_stat->StunRequestsA2B + pflow_stat->StunResponsesA2B; 
    }
    else
    {
        if(value->PersonB2ATransPackets == 0)
        {
            value->PersonB2ATransPackets = pflow_stat->UnknownB2A + pflow_stat->StunRequestsB2A + pflow_stat->StunResponsesB2A + pflow_stat->OtherB2A;
        }
        ++ value->PersonB2ATransPackets;
        value->PersonB2ATransBytes    = pflow_stat->BytesB2A;
        value->PersonB2AUnknown       = pflow_stat->UnknownB2A;
        value->PersonB2A_Stun_Packets = pflow_stat->StunRequestsB2A + pflow_stat->StunResponsesB2A; 
    }

    // 刷一下存在感
    value->PersonFirstActiveTime = pflow_stat->FirstActiveTime;
    value->PersonLastActiveTime  = pflow_stat->LastActiveTime;
    
    // 以后补充stun信息的地方

    // 收工!
    return 0;

}

static void identify_skype_media_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_SKYPE_MEDIA_CHAT] == 0)
    {
        return;
    }

    if (payload_len < 20)
    {
        return;
    }

    /* 判断UDP报文的前缀和类型 */
    if ( isNeedStun(payload, payload_len) || isSkypeUDP(payload) )
    {
        flow->real_protocol_id = PROTOCOL_SKYPE_MEDIA_CHAT;
    }

    /* 当前这路流不是Skype话单. 自我标识. */
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_SKYPE_MEDIA_CHAT);
}

void init_skype_media_dissector(void)
{
    udp_detection_array[PROTOCOL_SKYPE_MEDIA_CHAT].proto         = PROTOCOL_SKYPE_MEDIA_CHAT;
    udp_detection_array[PROTOCOL_SKYPE_MEDIA_CHAT].identify_func = identify_skype_media_chat;
    udp_detection_array[PROTOCOL_SKYPE_MEDIA_CHAT].dissect_func  = dissect_skype_media_chat;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_SKYPE_MEDIA_CHAT].excluded_protocol_bitmask, PROTOCOL_SKYPE_MEDIA_CHAT);

    return;
}

