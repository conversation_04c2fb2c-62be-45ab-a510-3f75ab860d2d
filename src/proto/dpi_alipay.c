#include <stdint.h>
#include <stdlib.h>
#include <sys/time.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include <string.h>

#include "glib.h"

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tbl_record_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "openssl/md5.h"
#include "dpi_protorecord.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_record_mempool;

typedef enum _em_alipay_type{
    EM_ALIPAY_SEND_UID,
    EM_ALIPAY_RECIVE_UID,
    EM_ALIPAY_GROUP_UID,
    EM_ALIPAY_COMMAND,
    EM_ALIPAY_SEND_FILE_TIME,
    EM_ALIPAY_SOURCE_FILENAME,
    EM_ALIPAY_FILETYPE,
    EM_ALIPAY_LOCAL_FILENAME,

    EM_ALIPAY_MAX
}em_alipay_type;

dpi_field_table alipay_array_f[] = {
    DPI_FIELD_D(EM_ALIPAY_SEND_UID,                   EM_F_TYPE_STRING,                 "sendUID"),
    DPI_FIELD_D(EM_ALIPAY_RECIVE_UID,                 EM_F_TYPE_STRING,                 "reciveUID"),
    DPI_FIELD_D(EM_ALIPAY_GROUP_UID,                  EM_F_TYPE_STRING,                 "groupUID"),
    DPI_FIELD_D(EM_ALIPAY_COMMAND,                    EM_F_TYPE_STRING,                 "command"),
    DPI_FIELD_D(EM_ALIPAY_SEND_FILE_TIME,             EM_F_TYPE_STRING,                 "sendFileTime"),
    DPI_FIELD_D(EM_ALIPAY_SOURCE_FILENAME,            EM_F_TYPE_STRING,                 "sourceFilename"),
    DPI_FIELD_D(EM_ALIPAY_FILETYPE,                   EM_F_TYPE_STRING,                 "filetype"),
    DPI_FIELD_D(EM_ALIPAY_LOCAL_FILENAME,             EM_F_TYPE_STRING,                 "localFilename"),
};



static void init_alipay_protorecord(void){
    write_proto_record_field_tab((dpi_record_field_table *)alipay_array_f, EM_ALIPAY_MAX, "alipay");
}

static __attribute((constructor)) void     before_init_alipay(void){
    dpi_proto_register("alipay", init_alipay_protorecord);
}

