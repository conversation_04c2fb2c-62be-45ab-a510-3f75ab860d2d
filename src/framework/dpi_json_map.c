#include "../include/rapidjson/document.h"
#include "../include/rapidjson/pointer.h"
#include "../include/rapidjson/stringbuffer.h"
#include "../include/rapidjson/writer.h"
#include "dpi_json_map.h"
#include <cstddef>
#include <iostream>
#include <map>
#include <string.h>
#include <utility>

class Recoder {
public:
  Recoder(const char *buffer) {
    d.Parse(buffer);
    // if(d.HasParseError()){
    // std::cout<<"parse error"<<std::endl;
    // }
  }

  int putValue(mb_mapping *mapping, int size) {
    for (int i = 0; i < size; ++i) {
      rapidjson::Value *value = rapidjson::Pointer(mapping[i].level).Get(d);
      if (value == NULL) {
        continue;
      }
      if (value->IsString()) {
        char *tmp_value = const_cast<char *>(value->GetString());
        // std::cout<<"value"<<value->GetString()<<std::endl;
        // std::cout<<"level="<<mapping[i].level<<"----->"<<tmp_value<<std::endl;
        map_[mapping[i].name] = tmp_value;
      } else {
        map_[mapping[i].name] = NULL;
      }
    }
    return 0;
  }

  int putValue_(mb_mapping *mapping, int size) {
    for (int i = 0; i < size; ++i) {
      rapidjson::Value *value = rapidjson::Pointer(mapping[i].level).Get(d);
      if (value == NULL) {
        continue;
      }
      std::string tmp_name(mapping[i].name);
      if (value->IsString()) {
        std::string tmp_value(value->GetString());
        map1_.insert(std::make_pair(tmp_name, tmp_value));
      } else {
        map1_.insert(std::make_pair(tmp_name, ""));
      }
    }
    return 0;
  }

  char *getValue(char *name) {
    auto search = map_.find(name);
    if (search != map_.end()) {
      // std::cout << "Found "<<map_[name]<< '\n';
      return map_[name];
    } else {
      // std::cout << "Not found\n";
      return NULL;
    }
  }

  const char *getValue_(const char *name_ptr) const {
    std::string name(name_ptr);
    auto search = map1_.find(name);
    if (search != map1_.end()) {
      return search->second.c_str();
    } else {
      return NULL;
    }
  }
  char *isKeyExist(const char *level) {
    rapidjson::Value *value = rapidjson::Pointer(level).Get(d);
    if (value == NULL) {
      return NULL;
    }
    if (value->IsString()) {
      return const_cast<char *>(value->GetString());
    }
  }

  std::string isKeyExist_(const char *level) {
    rapidjson::Value *value = rapidjson::Pointer(level).Get(d);
    if (value == NULL) {
      return "";
    }
    if (value->IsString()) {
      std::string value_(value->GetString());
      return value_;
    } else {
      return "";
    }
  }
  // ~Recoder() {}

private:
  std::map<char *, char *> map_;
  std::map<std::string, std::string> map1_;
  rapidjson::Document d;
};

#ifdef __cplusplus
extern "C" {
#endif

recoder_t *creat_recoder(char *buffer) { return new Recoder(buffer); }

void *recoder_putVal(recoder_t *recorder, mb_mapping *mapping,
                     int mapping_size) {
  recorder->putValue(mapping, mapping_size);
}
void *recoder_putVal_(recoder_t *recorder, mb_mapping *mapping,
                      int mapping_size) {
  recorder->putValue_(mapping, mapping_size);
}

char *recoder_getVal(recoder_t *recorder, char *key) {
  return recorder->getValue(key);
}
const char *recoder_getVal_(recoder_t *recorder, const char *key) {
  return recorder->getValue_(key);
}
void *destroy_recoder(recoder_t *recorder) {
  if (recorder != NULL) {
    delete recorder;
  }
}
const char *recoder_find_level_(recoder_t *recorder, char *level, char *buffer,
                                size_t buffer_size) {
  std::string result = recorder->isKeyExist_(level);
  if (result.length() != 0 && result.length() < buffer_size) {
    strcpy(buffer, result.c_str());
    return buffer;
  } else {
    return NULL;
  }
}
char *recoder_find_level(recoder_t *recorder, char *level) {
  return recorder->isKeyExist(level);
}
#ifdef __cplusplus
}
#endif